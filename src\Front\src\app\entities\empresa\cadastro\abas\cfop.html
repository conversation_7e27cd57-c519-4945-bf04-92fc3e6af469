<div class="form-horizontal">
    <hr-label dark="true"></hr-label>
    <br />
    <br />

    <div class="row">
        <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
            <div class="col-xs-6">
                <div class="form-group">
                    <consulta-padrao-modal name="Codigo" directivesizes="'col-xs-12 col-md-12'" 
                    labelsize="'col-xs-12 col-md-3 control-label alinhamento-labels'" 
                    label="'Código:'" placeholder="'Selecione um CFOP'" 
                    tabledefinition="vm.consultaCfop">
                    </consulta-padrao-modal>
                </div>
            </div>
            <div class="col-xs-6 text-right">
                <div class="form-group">
                    <button type="button" style="margin-left: 5px;" ng-disabled="!vm.consultaCfop.selectedValue" ng-click="vm.adicionarCfop()" class="btn btn-labeled btn-success text-right">
                        <span class="btn-label"><i class="fa fa-plus"></i></span>
                        Adicionar
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="form-horizontal">
            <hr />
            <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
                <div class="table-responsive">
                    <table class="table table-sm table-striped table-hover table-bordered" id="GridAdiantViagem">
                        <thead>
                            <th>Código</th>
                            <th>CFOP</th>
                            <th>Descrição</th>
                            <th></th>
                        </thead>
                        <tbody>
                            <tr ng-repeat="item in vm.empresa.empresaCfop">
                                <td>{{item.cfopId}}</td>
                                <td>{{item.cfop}}</td>
                                <td>{{item.descricao}}</td>
                                <td>
                                    <button title="Excluir" type="button" ng-click="vm.removeCfop(item.cfopId)" ng-class="'btn btn-xs btn-danger'">
                                        <i class="fa fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>