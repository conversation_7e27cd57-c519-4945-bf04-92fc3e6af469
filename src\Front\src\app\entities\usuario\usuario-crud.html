<style>
    #UsuarioCrudController .imgUsuario,
    .imgUsuarioFile {
        position: absolute;
        margin: -20px 105px;
        margin-left: 35%;
        height: 128px;
        width: 128px;
        border-radius: 65%;
        object-fit: cover;
        object-position: center;
    }
</style>
<div id="UsuarioCrudController" ng-controller="UsuarioCrudController as vm">
    <form-header ng-submit="return"
        items="[{name: 'Cadastros'}, {name: 'Usu<PERSON>rio',link: 'usuario.index'}, {name: vm.isNew() ? 'Novo' : 'Editar'}]"
        head="'Usuário'">
    </form-header>
    <div class="wrapper wrapper-content animated fadeIn mb-0 pb-0">
        <div class="row">
            <div class="col-lg-12">
                <div class="ibox animated fadeIn">
                    <div class="ibox-title">
                        <h5>{{vm.isNew() ? 'Novo' : 'Editar'}} usuário</h5>
                        <div ibox-tools></div>
                    </div>
                    <div class="ibox-content wizard">
                        <form name="formUsuario" role="form" novalidate ats-validator ng-submit="vm.save(formUsuario)"
                            show-validation>
                            <div form-wizard steps="4">
                                <div class="form-wizard">
                                    <ol class="row mt-n-10" style="margin-bottom: 0;">
                                        <li ng-class="{'active':wizard.active(1)}" ng-click="wizard.go(1)"
                                            class="control-label col-xs-12 col-sm-6 col-md-6 col-lg-6">
                                            <h4>Principal</h4>
                                        </li>
                                        <li ng-class="{'active':wizard.active(2)}" ng-click="wizard.go(2)"
                                            class="control-label col-xs-12 col-sm-6 col-md-6 col-lg-6">
                                            <h4>Filial</h4>
                                        </li>
                                    </ol>
                                    <ol class="row mt-n-10" style="margin-bottom: 0;">
                                        <li ng-class="{'active':wizard.active(3)}" ng-click="wizard.go(3)"
                                            class="control-label col-xs-12 col-sm-6 col-md-6 col-lg-6">
                                            <h4>Centro de custo</h4>
                                        </li>
                                        <li ng-class="{'active':wizard.active(4)}" ng-if="!vm.isNew()" ng-click="wizard.go(4)"
                                            class="control-label col-xs-12 col-sm-6 col-md-6 col-lg-6">
                                            <h4>Histórico</h4>
                                        </li>
                                    </ol>
                                    <div ng-show="!vm.carregandoEdit">
                                        <div ng-show="wizard.active(1)">
                                            <div ng-include="'app/entities/usuario/abas/aba-principal.html'"
                                                class="form-horizontal"></div>
                                        </div>
                                        <div ng-show="wizard.active(2)">
                                            <div ng-include="'app/entities/usuario/abas/aba-filial.html'"
                                                class="form-horizontal"></div>
                                        </div>
                                        <div ng-show="wizard.active(3)">
                                            <div ng-include="'app/entities/usuario/abas/aba-centro-custo.html'"
                                                class="form-horizontal"></div>
                                        </div>
                                        <div ng-if="wizard.active(4)">
                                            <div ng-include="'app/entities/usuario/abas/aba-historico.html'"
                                                class="form-horizontal"></div>
                                        </div>
                                    </div>
                                    <div ng-show="vm.carregandoEdit">
                                        <div class="spiner-example">
                                            <div class="sk-spinner sk-spinner-wave">
                                                <div class="sk-rect1"></div>
                                                <div class="sk-rect2"></div>
                                                <div class="sk-rect3"></div>
                                                <div class="sk-rect4"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row clearfix"> </div>
                                <hr-label dark="true"></hr-label>
                                <br/>
                                <div class="row">
                                    <div class="form-group">
                                        <div class="text-right">
                                            <button type="button" ng-disabled="vm.saving"
                                                ng-click="wizard.getActivePosition() == 1 ? $state.go('usuario.index') : wizard.go(wizard.getActivePosition() - 1)"
                                                class="btn btn-labeled btn-default">
                                                <span class="btn-label">
                                                    <i class="fa fa-arrow-circle-left"></i>
                                                </span>
                                                Voltar
                                            </button>
                                            <button type="button" ng-show="vm.isNew()? !wizard.active(3): !wizard.active(4)" ng-disabled="vm.saving"
                                                ng-click="wizard.go(wizard.getActivePosition() + 1);"
                                                class="btn btn-labeled btn-default">
                                                <span class="btn-label">
                                                    <i class="fa fa-arrow-circle-right"></i>
                                                </span>
                                                Avançar
                                            </button>
                                            <button type="submit" ng-disabled="vm.saving"
                                                class="btn btn-labeled btn-success text-right"
                                                data-style="expand-right">
                                                <span class="btn-label">
                                                    <i class="fa fa-check-circle"></i>
                                                </span>
                                                Salvar
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>