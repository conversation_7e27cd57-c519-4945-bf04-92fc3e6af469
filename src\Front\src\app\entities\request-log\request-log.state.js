(function () {
    'use strict';

    angular.module('bbcWeb.request.state', []).config(routeConfig);

    function routeConfig($urlRouterProvider, $stateProvider) {
        $stateProvider
            .state('requestLog', {
                url: "/requestLog",
                abstract: true,  
                templateUrl: "app/layout/content.html"
            })
            .state('requestLog.index', {
                url: "/index",
                templateUrl: "app/entities/request-log/request-log.html"
            })
    }
})();