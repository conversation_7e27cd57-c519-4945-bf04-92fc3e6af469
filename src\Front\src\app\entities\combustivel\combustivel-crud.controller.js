(function () {
    'use strict';

    angular.module('bbcWeb').controller('CombustivelCrudController', CombustivelCrudController);

    CombustivelCrudController.$inject = ['toastr', '$rootScope', 'BaseService', '$state', '$stateParams', '$window', 'PERFIL_ADMINISTRADOR', '$scope', '$timeout', 'PersistentDataService', 'DefaultsService', '$uibModal'];

    function CombustivelCrudController(
        toastr, $rootScope,
        BaseService, $state,
        $stateParams, $window,
        PERFIL_ADMINISTRADOR,
        $scope, $timeout,
        PersistentDataService,
        DefaultsService,
        $uibModal
    ) {
        var vm = this;
        vm.combustivel = [];
        vm.menusPai = [];

        vm.headerItems = [{
            name: 'Cadastros'
        }, {
            name: 'Combustível',
            link: 'combustivel.index'
        }, {
            name: $stateParams.link == 'novo' ? 'Novo' : 'Editar'
        }];

        var selfScope = PersistentDataService.get('CombustivelCrudController');

        if ($stateParams.link == 'novo')
            vm.combustivel.id = 'Auto';

        vm.isNew = function () {
            return $stateParams.link == 'novo';
        };

        vm.comboUnidade = {
            data: [{ id: "LT", descricao: 'Litros' }, { id: "MC", descricao: 'Metro cubico' }, { id: "UN", descricao: 'Unidade' }]
        };

        vm.loadEdit = function (id) {
            BaseService.get('Combustivel', 'ConsultarPorId', {
                idcombustivel: id
            }).then(function (response) {
                if (!response.success) {
                    toastr.error(response.message);
                    return;
                } else {
                    vm.combustivel = response.data;
                }
            });
        };

        vm.save = function (form) {
            if (!form.$valid) {
                toastr.error('Todos os campos obrigatórios devem ser preenchidos corretamente com seus respectivos valores');
                return;
            }

            if (vm.isSaving === true)
                return;

            var combustivel = {}

            combustivel.Id = vm.combustivel.id == "Auto" ? 0 : vm.combustivel.id;
            combustivel.Nome = vm.combustivel.nome;
            combustivel.Ativo = vm.combustivel.ativo;
            combustivel.UnidadeMedida = vm.combustivel.unidadeMedida;
            combustivel.CodigoExterno = vm.combustivel.codigoExterno;

            vm.isSaving = true;

            BaseService.post('Combustivel', 'Salvar', combustivel).then(function (response) {
                vm.isSaving = false;
                if (!response) {
                    toastr.error('Erro de comunicação com o servidor, tente novamente em alguns minutos.')
                    return
                }

                if (!response.success) {
                    toastr.error(response.message)
                    return
                }

                toastr.success(response.message)
                $state.go('combustivel.index');
            });
        };

        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.administrador == true;
        };

        function init() {
            if (vm.isNew()) {
                vm.combustivel.id = 'Auto';
            }
        };

        vm.onClickAvancar = function (wizard) {
            wizard.go(wizard.getActivePosition() + 1);
        };

        vm.onClickVoltar = function (wizard) {

            var ativoIndex = wizard.getActivePosition();
            if (ativoIndex == 1)
                $state.go('combustivel.index');

            wizard.go(ativoIndex - 1);
        };

        init();

        $scope.$on('$stateChangeStart', function (arg1, toState) {
            if (toState.name === 'combustivel.index')
                PersistentDataService.remove('CombustivelCrudController');
            else
                PersistentDataService.store('CombustivelCrudController', vm, "Cadastro - Combustível", null, "combustivel.combustivel-crud", vm.combustivel.id);
        });

        if (angular.isDefined(selfScope))
            angular.extend(vm, selfScope.data);
        else {
            if (!vm.isNew())
                vm.loadEdit($stateParams.link);
            else {
                vm.Combustivel = 0;
                vm.modulos = 1;
            }
        }

        $timeout(function () {
            PersistentDataService.remove('CombustivelController');
        }, 15);
        
    }
})();
