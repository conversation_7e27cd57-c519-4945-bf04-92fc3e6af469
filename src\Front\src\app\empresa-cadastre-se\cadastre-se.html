<div id="EmpresaCadastreSeController" ng-controller="EmpresaCadastreSeController as vm">
    <div class="row border-bottom">
        <nav class="navbar navbar-static-top white-bg cabecalho-gradiente" role="navigation"
            style="margin-bottom: 0; height: 85px !important;">
            <img class="logo-bbc-header" src="assets/images/BBC_LOGO_DIGITAL_PANTONE.png">
        </nav>
    </div>
    <div class="wrapper-content animated overflow-hidden fadeIn mb-0 pb-0">
        <div class="form-group">
            <div>
                <div class="ibox animated fadeIn">
                    <div class="ibox-content wizard">
                        <div form-wizard steps="1">
                            <form id="frmCadastreSe" name="frmCadastreSe" novalidate ats-validator
                                ng-submit="vm.save(frmCadastreSe)">
                                <div class="form-wizard">
                                    <ol class="row">
                                        <li ng-class="{'active':wizard.active(1)}" style="height: 20px"
                                            ng-click="wizard.go(1)"
                                            class="control-label col-xs-12">
                                            <h4>Empresa</h4>
                                        </li>
                                        <!--<li ng-class="{'active':wizard.active(2)}" style="height: 20px"
                                            ng-click="wizard.go(2)"
                                            class="control-label col-xs-12 col-sm-6 col-md-6 col-lg-6">
                                            <h4>Representante Legal</h4>
                                        </li>-->
                                    </ol>
                                    <div ng-show="wizard.active(1)">
                                        <div ng-include="'app/empresa-cadastre-se/abas/empresa-cadastre-se.html'"
                                            class="form-horizontal">
                                        </div>
                                    </div>
                                    <button id="buttonATSValidator1" type="submit" style="display: none"></button>
                                </div>
                            </form>
                            <!--<div ng-show="wizard.active(1)">
                                <div ng-include="'app/empresa-cadastre-se/abas/rep-legal-cadastre-se.html'"
                                    class="form-horizontal"> </div>
                            </div>-->
                            <div class="row">
                                <div class="form-group">
                                    <div class="col-md-12 col-lg-12 text-right">
                                        <button type="button" ng-click="vm.fnVoltar(wizard);"
                                            class="btn btn-labeled btn-default">
                                            <span class="btn-label">
                                                <i class="fa fa-arrow-circle-left"></i>
                                            </span>
                                            Voltar
                                        </button>
                                        <!--<button type="button" ng-show="!wizard.active(2)"
                                            ng-click="wizard.go(wizard.getActivePosition() + 1);"
                                            class="btn btn-labeled btn-default">
                                            <span class="btn-label">
                                                <i class="fa fa-arrow-circle-right"></i>
                                            </span>
                                            Avançar
                                        </button>-->
                                        <button type="button" ng-show="wizard.active(1)"
                                            onclick="document.getElementById('buttonATSValidator1').click()"
                                            ng-disabled="vm.saving"
                                            class="btn btn-labeled btn-success text-right ladda-button"
                                            data-style="expand-right">
                                            <span class="btn-label">
                                                <i class="fa fa-check-circle"></i>
                                            </span>
                                            Salvar
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .cabecalho-gradiente {
        background-image: linear-gradient(to right, rgba(255, 0, 0, 0), rgb(4, 107, 49))
    }
</style>