(function() {
    'use strict';

    angular.module('bbcWeb')
        .controller('EstadoCrudController', EstadoCrudController);

    EstadoCrudController.$inject = [
        '$scope',
        'PersistentDataService',
        'toastr',
        'BaseService',
        'TipoCarreta',
        '$state',
        '$stateParams',
        '$window',
        '$rootScope',
        '$timeout'
    ];

    function EstadoCrudController(
        $scope,
        PersistentDataService,
        toastr,
        BaseService,
        TipoCarreta,
        $state,
        $stateParams,
        $window,
        $rootScope,
        $timeout) {

        //Inicialização dos objetos e arrays
        var vm = this;
        vm.estado = {};
        vm.perfilUsuarioLogado = $rootScope.usuarioLogado.perfil;

        if ($stateParams.link == 'novo')
            vm.estado.IdEstado = 'Auto';
        vm.isNew = function() {
            return $stateParams.link == 'novo';
        };
        vm.headerItems = [{
                name: 'Administração'
            },
            {
                name: 'Estado',
                link: 'configuracao.estado'
            },
            {
                name: $stateParams.link == 'novo' ? 'Novo' : 'Editar'
            }
        ];

        vm.loadEdit = function(id) {
            vm.carregarPaises(function() {
                BaseService.get('EstadoAts', 'ConsultarPorId', {
                    idEstado: id
                }).then(function(response) {
                    vm.estado = angular.fromJson(response.data);
                });
            });
        };

        vm.load = function() {
            vm.carregarPaises();
        };

        //Selects de endereço
        vm.carregarPaises = function(callback) {
            vm.selectEstados = [];
            vm.selectCidades = [];
            BaseService.get('PaisAts', 'Consultar').then(function(response) {
                if (response.success)
                    vm.selectPaises = response.data;

                if (angular.isFunction(callback))
                    callback();
            });
        };

        //Salvar e atualizar
        vm.save = function() {
            BaseService.post('EstadoAts', $stateParams.link === 'novo' ? 'Cadastrar' : 'Editar', vm.estado)
                .then(function(response) {
                    if (response.success) {
                        toastr.success(response.message);
                        $state.go('configuracao.estado');
                    } else {
                        toastr.error(response.message);
                    }
                });
        };

        // DO NOT TOUCH!! 666
        $scope.$on('$stateChangeStart', function(_, toState) {
            if (toState.name === 'configuracao.estado')
                PersistentDataService.remove('EstadoCrudController');
            else
                PersistentDataService.store('EstadoCrudController', vm, "Estado", null, "configuracao.estado-crud", vm.estado.IdEstado);
        });
        var selfScope = PersistentDataService.get('EstadoCrudController');
        if (angular.isDefined(selfScope)) {
            angular.extend(vm, selfScope.data);
        } else {
            if (!vm.isNew())
                vm.loadEdit($stateParams.link);
            else
                vm.load();
        }

        $timeout(function() {
            PersistentDataService.remove('EstadoController');
        }, 15);
    }
})();