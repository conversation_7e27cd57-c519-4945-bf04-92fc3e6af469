(function () {
    'use strict';

    // Exemplo de código
    // <ats-code code="vm.porta"></ats-code>

    angular.module('bbcWeb').directive('atsCode', function () {
        return {
            bindToController: true,
            controller: function ($scope) {
                var vm = this;

                vm.setCode = function (code) {
                    angular.element("#codeStyle").html(syntaxHighlight(JSON.stringify(code, undefined, 4)));
                };

                function syntaxHighlight(json) {
                    json = json.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;');
                    return json.replace(/("(\\u[a-zA-Z0-9]{4}|\\[^u]|[^\\"])*"(\s*:)?|\b(true|false|null)\b|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?)/g, function (match) {
                        var cls = 'number';
                        if (/^"/.test(match)) {
                            if (/:$/.test(match)) {
                                cls = 'key';
                            } else
                                cls = 'string';
                        } else if (/true|false/.test(match)) {
                            cls = 'boolean';
                        } else if (/null/.test(match)) {
                            cls = 'null';
                        } else if (/undefined/.test(match)) {
                            cls = 'undefined';
                        }

                        return '<span class="' + cls + '">' + match + '</span>';
                    });
                }
            },
            controllerAs: 'vm',
            templateUrl: 'app/components/ats-code/ats-code.html',
            restrict: 'AE',
            scope: {
                code: '=code'
            }
        };
    });
})();