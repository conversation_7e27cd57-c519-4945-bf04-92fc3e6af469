<style>
    .input-group-custom {
    display: flex;
    align-items: center;
    }

    .custom-input {
        flex: 1;
        margin-right: 10px; /* Espaçamento entre o input e o toggle switch */
    }

    .custom-toggle {
        flex-shrink: 0;
    }

    span.switch-right {
        background: blue !important;
    }

    span.switch-left {
        background: green !important;
    }

    .input-group-custom {
        position: relative;
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
        }
        to {
            opacity: 1;
        }
    }

    @keyframes fadeOut {
        from {
            opacity: 1;
        }
        to {
            opacity: 0;
        }
    }

    .single-input {
        position: absolute;
        width: 100%;
        top: 0;
        left: 0;
        transition: opacity 0.3s ease-in-out;
    }

    .ng-enter {
        animation: fadeIn 0.3s forwards;
    }

    .ng-leave {
        animation: fadeOut 0.3s forwards;
    }

</style>

<div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
    <div class="row">
        <div class="col-xs-12 col-md-6">
            <div class="form-group">
                <label class="control-label col-xs-12 col-md-4">Tempo Atualização do telão <span style="color: red;">(Minutos)</span>:</label>
                <div class="input-group col-xs-12 col-md-8">
                    <input type="text" 
                        ng-model="vm.configuracaoTelao.tempoAtualizacaoTelao" 
                        name="ContaTarifaValePedagio"
                        ng-required="true"
                        required-message="'Conta tarifa é obrigatória'"
                        class="form-control ng-pristine ng-valid ng-empty ng-touched user-success custom-input single-input" />
                </div>
            </div>
        </div>
        <div class="col-xs-12 col-md-6">
            <div class="form-group">
                <label class="control-label col-xs-12 col-md-4"></label>
                <div class="input-group input-group-custom col-xs-12 col-md-8">
                    <label class="control-label col-xs-12 col-md-4">Quantidade de layouts do telão:</label>
                    <input type="text" 
                    ng-model="vm.configuracaoTelao.quantidadeLayoutTelao" 
                    name="ContaTarifaValePedagio"
                    ng-required="true"
                    required-message="'Conta tarifa é obrigatória'"
                    class="form-control ng-pristine ng-valid ng-empty ng-touched user-success custom-input single-input" />
                </div>
            </div>
        </div>
    
    </div>
   
</div>