/* ========================================================================
   Component: form-wizard.less
 ========================================================================== */

@wizard-primary-color:      #056233;
@wizard-steps-bg:             @jumbotron-bg;
@wizard-steps-bg-border:      #eaeaea;
@wizard-steps-bg-active:      @wizard-primary-color;
@wizard-steps-bg-border-active:  #056233;

@wizard-heading-color:        @text-color;
@wizard-subheading-color:     @text-muted;

.form-wizard {

  > ol {
    list-style-type: none;
    padding: 0 15px; // abort row expanding to border in panels
    text-align: right;

    // steps indicator
    > li {
      min-height: 33px;
      padding-top: 2px;
      padding-bottom: 0px;
      text-align: center;
      background-color: @wizard-steps-bg;
      border-color: @wizard-steps-bg-border;
      border-style: solid;
      cursor: pointer;
    @include transition(all .3s ease);

      // unstyle plugin anchors
      > a {
        text-decoration: none;
      }

      // label for step indicator
      .label {
        // display: inline-block;
      @include label-variant(@wizard-primary-color);
        vertical-align: super;
        margin-right: 6px;
      }

      // step title
      h4 {
        display: inline-block;
        color: @wizard-heading-color;
      }

      // subtext
      small {
        display: block;
        color: @wizard-subheading-color;
      }

      // Active step
      &.active {
        background-color: @wizard-primary-color;
        border-color: @wizard-steps-bg-border-active;
        border-style: solid;
        h4, small {
          color: #fff;
          height: 15px;
        }
        .label {
          background-color: #fff;
          border-color: #fff;
          color: @wizard-primary-color;
        }
      }
    }
  }

  .glyphicon {
    position: relative;
    top: 1px;
    display: inline-block;
    font-family: 'Glyphicons Halflings';
    font-style: normal;
    font-weight: normal;
    line-height: 2;
  
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  
  .time {
    padding-right: 5px;
  }

  .time-text-transaction {
    font-size: 14px;
    font-weight: bold;
  }

  .time-text-pos {
    font-size: 14px;
    font-weight: bold;
  }
  
  .bwizard-buttons {
    margin: 0;
  }

  // well is added to contain form elements
  .well {
    border: 0;
    margin: 0;
    padding: 0;
    box-shadow: 0 0 0 #000;
    fieldset {
      margin: 0;
    }
  }

  &.wizard-horizontal {
    @media only screen and (min-width: @mq-desktop) {
      > ol > li {
        display: inline-block;
      }
    }
  }
 
  &.wizard-vertical {
  @include clearfix;
    > ol {
      float: left;
      width: 39%;
      padding: 0;
      > li {
        display: block;
        padding: 10px;
        h4 {
          font-size: 14px;
        }
      }
    }
    > div {
      float: right;
      width: 59%;
    }
    .pager {
      clear: both;
      padding-top: 10px;
    }
    .well {
      padding: 0;
    }
  }

}

.form-wizard>ol>li small {
  display: block !important;
  height: 15px !important;
  text-decoration: underline;
}

.form-control[disabled],
.form-control[readonly],
fieldset[disabled] .form-control {
  background-color: #eeeeee75 !important;
  opacity: 1;
}
