﻿(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('PainelPagamentoController', PainelPagamentoController);

    PainelPagamentoController.inject = [
        'BaseService',
        '$rootScope',
        '$window',
        'toastr',
        '$scope',
        'PersistentDataService',
        '$timeout',
        '$state',
        '$uibModal',
        'PERFIL_ADMINISTRADOR',
        'oitozero.ngSweetAlert',
        'uiGridConstants'
    ];

    function PainelPagamentoController(
        BaseService, 
        $rootScope, 
        $window,
        toastr, 
        $scope,
        SweetAlert, 
        PersistentDataService, 
        $timeout, 
        $state,
        $uibModal,
        PERFIL_ADMINISTRADOR,
        uiGridConstants) {
        var vm = this;
        vm.headerItems = [{ name: 'Movimentações' }, { name: 'Painel de pagamento' }];
        vm.pagamentosRelatorio = [];
        vm.desabilitarBtnRelatorio = false;
        vm.consultandoPixManual = false;
        vm.primeiraConsulta = true;

        vm.exportarRelatorio = function (extensao) {
            vm.desabilitarBtnRelatorio = true;
            if(vm.origem === 1){
                if(extensao === 1){
                    vm.gridOptions.dataSource.consultarDadosRelatorio(function (response) {
                        vm.dadosJSLRelatorio = response.data.data;
                        $timeout(function () {
                            BaseService.exportarTabelaEmExcel2("exportable-xls", "Pagamentos", true)
                        }, 500);
                        vm.desabilitarBtnRelatorio = false;
                    });
                }else if(extensao === 2){
                    vm.gridOptions.dataSource.consultarDadosRelatorio(function (response) {
                        vm.dadosJSLRelatorio = response.data.data;
                        $timeout(function () {
                            BaseService.exportarTabelaEmPdfNovo("#exportable", "Pagamentos", "Pagamentos")
                        }, 500);
                        vm.desabilitarBtnRelatorio = false;
                    });
                }
            } else if(vm.origem === 2) {
                if(extensao === 1){
                    vm.gridBbcOptions.dataSource.consultarDadosRelatorio(function (response) {
                        vm.dadosBBCRelatorio = response.data.data.items;
                        $timeout(function () {
                            BaseService.exportarTabelaEmExcel2("exportable-xlsBbc", "Pagamentos", true)
                        }, 500);
                        vm.desabilitarBtnRelatorio = false;
                    }, "gridBbcOptions");
                }else if(extensao === 2){
                    vm.gridBbcOptions.dataSource.consultarDadosRelatorio(function (response) {
                        vm.dadosBBCRelatorio = response.data.data.items;
                        $timeout(function () {
                            BaseService.exportarTabelaEmPdfNovo("#exportableBbc", "Pagamentos", "Pagamentos")
                        }, 500);
                        vm.desabilitarBtnRelatorio = false;
                    }, "gridBbcOptions");
                }
            }
        };

        vm.abrirModalRelatorio = function (controllerPai, aba) {
            $uibModal.open({
                animation: true,
                ariaLabelledBy: 'modal-title',
                ariaDescribedBy: 'modal-body',
                templateUrl: 'app/entities/painel-pagamento/modal-relatorios/modal-relatorios.html',
                controller: function ($uibModalInstance, $uibModalStack) {
                    var vm = this;
                    controllerPai.origem = aba;

                    vm.modalJSLRelatorioOptions = [{}];
                    vm.modalBBCRelatorioOptions = [{}];

                    vm.headerItems = [{ name: 'Transações Financeiras' }];
                    if (aba === 1){
                        for (var x in controllerPai.gridOptions.columnDefs) {
                            vm.modalJSLRelatorioOptions[x] = {
                                name: controllerPai.gridOptions.columnDefs[x].displayName,
                                field: controllerPai.gridOptions.columnDefs[x].field,
                                pipe: controllerPai.gridOptions.columnDefs[x].pipe,
                                enabled: true
                            }
                        }
                    } else if(aba === 2){
                        for (var x in controllerPai.gridBbcOptions.columnDefs) {
                            vm.modalBBCRelatorioOptions[x] = {
                                name: controllerPai.gridBbcOptions.columnDefs[x].displayName,
                                field: controllerPai.gridBbcOptions.columnDefs[x].field,
                                pipe: controllerPai.gridBbcOptions.columnDefs[x].pipe,
                                enabled: true
                            }
                        }
                    }

                    vm.fechar = function () {
                        controllerPai.modalJSLRelatorioOptions = vm.modalJSLRelatorioOptions;
                        controllerPai.modalBBCRelatorioOptions = vm.modalBBCRelatorioOptions;
                        $uibModalStack.dismissAll();
                    }

                    vm.exportarRelatorio = function (extensao) {
                        if (vm.modalJSLRelatorioOptions.filter(function (x) {
                            return x.enabled
                        }).length < 2 && controllerPai.origem === 1) {
                            toastr.error("Escolha alguma coluna para gerar o relatório.");
                            return;
                        }else if (vm.modalBBCRelatorioOptions.filter(function (x) {
                            return x.enabled
                        }).length < 2 && controllerPai.origem === 2) {
                            toastr.error("Escolha alguma coluna para gerar o relatório.");
                            return;
                        }

                        if (controllerPai.dataJSL.endDate.diff(controllerPai.dataJSL.startDate, 'days') > 30 ||
                            controllerPai.dataBBC.endDate.diff(controllerPai.dataBBC.startDate, 'days') > 30) {
                            toastr.error("Escolha um período de no máximo 30 dias para gerar o relatório.")
                            return;
                        }
                        controllerPai.modalJSLRelatorioOptions = vm.modalJSLRelatorioOptions;
                        controllerPai.modalBBCRelatorioOptions = vm.modalBBCRelatorioOptions;
                        controllerPai.exportarRelatorio(extensao);

                        $uibModalStack.dismissAll();
                    }

                },
                controllerAs: 'vm',
                keyboard: true,
                size: 'lg'
            }).result.then(function () {
            });
        };

        vm.enumStatus = [
            { id: 5, descricao: 'Todos' },
            { id: 0, descricao: 'Bloqueado' },
            { id: 1, descricao: 'Aberto' },
            { id: 2, descricao: 'Baixado' },
            { id: 3, descricao: 'Cancelado' },
            { id: 4, descricao: 'Processamento' }
        ];

        vm.enumStatusPagamento = [
            { id: -1, descricao: 'Todos' },
            { id: 5, descricao: 'Processando' },
            { id: 0, descricao: 'Fechado' },
            { id: 1, descricao: 'Aberto' },
            { id: 2, descricao: 'Pendente' },
            { id: 3, descricao: 'Erro' },
            { id: 4, descricao: 'Cancelado' },
            { id: 6, descricao: 'Não Concluído' }
        ];

        vm.statusBbc = -1;

        vm.status = 5;

        vm.atualizaTela = function () {
            vm.gridOptions.dataSource.refresh();
        }
        vm.atualizaTelaBbc = function () {
            vm.gridBbcOptions.dataSource.refresh();
        }

        vm.novo = function () {
            toastr.info('Função indisponível no momento!');
        }

        vm.dataJSL = {
            startDate: moment().add(-1, 'days'),
            endDate: moment()
        };

        vm.dateOptions = {
            timePicker: false,
            timePicker24Hour: false,
            applyClass: 'btn-primary',
            locale: {
                applyLabel: "Aplicar",
                fromLabel: "De",
                format: "DD/MM/YYYY",
                toLabel: "Até",
                cancelLabel: 'Cancelar',
                customRangeLabel: 'Período',
                monthNames: ['Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho', 'Julho', 'Agosto', 'Setembro',
                    'Outubro', 'Novembro', 'Dezembro'],
                daysOfWeek: ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb']
            },
            ranges: {
                'Últimos 1 dias': [moment().add(-1, 'days'), moment()],
                'Últimos 7 dias': [moment().add(-7, 'days'), moment()]
            }
        };

        if ($rootScope.usuarioLogado.administrador == true || $rootScope.usuarioLogado.administrador == "true") {
            vm.usuAdm = true;
        } else {
            vm.usuAdm = null;
        }

        vm.isEmpresa = function () {
            return $rootScope.usuarioLogado.empresaId > 0;
        };

        vm.getNomeEmpresaUsuarioLogado = function () {
            return $window.localStorage.getItem("empresaNome");
        }

        vm.alterarStatus = function (entity, isCancelar) {
            var status;
            var msg = 'Cancelar pagamento do CIOT ' + entity.id + ' de ' + entity.valor + ' para o beneficiário: ' + entity.nome + '?';

            if (isCancelar)
                status = 3;
            else {
                if (entity.status === 'Bloqueado') {
                    status = 1;
                } else {
                    status = 0;
                }
            }

            if (isCancelar) {
                Sistema.Msg.confirm(msg, function () {
                    BaseService.post('PainelPagamento', "AlterarStatus", { id: entity.id, Status: status }).then(function (response) {
                        response.success ? (isCancelar ? toastr.success('Pagamento cancelado com sucesso!') : status === 'Bloqueado' ? toastr.success('Pagamento desbloqueado com sucesso!') : toastr.success('Pagamento bloqueado com sucesso!')) : toastr.error(response.message);
                        vm.gridOptions.dataSource.refresh();
                    });
                }, function () {

                });
            } else {
                BaseService.post('PainelPagamento', "AlterarStatus", { id: entity.id, Status: status }).then(function (response) {
                    response.success ? (isCancelar ? toastr.success('Pagamento cancelado com sucesso!') : status === 'Bloqueado' ? toastr.success('Pagamento desbloqueado com sucesso!') : toastr.success('Pagamento bloqueado com sucesso!')) : toastr.error(response.message);
                    vm.gridOptions.dataSource.refresh();
                });
            }
        };

        vm.consultarPixManual = function (id) {
            vm.consultandoPixManual = true;
            BaseService.post('PagamentoEvento', 'ConsultarPixManual', id).then(function (response) {
                vm.consultandoPixManual = false;

                if (!response) return

                if (!response.success) return toastr.error(response.message)

                toastr.success(response.message)

                vm.gridBbcOptions.dataSource.refresh()
            });
        }

        vm.reenviarPagamento = function (entity) {
            if (vm.usuAdm) {
                toastr.error('Administradores não podem reenviar pagamentos!');
                return;
            }

            var msg = 'Deseja reenviar o pagamento?';

            Sistema.Msg.confirm(msg, function () {
                BaseService.post('PainelPagamento', "ReenviarPagamento", { Id: entity.id }).then(function (response) {
                    response.success ? toastr.success('Pagamento efetuado com sucesso!') : toastr.error(response.message);
                    vm.gridOptions.dataSource.refresh();
                });
            }, function () {

            });
        };


        vm.gridOptions = {
            data: [],
            onRegisterApi: BaseService.dataGrid.defaultOnRegisterApi,
            urlRelatorio: "PainelPagamento/DadosRelatorioGridPagamentos",
            params: function () {
                return {
                    dataInicial: vm.dataJSL.startDate.format('DD/MM/YYYY').toString(),
                    dataFinal: vm.dataJSL.endDate.format('DD/MM/YYYY').toString(),
                    status: vm.status,
                    EmpresaId: vm.consultaEmpresa.selectedValue
                }
            },
            parametrosExtras: {
                IdEmpresa: $rootScope.usuarioLogado.idEmpresa
            },
            dataSource: {
                url: "PainelPagamento/ConsultarGridPainelPagamento",
                params: function () {
                    return {
                        dataInicial: vm.dataJSL.startDate.format('DD/MM/YYYY').toString(),
                        dataFinal: vm.dataJSL.endDate.format('DD/MM/YYYY').toString(),
                        status: vm.status,
                        EmpresaId: vm.consultaEmpresa.selectedValue
                    }
                }
            },
            columnDefs: [{
                name: 'Ações',
                width: '13%',
                cellTemplate: '<div ng-if="!row.groupHeader" class="ui-grid-cell-contents" align="left" title="TOOLTIP">\
                    <button tooltip-placement="right" uib-tooltip="Visualizar" type="button" ui-sref="painel-pagamento.painel-pagamento-crud({link: row.entity.id})"\
                        ng-class="{ \'btn btn-xs btn-info\': true }">\
                        <i class="fas fa-eye"></i>\
                    </button>\
                    <div class="container-btn-action" ng-show="row.entity.status===\'Aberto\' || row.entity.status===\'Bloqueado\'">\
                        <button tooltip-placement="right" uib-tooltip="Bloquear/Desbloquear" type="button" ng-click="grid.appScope.vm.alterarStatus(row.entity, false)" ng-class="row.entity.status===\'Aberto\' ? \'btn btn-xs btn-success\' : \'btn btn-xs btn-danger\'">\
                            <i ng-class="row.entity.status===\'Aberto\' ? \'fa fa-check\' : \'fa fa-ban\'"></i>\
                        </button>\
                    </div>\
                    <div class="container-btn-action" ng-show="row.entity.status===\'Aberto\'">\
                        <button tooltip-placement="right" uib-tooltip="Cancelar" type="button" ng-click="grid.appScope.vm.alterarStatus(row.entity, true)" ng-class="row.entity.status===\'Aberto\' ? \'btn btn-xs btn-danger\' : \'btn btn-xs btn-danger\'">\
                            <i ng-class="row.entity.status===\'Aberto\' ? \'fa fa-times\' : \'fa fa-times\'"></i>\
                        </button>\
                    </div>\
                    <div class="container-btn-action" ng-show="vm.usuAdm == null && row.entity.status!=\'Bloqueado\' && row.entity.status!=\'Cancelado\' && row.entity.status!=\'Baixado\'">\
                        <button tooltip-placement="right" uib-tooltip="Reenviar pagamento" type="button" ng-click="grid.appScope.vm.reenviarPagamento(row.entity)" ng-class="row.entity.status===\'Aberto\' ? \'btn btn-xs btn-danger\' : \'btn btn-xs btn-danger\'">\
                            <i ng-class="\'fa fa-paper-plane-o\'"></i>\
                        </button>\
                    </div>\
                </div>'
            },
            {
                name: 'Recibo',
                displayName: 'Recibo',
                width: 80,
                primaryKey: true,
                field: 'descricao',
                type: 'text',
                enableFiltering: true
            },
            {
                name: 'Origem pagamento',
                displayName: 'Origem pagamento',
                width: 120,
                field: 'origemPagamento',
                type: 'text',
                enableFiltering: false,
                cellTemplate: '<div class="ui-grid-cell-contents" align="left">\
                                    <p ng-show="row.entity.origemPagamento === \'OrigemJSLFrete\'"> Origem JSL Frete </p>\
                                    <p ng-show="row.entity.origemPagamento === \'OrigemBBC\'"> Origem BBC </p>\
                            </div>'
            },
            {
                name: 'Data/hora pagamento',
                displayName: 'Data/hora pagamento',
                width: 170,
                primaryKey: false,
                field: 'dataCadastro',
                type: 'date',
                enableFiltering: false
            },
            {
                name: 'Código',
                displayName: 'Código',
                width: 80,
                primaryKey: true,
                field: 'id',
                type: 'number',
                enableFiltering: true
            },
            {
                name: 'CIOT',
                displayName: 'CIOT',
                width: '*',
                minWidth: 150,
                field: 'ciot',
                serverField: 'Ciot.Ciot',
                enableFiltering: true
            },
            {
                name: 'Beneficiário',
                displayName: 'Beneficiário',
                width: '*',
                minWidth: 150,
                field: 'nome',
                serverField: 'Portador.Nome',
                enableFiltering: true
            },
            {
                name: 'Empresa',
                displayName: 'Empresa',
                width: '*',
                minWidth: 150,
                field: 'nomeEmpresa',
                serverField: 'nomeEmpresa',
                enableFiltering: false
            },
            {
                name: 'CPF/CNPJ Beneficiário',
                displayName: 'CPF/CNPJ Beneficiário',
                width: 175,
                field: 'cpfCnpj',
                serverField: 'Portador.cpfCnpj',
                enableFiltering: true
            },
            {
                name: 'Tipo',
                displayName: 'Tipo',
                width: 120,
                field: 'tipo',
                serverField: 'Tipo',
                enableGrouping: false,
                enableFiltering: true,
                enum: true,
                enumTipo: 'ETipoPagamentos'
            },
            {
                name: 'Forma Pagamento',
                displayName: 'Forma Pagamento',
                width: 155,
                field: 'formaPagamento',
                serverField: 'FormaPagamento',
                enableGrouping: false,
                enableFiltering: true,
                enum: true,
                enumTipo: 'EFormaPagamentos',
                cellTemplate: '<div class="ui-grid-cell-contents" align="left">\
                                        <p ng-show="row.entity.formaPagamento === \'Deposito\'"> Depósito </p>\
                                        <p ng-show="row.entity.formaPagamento === \'Cartao\'"> Cartão </p>\
                                        <p ng-show="row.entity.formaPagamento === \'Cheque\'"> Cheque </p>\
                                        <p ng-show="row.entity.formaPagamento === \'Outros\'"> Outros </p>\
                                        <p ng-show="row.entity.formaPagamento === \'Pix\'"> Pix </p>\
                                   </div>'
            },
            {
                name: 'Status',
                displayName: 'Status',
                width: 110,
                field: 'status',
                serverField: 'Status',
                enableGrouping: false,
                enableFiltering: true,
                enum: true,
                enumTipo: 'EStatusPagamentos'
            },
            {
                name: 'Valor',
                displayName: 'Valor',
                width: 145,
                field: 'valor',
                serverField: 'Valor',
                enableFiltering: true
            }]
        };

        vm.consultaEmpresa = {
            columnDefs: [{
                name: 'Cód.',
                field: 'id',
                width: 60,
                type: 'number',
                primaryKey: true
            }, {
                name: 'Nome Fantasia',
                field: 'nomeFantasia',
                width: '*'
            }, {
                name: 'Razão Social',
                field: 'razaoSocial',
                width: '*'
            }, {
                name: 'Email',
                field: 'email',
                width: 120
            }],
            desiredValue: 'id',
            desiredText: 'nomeFantasia',
            url: 'Empresa/ConsultarGridEmpresaCombo',
            paramsMethod: function () {
                return {}
            },
            executeAfterSelection: function () {
                vm.atualizaTela()
            },
            clearFunction: function () {
                vm.atualizaTela()
            }
        }

        vm.dataBBC = {
            startDate: moment().add(-1, 'days'),
            endDate: moment()
        };

        vm.gridBbcOptions = {
            data: [],
            enableFiltering: true,
            urlRelatorio: "PagamentoEvento/ConsultarGridPagamentoEvento",
            params: function () {
                return {
                    dataInicial: vm.dataBBC.startDate.format('DD/MM/YYYY').toString(),
                    dataFinal: vm.dataBBC.endDate.format('DD/MM/YYYY').toString(),
                    status: vm.statusBbc,
                    EmpresaId: vm.consultaEmpresa.selectedValue
                }
            },
            onRegisterApi: BaseService.dataGrid.getGridApiRegistration("gridBbcOptions"),
            dataSource: {
                autoBind: false,
                url: "PagamentoEvento/ConsultarGridPagamentoEvento",
                params: function () {
                    return {
                        dataInicial: vm.dataBBC.startDate.format('DD/MM/YYYY').toString(),
                        dataFinal: vm.dataBBC.endDate.format('DD/MM/YYYY').toString(),
                        status: vm.statusBbc,
                        EmpresaId: vm.consultaEmpresa.selectedValue
                    }
                }
            },
            columnDefs: [
                {
                    name: 'Ações',
                    width: '100',
                    cellTemplate: '<div ng-if="!row.groupHeader" class="ui-grid-cell-contents" align="left" title="TOOLTIP">\
                    <div class="container-btn-action" ng-show="row.entity.formaPagamento === \'Pix\' && (row.entity.status===\'Pendente\' || row.entity.status===\'NaoExecutado\')">\
                        <button tooltip-placement="right" uib-tooltip="Reconsultar Pix" type="button" \
                                ng-click="grid.appScope.vm.consultarPixManual(row.entity.id)"\
                                ng-class="{ \'btn btn-xs btn-info\': true }"\
                                ng-disabled="grid.appScope.vm.consultandoPixManual || row.entity.status===\'NaoExecutado\'">\
                            <i class="fa fa-refresh"></i>\
                        </button>\
                    </div>\
                    <div class="container-btn-action" ng-show="row.entity.contadorVerificacaoStatusPix >= 3">\
                        <button tooltip-placement="right" uib-tooltip="Adicionar Ocorrência" type="button" \
                                ui-sref="painel-pagamento.painel-pagamentoevento-crud({link: row.entity.id})"\
                                ng-disabled="row.entity.status===\'Pendente\'"\
                                ng-class="{ \'btn btn-xs btn-info\': true }">\
                                <i class="fa fa-eye"></i>\
                        </button>\
                    </div>\
                </div>'
                },
                {
                    name: 'Código',
                    displayName: 'Código',
                    width: 70,
                    field: 'id',
                    type: 'number',
                    enableFiltering: true
                },
                {
                    name: 'ViagemExternoId',
                    displayName: 'Viagem Externo Id',
                    width: 100,
                    field: 'viagemExternoId',
                    serverField: 'Viagem.ViagemExternoId',
                    type: 'number',
                    enableFiltering: true
                },
                {
                    name: 'PagamentoExternoId',
                    displayName: 'Pagamento Externo Id',
                    width: 100,
                    field: 'pagamentoExternoId',
                    type: 'number',
                    enableFiltering: true
                },
                {
                    name: 'Data baixa',
                    displayName: 'Data baixa',
                    width: 150,
                    field: 'dataBaixa',
                    type: 'date',
                    enableFiltering: false
                },
                {
                    name: 'Valor',
                    displayName: 'Valor',
                    width: 120,
                    field: 'valor',
                    type: 'number',
                    enableFiltering: true
                },
                {
                    name: 'Código Viagem',
                    displayName: 'Código Viagem',
                    width: 120,
                    field: 'viagemId',
                    type: 'number',
                    enableFiltering: true
                },
                {
                    name: 'CPF/CNPJ Motorista',
                    displayName: 'CPF/CNPJ Motorista',
                    width: 140,
                    enableSorting: false,
                    field: 'cpfCnpjMotorista',
                    enableFiltering: false
                },
                {
                    name: 'Valor Transferencia Motorista',
                    displayName: 'Valor Transferencia Motorista',
                    width: 120,
                    field: 'valorTransferenciaMotorista',
                    enableFiltering: true
                },
                {
                    name: 'CPF/CNPJ Proprietário',
                    displayName: 'CPF/CNPJ Proprietário',
                    width: 140,
                    enableSorting: false,
                    field: 'cpfCnpjProprietario',
                    enableFiltering: false
                },
                {
                    name: 'Tipo',
                    displayName: 'Tipo',
                    width: 120,
                    field: 'tipo',
                    serverField: 'Tipo',
                    enableGrouping: false,
                    enableFiltering: true,
                    enum: true,
                    enumTipo: 'ETipoEvento'
                },
                {
                    name: 'Forma Pagamento',
                    displayName: 'Forma Pagamento',
                    width: 100,
                    field: 'formaPagamento',
                    serverField: 'FormaPagamento',
                    enableGrouping: false,
                    enableFiltering: true,
                    enum: true,
                    enumTipo: 'EFormaPagamentoEvento',
                    cellTemplate: '<div class="ui-grid-cell-contents" align="left">\
                                        <p ng-show="row.entity.formaPagamento === \'Deposito\'"> Depósito </p>\
                                        <p ng-show="row.entity.formaPagamento === \'Pix\'"> Pix </p>\
                                    </div>'
                },
                {
                    name: 'Status',
                    displayName: 'Status',
                    width: 90,
                    field: 'status',
                    serverField: 'Status',
                    enableFiltering: true,
                    enum: true,
                    enumTipo: 'EStatusPagamentoEvento',
                    cellTemplate: '<div class="ui-grid-cell-contents" align="left">\
                                        <p ng-show="row.entity.status === \'Fechado\'"> Fechado </p>\
                                        <p ng-show="row.entity.status === \'Aberto\'"> Aberto </p>\
                                        <p ng-show="row.entity.status === \'Pendente\'"> Pendente </p>\
                                        <p ng-show="row.entity.status === \'Erro\'"> Erro </p>\
                                        <p ng-show="row.entity.status === \'Cancelado\'"> Cancelado </p>\
                                        <p ng-show="row.entity.status === \'Processando\'"> Processando </p>\
                                        <p ng-show="row.entity.status === \'NaoExecutado\'"> Não Executado </p>\
                                   </div>'
                },
                {
                    name: 'Motivo Pendência',
                    displayName: 'Motivo Pendência',
                    width: 210,
                    field: 'motivoPendencia',
                    type: 'text',
                    enableFiltering: false
                },
                {
                    name: 'Data Alteração',
                    displayName: 'Data Alteração',
                    width: 150,
                    field: 'dataAlteracao',
                    type: 'date',
                    enableFiltering: false
                },
                {
                    name: 'Contador Reenvio',
                    displayName: 'Contador Reenvio',
                    width: 120,
                    field: 'contadorReenvio',
                    type: 'number',
                    enableFiltering: true
                },
                {
                    name: 'Ocorrência',
                    displayName: 'Ocorrência',
                    width: 120,
                    field: 'ocorrencia',
                    type: 'text',
                    enableFiltering: true
                },
                {
                    name: 'Verificações Status Pix',
                    displayName: 'Verificações Status Pix',
                    width: 200,
                    field: 'contadorVerificacaoStatusPix',
                    type: 'number',
                    enableFiltering: true
                },
                {
                    name: 'Horas desde última Verificação',
                    displayName: 'Horas desde última Verificação',
                    width: 220,
                    field: 'horasDesdeTerceiraVerificacaoStatusPix',
                    type: 'number',
                    enableFiltering: false,
                    enableGrouping: false,
                    enableSorting: false
                },
                {
                    name: 'Data Cancelamento',
                    displayName: 'Data Cancelamento',
                    width: 170,
                    field: 'dataCancelamento',
                    type: 'date',
                    enableFiltering: false
                },
                {
                    name: 'EmpresaId',
                    displayName: 'EmpresaId',
                    width: 170,
                    field: 'empresaId',
                    type: 'number',
                    enableFiltering: true
                },
                {
                    name: 'Razão Social Empresa',
                    displayName: 'Razão Social Empresa',
                    width: 170,
                    field: 'razaoSocialEmpresa',
                    type: 'text',
                    serverField: 'Empresa.RazaoSocial',
                    enableFiltering: true
                },
                {
                    name: 'Cnpj Empresa',
                    displayName: 'Cnpj Empresa',
                    width: 170,
                    field: 'cnpjEmpresa',
                    type: 'text',
                    serverField: 'Empresa.Cnpj',
                    enableFiltering: true
                }
            ]
        };

        vm.isAdmin = function () {
            return $window.localStorage.getItem("administrador") == "true";
        };

      
        

        // Controle de aba!!
        $scope.$on('$stateChangeStart', function () {
            PersistentDataService.store('PainelPagamentoController', vm, "Painel de pagamento", "PainelPagamentoCrudController", "painel-pagamento.index"); 
        });
        var selfScope = PersistentDataService.get('PainelPagamentoController');
        var filho = PersistentDataService.get('PainelPagamentoCrudController');
        if (angular.isDefined(filho)) {
            $timeout(function () {
                $state.go('painel-pagamento.painel-pagamento-crud', {
                    link: filho.data.painelPagamento.id > 0 ? 'editar' : 'novo'
                });
            }, 50);
        }
       
    }
})();