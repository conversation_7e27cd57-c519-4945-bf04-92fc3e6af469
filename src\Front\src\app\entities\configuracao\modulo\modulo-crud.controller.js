(function() {
    'use strict';

    angular.module('bbcWeb')
        .controller('ModuloCrudController', ModuloCrudController);

    ModuloCrudController.$inject = [
        '$scope',
        'PersistentDataService',
        'toastr',
        'BaseService',
        'TipoCarreta',
        '$state',
        '$stateParams',
        '$window',
        '$rootScope',
        '$timeout'
    ];

    function ModuloCrudController(
        $scope,
        PersistentDataService,
        toastr,
        BaseService,
        TipoCarreta,
        $state,
        $stateParams,
        $window,
        $rootScope,
        $timeout) {

        //Inicialização dos objetos e arrays
        var vm = this;
        vm.modulo = {};
        vm.perfilUsuarioLogado = $rootScope.usuarioLogado.perfil;

        if ($stateParams.link == 'novo')
            vm.modulo.IdModulo = 'Auto';
        vm.isNew = function() {
            return $stateParams.link == 'novo';
        };
        vm.headerItems = [{
                name: 'Administração'
            },
            {
                name: '<PERSON><PERSON><PERSON><PERSON>',
                link: 'configuracao.modulo'
            },
            {
                name: $stateParams.link == 'novo' ? 'Novo' : 'Editar'
            }
        ];

        vm.loadEdit = function(id) {

            BaseService.get('ModuloAts', 'ConsultarPorId', {
                idModulo: id
            }).then(function(response) {
                vm.modulo = angular.fromJson(response.data);
            });

        };

        vm.load = function() {};

        //Salvar e atualizar
        vm.save = function() {
            BaseService.post('ModuloAts', $stateParams.link === 'novo' ? 'Cadastrar' : 'Editar', vm.modulo)
                .then(function(response) {
                    if (response.success) {
                        toastr.success(response.message);
                        $state.go('configuracao.modulo');
                    } else {
                        toastr.error(response.message);
                    }
                });
        };

        $scope.$on('$stateChangeStart', function(_, toState) {
            if (toState.name === 'configuracao.modulo')
                PersistentDataService.remove('ModuloCrudController');
            else
                PersistentDataService.store('ModuloCrudController', vm, "Módulo", null, "configuracao.modulo-crud", vm.modulo.IdModulo);
        });
        var selfScope = PersistentDataService.get('ModuloCrudController');
        if (angular.isDefined(selfScope)) {
            angular.extend(vm, selfScope.data);
        } else {
            if (!vm.isNew())
                vm.loadEdit($stateParams.link);
            else
                vm.load();
        }

        $timeout(function() {
            PersistentDataService.remove('ModuloController');
        }, 15);
    }
})();