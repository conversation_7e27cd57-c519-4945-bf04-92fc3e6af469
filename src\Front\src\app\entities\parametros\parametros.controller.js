(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('ParametroController', ParametroController);

    ParametroController.inject = ['BaseService', '$rootScope', 'toastr', '$scope', 'PersistentDataService', '$timeout', '$state', 'PERFIL_ADMINISTRADOR'];

    function ParametroController(BaseService, $rootScope,toastr ,$scope, PersistentDataService, $timeout, $state, PERFIL_ADMINISTRADOR) {
        var vm = this;

        vm.parametrosTipoBooleano = [
            "CodigoReenvioPagamentoEvento",
            "AprovarPagamentosAutomaticamente",
            "ForcarGeracaoPagamento",
            "VerificaContigencia",
            "EmailSsl"
        ];

        vm.parametrosComOutraTela = [
            "AprovacaoAutomaticaPrecoCombustivel",
            "ConfiguracaoDeSLA",
            "ConfiguracaoMonitoramentoCIOT",
            "ConfiguracaoValePedagio",
            "ConfiguracaoQualificacaoTransacao"
        ];

        vm.headerItems = [{
            name: 'Cada<PERSON><PERSON>'
        }, {
            name: 'Parâmetros gerais'
        }];

        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.perfil === PERFIL_ADMINISTRADOR;
        };

        vm.editarDocumento = function (obj) {
            var request = {
                id: obj.id,
                infoAdicional: obj.infoAdicional,
                tipoParametros: obj.tipoParametros,
                tipoValor: obj.tipoValor,
                valor: obj.valor
            };

            $state.go('parametros.parametros-crud', { "link": JSON.stringify(request) });
        }

        vm.configurar = function (tipoParametro) {
            switch (tipoParametro) {
                case 'ConfiguracaoDeSLA':
                    $state.go('parametros.configuracao-sla');
                    return;
                case 'AprovacaoAutomaticaPrecoCombustivel':
                    $state.go('parametros.configuracao-atualizacao-automatica-preco-combustivel');
                    return;
                case 'ConfiguracaoMonitoramentoCIOT':
                    $state.go('parametros.configuracao-monitoramento-ciot');
                    return;
                case 'ConfiguracaoValePedagio':
                    $state.go('parametros.configuracao-vale-pedagio');
                    return;
                case 'ConfiguracaoTelaoSaldo':
                    $state.go('parametros.configuracao-telao-saldo');
                    return;
                case 'ConfiguracaoQualificacaoTransacao':
                    $state.go('parametros.configuracao-qualificacao-transacao');
                    return;
            }
        }

        vm.getValorParametro = function (rowEntity) {
            if (vm.parametrosTipoBooleano.includes(rowEntity.tipoParametros)) {
                return rowEntity.valor === '1' ? 'Sim' : 'Não'
            } else if (vm.parametrosComOutraTela.includes(rowEntity.tipoParametros)) {
                return 'Edite para exibir as configurações'
            } else {
                return rowEntity.valor
            }
        }

        vm.sincronizarParametrosMicroservico = function (){
            BaseService.get('Parametros', 'SincronizaParametrosComMicroServico')
                .then(function (response) {
                    if (!response) {
                        toastr.error('Erro de comunicação com o servidor, tente novamente em alguns minutos.');
                        return;
                    }

                    if (!response.success) {
                        toastr.error(response.message);
                        return;
                    }
                });
        }

        vm.gridOptions = {
            data: [],
            onRegisterApi: BaseService.dataGrid.defaultOnRegisterApi,
            dataSource: {
                url: "Parametros/ConsultarGridParametro"
            },
            columnDefs: [{
                name: 'Ações',
                width: 80,
                cellTemplate: '<div ng-if="!row.groupHeader" class="ui-grid-cell-contents" title="TOOLTIP">\
                                    <button  title="Editar" type="button" \
                                        ng-click="row.entity.referenciaId == -1 ? \
                                        grid.appScope.vm.editarDocumento(row.entity) : \
                                        grid.appScope.vm.configurar(row.entity.tipoParametros)"\
                                        ng-class="{ \'btn btn-xs btn-info\': true }">\
                                        <i class="fa fa-edit"></i>\
                                    </button>\
                                </div>'
            }, {
                name: 'Código',
                width: 80,
                type: 'number',
                primaryKey: true,
                field: 'id'
            }, {
                name: 'Definições',
                width: '*',
                minWidth: 80,
                field: 'valor',
                enableFiltering: false,
                cellTemplate: '<div ng-if="!row.groupHeader" class="ui-grid-cell-contents" title="TOOLTIP">\
                                    <p ng-show="row.entity.tipoParametros == \'ConfiguracaoValePedagio\'"> Edite para exibir as configurações</p>\
                                    <p ng-show="row.entity.tipoParametros == \'ConfiguracaoQualificacaoTransacao\'"> Edite para exibir as configurações</p>\
                                    <p ng-show="row.entity.tipoParametros == \'ConfiguracaoMonitoramentoCIOT\'"> Edite para exibir as configurações</p>\
                                    <p ng-show="row.entity.tipoParametros == \'CodigoReenvioPagamentoEvento\' && row.entity.valor == \'1\' "> Sim </p>\
                                    <p ng-show="row.entity.tipoParametros == \'CodigoReenvioPagamentoEvento\' && row.entity.valor == \'0\' "> Não </p>\
                                    <p ng-show="row.entity.tipoParametros == \'AprovarPagamentosAutomaticamente\' && row.entity.valor == \'1\' "> Sim </p>\
                                    <p ng-show="row.entity.tipoParametros == \'AprovarPagamentosAutomaticamente\' && row.entity.valor == \'0\' "> Não </p>\
                                    <p ng-show="row.entity.tipoParametros == \'ForcarGeracaoPagamento\' && row.entity.valor == \'1\' "> Sim </p>\
                                    <p ng-show="row.entity.tipoParametros == \'ForcarGeracaoPagamento\' && row.entity.valor == \'0\' "> Não </p>\
                                    <p ng-show="row.entity.tipoParametros == \'VerificaContigencia\' && row.entity.valor == \'1\' "> Sim </p>\
                                    <p ng-show="row.entity.tipoParametros == \'VerificaContigencia\' && row.entity.valor == \'0\' "> Não </p>\
                                    <p ng-show="row.entity.tipoParametros == \'EmailSsl\' && row.entity.valor == \'1\' "> Sim </p>\
                                    <p ng-show="row.entity.tipoParametros == \'EmailSsl\' && row.entity.valor == \'0\' "> Não </p>\
                                    <p ng-show="row.entity.tipoParametros == \'ConfiguracaoDeSLA\'"> Edite para exibir as configurações</p>\
                                    <p ng-show="row.entity.tipoParametros == \'AprovacaoAutomaticaPrecoCombustivel\'"> Edite para exibir as configurações</p>\
                                    <p ng-show="row.entity.tipoParametros != \'CodigoReenvioPagamentoEvento\'"> {{row.entity.valor}} </p>\
                                </div>' 
                                // todo novo parametro que tenha uma definicao especifica deve estar antes da linha  <p ng-show="row.entity.tipoParametros != \'CodigoReenvioPagamentoEvento\'"> {{row.entity.valor}} </p>\
            }, {
                name: 'Parâmetro',
                width: '*',
                minWidth: 150,
                field: 'tipoParametros',
                enableFiltering: false,
                cellTemplate: '<div ng-if="!row.groupHeader" class="ui-grid-cell-contents" title="TOOLTIP">\
                                    <p ng-show="row.entity.tipoParametros ==\'CodigoContaTransferenciaValorRetencao\'">Conta de transferência de valor em retenção</p>\
                                    <p ng-show="row.entity.tipoParametros ==\'CodigoContaTransferenciaTarifaValorRetencao\'">Conta de transferência de valor de tarifa em retenção</p>\
                                    <p ng-show="row.entity.tipoParametros ==\'UrlComunicacaoPixBaas\'">Url de comunicação com Pix Baas</p>\
                                    <p ng-show="row.entity.tipoParametros ==\'CodigoLinkEmpresa\'">Link empresa</p>\
                                    <p ng-show="row.entity.tipoParametros ==\'CodigoTipoEmissaoCiot\'">Tipo de emissão de CIOT</p>\
                                    <p ng-show="row.entity.tipoParametros ==\'CodigoPeriodoMaximoProcessamento\'">Período máximo de processamento</p>\
                                    <p ng-show="row.entity.tipoParametros ==\'CodigoReenvioPagamentoEvento\'">Reenvio de pagamento evento</p>\
                                    <p ng-show="row.entity.tipoParametros ==\'CodigoLinkCiot\'">Link CIOT</p>\
                                    <p ng-show="row.entity.tipoParametros ==\'LimiteMaximoRetentativaFrete\'">Limite máximo retentativas de pagamento de fretes</p>\
                                    <p ng-show="row.entity.tipoParametros ==\'IntervaloMinimoRetentativaCancelamentoPagamentoFrete\'">Intervalo mínimo retentativas cancelamento de pagamentos de frete</p>\
                                    <p ng-show="row.entity.tipoParametros ==\'ForcarGeracaoPagamento\'">Forçar geração de pagamento abastecimento</p>\
                                    <p ng-show="row.entity.tipoParametros ==\'NumeroRetentativaEnvioPagamento\'">Numero de retentativas de pagamento</p>\
                                    <p ng-show="row.entity.tipoParametros ==\'AprovarPagamentosAutomaticamente\'">Aprovar pagamentos automaticamente?</p>\
                                    <p ng-show="row.entity.tipoParametros ==\'PrazoMaximaParaCancelamentoPagamentoFrete\'">Prazo maximo para cancelamento de pagamentos de frete</p>\
                                    <p ng-show="row.entity.tipoParametros ==\'LimiteMaximoRetentativaCancelamentoPagamentoFrete\'">Limite maximo de tentativa de cancelamento de pagamentos de frete</p>\
                                    <p ng-show="row.entity.tipoParametros ==\'PeriodoMaximoInatividadePortador\'">Período máximo de inatividade do portador</p>\
                                    <p ng-show="row.entity.tipoParametros ==\'PeriodoMaximoInatividadeSenhaProvisoria\'">Período máximo de duração da senha provisória</p>\
                                    <p ng-show="row.entity.tipoParametros ==\'TempoMaximoUsuarioInativo\'">Sessão expirada – tempo máximo usuário inativo</p>\
                                    <p ng-show="row.entity.tipoParametros ==\'CodigoContaCorrenteReceita\'">Conta de transferência de valor de receitas</p>\
                                    <p ng-show="row.entity.tipoParametros ==\'PeriodoDuracaoSenha\'">Período de duração da senha de acesso</p>\
                                    <p ng-show="row.entity.tipoParametros ==\'ConfiguracaoDeSLA\'">Configuração de semáforos SLA</p>\
                                    <p ng-show="row.entity.tipoParametros ==\'AprovacaoAutomaticaPrecoCombustivel\'">Aprovação automática atualização preço combustível</p>\
                                    <p ng-show="row.entity.tipoParametros ==\'UrlComunicacaoConductor\'">Conductor URL</p>\
                                    <p ng-show="row.entity.tipoParametros ==\'UrlComunicacaoConductorAuth\'">Conductor Autenticação URL</p>\
                                    <p ng-show="row.entity.tipoParametros ==\'ComunicacaoConductorUsuario\'">Conductor Usuário</p>\
                                    <p ng-show="row.entity.tipoParametros ==\'ComunicacaoConductorSenha\'">Conductor Senha</p>\
                                    <p ng-show="row.entity.tipoParametros ==\'UrlComunicacaoConductorRegDocs\'">Conductor regdocs URL</p>\
                                    <p ng-show="row.entity.tipoParametros ==\'UrlComunicacaoConductorAliasBank\'">Conductor aliasbank URL</p>\
                                    <p ng-show="row.entity.tipoParametros ==\'UrlComunicacaoConductorCompanies\'">Conductor companies URL</p>\
                                    <p ng-show="row.entity.tipoParametros ==\'UrlComunicacaoCiot\'">Ciot URL</p>\
                                    <p ng-show="row.entity.tipoParametros ==\'UrlConfiguracaoWeb\'">URL de configuração WEB</p>\
                                    <p ng-show="row.entity.tipoParametros ==\'ConfiguracaoWebToken\'">Configuração WEB token</p>\
                                    <p ng-show="row.entity.tipoParametros ==\'ConfiguracaoWebHostName\'">Configuração WEB host</p>\
                                    <p ng-show="row.entity.tipoParametros ==\'VerificaContigencia\'">Verificar contingência</p>\
                                    <p ng-show="row.entity.tipoParametros ==\'UrlComunicacaoCaptalys\'">Captalys URL</p>\
                                    <p ng-show="row.entity.tipoParametros ==\'ComunicacaoCaptalysToken\'">Captalys token</p>\
                                    <p ng-show="row.entity.tipoParametros ==\'UrlComunicacaoCaptalysRetencao\'">Captalys retenção URL</p>\
                                    <p ng-show="row.entity.tipoParametros ==\'ComunicacaoCaptalysTokenRetencao\'">Captalys retenção token</p>\
                                    <p ng-show="row.entity.tipoParametros ==\'UrlComunicacaoCaruana\'">Caruana URL</p>\
                                    <p ng-show="row.entity.tipoParametros ==\'UrlComunicacaoLesing\'">Leasing URL</p>\
                                    <p ng-show="row.entity.tipoParametros ==\'EmailSmtpClient\'">E-mail smtp</p>\
                                    <p ng-show="row.entity.tipoParametros ==\'EmailPort\'">E-mail porta</p>\
                                    <p ng-show="row.entity.tipoParametros ==\'EmailUsuario\'">E-mail usuário</p>\
                                    <p ng-show="row.entity.tipoParametros ==\'EmailSenha\'">E-mail senha</p>\
                                    <p ng-show="row.entity.tipoParametros ==\'EmailSsl\'">E-mail ssl</p>\
                                    <p ng-show="row.entity.tipoParametros ==\'UrlComunicacaoMobile2You\'">Mobile2You URL</p>\
                                    <p ng-show="row.entity.tipoParametros ==\'ComunicacaoMobile2YouToken\'">Mobile2You token</p>\
                                    <p ng-show="row.entity.tipoParametros ==\'EmpresaPagamento\'">Empresas de pagamento</p>\
                                    <p ng-show="row.entity.tipoParametros ==\'EmailGestorAbastecimentosMovida\'">E-mail de notificação de reenvio integração abastecimento MOVIDA</p>\
                                    <p ng-show="row.entity.tipoParametros ==\'QuantidadeTentativasReenvioAbastecimentoMovida\'">Tentativas de reenvio para envio de e-mail de notificação MOVIDA</p>\
                                    <p ng-show="row.entity.tipoParametros ==\'HorarioEnvioEmailGestorAbastecimentosMovida\'">Horário para envio de e-mail de notificação MOVIDA</p>\
                                    <p ng-show="row.entity.tipoParametros ==\'DiasRetroativosGerarReceita\'">Dias retroativos para gerar registro de receita</p>\
                                    <p ng-show="row.entity.tipoParametros ==\'MargemErroArredondamentoXmlProtocolo\'">Margem arredondamento casas decimais de valor unitário (XML protocolo)</p>\
                                    <p ng-show="row.entity.tipoParametros ==\'BankNumberDock\'">Número do banco na dock</p>\
                                    <p ng-show="row.entity.tipoParametros ==\'LinkAplicativoCadastroPortador\'">Link para download do aplicativo no email</p>\
                                    <p ng-show="row.entity.tipoParametros ==\'TempoEsperaSegundosPix\'">Tempo de espera reconsulta pix após pagamento (segundos)</p>\
                                    <p ng-show="row.entity.tipoParametros ==\'QuantidadeVezesConsultaPix\'">Quantidade de reconsulta pix após pagamento</p>\
                                    <p ng-show="row.entity.tipoParametros ==\'TempoRetroativoPixDuplicado\'">Tempo de consideração para pix duplicado</p>\
                                    <p ng-show="row.entity.tipoParametros ==\'ConfiguracaoMonitoramentoCIOT\'">Configuração de Monitoramento CIOT</p>\
                                    <p ng-show="row.entity.tipoParametros ==\'ConfiguracaoValePedagio\'">Configuração de Vale Pedágio</p>\
                                    <p ng-show="row.entity.tipoParametros ==\'ConfiguracaoTentativaReenvioPagamentoFrete\'">Configuração de tentativa de reenvio pagamento de frete</p>\
                                    <p ng-show="row.entity.tipoParametros ==\'ConfiguracaoTelaoSaldo\'">Configuração do telão de saldo</p>\
                                    <p ng-show="row.entity.tipoParametros ==\'MargemArredondamentoCasasDecimaisLitragemXml\'">Margem arredondamento casas decimais de litragem (XML protocolo), </p>\
                                    <p ng-show="row.entity.tipoParametros ==\'MargemErroTotalAbastecimentoXmlProtocolo\'">Margem arredondamento casas decimais do valor TOTAL dos abastecimentos (XML protocolo)</p>\
                                    <p ng-show="row.entity.tipoParametros ==\'ConfiguracaoQualificacaoTransacao\'">Configuração de qualificação de transação</p>\
                                    </div>'
            }]
        };
       
        // Controle de aba!!
        $scope.$on('$stateChangeStart', function () {
            PersistentDataService.store('ParametroController', vm, "Parâmetros gerais", "ParametroCrudController", "parametros.index");
        });

        var selfScope = PersistentDataService.get('ParametroController');
        var filho = PersistentDataService.get('ParametroCrudController');
        if (angular.isDefined(filho)) {
            $timeout(function () {
                $state.go('parametros.parametros-crud', {
                    link: filho.data.parametro.Id > 0 ? 'editar' : 'novo'
                });
            }, 50);
        } else if (angular.isDefined(selfScope)) {
            angular.extend(vm, selfScope.data);

            $timeout(function () {
                vm.gridOptions.dataSource.refresh();
            }, 450);
        }
    }
})();