(function () {
    'use strict';

    angular.module('bbcWeb.client-secret.state', []).config(routeConfig);

    function routeConfig($urlRouterProvider, $stateProvider) {
        $stateProvider.state('client-secret', {
            abstract: true,
            url: "/client-secret",
            templateUrl: "app/layout/content.html"
        }).state('client-secret.index', {
            url: '/index',
            templateUrl: 'app/entities/client-secret/client-secret.html'
        }).state('client-secret.client-secret-crud', {
            url: '/:link',
            templateUrl: 'app/entities/client-secret/client-secret-crud.html'
        });
    }
})();