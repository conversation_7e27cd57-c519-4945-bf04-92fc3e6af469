<!doctype html>
<html>

<head>
    <meta charset="utf-8">
    <title>BBC - Cadastre-se</title>

    <meta name="viewport" content="width=device-width, user-scalable=no">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta http-equiv="cache-control" content="max-age=0" />
    <meta http-equiv="cache-control" content="no-cache" />
    <meta http-equiv="expires" content="0" />
    <meta http-equiv="expires" content="Tue, 01 Jan 1980 1:00:00 GMT" />
    <meta http-equiv="pragma" content="no-cache" />
    <!-- Place favicon.ico and apple-touch-icon.png in the root directory -->
    <link href="../assets/images/favicon2.PNG" rel="shortcut icon" type="image/x-icon" />
    <!-- build:css({.tmp/serve,src}) styles/vendor.css -->
    <!-- bower:css -->
    <link rel="stylesheet" href="../bower_components/animate.css/animate.css" />
    <link rel="stylesheet" href="../bower_components/metisMenu/dist/metisMenu.css" />
    <link rel="stylesheet" href="../bower_components/angular-toastr/dist/angular-toastr.css" />
    <link rel="stylesheet" href="../bower_components/angular-bootstrap-lightbox/dist/angular-bootstrap-lightbox.css" />
    <link rel="stylesheet" href="../bower_components/angular-ui-grid/ui-grid.css" />
    <link rel="stylesheet" href="../bower_components/angular-ui-switch/angular-ui-switch.css" />
    <link rel="stylesheet" href="../bower_components/bootstrap-daterangepicker/daterangepicker.css" />
    <link rel="stylesheet" href="../bower_components/switchery/dist/switchery.css" />
    <link rel="stylesheet" href="../bower_components/angular-ui-select/dist/select.css" />
    <link rel="stylesheet" href="../bower_components/angular-confirm/css/angular-confirm.css" />
    <link rel="stylesheet"
        href="../bower_components/angular-bootstrap-toggle-switch/style/bootstrap3/angular-toggle-switch-bootstrap-3.css" />
    <!-- endbower -->
    <!-- endbuild -->

    <!-- build:css({.tmp/serve,src}) styles/app.css -->
    <!-- inject:css -->
    <link rel="stylesheet" href="app/main.css">
    <link rel="stylesheet" href="../src/app/less/app/_bootstrap-datetimepicker.less" />
    <link rel="stylesheet" href="../src/app/less/app/bootstrap-datetimepicker-build.less" />
    <link rel="stylesheet" href="../src/assets/css/bootstrap-datetimepicker.css" />
    <link rel="stylesheet" href="../src/assets/css/bootstrap-datetimepicker.min.css" />
    <link rel="stylesheet" href="../src/assets/css/bootstrap-datetimepicker-standalone.css" />

    <!-- endinject -->
    <!-- endbuild -->

    <style>
        .cabecalho-gradiente {
            background-image: linear-gradient(to right, rgba(255, 0, 0, 0), rgb(4, 107, 49))
        }
    </style>
</head>

<body style="overflow-y:auto; background-color: #f3f3f4; background: none !important;" ng-app="">
    <div class="row border-bottom">
        <nav class="navbar navbar-static-top white-bg cabecalho-gradiente" role="navigation"
            style="margin-bottom: 0; height: 85px !important;">
            <img style="height: 60%; margin-left: 2%" class="mt-15" id=""
                src="assets/images/BBC_LOGO_DIGITAL_PANTONE.png">
        </nav>
    </div>

    <div class="ibox-content wizard">
        <form name="frmCadastreSe" role="form" novalidate ats-validator show-validation>
            <div form-wizard steps="2">
                <div class="form-wizard">
                    <ol class="row">
                        <li id="divEmpresa" onclick="habilitaAba(1)" style="height: 20px"
                            class="control-label col-xs-12 col-sm-6 col-md-6 col-lg-6 active">
                            <h4>Empresa</h4>
                        </li>
                        <li id="divRepresentante" onclick="habilitaAba(2)" style="height: 20px"
                            class="control-label col-xs-12 col-sm-6 col-md-6 col-lg-6 {'active':habilitaAba(2)}">
                            <h4>Representante Legal</h4>
                        </li>
                    </ol>
                    <!-- EMPRESA -->
                    <div class="gray-bg" id="mostraEmpresa" style="height: 100%">
                        <div class="animated fadeIn filter-position">
                            <!-- <div class="col-lg-1 mb-30"></div> -->
                            <div class="col-lg-7 mb-30">
                                <div class="ibox animated fadeInRight">
                                    <div class="ibox-title" style="background-color: #056233;">
                                        <h5 style="color: white">Dados da empresa</h5>
                                    </div>
                                    <div class="ibox-content">
                                        <div class="row">
                                            <div class="col-md-12">
                                                <div class="form-group">
                                                    <label><span class="text-danger">*</span> Razão social</label>
                                                    <input type="text" id="razao-social" maxlength="200" required
                                                        class="form-control" />
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-12">
                                                <div class="form-group">
                                                    <label><span class="text-danger">*</span> Nome fantasia</label>
                                                    <input type="text" maxlength="200" id="nome-fantasia"
                                                        class="form-control" />
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label><span class="text-danger">*</span> E-mail</label>
                                                    <input type="text" maxlength="200" id="email"
                                                        class="form-control" />
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label><span class="text-danger">*</span> CNPJ</label>
                                                    <input type="text" id="cnpj" oninput="mascara(this, 'cnpj')"
                                                        required class="form-control" />
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label><span class="text-danger">*</span> Telefone</label>
                                                    <input type="text" id="telefone" oninput="mascara(this, 'tel')"
                                                        class="form-control" />
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label><span class="text-danger">*</span> Celular</label>
                                                    <input type="text" id="celular" oninput="mascara(this, 'tel')"
                                                        required class="form-control" />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-5 mb-30">
                                <div class="ibox animated fadeInRight">
                                    <div class="ibox-title" style="background-color: #056233;">
                                        <h5 style="color: white">Endereço</h5>
                                    </div>
                                    <div class="ibox-content">
                                        <div class="row">
                                            <div class="col-md-12">
                                                <div class="form-group">
                                                    <label><span class="text-danger">*</span> Endereço</label>
                                                    <input type="text" maxlength="200" id="endereco"
                                                        class="form-control" />
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-12">
                                                <div class="form-group">
                                                    <label><span class="text-danger">*</span> Bairro</label>
                                                    <input type="text" maxlength="200" id="bairro"
                                                        class="form-control" />
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-4">
                                                <div class="form-group">
                                                    <label><span class="text-danger">*</span> Número</label>
                                                    <input type="text" maxlength="10"
                                                        onkeypress="return isNumber(event)" id="numero"
                                                        class="form-control" />
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="form-group">
                                                    <label><span class="text-danger">*</span> CEP</label>
                                                    <input type="text" id="cep" oninput="mascara(this, 'cep')"
                                                        class="form-control" />
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="form-group">
                                                    <label>Complemento</label>
                                                    <input type="text" maxlength="200" id="complemento"
                                                        class="form-control" />
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label><span class="text-danger">*</span> Estado</label>
                                                    <select id="select-estados" name="select-estados"
                                                        class="form-control"></select>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label><span class="text-danger">*</span> Cidade</label>
                                                    <select id="select-cidades" name="select-cidades"
                                                        class="form-control"></select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <div style="margin-top: -75px" class="ibox animated fadeInRight">
                                    <div class="ibox-content">
                                        <div class="row">
                                            <div class="col-md-8">
                                            </div>
                                            <div class="col-md-4">
                                                <div class="pull-right">
                                                    <button type="button" id="cancelar" style="width: 130px"
                                                        class="btn btn-labeled btn-white text-right">
                                                        <span class="btn-label">
                                                            <i class="fa fa-times"></i>
                                                        </span>
                                                        Cancelar
                                                    </button>
                                                    <button type="submit" id="proximo" onclick="habilitaAba(2)"
                                                        style="width: 130px;"
                                                        class="btn btn-labeled btn-white text-right">
                                                        <span class="btn-label">
                                                            <i class="fa fa-arrow-circle-right"></i>
                                                        </span>
                                                        Próximo
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- <div class="col-lg-1 mb-30"></div> -->
                        </div>
                    </div>

                    <div class="gray-bg" id="mostraRepresentanteLegal" style="height: 100%">
                        <div class="animated fadeIn filter-position">
                            <!-- <div class="col-lg-1 mb-30"></div> -->
                            <div class="col-lg-7 mb-30">
                                <div class="ibox animated fadeInRight">
                                    <div class="ibox-title" style="background-color: #056233;">
                                        <h5 style="color: white">Dados do representante legal</h5>
                                    </div>
                                    <div class="ibox-content">
                                        <div class="row">
                                            <div class="col-md-12">
                                                <div class="form-group">
                                                    <label><span class="text-danger">*</span> Nome</label>
                                                    <input type="text" id="nome-rep" maxlength="200" required
                                                        class="form-control" />
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label><span class="text-danger">*</span> CPF</label>
                                                    <input type="text" maxlength="11" oninput="mascara(this, 'cpf')"
                                                        id="cpf-rep" class="form-control" required />
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label><span class="text-danger">*</span> Sexo</label>
                                                    <select id="select-sexos" name="select-sexos"
                                                        class="form-control"></select>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label><span class="text-danger">*</span> E-mail</label>
                                                    <input type="text" maxlength="200" id="email-rep"
                                                        class="form-control" />
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label>RNTRC</label>
                                                    <input type="text" maxlength="8" id="rntrc-rep"
                                                        onkeypress="return isNumber(event)" class="form-control" />
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label><span class="text-danger">*</span> Telefone</label>
                                                    <input type="text" id="telefone-rep" oninput="mascara(this, 'tel')"
                                                        class="form-control" />
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label><span class="text-danger">*</span> Celular</label>
                                                    <input type="text" id="celular-rep" oninput="mascara(this, 'tel')"
                                                        required class="form-control" />
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label><span class="text-danger">*</span> Nome da mãe</label>
                                                    <input type="text" id="nome-mae-rep" maxlength="200" required
                                                        class="form-control" />
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label><span class="text-danger">*</span> Nome do pai</label>
                                                    <input type="text" id="nome-pai-rep" maxlength="200" required
                                                        class="form-control" />
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label><span class="text-danger">*</span> Número identidade</label>
                                                    <input type="text" id="numero-identidade-rep" required
                                                        class="form-control" onkeypress="return isNumber(event)" />
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label><span class="text-danger">*</span> Orgão emissão</label>
                                                    <input type="text" id="orgao-emissao-rep" required
                                                        class="form-control" />
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label><span class="text-danger">*</span> UF Emissão</label>
                                                    <select id="select-uf-emissao" name="select-uf-emissao"
                                                        class="form-control" required></select>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label><span class="text-danger">*</span> Emissão identidade</label>
                                                    <!-- <div class="input-group date">
                                                        <input type="text" class="form-control" id="emissao-identidade"
                                                            maxlength="10" data-date-format="DD/MM/YYYY">
                                                        <span class="input-group-addon" id="teste">
                                                            <i class="fa fa-calendar"></i>
                                                        </span>
                                                    </div> -->
                                                    <div class="input-group date" id="emissao-identidade-picker">
                                                        <input type="text" class="form-control"
                                                            data-date-format="DD/MM/YYYY" id="emissao-identidade" />
                                                        <div class="input-group-addon">
                                                            <span class="fa fa-calendar"></span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label><span class="text-danger">*</span> Data nascimento</label>
                                                    <div class="input-group date" id="data-nascimento-picker">
                                                        <input type="text" class="form-control"
                                                            data-date-format="DD/MM/YYYY" id="data-nascimento" />
                                                        <div class="input-group-addon">
                                                            <span class="fa fa-calendar"></span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-5 mb-30">
                                <div class="ibox animated fadeInRight">
                                    <div class="ibox-title" style="background-color: #056233;">
                                        <h5 style="color: white">Endereço</h5>
                                    </div>
                                    <div class="ibox-content">
                                        <div class="row">
                                            <div class="col-md-12">
                                                <div class="form-group">
                                                    <label><span class="text-danger">*</span> Endereço</label>
                                                    <input type="text" maxlength="200" id="endereco-rep"
                                                        class="form-control" />
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-12">
                                                <div class="form-group">
                                                    <label><span class="text-danger">*</span> Bairro</label>
                                                    <input type="text" maxlength="200" id="bairro-rep"
                                                        class="form-control" />
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-4">
                                                <div class="form-group">
                                                    <label><span class="text-danger">*</span> Número</label>
                                                    <input type="text" maxlength="10"
                                                        onkeypress="return isNumber(event)" id="numero-rep"
                                                        class="form-control" />
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="form-group">
                                                    <label><span class="text-danger">*</span> CEP</label>
                                                    <input type="text" id="cep-rep" oninput="mascara(this, 'cep')"
                                                        class="form-control" />
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="form-group">
                                                    <label>Complemento</label>
                                                    <input type="text" maxlength="200" id="complemento-rep"
                                                        class="form-control" />
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label><span class="text-danger">*</span> Estado</label>
                                                    <select id="select-estados-rep" name="select-estados-rep"
                                                        class="form-control"></select>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label><span class="text-danger">*</span> Cidade</label>
                                                    <select id="select-cidades-rep" name="select-cidades-rep"
                                                        class="form-control"></select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <div style="margin-top: -140px" class="ibox animated fadeInRight">
                                    <div class="text-right">
                                        <button type="button" class="btn btn-xs btn-info"
                                            onclick="adicionarRepLegal(true, false)">
                                            <i class="fa fa-plus"></i> Adicionar
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <div style="margin-top: -100px" class="ibox animated fadeInRight">
                                    <div class="table-responsive" id="box"></div>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <div style="margin-top: -40px" class="ibox animated fadeInRight">
                                    <div class="ibox-content">
                                        <div class="row">
                                            <div class="col-md-8">
                                                <h3 id="mensagem-validacao" style="display: none" class="text-danger">
                                                </h3>
                                                <h3 id="mensagem-sucesso" style="display: none" class="text-success">
                                                </h3>
                                                <h3 id="mensagem-sucesso-2" style="display: none" class="text-success">
                                                    Aguarde, você será redirecionado para a página de login.</h3>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="pull-right">
                                                    <button type="button" id="voltar" style="width: 130px"
                                                        class="btn btn-labeled btn-white text-right">
                                                        <span class="btn-label">
                                                            <i class="fa fa-arrow-circle-left"></i>
                                                        </span>
                                                        Voltar
                                                    </button>
                                                    <button type="submit" id="salvar"
                                                        style="width: 130px; background-color: #056233;"
                                                        class="btn btn-labeled btn-success text-right">
                                                        <span class="btn-label">
                                                            <i class="fa fa-check-circle"></i>
                                                        </span>
                                                        Salvar
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- <div class="col-lg-1 mb-30"></div> -->
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <!--[if lt IE 10]>
      <p class="browsehappy">You are using an <strong>outdated</strong> browser. Please <a href="http://browsehappy.com/">upgrade your browser</a> to improve your experience.</p>
    <![endif]-->
    <!-- Wrapper-->

    <!-- End wrapper-->
    <!-- build:js(src) scripts/vendor.js -->
    <!-- bower:js -->
    <script src="../bower_components/jquery/dist/jquery.js"></script>
    <script src="../bower_components/angular/angular.js"></script>
    <script src="../bower_components/angular-animate/angular-animate.js"></script>
    <script src="../bower_components/angular-cookies/angular-cookies.js"></script>
    <script src="../bower_components/angular-touch/angular-touch.js"></script>
    <script src="../bower_components/angular-sanitize/angular-sanitize.js"></script>
    <script src="../bower_components/angular-messages/angular-messages.js"></script>
    <script src="../bower_components/angular-aria/angular-aria.js"></script>
    <script src="../bower_components/angular-resource/angular-resource.js"></script>
    <script src="../bower_components/angular-ui-router/release/angular-ui-router.js"></script>
    <script src="../bower_components/tether/dist/js/tether.js"></script>
    <script src="../bower_components/bootstrap/dist/js/bootstrap.js"></script>
    <script src="../bower_components/angular-bootstrap/ui-bootstrap-tpls.js"></script>
    <script src="../bower_components/moment/moment.js"></script>
    <script src="../bower_components/pace/pace.js"></script>
    <script src="../bower_components/metisMenu/dist/metisMenu.js"></script>
    <script src="../bower_components/angular-toastr/dist/angular-toastr.tpls.js"></script>
    <script src="../bower_components/angular-ui-mask/dist/mask.js"></script>
    <script src="../bower_components/angular-drag-and-drop-lists/angular-drag-and-drop-lists.js"></script>
    <script src="../bower_components/angular-bootstrap-lightbox/dist/angular-bootstrap-lightbox.js"></script>
    <script src="../bower_components/bootbox/src/bootbox.js"></script>
    <script src="../bower_components/ngBootbox/dist/ngBootbox.js"></script>
    <script src="../bower_components/angular-ui-grid/ui-grid.js"></script>
    <script src="../bower_components/angular-ui-switch/angular-ui-switch.js"></script>
    <script src="../bower_components/ngmap/build/scripts/ng-map.js"></script>
    <script src="../bower_components/bootstrap-daterangepicker/daterangepicker.js"></script>
    <script src="../bower_components/angular-daterangepicker/js/angular-daterangepicker.js"></script>
    <script src="../bower_components/moment-duration-format/lib/moment-duration-format.js"></script>
    <script src="../bower_components/angular-bootstrap-confirm/dist/angular-bootstrap-confirm.js"></script>
    <script src="../bower_components/angularjs-datetime-picker/angularjs-datetime-picker.js"></script>
    <script src="../bower_components/angular-input-masks/angular-input-masks-standalone.js"></script>
    <script src="../bower_components/signalr/jquery.signalR.js"></script>
    <script src="../bower_components/angular-signalr-hub/signalr-hub.js"></script>
    <script src="../bower_components/transitionize/dist/transitionize.js"></script>
    <script src="../bower_components/fastclick/lib/fastclick.js"></script>
    <script src="../bower_components/switchery/dist/switchery.js"></script>
    <script src="../bower_components/angular-ui-select/dist/select.js"></script>
    <script src="../bower_components/angular-ladda/dist/angular-ladda.min.js"></script>
    <script src="../bower_components/angular-confirm/js/angular-confirm.js"></script>
    <script src="../bower_components/angular-bootstrap-toggle-switch/angular-toggle-switch.js"></script>
    <script src="../bower_components/angular-img-cropper/dist/angular-img-cropper.min.js"></script>
    <script src="../bower_components/angular-base64-upload/src/angular-base64-upload.js"></script>
    <script src="../bower_components/angular1-star-rating/dist/index.js"></script>
    <script src="../bower_components/lodash/lodash.js"></script>
    <!-- endbower -->
    <!-- endbuild -->


    <!-- inject:js -->
    <script src="app/app.module.js"></script>
    <script
        src="app/entities/empresa/modal/vinculo-primeiro-usuario-empresa/vinculo-primeiro-usuario-empresa-modal.controller.js"></script>
    <script src="app/entities/empresa/avaliacao/services/avaliacao-empresa.service.js"></script>
    <script src="app/entities/relatorios/portas-abertas/porta-aberta.controller.js"></script>
    <script src="app/entities/empresa/consulta/empresa.controller.js"></script>
    <script src="app/entities/empresa/cadastro/empresa-crud.controller.js"></script>
    <script src="app/entities/empresa/avaliacao/avaliacao-empresa.controller.js"></script>
    <script src="app/entities/empresa/avaliacao/avaliacao-empresa-validar.state.js"></script>
    <script src="app/entities/empresa/avaliacao/avaliacao-empresa-validar.controller.js"></script>
    <script src="app/entities/configuracao/pais/pais.controller.js"></script>
    <script src="app/entities/configuracao/pais/pais-crud.controller.js"></script>
    <script src="app/entities/configuracao/modulo/modulo.state.js"></script>
    <script src="app/entities/configuracao/modulo/modulo.controller.js"></script>
    <script src="app/entities/configuracao/modulo/modulo-crud.controller.js"></script>
    <script src="app/entities/configuracao/estado/estado.controller.js"></script>
    <script src="app/entities/configuracao/estado/estado-crud.controller.js"></script>
    <script src="app/entities/configuracao/cidade/cidade.controller.js"></script>
    <script src="app/entities/configuracao/cidade/cidade-crud.controller.js"></script>
    <script src="app/entities/veiculo/veiculo.state.js"></script>
    <script src="app/entities/veiculo/veiculo.controller.js"></script>
    <script src="app/entities/veiculo/veiculo-crud.controller.js"></script>
    <script src="app/entities/usuario/usuario.state.js"></script>
    <script src="app/entities/usuario/usuario.controller.js"></script>
    <script src="app/entities/usuario/usuario-crud.controller.js"></script>
    <script src="app/entities/ultimos-cadastros/ultimos-cadastros.state.js"></script>
    <script src="app/entities/ultimos-cadastros/ultimos-cadastros.controller.js"></script>
    <script src="app/entities/ultimos-cadastros/ultimos-cadastros-detalhes.controller.js"></script>
    <script src="app/entities/tipo-notificacao/tipo-notificacao.controller.js"></script>
    <script src="app/entities/tipo-notificacao/tipo-notificacao-crud.factory.js"></script>
    <script src="app/entities/tipo-notificacao/tipo-notificacao-crud.controller.js"></script>
    <script src="app/entities/request-log/request-log.state.js"></script>
    <script src="app/entities/request-log/request-log.controller.js"></script>
    <script src="app/entities/portador/portador.state.js"></script>
    <script src="app/entities/portador/portador.controller.js"></script>
    <script src="app/entities/portador/portador-crud.controller.js"></script>
    <script src="app/entities/painel-pagamento/painel-pagamento.state.js"></script>
    <script src="app/entities/painel-pagamento/painel-pagamento.controller.js"></script>
    <script src="app/entities/painel-pagamento/painel-pagamento-crud.controller.js"></script>
    <script src="app/entities/painel-ciot/painel-ciot.state.js"></script>
    <script src="app/entities/painel-ciot/painel-ciot.controller.js"></script>
    <script src="app/entities/painel-ciot/painel-ciot-modal-cancel-crud.controller.js"></script>
    <script src="app/entities/painel-ciot/painel-ciot-modal-cad-veic-crud.controller.js"></script>
    <script src="app/entities/painel-ciot/painel-ciot-modal-cad-portador-crud.controller.js"></script>
    <script src="app/entities/painel-ciot/painel-ciot-modal-cad-filial-crud.controller.js"></script>
    <script src="app/entities/painel-ciot/painel-ciot-modal-cad-cliente-crud.controller.js"></script>
    <script src="app/entities/painel-ciot/painel-ciot-crud.controller.js"></script>
    <script src="app/entities/notificacao-push/notificacao-push.controller.js"></script>
    <script src="app/entities/notificacao-push/notificacao-push-crud.controller.js"></script>
    <script src="app/entities/menu/menu.state.js"></script>
    <script src="app/entities/menu/menu.controller.js"></script>
    <script src="app/entities/menu/menu-crud.controller.js"></script>
    <script src="app/entities/log-sms/log-sms.state.js"></script>
    <script src="app/entities/log-sms/log-sms.controller.js"></script>
    <script src="app/entities/painel-saldo/painel-saldo.state.js"></script>
    <script src="app/entities/painel-saldo/painel-saldo.controller.js"></script>
    <script src="app/entities/grupo-usuario/grupo-usuario.state.js"></script>
    <script src="app/entities/grupo-usuario/grupo-usuario.controller.js"></script>
    <script src="app/entities/grupo-usuario/grupo-usuario-crud.controller.js"></script>
    <script src="app/entities/filial/filial.state.js"></script>
    <script src="app/entities/filial/filial.controller.js"></script>
    <script src="app/entities/filial/filial-crud.controller.js"></script>
    <script src="app/entities/empresa/empresa.state.js"></script>
    <script src="app/entities/configuracao/configuracao.state.js"></script>
    <script src="app/entities/cliente/cliente.state.js"></script>
    <script src="app/entities/cliente/cliente.controller.js"></script>
    <script src="app/entities/cliente/cliente-crud.controller.js"></script>
    <script src="app/components/utils/files.js"></script>
    <script src="app/components/sweetalert/sweet-alert.min.js"></script>
    <script src="app/components/sweetalert/angular-sweet-alert.min.js"></script>
    <script src="app/components/spinner-loader/spinner-loader.directive.js"></script>
    <script src="app/components/sidebar/timer.directive.js"></script>
    <script src="app/components/sidebar/sidebar.directive.js"></script>
    <script src="app/components/sidebar/sidebar.controller.js"></script>
    <script src="app/components/percent-field/percentageField.js"></script>
    <script src="app/components/notificacao/notificacao.controller.js"></script>
    <script src="app/components/minimaliza-sidebar/minimaliza-sidebar.directive.js"></script>
    <script src="app/components/label-obrigatorio/label-obrigatorio.directive.js"></script>
    <script src="app/components/janelas-abertas/backTop.directive.js"></script>
    <script src="app/components/input-date/input-type-date.directive.js"></script>
    <script src="app/components/iboxTools/iboxTools.directive.js"></script>
    <script src="app/components/hrLabel/hr-label.directive.js"></script>
    <script src="app/components/header/header.controller.js"></script>
    <script src="app/components/form-header/form-header.directive.js"></script>
    <script src="app/components/form/ui-switch.directive.js"></script>
    <script src="app/components/form/props.filter.js"></script>
    <script src="app/components/form/limit-to-caracter.filter.js"></script>
    <script src="app/components/form/form-wizard.directive.js"></script>
    <script src="app/components/consulta-padrao/consulta-padrao.directive.js"></script>
    <script src="app/components/consulta-padrao/consulta-padrao-modal.directive.js"></script>
    <script src="app/components/consulta-padrao/consulta-padrao-modal.controller.js"></script>
    <script src="app/components/chart/chart.directive.js"></script>
    <script src="app/components/ats-validator/ats-validator.directive.js"></script>
    <script src="app/components/ats-ui-select-validator/ats-ui-select-validator.directive.js"></script>
    <script src="app/components/ats-ui-grid-exporter/ats-ui-grid-exporter.directive.js"></script>
    <script src="app/components/ats-repeat-end/ats-repeat-end.directive.js"></script>
    <script src="app/components/ats-price/ats-price.directive.js"></script>
    <script src="app/components/ats-peso/ats-peso.directive.js"></script>
    <script src="app/components/ats-codigo/ats-codigo.directive.js"></script>
    <script src="app/components/ats-numeric/ats-numeric.directive.js"></script>
    <script src="app/components/ats-number/ats-number.directive.js"></script>
    <script src="app/components/ats-code/ats-code.directive.js"></script>
    <script src="app/blocks/interceptors/authSession.interceptor.js"></script>
    <script src="app/blocks/handlers/state.handler.js"></script>
    <script src="app/blocks/config/ui-select.config.js"></script>
    <script src="app/blocks/config/ui-mask.config.js"></script>
    <script src="app/blocks/config/ui-grid-i18n.config.js"></script>
    <script src="app/blocks/config/toastr.config.js"></script>
    <script src="app/blocks/config/ngBootbox.config.js"></script>
    <script src="app/blocks/config/angular-datatables.config.js"></script>
    <script src="app/blocks/config/angula-bootstrap-datepicker.config.js"></script>
    <script src="app/account/redefinicao/redefinicao-dialog.controller.js"></script>
    <script src="app/account/recuperar/recuperar-dialog.controller.js"></script>
    <script src="app/account/logout/logout.controller.js"></script>
    <script src="app/account/login/login.controller.js"></script>
    <script src="app/services/signalRSvc.service.js"></script>
    <script src="app/services/signalR.service.js"></script>
    <script src="app/services/persistentData.service.js"></script>
    <script src="app/services/defaultsService.service.js"></script>
    <script src="app/services/baseService.service.js"></script>
    <script src="app/main/main.controller.js"></script>
    <script src="app/environment.js"></script>
    <script src="app/config.js"></script>
    <script src="app/app.state.js"></script>
    <script src="app/app.constants.js"></script>
    <!-- endinject -->
    <script src="../assets/lib/angular-locale_pt-br.js"></script>
    <script src="../assets/lib/polyfiller.js"></script>
    <script src="../assets/lib/circle-progress.js"></script>
    <script src="../assets/lib/markerclusterer.js"></script>
    <script src="../assets/lib/jquery.priceformat.min.js"></script>
    <script src="../assets/lib/swal.js"></script>
    <script src="../assets/lib/angular-base64-upload.js"></script>
    <script src="../assets/lib/moment-with-locales.js"></script>
    <script src="../assets/lib/bootstrap-datetimepicker.js"></script>
    <!-- inject:partials -->
    <!-- angular templates will be automatically converted in js and inserted here -->
    <!-- endinject -->
    <!-- endbuild -->
</body>
<script type="text/javascript">
    const PROPRIEDADE_VAZIA = "";
    const MENSAGEM_PADRAO_VALIDACAO = "CAMPOS MARCADOS COM (*) SÃO OBRIGATÓRIOS";
    RepLegalList = [];
    Cidades = [];
    Estados = [];
    CidadesRep = [];
    EstadosRep = [];

    $("#cep").blur(function () {
        //Preenche os campos com "..." enquanto nao retorna os dados
        $("#endereco").val("")
        $("#bairro").val("")
        $("#select-cidades").val("")
        $("#select-estados").val("")
        // seta a variavel requisitada no campo cep
        consulta = $("#cep").val();

        // Fazendo a consulta
        //var resultadoCEP = $.getScript("http://webservice.kinghost.net/web_cep.php?auth=3fb311c918cf2aea198e1648aa1c4462&formato=javascript&cep="+ consulta
        var resultadoCEP = $.get('https://viacep.com.br/ws/' + consulta + '/json', function () {
            // seta as variáveis de retorno
            //unescape - Decodifica uma string codificada com o método escape.
            rua = unescape(resultadoCEP.responseJSON.logradouro)
            bairro = unescape(resultadoCEP.responseJSON.bairro)
            localidade = unescape(resultadoCEP.responseJSON.localidade)
            uf = unescape(resultadoCEP.responseJSON.uf)

            consultarEstados();
            setTimeout(function () {
                var estado = _.find(Estados, ['sigla', uf]);
                $("#select-estados").val(estado.id);

                consultarCidadesPorEstado(estado.id);
                setTimeout(function () {
                    var cidade = _.find(Cidades, ['descricao', localidade]);
                    $("#select-cidades").val(cidade.id);
                }, 1500);
            }, 1500);

            // preenche os campos
            $("#endereco").val(rua)
            $("#bairro").val(bairro)
        });
    });

    $("#cep-rep").blur(function () {
        //Preenche os campos com "..." enquanto nao retorna os dados
        $("#endereco-rep").val("")
        $("#bairro-rep").val("")
        $("#select-cidades-rep").val("")
        $("#select-estados-rep").val("")
        // seta a variavel requisitada no campo cep
        consulta = $("#cep-rep").val();

        // Fazendo a consulta
        //var resultadoCEP = $.getScript("http://webservice.kinghost.net/web_cep.php?auth=3fb311c918cf2aea198e1648aa1c4462&formato=javascript&cep="+ consulta
        var resultadoCEP = $.get('https://viacep.com.br/ws/' + consulta + '/json', function () {
            // seta as variáveis de retorno
            //unescape - Decodifica uma string codificada com o método escape.
            rua = unescape(resultadoCEP.responseJSON.logradouro)
            bairro = unescape(resultadoCEP.responseJSON.bairro)
            localidade = unescape(resultadoCEP.responseJSON.localidade)
            uf = unescape(resultadoCEP.responseJSON.uf)

            consultarEstadosRep();
            setTimeout(function () {
                var estado = _.find(EstadosRep, ['sigla', uf]);
                $("#select-estados-rep").val(estado.id);

                consultarCidadesPorEstadoRep(estado.id);
                setTimeout(function () {
                    var cidade = _.find(CidadesRep, ['descricao', localidade]);
                    $("#select-cidades-rep").val(cidade.id);
                }, 1500);
            }, 1500);
            // preenche os campos
            $("#endereco-rep").val(rua)
            $("#bairro-rep").val(bairro)
        });
    });

    consultarEstados();
    consultarEstadosRep();
    consultarUfEmissao();
    comboSexo = {
        data: [{ id: 1, descricao: 'Masculino' }, { id: 2, descricao: 'Feminino' }, { id: 3, descricao: 'Outros' }, { id: 4, descricao: 'Indefinido' }]
    };

    document.getElementById("mostraEmpresa").style.display = "block";
    document.getElementById("mostraRepresentanteLegal").style.display = "none";
    function habilitaAba(a) {
        if (a == 1) {
            document.getElementById("mostraEmpresa").style.display = "block";
            document.getElementById("mostraRepresentanteLegal").style.display = "none";
            document.getElementById('divEmpresa').className = "control-label col-xs-12 col-sm-6 col-md-6 col-lg-6 active";
            document.getElementById('divRepresentante').className = "control-label col-xs-12 col-sm-6 col-md-6 col-lg-6";
        }
        else {
            document.getElementById("mostraEmpresa").style.display = "none";
            document.getElementById("mostraRepresentanteLegal").style.display = "block";
            document.getElementById('divEmpresa').className = "control-label col-xs-12 col-sm-6 col-md-6 col-lg-6";
            document.getElementById('divRepresentante').className = "control-label col-xs-12 col-sm-6 col-md-6 col-lg-6 active";
        }
    }

    function removerRepLegal(i) {
        RepLegalList.splice(i, 1);
        adicionarRepLegal(false, true);
    }

    adicionarEnabled = function (PortadorRepLegal) {
        return (PortadorRepLegal.Nome != PROPRIEDADE_VAZIA && PortadorRepLegal.CpfCnpj != PROPRIEDADE_VAZIA && PortadorRepLegal.Sexo != undefined && PortadorRepLegal.Email != PROPRIEDADE_VAZIA && PortadorRepLegal.Telefone != PROPRIEDADE_VAZIA
            && PortadorRepLegal.Celular != PROPRIEDADE_VAZIA && PortadorRepLegal.NomePai != PROPRIEDADE_VAZIA && PortadorRepLegal.NomeMae != PROPRIEDADE_VAZIA && PortadorRepLegal.NumeroIdentidade != PROPRIEDADE_VAZIA && PortadorRepLegal.OrgaoEmissor != PROPRIEDADE_VAZIA
            && PortadorRepLegal.UfEmissao != PROPRIEDADE_VAZIA && PortadorRepLegal.EmissaoIdentidade != PROPRIEDADE_VAZIA && PortadorRepLegal.DataNascimento != PROPRIEDADE_VAZIA && PortadorRepLegal.Endereco != PROPRIEDADE_VAZIA && PortadorRepLegal.Bairro != PROPRIEDADE_VAZIA && PortadorRepLegal.EnderecoNumero != PROPRIEDADE_VAZIA
            && PortadorRepLegal.Cep != PROPRIEDADE_VAZIA && PortadorRepLegal.EstadoId != "0" && PortadorRepLegal.CidadeId != "0")
    }

    clearConsultaRepLegal = function () {
        $('#nome-rep').val(null);
        $('#cpf-rep').val(null);
        $('#select-sexos').val(null);
        $('#email-rep').val(null);
        $('#rntrc-rep').val(null);
        $('#telefone-rep').val(null);
        $('#celular-rep').val(null);
        $('#nome-pai-rep').val(null);
        $('#nome-mae-rep').val(null);
        $('#numero-identidade-rep').val(null);
        $('#orgao-emissao-rep').val(null);
        $('#select-uf-emissao').val(null);
        $('#emissao-identidade').val(null);
        $('#data-nascimento').val(null);
        $('#endereco-rep').val(null);
        $('#bairro-rep').val(null);
        $('#numero-rep').val(null);
        $('#cep-rep').val(null);
        $('#complemento-rep').val(null);
        $('#select-cidades-rep').val(null);
        $('#select-estados-rep').val(null);

    }

    function adicionarRepLegal(add, remove) {
        var adiciona = false;
        if (add) {
            var PortadorRepLegal = {
                Id: "0", Nome: $('#nome-rep').val(), CpfCnpj: $('#cpf-rep').val(), Sexo: $('#select-sexos').val(),
                Email: $('#email-rep').val(), RNTRC: $('#rntrc-rep').val(), Telefone: $('#telefone-rep').val(),
                Celular: $('#celular-rep').val(), NomePai: $('#nome-pai-rep').val(), NomeMae: $('#nome-mae-rep').val(),
                NumeroIdentidade: $('#numero-identidade-rep').val(), OrgaoEmissor: $('#orgao-emissao-rep').val(),
                UfEmissao: $('#select-uf-emissao').val(), EmissaoIdentidade: $('#emissao-identidade').val(), DataNascimento: $('#data-nascimento').val(), Endereco: $('#endereco-rep').val(),
                Bairro: $('#bairro-rep').val(), EnderecoNumero: $('#numero-rep').val(), Cep: $('#cep-rep').val(),
                Complemento: $('#complemento-rep').val(), CidadeId: $('#select-cidades-rep').val(), EstadoId: $('#select-estados-rep').val()
            };
            
            var objetosValidados = _.filter(RepLegalList, function (v) {
                return v.CpfCnpj === PortadorRepLegal.CpfCnpj;
            });

            if (objetosValidados.length > 0) {
                mensagemValidacao(true, "Este representante legal já foi adicionado.");
                return;
            }

            if (adicionarEnabled(PortadorRepLegal)) {
                adiciona = true;
                RepLegalList.push(PortadorRepLegal);
            }
        }

        if (adiciona || remove) {
            var html = "<table class='table table-bordered table-hover'>\
                            <thead>\
                                <tr>\
                                    <th width='80%'>Nome</th>\
                                    <th width='15%'>Cpf</th>\
                                    <th width='5%''>Ações</th>\
                                </tr>\
                            </thead>";
            for (var i = 0; i < RepLegalList.length; i++) {
                html += "<tr>";
                html += "<td>" + RepLegalList[i].Nome + "</td>";
                html += "<td>" + RepLegalList[i].CpfCnpj + "</td>";
                html += "<td class='text-center' style='vertical-align: middle'>\
                            <button type='button' uib-tooltip='Remover' class='btn btn-xs btn-danger' onclick='removerRepLegal("+ i + ")'>\
                                <i class='fa fa-trash-o'></i>\
                            </button>\
                        </td>";
                html += "</tr>";
            }
            html += "</table>";
            document.getElementById("box").innerHTML = html;
            mensagemValidacao(false, "");
            clearConsultaRepLegal();
        }
        else {
            mensagemValidacao(true, MENSAGEM_PADRAO_VALIDACAO);
        }
    }

    function isNumber(evt) {
        evt = (evt) ? evt : window.event;
        var charCode = (evt.which) ? evt.which : evt.keyCode;
        if (charCode > 31 && (charCode < 48 || charCode > 57)) {
            return false;
        }
        return true;
    }

    $(function () {
        $('#emissao-identidade-picker').datetimepicker({
            locale: 'pt-br'
        });
    });

    $(function () {
        $('#data-nascimento-picker').datetimepicker({
            locale: 'pt-br'
        });
    });

    $(document).ready(function () {
        $("#telefone-rep").bind('paste', function (e) {
            e.preventDefault();
        });
    });

    $(document).ready(function () {
        $("#celular-rep").bind('paste', function (e) {
            e.preventDefault();
        });
    });

    $(document).ready(function () {
        $("#cpf-rep").bind('paste', function (e) {
            e.preventDefault();
        });
    });

    $(document).ready(function () {
        $("#cep-rep").bind('paste', function (e) {
            e.preventDefault();
        });
    });

    $(document).ready(function () {
        $("#telefone").bind('paste', function (e) {
            e.preventDefault();
        });
    });

    $(document).ready(function () {
        $("#celular").bind('paste', function (e) {
            e.preventDefault();
        });
    });

    $(document).ready(function () {
        $("#cnpj").bind('paste', function (e) {
            e.preventDefault();
        });
    });

    $(document).ready(function () {
        $("#cep").bind('paste', function (e) {
            e.preventDefault();
        });
    });

    adicionarItemVazioSelect("select-sexos");

    for (var i = 0; i < 4; i++) {
        var item = comboSexo.data[i];
        var option = document.createElement("option");
        document.getElementById("select-sexos").options.add(option);

        option.value = item.id;
        option.text = item.descricao;
    }

    document.getElementById('select-uf-emissao').onchange = function () {
        var ufEmissao = $(this).val();
    };

    function consultarUfEmissao() {
        consultarUrl(function (url) {
            $.ajax({
                url: url + "Estado/ConsultarEstados",
                type: 'GET',
                data: {},
                datatype: 'json',
                success: function (response) {
                    adicionarItemVazioSelect("select-uf-emissao");

                    for (var i = 0; i < response.data.length; i++) {
                        var item = response.data[i];
                        var option = document.createElement("option");
                        document.getElementById("select-uf-emissao").options.add(option);

                        option.value = item.id;
                        option.text = item.descricao;
                    }
                }
            });
        });
    }

    document.getElementById('select-estados').onchange = function () {
        var estadoId = $(this).val();
        removerItensCidade();
        consultarCidadesPorEstado(estadoId);
    };

    document.getElementById('select-estados-rep').onchange = function () {
        var estadoId = $(this).val();
        removerItensCidadeRep();
        consultarCidadesPorEstadoRep(estadoId);
    };

    document.getElementById('salvar').onclick = function () {
        salvar();
    }

    document.getElementById('proximo').onclick = function () {
        // salvar();
        document.getElementById("mostraEmpresa").style.display = "none";
        document.getElementById("mostraRepresentanteLegal").style.display = "block";
        document.getElementById('divEmpresa').className = "control-label col-xs-12 col-sm-6 col-md-6 col-lg-6";
        document.getElementById('divRepresentante').className = "control-label col-xs-12 col-sm-6 col-md-6 col-lg-6 active";
    }

    document.getElementById('cancelar').onclick = function () {
        window.location.href = "index.html";
    }

    document.getElementById('voltar').onclick = function () {
        document.getElementById("mostraEmpresa").style.display = "block";
        document.getElementById("mostraRepresentanteLegal").style.display = "none";
        document.getElementById('divEmpresa').className = "control-label col-xs-12 col-sm-6 col-md-6 col-lg-6 active";
        document.getElementById('divRepresentante').className = "control-label col-xs-12 col-sm-6 col-md-6 col-lg-6";
    }

    function consultarEstados() {
        consultarUrl(function (url) {
            $.ajax({
                url: url + "Estado/ConsultarEstados",
                type: 'GET',
                data: {},
                datatype: 'json',
                success: function (response) {
                    adicionarItemVazioSelect("select-estados");
                    Estados = response.data;

                    for (var i = 0; i < response.data.length; i++) {
                        var item = response.data[i];
                        var option = document.createElement("option");
                        document.getElementById("select-estados").options.add(option);

                        option.value = item.id;
                        option.text = item.descricao;
                    }
                }
            });
        });
    }

    function consultarEstadosRep() {
        consultarUrl(function (url) {
            $.ajax({
                url: url + "Estado/ConsultarEstados",
                type: 'GET',
                data: {},
                datatype: 'json',
                success: function (response) {
                    adicionarItemVazioSelect("select-estados-rep");
                    EstadosRep = response.data;
                    for (var i = 0; i < response.data.length; i++) {
                        var item = response.data[i];
                        var option = document.createElement("option");
                        document.getElementById("select-estados-rep").options.add(option);

                        option.value = item.id;
                        option.text = item.descricao;
                    }
                }
            });
        });
    }

    function consultarCidadesPorEstado(estadoId) {
        consultarUrl(function (url) {
            $.ajax({
                url: url + "Cidade/ConsultarCidadesPorEstado",
                type: 'GET',
                data: { estadoId: estadoId },
                datatype: 'json',
                success: function (response) {
                    adicionarItemVazioSelect("select-cidades");
                    Cidades = response.data;
                    for (var i = 0; i < response.data.length; i++) {
                        var item = response.data[i];
                        var option = document.createElement("option");
                        document.getElementById("select-cidades").options.add(option);

                        option.value = item.id;
                        option.text = item.descricao;
                    }
                }
            });
        })
    }
    function consultarCidadesPorEstadoRep(estadoId) {
        consultarUrl(function (url) {
            $.ajax({
                url: url + "Cidade/ConsultarCidadesPorEstado",
                type: 'GET',
                data: { estadoId: estadoId },
                datatype: 'json',
                success: function (response) {
                    adicionarItemVazioSelect("select-cidades-rep");
                    CidadesRep = response.data;

                    for (var i = 0; i < response.data.length; i++) {
                        var item = response.data[i];
                        var option = document.createElement("option");
                        document.getElementById("select-cidades-rep").options.add(option);

                        option.value = item.id;
                        option.text = item.descricao;
                    }
                }
            });
        })
    }

    function removerItensCidade() {
        var itensSelectCidade = document.getElementById("select-cidades")
        var quantidadeItens = itensSelectCidade.length;

        for (var i = 0; i < quantidadeItens; i++) {
            var cidade = document.getElementById("select-cidades");
            cidade.remove(cidade[i]);
        }
    }

    function removerItensCidadeRep() {
        var itensSelectCidade = document.getElementById("select-cidades-rep")
        var quantidadeItens = itensSelectCidade.length;

        for (var i = 0; i < quantidadeItens; i++) {
            var cidade = document.getElementById("select-cidades-rep");
            cidade.remove(cidade[i]);
        }
    }

    function adicionarItemVazioSelect(elementId) {
        var optionEmpty = document.createElement("option");
        document.getElementById(elementId).options.add(optionEmpty);
        optionEmpty.value = 0;
        optionEmpty.text = "";
    }

    function salvar() {
        mensagemValidacao(false, "");
        var objCadastro = organizarObjetoCreate();

        if (!validarObjeto(objCadastro)) {
            mensagemValidacao(true, MENSAGEM_PADRAO_VALIDACAO);
            return;
        }

        if (!validaRepLegal(objCadastro.RepLegalList)) {
            mensagemValidacao(true, MENSAGEM_PADRAO_VALIDACAO);
            return;
        }

        realizarPost(objCadastro);
    }

    function realizarPost(empresa) {
        desabilitarBotoes();

        consultarUrl(function (url) {
            $.ajax({
                type: 'POST',
                url: url + "Empresa/Cadastre",
                contentType: "application/json; charset=utf-8",
                data: JSON.stringify(empresa),
                dataType: "json",
                success: function (response) {
                    if (!response.success) {
                        mensagemValidacao(true, response.message);
                        habilitarBotoes();
                    } else {
                        mensagemSucesso(true, "Informações enviadas com sucesso! Seu cadastro foi enviado para aprovação e você receberá um e-mail após a validação.");
                        setTimeout(() => {
                            window.location.href = "index.html";
                        }, 10000);
                    }
                },
                error: function (response) {
                    mensagemValidacao(true, "Ocorreu um erro interno do servidor, tente novamente mais tarde.");
                    habilitarBotoes();
                }
            });
        })
    }

    function organizarObjetoCreate() {
        var obj = {
            Id: "0", NomeFantasia: $('#razao-social').val(), RazaoSocial: $('#nome-fantasia').val(),
            Email: $('#email').val(), Cnpj: $('#cnpj').val(),
            Telefone: $('#telefone').val(), Celular: $('#celular').val(),
            Endereco: $('#endereco').val(), Bairro: $('#bairro').val(), EstadoId: $('#select-estados').val(), CidadeId: $('#select-cidades').val(),
            EnderecoNumero: $('#numero').val(), Cep: $('#cep').val(), Complemento: $('#complemento').val(),
            StatusCadastro: 2, RepLegalList
        }
        return obj;
    }

    function mensagemValidacao(mostrar, mensagem) {
        $("#mensagem-validacao").html(mensagem);

        if (mostrar) {
            document.getElementById("mensagem-validacao").style.display = "block";
        }
        else {
            document.getElementById("mensagem-validacao").style.display = "none";
        }
    }

    function mensagemSucesso(mostrar, mensagem) {
        $("#mensagem-sucesso").html(mensagem);

        if (mostrar) {
            document.getElementById("mensagem-sucesso").style.display = "block";
            document.getElementById("mensagem-sucesso-2").style.display = "block";
        }
        else {
            document.getElementById("mensagem-sucesso").style.display = "none";
            document.getElementById("mensagem-sucesso-2").style.display = "none";
        }
    }

    function desabilitarBotoes() {
        $("#salvar").prop("disabled", true);
        $("#cancelar").prop("disabled", true);
    }

    function habilitarBotoes() {
        $("#salvar").prop("disabled", false);
        $("#cancelar").prop("disabled", false);
    }

    function consultarUrl(callback) {
        var url = "http://localhost:51001/";
        callback(url);
    }

    function validarObjeto(obj) {
        if (obj.NomeFantasia === undefined || obj.NomeFantasia === PROPRIEDADE_VAZIA)
            return false;
        if (obj.Celular === undefined || obj.Celular === PROPRIEDADE_VAZIA)
            return false;
        if (obj.Cep === undefined || obj.Cep === PROPRIEDADE_VAZIA)
            return false;
        if (obj.CidadeId === undefined || obj.CidadeId === null || obj.CidadeId === PROPRIEDADE_VAZIA)
            return false;
        if (obj.Email === undefined || obj.Email === PROPRIEDADE_VAZIA)
            return false;
        if (obj.Endereco === undefined || obj.Endereco === PROPRIEDADE_VAZIA)
            return false;
        if (obj.EnderecoNumero === undefined || obj.EnderecoNumero === PROPRIEDADE_VAZIA)
            return false;
        if (obj.NomeFantasia === undefined || obj.NomeFantasia === PROPRIEDADE_VAZIA)
            return false;
        if (obj.RazaoSocial === undefined || obj.RazaoSocial === PROPRIEDADE_VAZIA)
            return false;
        if (obj.Telefone === undefined || obj.Telefone === PROPRIEDADE_VAZIA)
            return false;

        return true;
    }

    function validaRepLegal(obj) {
        for (var i = 0; i < obj.length; i++) {
            if (obj[i].Nome === undefined || obj[i].Nome == PROPRIEDADE_VAZIA)
                return false;
            if (obj[i].CpfCnpj === undefined || obj[i].CpfCnpj == PROPRIEDADE_VAZIA)
                return false;
            if (obj[i].Email === undefined || obj[i].Email == PROPRIEDADE_VAZIA)
                return false;
            if (obj[i].Telefone === undefined || obj[i].Telefone == PROPRIEDADE_VAZIA)
                return false;
            if (obj[i].Celular === undefined || obj[i].Celular == PROPRIEDADE_VAZIA)
                return false;
            if (obj[i].NomePai === undefined || obj[i].NomePai == PROPRIEDADE_VAZIA)
                return false;
            if (obj[i].NomeMae === undefined || obj[i].NomeMae == PROPRIEDADE_VAZIA)
                return false;
            if (obj[i].NumeroIdentidade === undefined || obj[i].NumeroIdentidade == PROPRIEDADE_VAZIA)
                return false;
            if (obj[i].OrgaoEmissor === undefined || obj[i].OrgaoEmissor == PROPRIEDADE_VAZIA)
                return false;
            if (obj[i].UfEmissao === undefined || obj[i].UfEmissao == PROPRIEDADE_VAZIA)
                return false;
            if (obj[i].EmissaoIdentidade === undefined || obj[i].EmissaoIdentidade == PROPRIEDADE_VAZIA)
                return false;
            if (obj[i].Endereco === undefined || obj[i].Endereco == PROPRIEDADE_VAZIA)
                return false;
            if (obj[i].Bairro === undefined || obj[i].Bairro == PROPRIEDADE_VAZIA)
                return false;
            if (obj[i].EnderecoNumero === undefined || obj[i].EnderecoNumero == PROPRIEDADE_VAZIA)
                return false;
            if (obj[i].Cep === undefined || obj[i].Cep == PROPRIEDADE_VAZIA)
                return false;
            if (obj[i].EstadoId === undefined || obj[i].EstadoId == "0")
                return false;
            if (obj[i].CidadeId === undefined || obj[i].CidadeId == "0")
                return false;
            return true;
        }
    }
    function mascara(i, t) {

        var v = i.value;

        if (isNaN(v[v.length - 1])) {
            i.value = v.substring(0, v.length - 1);
            return;
        }

        if (t == "cnpj") {
            i.setAttribute("maxlength", "18");
            if (v.length == 2 || v.length == 6) i.value += ".";
            if (v.length == 10) i.value += "/";
            if (v.length == 15) i.value += "-";
        }

        if (t == "cpf") {
            i.setAttribute("maxlength", "14");
            if (v.length == 3 || v.length == 7) i.value += ".";
            if (v.length == 11) i.value += "-";
        }

        if (t === "tel") {
            if (v.length === 1) i.value = "(" + i.value;
            if (v.length === 3) i.value += ") ";
            if (v[5] == 9) {
                i.setAttribute("maxlength", "15");
                if (v.length === 10) i.value += "-";
            } else {
                i.setAttribute("maxlength", "14");
                if (v.length === 9) i.value += "-";
            }
        }

        if (t == "cep") {
            i.setAttribute("maxlength", "9");
            if (v.length == 5) i.value += "-"
        }
    }

    function isNumber(evt) {
        evt = (evt) ? evt : window.event;
        var charCode = (evt.which) ? evt.which : evt.keyCode;
        if (charCode > 31 && (charCode < 48 || charCode > 57)) {
            return false;
        }

        return true;
    }
</script>

</html>