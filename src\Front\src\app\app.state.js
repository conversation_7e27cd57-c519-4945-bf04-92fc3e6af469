(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .config(stateConfig);

    /** @ngInject */
    function stateConfig($stateProvider, $urlRouterProvider) {
        $urlRouterProvider.otherwise('/index/main');

        $stateProvider
            .state('index', {
                abstract: true,
                url: "/index",
                templateUrl: "app/layout/content.html"
            })
            .state('index.main', {
                url: "/main",
                templateUrl: "app/main/main.html"
            })

            // Login
            // .state('login', {
            //     abstract: true,
            //     url: "/auth",
            //     // templateUrl: "app/layout/content.html"
            // })
            .state('login', {
                url: "/login",
                views: {
                    'login': {
                        templateUrl: 'app/account/login/login.html',
                        controller: 'LoginController',
                        controllerAs: 'vm'
                    }
                }
            })
            .state('logout', {
                url: "/logout",
                //templateUrl: 'app/account/logout/logout.html',
                onEnter: ['$uibModal', function ($uibModal) {
                    var sk = window.localStorage.getItem('SessionKey');
                    if (sk !== null && angular.isDefined(sk)) {
                        $uibModal.open({
                            animation: true,
                            templateUrl: 'app/account/logout/logout.html',
                            controller: 'LogoutController',
                            controllerAs: 'LogoutCtrl',
                            backdrop: 'static',
                            keyboard: false,
                            size: 'sm'
                        });
                    }
                }]
            })
            .state('login.sessao-expirada-login', {
                url: '/expirou',
                onEnter: ['$uibModal', function ($uibModal) {
                    window.localStorage.clear();
                    window.localStorage.setItem('SessionKey', 'invalid-key-value');
                    $uibModal.open({
                        animation: true,
                        templateUrl: 'app/account/login/login.html',
                        controller: 'LoginController',
                        controllerAs: 'vm',
                        backdrop: 'static',
                        keyboard: false,
                        size: 'sm'
                    });
                }]
            })

            .state('login.change-password', {
                url: '/expirou',
                onEnter: ['$uibModal', function ($uibModal) {
                    window.localStorage.clear();
                    window.localStorage.setItem('SessionKey', 'invalid-key-value');
                    $uibModal.open({
                        animation: true,
                        templateUrl: 'app/account/login/login.html',
                        controller: 'LoginController',
                        controllerAs: 'vm',
                        backdrop: 'static',
                        keyboard: false,
                        size: 'sm'
                    });
                }]
            })

            .state('relatorio', {
                abstract: true,
                url: "/relatorio",
                templateUrl: "app/layout/content.html"
            })
            .state('relatorio.portas-abertas', {
                url: '/portas-abertas',
                templateUrl: 'app/entities/relatorios/portas-abertas/porta-aberta.html'
            })

            //Estabelecimentos
            .state('tipo-estabelecimento', {
                abstract: true,
                url: "/tipo-estabelecimento",
                templateUrl: "app/layout/content.html"
            })
            .state('tipo-estabelecimento.index', {
                url: '/index',
                templateUrl: 'app/entities/estabelecimento/tipo-estabelecimento/tipo-estabelecimento.html'
            })
            .state('tipo-estabelecimento.crud', {
                url: '/:link',
                templateUrl: 'app/entities/estabelecimento/tipo-estabelecimento/tipo-estabelecimento-crud.html'
            })
            .state('estabelecimento', {
                abstract: true,
                url: "/estabelecimento",
                templateUrl: "app/layout/content.html"
            })
            .state('estabelecimento.index', {
                url: '/index',
                templateUrl: 'app/entities/estabelecimento/estabelecimento.html'
            })
            .state('estabelecimento.crud', {
                url: '/:link',
                templateUrl: 'app/entities/estabelecimento/estabelecimento-crud.html'
            })

            .state('estabelecimento-base', {
                abstract: true,
                url: "/estabelecimento-base",
                templateUrl: "app/layout/content.html"
            })
            .state('estabelecimento-base.index', {
                url: '/index',
                templateUrl: 'app/entities/estabelecimento-base/estabelecimento.html'
            })
            .state('estabelecimento-base.crud', {
                url: '/:link',
                templateUrl: 'app/entities/estabelecimento-base/estabelecimento-crud.html',
                params: {
                    forcarCadastramentoPeloUsuarioLogado: false
                }
            })

    }
})();