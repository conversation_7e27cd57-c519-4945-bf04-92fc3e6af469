(function () {
    'use strict';

    angular.module('bbcWeb.modeloVeiculo.state', []).config(routeConfig);

    function routeConfig($urlRouterProvider, $stateProvider) {
        $stateProvider.state('modelo-veiculo', {
            abstract: true,
            url: "/modelo-veiculo",
            templateUrl: "app/layout/content.html"
        }).state('modelo-veiculo.index', {
            url: '/index',
            templateUrl: 'app/entities/modelo-veiculo/modelo-veiculo.html'
        }).state('modelo-veiculo.modelo-veiculo-crud', {
            url: '/:link',
            templateUrl: 'app/entities/modelo-veiculo/modelo-veiculo-crud.html'
        });
    }
})();