(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .directive('formHeader', function ($rootScope) {
            var directive = {
                bindToController: true,
                controller: function () {
                    var vm = this;
                    vm.imgLogo = $rootScope.imgCustomLogoDashboard;
                },
                controllerAs: 'vm',
                templateUrl: 'app/components/form-header/form-header.directive.html',
                link: function () {},
                restrict: 'AE',
                scope: {
                    items: '=',
                    head: '='
                }
            };
            return directive;
        });
})();