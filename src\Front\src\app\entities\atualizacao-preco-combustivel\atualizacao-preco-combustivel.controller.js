(function () {
    'use strict';

    angular
        .module('bbcWeb').controller('AtualizacaoPrecoCombustivelController', AtualizacaoPrecoCombustivelController);

    AtualizacaoPrecoCombustivelController.inject = ['URL_SERVER_DEV', 'BaseService', '$rootScope', 'toastr', '$scope'];

    function AtualizacaoPrecoCombustivelController(URL_SERVER_DEV, BaseService, $rootScope, toastr, $scope) {
        var vm = this;

        vm.nomeBotaoSelecionarTodos = "Selecionar todos";
        vm.solicitacoesPendentes = [];
        vm.idPosto = "";
        vm.MotivoInterno = "";
        vm.MotivoExterno = "";
        vm.status = "";

        vm.headerItems = [{
            name: 'Administração'
        }, {
            name: 'Atualização preço combustível'
        }];

        vm.comboStatus = [
            {data: 1, label: 'Aprovada'},
            {data: 0, label: 'Reprovada'}
        ];

        vm.dateOptions = {
            timePicker: false,
            applyClass: 'btn-primary',
            locale: {
                applyLabel: "Aplicar",
                fromLabel: "De",
                format: "DD/MM/YYYY",
                toLabel: "Até",
                cancelLabel: 'Cancelar',
                customRangeLabel: 'Período',
                monthNames: ['Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho', 'Julho', 'Agosto', 'Setembro',
                    'Outubro', 'Novembro', 'Dezembro'
                ],
                daysOfWeek: ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb']
            },
            ranges: {
                'Últimos 1 dias': [moment().add(-1, 'days'), moment()],
                'Últimos 7 dias': [moment().add(-7, 'days'), moment()]
            }
        };

        vm.date = {
            startDate: moment().add(-1, 'days'),
            endDate: moment()
        };

        vm.consultaPostoModal = {
            columnDefs: [{
                name: 'Cód.',
                field: 'id',
                width: 60,
                type: 'number',
                primaryKey: true
            }, {
                name: 'Razão Social',
                field: 'razaoSocial',
                width: 120
            }, {
                name: 'CNPJ',
                field: 'cnpj',
                width: 200
            }],
            desiredValue: 'id',
            desiredText: 'razaoSocial',
            url: 'Posto/ConsultarGridPosto',
            paramsMethod: function () {
                return {}
            }
        }

        vm.buscar = function () {
            if (vm.date.startDate > vm.date.endDate) {
                toastr.error('O período inicial deve ser menor que o período final!');
                return;
            }

            var dtInicial = vm.date.startDate.toDate();
            dtInicial.setHours(0, 0, 0, 0)
            var dtFinal = vm.date.endDate.toDate();
            dtFinal.setHours(23, 59, 59, 999)
            var postoId = vm.consultaPostoModal.selectedValue ? vm.consultaPostoModal.selectedValue : 0;

            BaseService.get("AtualizacaoPrecoCombustivel", "SolicitacoesPendentes", {
                startDate: dtInicial,
                endDate: dtFinal,
                idPosto: postoId,
            }).then(function (response) {
                if (!response.success) {
                    vm.solicitacoesPendentes = [];
                    toastr.error(response.message);
                    return
                }

                if (!response.data || response.data.length <= 0) {
                    vm.gridOptionsAbastecimentoHistorico.dataSource.refresh();
                    toastr.info("Não existem dados a serem exibidos para o posto selecionado.");
                    return
                }

                vm.solicitacoesPendentes = response.data;
                for (var i = 0; i < vm.solicitacoesPendentes.length; i++) {
                    vm.solicitacoesPendentes[i].selecionado = 'N';
                }
                
                vm.gridOptionsAbastecimentoHistorico.dataSource.refresh();
            });
        };

        vm.LimparCampos = function () {
            vm.date = {
                startDate: moment().add(-1, 'days'),
                endDate: moment()
            };

            vm.solicitacoesPendentes = [];
            vm.consultaPostoModal.selectedValue = null;
            vm.consultaPostoModal.selectedText = "";
            vm.MotivoInterno = "";
            vm.MotivoExterno = "";
            vm.status = "";
            vm.gridOptionsAbastecimentoHistorico.data = [];
        };


        vm.MarcarTodos = function () {
            if (vm.solicitacoesPendentes) {
                for (var i = 0; i < vm.solicitacoesPendentes.length; i++) {
                    vm.solicitacoesPendentes[i].selecionado = 'S';
                }
            }
        }

        vm.DesmarcarTodos = function () {
            if (vm.solicitacoesPendentes) {
                for (var i = 0; i < vm.solicitacoesPendentes.length; i++) {
                    vm.solicitacoesPendentes[i].selecionado = 'N';
                }
            }
        }

        vm.salvar = function (frm) {
            if (!frm.$valid) {
                toastr.error('Todos os campos obrigatórios e/ou inválidos devem ser preenchidos corretamente com seus respectivos valores!');
                return;
            }

            var encontrouItemSelecionado = false;

            for (var i = 0; i < vm.solicitacoesPendentes.length; i++) {
                const element = vm.solicitacoesPendentes[i];
                if (element.selecionado === 'S') {
                    encontrouItemSelecionado = true;
                    break;
                }
            }

            if (!encontrouItemSelecionado) {
                toastr.error("Informe ao menos um evento para aprovação!");
                return;
            }

            var solicitacoes = [];

            for (var j = 0; j < vm.solicitacoesPendentes.length; j++) {
                const element = vm.solicitacoesPendentes[j];
                if (element.selecionado === 'S') {
                    var solicitacao = {}
                    solicitacao = element;
                    solicitacao.valorCombustivelBomba = element.valorBomba;
                    solicitacao.valorCombustivelBBC = element.valorBBC;
                    solicitacao.statusAprovacao = vm.status;
                    solicitacao.motivoExterno = vm.MotivoExterno;
                    solicitacao.motivoInterno = vm.MotivoInterno;
                    solicitacao.usuarioAlteracao = $rootScope.usuarioLogado.idUsuario;
                    solicitacoes.push(solicitacao);
                }
            }

            BaseService.post("AtualizacaoPrecoCombustivel", "SalvarBBC", {solicitacoes: solicitacoes})
                .then(function (response) {
                    if (!response.sucesso) {
                        toastr.error(response.mensagem);
                        return
                    }
                    toastr.success(response.mensagem);
                    vm.LimparCampos();
                });
        }

        vm.onClickLimpar = function () {
            vm.LimparCampos();
        };

        vm.onClickLimparMotivoStatus = function () {
            vm.MotivoInterno = "";
            vm.MotivoExterno = "";
            vm.status = "";
        };


        vm.gridOptionsAbastecimentoHistorico = {
            data: [],
            onRegisterApi: BaseService.dataGrid.getGridApiRegistration("gridOptionsAbastecimentoHistorico"),
            dataSource: {
                autoBind: false,
                url: "AtualizacaoPrecoCombustivel/HistoricoSolicitacoesPendentes",
                params: function () {
                    return {
                        idPosto: vm.idPosto
                    };
                }
            },
            columnDefs : [
                {
                    name: 'Código do posto',
                    field: 'postoId',
                    enableFiltering: false,
                    width: '*'
                },
                {
                    name: 'Nome do posto',
                    field: 'nomePosto',
                    enableFiltering: false,
                    width: '*'
                },
                {
                    name: 'Nome do combustível',
                    field: 'nomeCombustivel',
                    enableFiltering: false,
                    width: '*'
                },
                {
                    name: 'Valor bomba anterior',
                    field: 'valorBomba',
                    cellTemplate: '<div class="ui-grid-cell-contents">\
                                    <input type="text" ng-model="row.entity.valorBomba" readonly\
                                            class="no-borders" style="background: none;" ui-money-mask="3" />\
                               </div>',
                    enableFiltering: false,
                    width: '*'
                },
                {
                    name: 'Valor BBC anterior',
                    field: 'valorBBC',
                    cellTemplate: '<div class="ui-grid-cell-contents">\
                                    <input type="text" ng-model="row.entity.valorBBC" readonly\
                                            class="no-borders" style="background: none;" ui-money-mask="3" />\
                               </div>',
                    enableFiltering: false,
                    width: '*'
                },
                {
                    name: 'Valor BBC solicitado',
                    field: 'valorBBCSolicitado',
                    cellTemplate: '<div class="ui-grid-cell-contents">\
                                    <input type="text" ng-model="row.entity.valorBBCSolicitado" readonly\
                                            class="no-borders" style="background: none;" ui-money-mask="3" />\
                               </div>',
                    enableFiltering: false,
                    width: '*'
                },
                {
                    name: 'Valor bomba solicitado',
                    field: 'valorBombaSolicitado',
                    cellTemplate: '<div class="ui-grid-cell-contents">\
                                    <input type="text" ng-model="row.entity.valorBombaSolicitado" readonly\
                                            class="no-borders" style="background: none;" ui-money-mask="3" />\
                               </div>',
                    enableFiltering: false,
                    width: '*'
                },
                {
                    name: 'Diferença entre valores',
                    field: 'diferencaEntreValores',
                    cellTemplate: '<div class="ui-grid-cell-contents">\
                                    <input type="text" ng-model="row.entity.diferencaEntreValores" readonly\
                                            class="no-borders" style="background: none;" ui-money-mask="3" />\
                               </div>',
                    enableFiltering: false,
                    width: '*'
                },
                {
                    name: 'Status',
                    field: 'statusAprovacaoDescricao',
                    enableFiltering: false,
                    width: '*'
                },
                {
                    name: 'Data solicitação',
                    field: 'dataCadastro',
                    enableFiltering: false,
                    width: '*'
                },
                {
                    name: 'Data retorno',
                    field: 'dataAlteracao',
                    enableFiltering: false,
                    width: '*'
                },
                {
                    name: 'Motivo solicitação',
                    field: 'motivoSolicitacao',
                    enableFiltering: false,
                    width: '*'
                },
                {
                    name: 'Usuário alteração',
                    field: 'usuarioAlteracao',
                    enableFiltering: false,
                    width: '*'
                }]
        };

        $scope.$watch('vm.consultaPostoModal.selectedValue', function (value) {
            vm.idPosto = value ? value : 0;
            vm.solicitacoesPendentes = [];
            vm.gridOptionsAbastecimentoHistorico.data = [];
        });

        vm.checkBoxSelecionado = function (item) {
            if (!angular.isUndefinedOrNullOrEmpty(item))
                item.selecionado = item.selecionado === 'S' ? 'N' : 'S';
        };

        //Remove evento click nas setas
        $(document).off('click', 'table .arrow');
        //Evento click nas setas para mostrar os dados
        $(document).on('click', 'table .arrow', function () {
            var $tr = $(this).parent().parent().parent().find('tr.subItem');
            if (!$($tr).is(':visible')) {
                $($tr).show();
                $(this).attr("class", "arrow rotated");
            } else {
                $($tr).hide();
                $(this).attr("class", "arrow");
            }
        });


    }
})();