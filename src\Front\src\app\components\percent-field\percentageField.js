(function() {
    'use strict';


    angular
        .module('bbcWeb')
        .directive("percent", function($filter, $locale, $window) {
            var p = function(viewValue) {
                if (viewValue == '') {
                    return 0;
                }
                var value = stringToNumber(viewValue);
                if (value > 100)
                    value = 100;
                if (value < 0)
                    value = 0;
                return value / 100;
            };

            var stringToNumber = function(value) {
                if (angular.isNumber(parseFloat(value))) {
                    if (value.indexOf(',') > 0) {
                        var number = angular.copy(value) + '';
                        number = number.replaceAll($locale.NUMBER_FORMATS.GROUP_SEP, '');
                        number = number.replaceAll($locale.NUMBER_FORMATS.DECIMAL_SEP, '.');
                        number = parseFloat(number);
                        if (isNaN(number)) {
                            return 0;
                        } else {
                            return number;
                        }
                    } else {
                        return value;
                    }
                } else {
                    var number = angular.copy(value) + '';
                    number = number.replaceAll($locale.NUMBER_FORMATS.GROUP_SEP, '');
                    number = number.replaceAll($locale.NUMBER_FORMATS.DECIMAL_SEP, '.');
                    number = parseFloat(number);
                    if (isNaN(number)) {
                        return 0;
                    } else {
                        return number;
                    }
                }
            }

            var f = function(modelValue) {
                if (angular.isDefined(modelValue)) {
                    return $filter('number')(modelValue * 100, 2);
                }
            };

            return {
                require: 'ngModel',
                link: function(scope, ele, attr, ctrl) {
                    ctrl.$parsers.unshift(p);
                    ctrl.$formatters.unshift(f);
                    ele.on('blur', function() {
                        if (angular.isDefined(ctrl.$modelValue)) {
                            ele.val($filter('number')(ctrl.$modelValue * 100, 2));
                        }
                    });
                    /*
                    ele.on('click', function () {
                        if (!$window.getSelection().toString()) {
                            this.setSelectionRange(0, this.value.length)
                        }
                    });
                    */
                }
            };
        });

})();