(function () {
    'use strict';

    angular.module('bbcWeb').directive('atsNumber', function ($filter) {
        return {
            bindToController: true,
            require: 'ngModel',
            controller: function () {

            },
            controllerAs: 'vm',
            link: function (scope, element, attrs, ctrl) {
                ctrl.$parsers.unshift(function (value) {

                    value = value.replace(/\,/gi, "");
                    value = value.replace(/\./gi, "");
                    value = value.replace(/\D/g,'');  

                    var maxLength = element[0].attributes['max'].value;
                    
                    if (value.length >  maxLength)
                        value = value.substr(0, maxLength);

                    var castValue = Number(value);
                    if (value == "" || value == null || ( Number.isNaN(castValue)))
                        return element[0].value = value.replace(value, "");
                    
                    element[0].value = Number.parseInt(value).toFixed(0).toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
                    return element[0].value;
                });

                ctrl.$formatters.unshift(function (value){
                    if (ctrl.$modelValue >= 0)
                    element[0].value = $filter('number')(ctrl.$modelValue);
                        
                    return element[0].value;
                });
            },
            restrict: 'AE',
            scope: {}
        };
    });
})();