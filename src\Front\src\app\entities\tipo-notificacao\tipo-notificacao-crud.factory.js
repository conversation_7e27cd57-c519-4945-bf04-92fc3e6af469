(function() {
    'use strict';

    angular.module('bbcWeb').factory('TipoNotificacaoCrudFactory', TipoNotificacaoCrudFactory);

    function TipoNotificacaoCrudFactory() {
        var tipoCrud = function(Codigo, Descricao, IdEmpresa, IdFilial) {
            this.Codigo = Codigo;
            this.Descricao = Descricao;
            this.IdEmpresa = IdEmpresa;
            this.IdFilial = IdFilial;
        };

        return tipoCrud;
    }
})();