(function () {
    'use strict';

    angular.module('bbcWeb.central-notificacoes.state', []).config(routeConfig);

    function routeConfig($urlRouterProvider, $stateProvider) {
        $stateProvider.state('central-notificacoes', {
            abstract: true,
            url: "/central-notificacoes",
            templateUrl: "app/layout/content.html"
        }).state('central-notificacoes.index', {
            url: '/index',
            templateUrl: 'app/entities/central-notificacoes/central-notificacoes.html'
        }).state('central-notificacoes.central-notificacoes-crud', {
            url: '/:link',
            templateUrl: 'app/entities/central-notificacoes/central-notificacoes-crud.html'
        }).state('central-notificacoes.central-notificacoes-vale-pedagio-crud', {
            url: '/vale-pedagio/:link',
            templateUrl: 'app/entities/central-notificacoes/central-notificacoes-vale-pedagio-crud.html'
        });
    }
})();