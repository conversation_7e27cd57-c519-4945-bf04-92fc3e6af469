<div class="form-horizontal">
    <hr />
    <div>
        <div>
            <div class="text-left">
                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                    <div class="form-group form-horizontal">
                        <label class="col-xs-12 col-sm-12 col-md-2 col-lg-2 control-label fixLeftLabel"
                            style="text-align: left">Cartão:</label>
                        <p class="input-group col-xs-12 col-sm-12 col-md-10 col-lg-10">
                            <input type="text" name="Cartao" ats-numeric
                                onkeypress="return event.charCode >= 48 && event.charCode <= 57" maxlength="16"
                                class="form-control"
                                ng-disabled="((vm.isNew() && vm.portador.tipoPessoa === 2) || (vm.portador.contaConductor === null && vm.portador.tipoPessoa === 2))"
                                validate-on="blur" ng-model="vm.cartaoId" />
                        </p>
                    </div>
                </div>
                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6"
                    ng-show="vm.portador.contaConductor !== null && !vm.isNew()">
                    <div class="form-group form-horizontal">
                        <label ng-show="vm.portador.contaConductor !== null && !vm.isNew()"
                            class="col-xs-12 col-sm-12 col-md-2 col-lg-2 control-label fixLeftLabel"
                            style="text-align: left">Motivo:</label>
                        <p class="input-group col-xs-12 col-sm-12 col-md-10 col-lg-10">
                            <input type="text" name="Motivo" maxlength="200" class="form-control" validate-on="blur"
                                ng-model="vm.motivo" />
                        </p>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="text-right">
                    <div class="form-header" style="width: 98%;">
                        <button type="button" class="mr-5 btn-labeled btn btn-info"
                            ng-disabled="((vm.cartaoId === null || vm.cartaoId === undefined) && (vm.portador.contaConductor !== null && !vm.isNew() || (vm.motivo===null || vm.motivo==='' || vm.motivo===undefined)))"
                            ng-click="vm.AtribuirTitular()">
                            <i class="fa fa-plus"></i> Adicionar
                        </button>
                    </div>
                </div>
            </div>

            <div ng-show="vm.carregandoSpinner">
                <div class="spiner-example">
                    <div class="sk-spinner sk-spinner-wave">
                        <div class="sk-rect1"></div>
                        <div class="sk-rect2"></div>
                        <div class="sk-rect3"></div>
                        <div class="sk-rect4"></div>
                        <div class="sk-rect5"></div>
                    </div>
                </div>
            </div>
        </div>
        <br />
        <div>
            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead>
                        <tr>
                            <th width="5%">Conta</th>
                            <th width="8%">Agência</th>
                            <th width="30%">Número Conta</th>
                            <th width="15%">Status Conta</th>
                            <th width="10%">Ações</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr ng-repeat="conta in vm.portador.contaConductor">
                            <td>{{conta.contaId}}</td>
                            <td>{{conta.agencia}}</td>
                            <td>{{conta.numeroConta}}</td>
                            <td>{{conta.statusConta}}</td>
                            <td class="text-center" style="vertical-align: middle">
                                <button type="button" uib-tooltip="Bloquear"
                                    ng-disabled="conta.statusConta==='Bloqueada'" class="btn btn-xs btn-danger"
                                    ng-click="vm.BloquearConta(conta.contaId)">
                                    <i class="fa fa-ban"></i>
                                </button>
                                <button type="button" uib-tooltip="Reativar" ng-disabled="conta.statusConta==='Normal'"
                                    class="btn btn-xs btn-success" ng-click="vm.ReativarConta(conta.contaId)">
                                    <i class="fa fa-check"></i>
                                </button>
                                <button type="button" uib-tooltip="Ver cartões" class="btn btn-xs btn-info"
                                    ng-click="vm.VerCartao(conta.contaId)">
                                    <i class="fa fa-credit-card"></i>
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>