/**
 * Created by alan.rezende on 01/02/2017.
 */
(function () {
  'use strict';

  angular.module('bbcWeb')
    .config(function (toastrConfig) {
      angular.extend(toastrConfig, {
        allowHtml: true,
        closeButton: false,
        closeHtml: '<button>&times;</button>',
        extendedTimeOut: 500,
        iconClasses: {
          error: 'toast-error',
          info:  'toast-info',
          success: 'toast-success',
          warning: 'toast-warning'
        },
        messageClass: 'toast-message',
        onHidden: null,
        onShown: null,
        onTap: null,
        progressBar: false,
        tapToDismiss: true,
        templates: {
          toast: 'directives/toast/toast.html',
          progressbar: 'directives/progressbar/progressbar.html'
        },
        timeOut: 3000,
        titleClass: 'toast-title',
        toastClass: 'toast'
      });
    });
})();