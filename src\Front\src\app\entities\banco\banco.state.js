(function () {
    'use strict';

    angular.module('bbcWeb.banco.state', []).config(routeConfig);

    function routeConfig($urlRouterProvider, $stateProvider) {
        $stateProvider.state('banco', {
            abstract: true,
            url: "/banco",
            templateUrl: "app/layout/content.html"
        }).state('banco.index', {
            url: '/index',
            templateUrl: 'app/entities/banco/banco.html'
        }).state('banco.banco-crud', {
            url: '/:link',
            templateUrl: 'app/entities/banco/banco-crud.html'
        });
    }
})();