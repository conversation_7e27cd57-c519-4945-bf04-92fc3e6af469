(function () {
    'use strict';

    angular.module('bbcWeb').directive('atsRepeatEnd', function ($filter, $timeout) {
        return {
            restrict: "A",
            link: function (scope, element, attrs) {
                if (scope.$last) {
                    $timeout(function () {
                        scope.$eval(attrs.atsRepeatEnd);
                    }, 1000);
                }
            }
        };
    });
})();