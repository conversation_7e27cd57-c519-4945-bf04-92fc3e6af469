<div class="form-horizontal">
    <hr />
    <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">         
        <div class="row">
            <consulta-padrao-modal tabledefinition="vm.consultaCombustivel" idname="combustivelConsulta" label="'Combustivel:'" 
                placeholder="'Selecione um combustivel'" 
                directivesizes="'col-xs-12 col-sm-12 col-md-6 col-lg-6'"
                labelsize="'col-xs-12 col-sm-3 col-md-3 col-lg-3 control-label'">
            </consulta-padrao-modal>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-3 col-lg-3 control-label">
                        Autonomia:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-9 col-lg-9">
                        <input type="text" onpaste="return event.charCode >= 48 && event.charCode <= 57 || event.charCode == 44"
                        onkeypress="return event.charCode >= 48 && event.charCode <= 57 || event.charCode == 44" ng-model="vm.autonomia" maxlength="200" validate-on="blur" name="Autonomia" class="form-control"
                        />
                    </div>
                </div>
            </div>   
        </div>
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-3 col-lg-3 control-label">
                        Capacidade:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-9 col-lg-9">
                        <input type="text" onpaste="return event.charCode >= 48 && event.charCode <= 57 || event.charCode == 44"
                        onkeypress="return event.charCode >= 48 && event.charCode <= 57 || event.charCode == 44" ng-model="vm.capacidadeTanque" maxlength="200" validate-on="blur" name="Capacidade" class="form-control"
                        />
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6 text-right">
                <div class="form-group">
                    <button type="button" class="mr-5 btn-labeled btn btn-info" ng-click="vm.adicionarCombustivel()">
                        <i class="fa fa-plus"></i> Adicionar
                    </button>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="table-responsive" style="margin-top: 5px;">
                <table class="table table-bordered table-hover col-xs-12">
                    <thead>
                        <tr>
                            <th width="40%">Combustível</th>
                            <th width="20%">Autonomia</th>
                            <th width="20%">Capacidade</th>
                            <th width="10%">Ações</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr ng-repeat="combustiveis in vm.veiculo.veiculoCombustiveis">
                            <td>{{combustiveis.combustivelNome}}</td>
                            <td>{{combustiveis.autonomia}}</td>
                            <td>{{combustiveis.capacidade}}</td>
                            <td class="text-center" style="vertical-align: middle">
                                <button type="button" uib-tooltip="Editar" class="btn btn-xs btn-info"
                                    ng-click="vm.editarCombustivel(combustiveis)">
                                    <i class="fa fa-edit"></i>
                                </button>
                                <button type="button" uib-tooltip="Remover" class="btn btn-xs btn-danger"
                                    ng-click="vm.removerCombustivel(combustiveis)">
                                    <i class="fa fa-trash-o"></i>
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>