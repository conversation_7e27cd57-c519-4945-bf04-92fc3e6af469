(function () {
    'use strict';

    angular.module('bbcWeb').controller('TipoNotificacaoController', TipoNotificacaoController);

    TipoNotificacaoController.$inject = [
        '$scope',
        'PersistentDataService',
        'BaseService',
        'toastr',
        '$window',
        'PERFIL_ADMINISTRADOR',
        'PERFIL_EMPRESA',
        '$rootScope',
        '$state',
        '$timeout'
    ];

    function TipoNotificacaoController(
        $scope,
        PersistentDataService,
        BaseService,
        toastr,
        $window,
        PERFIL_ADMINISTRADOR,
        PERFIL_EMPRESA,
        $rootScope,
        $state,
        $timeout) {
        //Inicialização dos objetos e arrays
        var vm = this;
        vm.headerItems = [{
            name: 'Cadastros'
        }, {
            name: 'Tipo de notificação'
        }];
        vm.tipo = {};
        vm.tipos = [];
        vm.consultaEmpresa = {};
        vm.consultaFilial = {};
        vm.perfilUsuarioLogado = $rootScope.usuarioLogado.perfil;


        // Configurações da grid...
        vm.gridOptions = {
            data: [],
            onRegisterApi: BaseService.dataGrid.defaultOnRegisterApi,
            dataSource: {
                autoBind: true,
                url: "TipoNotificacaoAts/ConsultarGrid",
                params: function () {
                    return {
                        idEmpresa: vm.consultaEmpresa.selectedValue,
                        idFilial: vm.consultaFilial.selectedValue,
                        descricao: vm.tipo.Descricao
                    };
                }
            },
            columnDefs: [{
                    name: 'Código',
                    width: 80,
                    primaryKey: true,
                    field: 'IdTipoNotificacao',
                    type: 'number'
                },
                {
                    name: 'Descrição',
                    field: 'Descricao',
                    width: '*',
                    minWidth: 250,
                    enableGrouping: false
                },
                {
                    name: 'Empresa',
                    serverField: 'Empresa.NomeFantasia',
                    width: '*',
                    minWidth: 250,
                    field: 'RazaoSocialEmpresa'
                },
                {
                    name: 'Filial',
                    serverField: 'Filial.NomeFantasia',
                    width: '*',
                    minWidth: 250,
                    field: 'RazaoSocialFilial'
                },
                {
                    name: 'Ações',
                    width: 80,
                    enableColumnMenu: false,
                    cellTemplate: '<div  ng-if="!row.groupHeader" class="ui-grid-cell-contents" title="TOOLTIP">\
                                    <button ng-disabled="!row.entity.Ativo" type="button" ui-sref="gestao-logistica-tipo-notificacao.tiponotificacao-crud({link: row.entity.IdTipoNotificacao})"\
                                        ng-class="{ \'btn btn-xs btn-info\': true }">\
                                        <i class="fa fa-edit"></i>\
                                    </button>\
                                    <button type="button" ng-click="grid.appScope.vm.alterarStatus(row.entity.IdTipoNotificacao, row.entity.Ativo)" ng-class="row.entity.Ativo ? \'btn btn-xs btn-success\' : \'btn btn-xs btn-danger\'">\
                                        <i ng-class="row.entity.Ativo ? \'fa fa-check\' : \'fa fa-times\'"></i>\
                                    </button>\
                                </div>'
                }
            ]
        };
        // Fim config Grid

        vm.alterarStatus = function (id, ativo) {
            BaseService.post('TipoNotificacaoAts', ativo ? "Inativar" : "Reativar", {
                IdTipoNotificacao: id
            }).then(function (response) {
                if (response.success)
                    toastr.success(response.message);
                else
                    toastr.error(response.message);

                vm.gridOptions.dataSource.refresh();
            });
        };

        vm.empresaVisivel = function () {
            return $rootScope.usuarioLogado.perfil == PERFIL_ADMINISTRADOR;
        };

        vm.filialVisivel = function () {
            return $rootScope.usuarioLogado.perfil == PERFIL_ADMINISTRADOR || $rootScope.usuarioLogado.perfil == PERFIL_EMPRESA;
        };

        $scope.$watch('vm.consultaEmpresa.selectedValue', function (newValue, oldValue) {
            if (angular.isUndefined(newValue)) {
                vm.consultaFilial.selectedValue = undefined;
                vm.consultaFilial.selectedText = undefined;
                vm.consultaFilial.values = [];
            }
        });

        vm.consultaFilial = {
            columnDefs: [{
                name: 'Código',
                field: 'IdFilial',
                width: 80,
                primaryKey: true
            }, {
                name: 'Razão Social',
                field: 'RazaoSocial',
                width: '*',
                minWidth: 250
            }, {
                name: 'CNPJ',
                field: 'CNPJ',
                enableGrouping: true,
                width: 145
            }],
            desiredValue: 'IdFilial',
            desiredText: 'RazaoSocial',
            url: 'FilialAts/ConsultarGrid',
            paramsMethod: function () {
                return {
                    IdEmpresa: vm.consultaEmpresa.selectedValue
                }
            }
        };

        vm.consultaEmpresa = {
            columnDefs: [{
                name: 'Código',
                field: 'IdEmpresa',
                width: 80,
                primaryKey: true
            }, {
                name: 'Razão Social',
                field: 'RazaoSocial',
                enableGrouping: true,
                width: '*',
                minWidth: 200
            }, {
                name: 'CNPJ',
                field: 'CNPJ',
                enableGrouping: true,
                width: 145
            }],
            desiredValue: 'IdEmpresa',
            desiredText: 'RazaoSocial',
            url: 'EmpresaAts/ConsultarGrid'
        };

        vm.consultaEmpresa.selectedValue = $window.localStorage.getItem("idEmpresa").toFixedType();

        // DO NOT TOUCH!!
        $scope.$on('$stateChangeStart', function (_, toParams) {
            PersistentDataService.store('TipoNotificacaoController', vm, "Tipos de notificação", "TipoNotificacaoCrudController", "gestao-logistica-tipo-notificacao.tiponotificacao");
        });
        var selfScope = PersistentDataService.get('TipoNotificacaoController');
        var filho = PersistentDataService.get('TipoNotificacaoCrudController');
        if (angular.isDefined(filho)) {
            $timeout(function () {
                $state.go('gestao-logistica-tipo-notificacao.tiponotificacao-crud', {
                    link: filho.data.tipoNotificacao.IdTipoNotificacao > 0 ? 'editar' : 'novo'
                });
            }, 50);
        } else if (angular.isDefined(selfScope))
            angular.extend(vm, selfScope.data);
        // DO NOT TOUCH!!
    }
})();