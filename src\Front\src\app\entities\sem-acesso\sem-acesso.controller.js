(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('SemAcessoController', SemAcessoController);

        SemAcessoController.inject = ['BaseService', '$rootScope', 'toastr', '$scope', 'PersistentDataService', '$timeout', '$state', 'PERFIL_ADMINISTRADOR'];

    function SemAcessoController(BaseService, $rootScope, toastr, $scope, PersistentDataService, $timeout, $state, PERFIL_ADMINISTRADOR) {
        var vm = this;                    
        
    }
})();