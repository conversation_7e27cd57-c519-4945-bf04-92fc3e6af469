(function() {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('ModuloController', ModuloController);

    ModuloController.$inject = [
        '$scope',
        'PersistentDataService',
        'BaseService',
        'toastr',
        '$rootScope',
        '$state',
        '$timeout'
    ];

    function ModuloController(
        $scope,
        PersistentDataService,
        BaseService,
        toastr,
        $rootScope,
        $state,
        $timeout) {
        var vm = this;


        vm.headerItems = [{
            name: 'Administração'
        }, {
            name: '<PERSON><PERSON><PERSON><PERSON>'
        }];
        vm.modulo = {};
        vm.modulos = [];
        vm.perfilUsuarioLogado = $rootScope.usuarioLogado.perfil;
        // Configurações da grid...
        vm.gridOptions = {
            data: [],
            onRegisterApi: BaseService.dataGrid.defaultOnRegisterApi,
            dataSource: {
                autoBind: true,
                url: "ModuloAts/ConsultarGrid"
            },
            columnDefs: [{
                    name: '<PERSON><PERSON><PERSON>',
                    primaryKey: true,
                    width: 80,
                    field: 'IdModulo',
                    type: 'number'
                },
                {
                    name: '<PERSON><PERSON><PERSON><PERSON>',
                    field: 'Des<PERSON>ricao',
                    width: '*',
                    minWidth: 250
                },
                {
                    name: 'Ações',
                    width: 80,
                    cellTemplate: '<div  ng-if="!row.groupHeader" class="ui-grid-cell-contents" title="TOOLTIP">\
                                    <button ng-disabled="!row.entity.Ativo" type="button" ui-sref="configuracao.modulo-crud({link: row.entity.IdModulo})"\
                                        ng-class="{ \'btn btn-xs btn-info\': true }">\
                                        <i class="fa fa-edit"></i>\
                                    </button>\
                                    <button type="button" ng-click="grid.appScope.vm.alterarStatus(row.entity.IdModulo, row.entity.Ativo)" ng-class="row.entity.Ativo ? \'btn btn-xs btn-success\' : \'btn btn-xs btn-danger\'">\
                                        <i ng-class="row.entity.Ativo ? \'fa fa-check\' : \'fa fa-times\'"></i>\
                                    </button>\
                                  </div>'
                }
            ]
        };
        // Fim config Grid
        vm.alterarStatus = function(id, ativo) {
            BaseService.post('ModuloAts', ativo ? "Inativar" : "Reativar", {
                idModulo: id
            }).then(function(response) {
                if (response.success)
                    toastr.success(response.message);
                else
                    toastr.error(response.message);

                vm.gridOptions.dataSource.refresh();
            });
        };

        $scope.$on('$stateChangeStart', function(_, toParams) {
            PersistentDataService.store('ModuloController', vm, "Módulo", "ModuloCrudController", "configuracao.modulo");
        });
        var selfScope = PersistentDataService.get('ModuloController');
        var filho = PersistentDataService.get('ModuloCrudController');
        if (angular.isDefined(filho)) {
            $timeout(function() {
                $state.go('configuracao.modulo-crud', {
                    link: filho.data.modulo.IdModulo > 0 ? 'editar' : 'novo'
                });
            }, 50);
        } else if (angular.isDefined(selfScope))
            angular.extend(vm, selfScope.data);
    }
})();