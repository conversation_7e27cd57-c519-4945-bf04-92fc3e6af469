(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('ServidoresCiotHistoricoController', ServidoresCiotHistoricoController);

        ServidoresCiotHistoricoController.inject = [
        'BaseService',
        '$rootScope',
        'toastr',
        '$scope',
        'PersistentDataService',
        '$timeout',
        '$state',
        '$stateParams',
        'PERFIL_ADMINISTRADOR',
        'oitozero.ngSweetAlert',
        '$uibModal',
        '$window'
    ];

    function ServidoresCiotHistoricoController(
        BaseService,
        $rootScope,
        toastr,
        $scope,
        PersistentDataService,
        $timeout,
        $state,
        $stateParams,
        PERFIL_ADMINISTRADOR,
        SweetAlert,
        $uibModal,
        $window) {
        var vm = this;


        vm.headerItems = [{ name: 'CIOT' }, { name: 'Monitoramento de histórico dos servidores CIOT' }];

        vm.servidorCiotHistorico = [];

        function init(){
            carregaHistorico();
        }

        vm.load = function(){
            carregaHistorico();
        }

        function carregaHistorico() {
            BaseService.get("MonitoramentoCiot", "ConsultarListaServidoresCiotHistorico")
                .then(function (response) {
                    if (response.success) {
                        vm.servidorCiotHistorico = response.data;
                    }
                });
        };
        init()
       
        // Controle de aba!!
        $scope.$on('$stateChangeStart', function () {
            PersistentDataService.store('MonitoramentoServidoresCiotController', vm, "Painel protocolo abastecimento", "MonitoramentoServidoresCiotController", "monitoramento-servidores-ciot.index");
        });

        var selfScope = PersistentDataService.get('MonitoramentoServidoresCiotController');
    }
})();