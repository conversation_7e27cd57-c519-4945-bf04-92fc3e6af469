<div class="form-horizontal">
    <hr-label dark="true"></hr-label><br><br>
    <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-4 control-label">
                        % Taxa de Transação Abastecimento:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-7 col-lg-8">
                        <input type="number" ng-model="vm.empresa.taxaAbastecimento"
                            max="100" min="0" name="taxa" class="form-control"/>
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-5 col-lg-4 control-label">
                        % de Cashback:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-7 col-lg-8">
                        <input type="number" ng-model="vm.empresa.cashback"
                            max="100" min="0" name="cash" class="form-control" />
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-12 col-md-4 col-lg-4 control-label">Contas:</label>
                    <div class="input-group col-xs-12 col-sm-12 col-md-6 col-lg-6">
                        <ui-select name="contaAbastecimento" ng-model="vm.empresa.contaAbastecimento" ats-ui-select-validator>
                            <ui-select-match>
                                <span>{{$select.selected.descricao}}</span>
                            </ui-select-match>
                            <ui-select-choices repeat="ex.id as ex in vm.contas | filter: $select.search">
                                <div ng-bind-html="ex.descricao | highlight: $select.search"></div>
                            </ui-select-choices>
                        </ui-select>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>