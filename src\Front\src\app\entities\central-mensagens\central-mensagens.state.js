﻿(function () {
    'use strict';

    angular.module('bbcWeb.central-mensagens.state', []).config(routeConfig);

    function routeConfig($urlRouterProvider, $stateProvider) {
        $stateProvider
        .state('central-mensagens', {
            abstract: true,
            url: "/central-mensagens",
            templateUrl: "app/layout/content.html"
        })
        .state('central-mensagens.index', {
            url: '/index',
            templateUrl: 'app/entities/central-mensagens/central-mensagens.html'
        })
        .state('central-mensagens.central-mensagens-crud', {
            url: '/:link',
            templateUrl: 'app/entities/central-mensagens/central-mensagens-crud.html'
        });
    }
})();