<div class="form-horizontal">
    <hr-label dark="true" title="'Banco'"></hr-label>
    <br>
    <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
        <div class="row">
            <consulta-padrao-modal tabledefinition="vm.consultaBanco" label="'Banco:'" idname="consultaBanco"
                                   placeholder="'Selecione um banco'" required-message="'Banco é obrigatório'"
                                   ng-required="true" directivesizes="'col-xs-12 col-sm-12 col-md-6 col-lg-6'"
                                   ng-disabled="vm.bancoCarregadoIsBbc"
                                   functionclear="vm.onClearBanco" ng-disabled="vm.disabledCamposConta"
                                   labelsize="'col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label'">
            </consulta-padrao-modal>
        </div>
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-3 col-lg-3 control-label">
                        <span class="text-danger mr-5">*</span>Agência:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-9 col-lg-9">
                        <input type="text" maxlength="200" required ng-disabled="vm.bancoCarregadoIsBbc" ng-model="vm.posto.agencia"
                               required-message="'Agência é obrigatória'" validate-on="blur" name="agencia"
                               class="form-control"
                        />
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-3 col-lg-3 control-label">
                        <span class="text-danger mr-5">*</span>DV
                    </label>
                    <div class="input-group col-xs-12 col-sm-3 col-md-3 col-lg-3">
                        <input type="text" maxlength="200" required ng-disabled="vm.bancoCarregadoIsBbc" ng-model="vm.posto.agenciaDV"
                               required-message="'DV é obrigatória'" validate-on="blur" name="dvagencia"
                               class="form-control"
                        />
                    </div>
                </div>
            </div>

        </div>
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-3 col-lg-3 control-label">
                        <span class="text-danger mr-5">*</span>Conta corrente:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-9 col-lg-9">
                        <input type="text" maxlength="200" required ng-disabled="vm.bancoCarregadoIsBbc" ng-model="vm.posto.contaCorrente"
                               required-message="'Conta corrente é obrigatória'" validate-on="blur" name="contacorrente"
                               class="form-control"
                        />
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-3 col-lg-3 control-label">
                        <span class="text-danger mr-5">*</span>DV
                    </label>
                    <div class="input-group col-xs-12 col-sm-3 col-md-3 col-lg-3">
                        <input type="text" required maxlength="200" ng-disabled="vm.bancoCarregadoIsBbc" ng-model="vm.posto.contaCorrenteDV"
                               required-message="'DV é obrigatória'" validate-on="blur" name="dvconta"
                               class="form-control"
                        />
                    </div>
                </div>
            </div>

        </div>

        <hr-label dark="true" title="'Faturamento'"></hr-label>
        <br>
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-3 col-lg-3 control-label">
                        <span class="text-danger mr-5">*</span>CNPJ faturamento:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-9 col-lg-9">
                        <input type="text" ui-br-cnpj-mask required ng-model="vm.posto.cnpjFaturamento"
                               required-message="'CNPJ é obrigatório'" maxlength="18" validate-on="blur" name="CnpjFat"
                               class="form-control" invalid-message="'CNPJ faturamento é inválido'"/>
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-2 col-md-4 col-lg-3">
                        <span class="text-danger mr-5">*</span>Prazo de pagamento:
                    </label>
                    <div class="input-group col-xs-12 col-sm-10 col-md-8 col-lg-9">
                        <ui-select
                                ng-disabled="vm.prazoPagamentoDisabled"
                                name="prazopag"
                                required-message="'Prazo de pagamento é obrigatório'"
                                required
                                ats-ui-select-validator
                                ng-model="vm.prazoMdrSelect">
                            <ui-select-match>
                                <span>{{$select.selected.prazo}}</span>
                            </ui-select-match>
                            <ui-select-choices
                                    repeat="ex in vm.prazoPagamentoMDRCombo">
                                <div ng-bind-html="ex.prazo | highlight: $select.search"></div>
                            </ui-select-choices>
                        </ui-select>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-3 col-lg-3 control-label">
                        MDR
                    </label>
                    <div class="col-xs-12 col-sm-3 col-md-3 col-lg-3 input-group ">
                        <input type="text" maxlength="200" required ng-model="vm.prazoMdrSelect.mdr"
                               placeholder="Escolha o prazo" disabled
                               required-message="'MDR é obrigatório, selecione um prazo'" validate-on="blur" name="mdr"
                               class="form-control"
                        />
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>