(function () {
    'use strict';

    angular.module('bbcWeb').controller('CentralPendenciaValePedagioController', CentralPendenciaValePedagioController);

    CentralPendenciaValePedagioController.$inject = ['toastr', '$rootScope', 'BaseService', '$state', '$stateParams', '$window', 'PERFIL_ADMINISTRADOR', '$scope', '$timeout', 'PersistentDataService', 'DefaultsService', '$uibModal'];

    function CentralPendenciaValePedagioController(
        toastr, $rootScope,
        BaseService, $state,
        $stateParams, $window,
        PERFIL_ADMINISTRADOR,
        $scope, $timeout,
        PersistentDataService,
        DefaultsService,
        $uibModal
    ) {
        var vm = this;
        vm.pendencia = [];

        vm.headerItems = [{
            name: 'Movimentações'
        }, {
            name: 'Central de pendências',
            link: 'central-pendencias.index'
        }, {
            name: 'Visualizar'
        }];

        if ($stateParams.link == 'novo')
            vm.pendencia.id = 'Auto';
    

        vm.gridOptions = {
            data: [],
            onRegisterApi: BaseService.dataGrid.defaultOnRegisterApi,
            urlRelatorio: "",
            enableSorting: false,
            dataSource: {
                url: "Pedagio/ConsultarGridTransacaoPagamentoValePedagio",
                params: function () {
                    return {
                        PagamentoValePedagioId: vm.pendencia.id
                    }
                },
            },
            columnDefs: [
                {
                    name: 'id',
                    displayName: 'Código',
                    width: 150,
                    field: 'id',
                    serverField: 'id',
                    type: 'number',
                    enableFiltering: true
                },
                {
                    name: 'TipoTransacao',
                    displayName: 'Tipo de Transação',
                    width: 175,
                    field: 'tipoTransacao',
                    serverField: 'tipoTransacao',
                    enableFiltering: false,
                    enableSorting: false
                },
                {
                    name: 'DataCadastro',
                    displayName: 'Data de Cadastro',
                    width: '*',
                    minWidth: 150,
                    field: 'dataCadastro',
                    serverField: 'dataCadastro',
                    enableFiltering: true
                },
                {
                    name: 'DataBaixa',
                    displayName: 'Data Baixa',
                    width: '*',
                    minWidth: 150,
                    field: 'dataBaixa',
                    serverField: 'dataBaixa',
                    enableFiltering: true
                },
                {
                    name: 'Valor',
                    displayName: 'Valor',
                    width: 175,
                    field: 'valor',
                    serverField: 'valor',
                    enableFiltering: false
                },
                {
                    name: 'Status',
                    displayName: 'Status',
                    width: 175,
                    field: 'status',
                    serverField: 'status',
                    enableFiltering: false,
                },
                {
                    name: 'ContaOrigem',
                    displayName: 'Conta Origem',
                    width: 175,
                    field: 'contaOrigem',
                    serverField: 'contaOrigem',
                    enableFiltering: false
                },
                {
                    name: 'ContaDestino',
                    displayName: 'Conta Destino',
                    width: 170,
                    field: 'contaDestino',
                    serverField: 'contaDestino',
                    enableFiltering: false,
                },
                {
                    name: 'UUID',
                    displayName: 'UUID',
                    width: 170,
                    field: 'uuid',
                    serverField: 'uuid',
                    enableFiltering: true
                },
                {
                    name: 'Mensagem',
                    displayName: 'Mensagem',
                    width: 400,
                    field: 'mensagem',
                    serverField: 'mensagem',
                    enableFiltering: false,
                },
                {
                    name: 'JsonEnvioDock',
                    displayName: 'Json envio Dock',
                    width: 250,
                    field: 'jsonEnvioDock',
                    serverField: 'JsonEnvioDock',
                    enableFiltering: false,
                    cellTooltip: true
                },
                {
                    name: 'JsonRespostaDock',
                    displayName: 'Json resposta Dock',
                    width: 250,
                    field: 'jsonRespostaDock',
                    serverField: 'jsonRespostaDock',
                    enableFiltering: false,
                    cellTooltip: true
                },

            ]
        };

        $scope.$watch('vm.pendencia.idPedagioMs', function () {
            try {
                vm.gridOptions.dataSource.refresh();
            console.log("fas");
            } catch (error) {return;}
            
        })

        vm.loadEdit = function (id) {
            BaseService.get('Pedagio', 'ConsultarPorId', {
                idPagamentoValePedagio: id
            }).then(function (response) {
                if (!response.success) {
                    toastr.error(response.message);
                    return;
                } else {
                    vm.pendencia = response.data;
                    vm.gridOptions.dataSource.refresh();
                }
            });
        }
        vm.loadEdit($stateParams.link);
        
        vm.onClickVoltar = function (wizard) {
            var ativoIndex = wizard.getActivePosition();
            if (ativoIndex == 1)
                $state.go('central-pendencias.index');
        };
    }
})();
