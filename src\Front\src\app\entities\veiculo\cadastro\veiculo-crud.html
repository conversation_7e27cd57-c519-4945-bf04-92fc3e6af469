<div id="VeiculoCrudController" ng-controller="VeiculoCrudController as vm">
    <form-header ng-submit="return" items="vm.headerItems" head="'Veículos'"></form-header>
    <div class="wrapper-content animated overflow-hidden fadeIn mb-0 pb-0">
        <div class="row">
            <div class="col-lg-12">
                <div class="ibox">
                    <div class="ibox-title">
                        <h5>{{vm.isNew() ? 'Novo' : 'Editar'}} veículo</h5>
                        <div ibox-tools></div>
                    </div>
                    <div class="ibox-content wizard">
                        <form name="frmVeiculoCrud" role="form" novalidate ats-validator ng-submit="vm.save(frmVeiculoCrud)" show-validation>
                            <div form-wizard steps="4">
                                <div class="form-wizard">
                                    <ol class="row">
                                        <li ng-click="wizard.go(1)" class="fixLRpg col-sm-4" ng-class="{'active': wizard.active(1)}">
                                            <h4>Principal</h4>
                                        </li>                                    
                                        <li ng-click="wizard.go(2)" class="fixLRpg col-sm-4" ng-class="{'active': wizard.active(2)}">
                                            <h4>Combustível</h4>
                                        </li>
                                        <li ng-click="wizard.go(3)" class="fixLRpg col-sm-4" ng-class="{'active': wizard.active(3)}">
                                            <h4>Frota</h4>
                                        </li>
                                    </ol>
                                    <br/>
                                </div>
                                <div id="activateTab1" ng-show="wizard.active(1)">
                                    <div ng-include="'app/entities/veiculo/cadastro/abas/principal.html'" class="form-horizontal"> </div>
                                </div>
                                <div id="activateTab2" ng-show="wizard.active(2)">
                                    <div ng-include="'app/entities/veiculo/cadastro/abas/combustivel.html'" class="form-horizontal"> </div>
                                </div>
                                <div id="activateTab3" ng-show="wizard.active(3)">
                                    <div ng-include="'app/entities/veiculo/cadastro/abas/frota.html'" class="form-horizontal"> </div>
                                </div>
                            </div>
                            <div class="row clearfix"> </div>
                            <hr-label dark="true"></hr-label>
                            <br/>
                            <div class="row">
                                <div class="form-group">
                                    <div class="col-md-12 col-lg-12 text-right">
                                        <button type="button" ng-disabled="vm.isSaving"
                                            ng-click="vm.onClickVoltar(wizard)" class="btn btn-labeled
                                                btn-default">
                                            <span class="btn-label">
                                                <i class="fa fa-arrow-circle-left"></i>
                                            </span>
                                            Voltar
                                        </button>
                                        <button type="button" ng-show="!wizard.active(3)" ng-disabled="vm.saving"
                                            ng-click="wizard.go(wizard.getActivePosition() + 1);"
                                            class="btn btn-labeled btn-default">
                                            <span class="btn-label">
                                                <i class="fa fa-arrow-circle-right"></i>
                                            </span>
                                            Avançar
                                        </button>
                                        <button type="submit" ng-show="wizard.active(3)" ng-disabled="vm.saving"
                                            class="btn btn-labeled btn-success text-right"
                                            data-style="expand-right">
                                            <span class="btn-label">
                                                <i class="fa fa-check-circle"></i>
                                            </span>
                                            Salvar
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>