<style>
    .marginTop {
        margin-top: 20px !important;
    }
</style>
<div id="PainelProtocoloAbastecimentoController" ng-controller="PainelProtocoloAbastecimentoController as vm">
    <form-header items="vm.headerItems" head="'Painel protocolo abastecimento'"></form-header>
    <div class="wrapper wrapper-content animated fadeIn mt-5 overflow-hidden">
        <div class="row">
            <div class="col-lg-12">
                <div class="ibox float-e-margins">
                    <div style="height: 38px;" class="ibox-title">
                        <h5>Painel protocolo abastecimento</h5>
                        <div ibox-tools></div>
                    </div>
                    <div class="ibox-content">
                        <div class="row">
                            <div class="col-xs-12 col-md-6">
                                <label class="col-xs-12 col-md-3 control-label"
                                    style="text-align: right; padding-top: 10px;">
                                    Período:</label>
                                <div class="input-group col-xs-12 col-md-9">
                                    <input date-range-picker class="form-control date-picker" type="text"
                                        ng-model="vm.date" options="vm.dateOptions"
                                        style="background-color: white !important;" readonly />
                                </div>
                            </div>
                            <consulta-padrao-modal 
                                ng-show="vm.isAdmin()"
                                tabledefinition="vm.consultaEmpresa" 
                                label="'Empresa:'"
                                idname="consultaEmpresa" placeholder="'Selecione uma empresa'">
                            </consulta-padrao-modal>      
                        </div>
                        <div class="row">
                            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                                <div class="form-group" ng-class="{ 'marginTop': !vm.isAdmin()}">
                                    <label class="col-xs-12 col-md-3 control-label"
                                        style="text-align: right; padding-top: 10px;">
                                        Status:
                                    </label>
                                    <div class="input-group col-xs-12 col-md-9">
                                        <ui-select name="status" ats-ui-select-validator validate-on="blur"
                                            ng-model="vm.status">
                                            <ui-select-match>
                                                <span>{{$select.selected.descricao}}</span>
                                            </ui-select-match>
                                            <ui-select-choices
                                                repeat="ex.id as ex in vm.enumStatus | propsFilter: {descricao: $select.search}">
                                                <div ng-bind-html="ex.descricao | highlight: $select.search"></div>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3 pull-right">
                                <button tooltip-placement="top" type='button' on-blur="vm.atualizaTela()"
                                    ng-click="vm.gridOptions.dataSource.refresh()"
                                    class="btn btn-labeled btn-primary pull-right">
                                    <span class="btn-label text-right">
                                    </span>
                                    <span class="pl-5 ">Consultar</span>
                                </button>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3 pull-right">
                                <button ng-show="vm.tipoOperacao != '1'" ng-click="vm.inativarAtivarTodasContas()"
                                    type='button'
                                    class="btn btn-labeled btn-primary pull-right buttons-ativar-desativar-celular">
                                    <span class="pl-5">Ativar/Inativar todos</span>
                                </button>
                            </div>
                        </div>
                        <div class="row"></div>
                        <hr>
                        <div ui-grid="vm.gridOptions" ng-style="{height: vm.gridOptions.getGridHeight()}" class="grid" style="width: 100%;"
                            ui-grid-pinning ui-grid-save-state ui-grid-pagination ui-grid-auto-resize
                            ui-grid-resize-columns ui-grid-grouping>
                        </div>
                        <br>
                        <div class="row">
                            <div class="row">
                                <div class="col-xs-12 col-md-12">
                                    <div class="pull-right mt-15">
                                        <button type="button" class="btn btn-danger" ng-disabled="vm.atualizando"
                                            ng-click="vm.reprovaProtocolo();">
                                            <span class="btn-label">
                                                <i class="fa fa-times-circle"></i>
                                            </span>
                                            Reprovar marcados
                                        </button>
                                        <button type="button" class="btn btn-success" ng-disabled="vm.atualizando"
                                            ng-click="vm.aprovaProtocolo();">
                                            <span class="btn-label">
                                                <i class="fa fa-check-circle"></i>
                                            </span>
                                            Aprovar marcados
                                        </button>
                                        <button type="button" ng-disabled="vm.desabilitarBtnRelatorio"
                                            class="btn btn-labeled btn-primary" ng-click="vm.abrirModalRelatorio(vm)">
                                            <i class="fa fa-file-pdf-o"></i> Exportar Relatório
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div id="exportable-xls">
                            <table style="font-family:Arial, Helvetica, sans-serif" ng-show="false" id="exportable"
                                class="table table-bordered" width="100%">
                                <thead>
                                    <tr>
                                        <th style="text-align: left" ng-repeat="option in vm.modalRelatorioOptions"
                                            ng-if="option.enabled && option.field">
                                            {{option.name}}
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr style="text-align: left" ng-repeat="item in vm.dadosRelatorio">
                                        <td ng-repeat="option in vm.modalRelatorioOptions"
                                            ng-if="option.enabled && option.field">
                                            {{option.pipe ? (option.pipe(item[option.field])) : (item[option.field])}}
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>