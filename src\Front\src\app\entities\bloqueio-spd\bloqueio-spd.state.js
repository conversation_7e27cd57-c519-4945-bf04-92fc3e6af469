(function () {
    'use strict';

    angular.module('bbcWeb.bloqueioSpd.state', []).config(routeConfig);

    function routeConfig($urlRouterProvider, $stateProvider) {
        $stateProvider.state('bloqueio-spd', {
            abstract: true,
            url: "/bloqueio-spd",
            templateUrl: "app/layout/content.html"
        }).state('bloqueio-spd.index', {
            url: '/index',
            templateUrl: 'app/entities/bloqueio-spd/bloqueio-spd.html'
        }).state('bloqueio-spd.bloqueio-spd-crud', {
            url: '/:link',
            templateUrl: 'app/entities/bloqueio-spd/bloqueio-spd-crud.html'
        });
    }
})();