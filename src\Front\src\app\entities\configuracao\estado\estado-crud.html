<div ng-controller="EstadoCrudController as vm">
    <form-header items="vm.headerItems" head="'Estado'"></form-header>
    <div class="wrapper wrapper-content animated fadeIn mb-0 pb-0">
        <div class="row">
            <div class="col-lg-12">
                <div class="ibox">
                    <div class="ibox-title">
                        <h5>{{vm.isNew() ? 'Novo' : 'Editar'}} estado</h5>
                        <div ibox-tools></div>
                    </div>
                    <div class="ibox-content wizard">
                        <form name="formEstado" role="form" novalidate ng-submit="vm.save()" show-validation>
                            <div form-wizard steps="2">
                                <div class="form-wizard">
                                    <ol class="row">
                                        <li ng-class="{'active':wizard.active(1)}" ng-click="wizard.go(1)" class="col-sm-12 col-md-12 col-lg-12">
                                            <h4>Principal</h4> 
                                        </li>
                                    </ol>
                                    <div ng-show="wizard.active(1)">
                                        <div class="form-horizontal">
                                            <hr/>
                                            <div class="col-sm-12">
                                                <div class="row">
                                                    <div class="col-sm-12 col-md-6 col-lg-6">
                                                        <div class="form-group"> <label class="col-sm-3 col-md-4 col-lg-3 control-label">Código:</label>
                                                            <div class="input-group col-sm-2 col-md-2 col-lg-2">
                                                                <input type="text" id="codigo" name="codigo" ng-model="vm.estado.IdEstado" class="form-control" disabled value="{{vm.isNew()}}" />
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-sm-12 col-md-6 col-lg-6">
                                                        <div class="form-group"> <label class="col-sm-3 col-md-4 col-lg-3 control-label"><span class="text-danger mr-5">*</span>Descrição:</label>
                                                            <div class="input-group col-sm-9 col-md-8 col-lg-9">
                                                                <input type="text" id="descricao" maxlength="100" placeholder="Informe uma descrição" name="descricao" ng-model="vm.estado.Nome" class="form-control" required/>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-sm-12 col-md-6 col-lg-6">
                                                        <div class="form-group"> <label class="col-sm-3 col-md-4 col-lg-3 control-label"><span class="text-danger mr-5">*</span>IBGE:</label>
                                                            <div class="input-group col-sm-9 col-md-8 col-lg-9">
                                                                <input type="number" id="ibge" placeholder="Informe o código de IBGE" name="ibge" ng-model="vm.estado.IBGE" class="form-control" required/>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-sm-12 col-md-6 col-lg-6">
                                                        <div class="form-group"> <label class="col-sm-3 col-md-4 col-lg-3 control-label"><span class="text-danger mr-5">*</span>Sigla:</label>
                                                            <div class="input-group col-sm-9 col-md-8 col-lg-9">
                                                                <input type="text" id="sigla" maxlength="2" placeholder="Informe o código de IBGE" name="sigla" ng-model="vm.estado.Sigla" class="form-control" required/>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-sm-12 col-md-6 col-lg-6">
                                                        <div class="form-group"> <label class="col-sm-3 col-md-4 col-lg-3 control-label"><span class="text-danger mr-5">*</span>País:</label>
                                                            <div class="input-group col-sm-9 col-md-8 col-lg-9">
                                                                <ui-select ng-model="vm.estado.IdPais" required>
                                                                    <ui-select-match placeholder="Selecione o País"> <span>{{$select.selected.Nome}}</span>
                                                                        <div id="clearPais" class="fa fa-close m-l-n ui-select-vas" ng-click="vm.estado.IdPais = undefined"></div>
                                                                    </ui-select-match>
                                                                    <ui-select-choices repeat="pais.IdPais as pais in vm.selectPaises | propsFilter: {Nome: $select.search}">
                                                                        <div ng-bind-html="pais.Nome | highlight: $select.search"></div>
                                                                    </ui-select-choices>
                                                                </ui-select>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <hr/>
                                            <div class="form-group">
                                                <div class="col-md-12 col-lg-12 text-right"> <button type="button" ui-sref='configuracao.estado' class="btn btn-labeled btn-default">
                                                        <span class="btn-label"><i class="fa fa-arrow-circle-left"></i></span>
                                                        Voltar
                                                        </button>
                                                    <button type="submit" ng-disabled="formEstado.$invalid" class="btn btn-labeled btn-success text-right">
                                                        <span class="btn-label"><i class="fa fa-check-circle"></i></span>
                                                        Salvar
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>