/**
 * Slovak translation for bootstrap-datetimepicker
 * <PERSON><PERSON> <<EMAIL>>
 * Fixes by <PERSON><PERSON> <<EMAIL>>
 */
;(function($){
	$.fn.datetimepicker.dates["sk"] = {
		days: ["Ned<PERSON><PERSON><PERSON>", "Pondelok", "Utorok", "Streda", "Štvrtok", "Piatok", "<PERSON>bot<PERSON>", "Nedeľa"],
		daysShort: ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Štv", "<PERSON><PERSON>", "Sob", "<PERSON>"],
		daysMin: ["Ne", "<PERSON>", "Ut", "<PERSON>", "<PERSON>t", "<PERSON>", "So", "Ne"],
		months: ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "August", "September", "<PERSON>t<PERSON><PERSON>", "November", "December"],
		monthsShort: ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Okt", "Nov", "Dec"],
		today: "Dnes",
		suffix: [],
		meridiem: [],
		weekStart: 1,
		format: "dd.mm.yyyy"
	};
}(j<PERSON><PERSON>y));
