(function () {
    'use strict';

    angular.module('bbcWeb').controller('PostoCrudController', PostoCrudController);

    PostoCrudController.$inject = ['toastr', '$rootScope', 'BaseService', '$state', '$stateParams', '$window', 'PERFIL_ADMINISTRADOR', '$scope', '$timeout', 'PersistentDataService', 'DefaultsService', '$uibModal'];

    function PostoCrudController(
        toastr, $rootScope,
        BaseService, $state,
        $stateParams, $window,
        PERFIL_ADMINISTRADOR,
        $scope, $timeout,
        PersistentDataService,
        DefaultsService,
        $uibModal
    ) {
        var vm = this;

        vm.posto = {};
        vm.posto.postoContatos = [];
        vm.posto.postoCombustiveis = [];
        vm.posto.postoCombustivelProduto = [];
        vm.posto.documentos = [];
        vm.posto.statusCadastro = 1;

        vm.menusPai = [];
        vm.tipo = 0;
        vm.tipoDocumento = 0;
        vm.fileDocumento = "";
        vm.aprovaDocumento = 1;
        vm.search = {};
        vm.search.acao = 0;
        vm.estados = [];
        vm.cidades = [];
        vm.estadosDisabled = true;
        vm.cidadesDisabled = true;
        vm.editarVinculo = true;
        vm.prazoPagamentoDisabled = true;
        vm.bancoSelecionadoOld = undefined;
        vm.bancoModificado = false;


        vm.tipoCombo = {
            data: [{ id: 0, descricao: 'Contato interno' }, { id: 1, descricao: 'Contato externo' }]
        };

        vm.statusCadastroCombo = {
            data: [{ id: 1, descricao: 'Aprovado' }, { id: 2, descricao: 'Reprovado' }, { id: 3, descricao: 'Bloqueado' }, { id: 0, descricao: 'Aguardando aprovação' }]
        };

        vm.tipoDocCombo = {
            data: [{ id: 0, descricao: 'Imagem' }, { id: 1, descricao: 'PDF' }]
        };

        vm.prazoPagamentoMDRCombo = [{}];
        vm.prazoMdrSelect = {};

        vm.headerItems = [{
            name: 'Cadastros'
        }, {
            name: 'Posto',
            link: 'posto.index'
        }, {
            name: $stateParams.link == 'novo' ? 'Novo' : 'Editar'
        }];

        var selfScope = PersistentDataService.get('PostoCrudController');

        $scope.$watch('vm.arquivo', function (nVal) {
            var tipo = vm.tipoDocumento == 0 ? "image/jpeg" : "application/pdf";
            if (!nVal) {
                return;
            }
            if (nVal.filetype == tipo) {
                if (nVal.filesize > 524288 && vm.tipoDocumento == 0) { // 524288 = 512kb 
                    vm.fileDocumento = "";
                    toastr.error('A imagem escolhida excede o tamanho de 512Kb!');
                } else {
                    vm.fileDocumento = nVal.base64;
                }
            } else {
                vm.arquivo.base64 = null;
                vm.arquivo.filename = null;
                vm.arquivo.filesize = null;
                vm.arquivo.filetype = null;
                vm.fileDocumento = "";
                toastr.error("Tipo de arquivo diferente do informado!");
            }
        });

        $scope.$watch('vm.consultaBanco.selectedValue', function () {

            var bancoId = vm.consultaBanco.selectedValue;

            if (!bancoId) {
                vm.prazoPagamentoDisabled = true;
                return;
            }

            if ((vm.bancoSelecionadoOld && (vm.consultaBanco.selectedValue !== vm.bancoSelecionadoOld)) || vm.bancoModificado) {
                vm.bancoModificado = true;
                vm.prazoPagamentoMDRCombo = undefined;
                vm.posto.prazoPagamento = '';
                vm.posto.MDRId = '';
                vm.prazoMdrSelect = undefined;
            }

            BaseService.get('MDRPrazos', 'ConsultarMDRPrazosPorBancoId', {
                lBancoId: bancoId
            }).then(function (response) {
                if (!response.success) {
                    toastr.error(response.message);
                } else if (response.data.length === 0) {
                    toastr.warning('O banco selecionado não possui MDR/Prazos ativos vinculados.');
                    vm.prazoPagamentoMDRCombo = undefined;
                    vm.posto.prazoPagamento = '';
                    vm.posto.MDRId = '';
                    vm.prazoMdrSelect = undefined;
                } else {
                    vm.prazoPagamentoMDRCombo = response.data;
                    vm.prazoPagamentoDisabled = false;
                }
            });
        })

        $scope.$watch('vm.prazoMdrSelect', function () {
            if (!vm.prazoMdrSelect) return;
            vm.posto.prazoPagamento = vm.prazoMdrSelect.prazo;
            vm.posto.MDRId = vm.prazoMdrSelect.id;
        })

        $scope.$watch('vm.posto.latitude', function (value) {
            if (vm.posto.latitude > 90 || vm.posto.latitude < -90) {
                toastr.error('A latitude informada está fora do intervalo permitido (-90 a 90)!');
                vm.posto.latitude = null;
            }
        });

        $scope.$watch('vm.posto.longitude', function (value) {
            if (vm.posto.longitude > 180 || vm.posto.longitude < -180) {
                toastr.error('A longitute informada está fora do intervalo permitido (-180 a 180)!');
                vm.posto.longitude = null;
            }
        });

        function carregarFuncoesIniciais() {
            carregarEstados();
        };

        if ($stateParams.link == 'novo') {
            vm.posto.id = 'Auto';
        }

        vm.isNew = function () {
            return $stateParams.link == 'novo';
        };

        vm.load = function () {
            carregarFuncoesIniciais();
        };

        vm.loadEdit = function (id) {
            BaseService.get('posto', 'ConsultarPorId', {
                idPosto: id
            }).then(function (response) {
                if (!response.success) {
                    toastr.error(response.message);
                } else {
                    carregarCidades(response.data.estadoId);
                    vm.posto = response.data;

                    vm.consultaBanco.selectedValue = response.data.bancoId;

                    if (response.data.bancoId)
                        vm.bancoSelecionadoOld = response.data.bancoId;

                    vm.consultaBanco.selectedText = response.data.banco.nome;

                    vm.consultaFilial.selectedValue = response.data.filialId;
                    vm.consultaFilial.selectedText = response.data.filial ? response.data.filial.nomeFantasia : null;

                    vm.posto.Cep = response.data.cep;
                    vm.posto.EstadoId = response.data.estadoId;
                    vm.posto.CidadeId = response.data.cidadeId;
                    vm.posto.Endereco = response.data.endereco;
                    vm.posto.Bairro = response.data.bairro;
                    vm.posto.EnderecoNumero = response.data.enderecoNumero;
                    vm.posto.Complemento = response.data.complemento;
                    vm.posto.MDRId = response.data.mdrId;
                    vm.posto.prazoPagamento = response.data.prazoPagamento;
                    vm.prazoMdrSelect = response.data.mdrPrazos;
                    vm.bancoCarregadoIsBbc = response.data.banco.isBbc > 0;

                    vm.posto.dataCadastro = dataAtualFormatada(vm.posto.dataCadastro);

                    if (vm.posto.documentos.length > 0)
                        for (var i = 0; i <= vm.posto.documentos.length - 1; i++)
                            vm.posto.documentos[i].dataCadastro = dataAtualFormatada(vm.posto.documentos[i].dataCadastro);
                }
            });
        };

        //Grid historico
        vm.gridOptions = {
            data: [],
            onRegisterApi: BaseService.dataGrid.defaultOnRegisterApi,
            dataSource: {
                autoBind: false,
                url: "AtualizacaoPrecoCombustivel/HistoricoSolicitacoesPendentes",
                params: function () {
                    return {
                        idPosto: vm.posto.id
                    };
                }
            },
            columnDefs: [
                {
                    displayName: 'Combustível',
                    field: 'nomeCombustivel',
                    minWidth: 120,
                    width: '*',
                    enableFiltering: false,
                    enableSorting: false
                },
                {
                    displayName: 'Unidade de medida',
                    field: 'unidadeMedida',
                    minWidth: 120,
                    width: '*',
                    cellTemplate: '<div class="ui-grid-cell-contents">\
                                        <p ng-show="row.entity.unidadeMedida === \'LT\'"> Litros (LT) </p>\
                                        <p ng-show="row.entity.unidadeMedida === \'MC\'"> Metro cubico (MC) </p>\
                                        <p ng-show="row.entity.unidadeMedida === \'UN\'"> Unidade (UN) </p>\
                                   </div>',
                    enableFiltering: false,
                    enableSorting: false
                },
                {
                    displayName: 'Valor bomba',
                    field: 'valorBomba',
                    minWidth: 120,
                    width: '*',
                    cellTemplate: '<div class="ui-grid-cell-contents">\
                                        <input type="text" ng-model="row.entity.valorBomba" readonly\
                                                class="no-borders" style="background: none;" ui-money-mask="3" />\
                                   </div>',
                    enableFiltering: false,
                    enableSorting: false
                },
                {
                    displayName: 'Valor BBC',
                    field: 'valorBBC',
                    minWidth: 120,
                    width: '*',
                    cellTemplate: '<div class="ui-grid-cell-contents">\
                                        <input type="text" ng-model="row.entity.valorBBC" readonly\
                                                class="no-borders" style="background: none;" ui-money-mask="3" />\
                                   </div>',
                    enableFiltering: false,
                    enableSorting: false
                },
                {
                    displayName: 'Data alteração',
                    field: 'dataCadastro',
                    minWidth: 120,
                    width: '*',
                    enableFiltering: false,
                    enableSorting: false
                },
                {
                    displayName: 'Usuário alteração',
                    field: 'usuarioCadastro',
                    minWidth: 120,
                    width: '*',
                    enableFiltering: false,
                    enableSorting: false
                }
            ]
        };

        function carregarModais() {
            vm.consultaBanco = {
                columnDefs: [{
                    name: 'Código',
                    field: 'id',
                    width: 80,
                    primaryKey: true
                }, {
                    name: 'Nome',
                    field: 'nome',
                    width: '*',
                    minWidth: 150
                }],
                desiredValue: 'id',
                desiredText: 'nome',
                url: 'Banco/ConsultarGridBancoComMDRCombo',
                paramsMethod: function () {
                    return {}
                },
            };
            vm.consultaMDR = {
                columnDefs: [{
                    name: 'Código',
                    field: 'id',
                    width: 80,
                    primaryKey: true
                }, {
                    name: 'Descrição',
                    field: 'descricao',
                    width: '*',
                    minWidth: 150
                }],
                desiredValue: 'id',
                desiredText: 'descricao',
                url: 'MDRPrazos/ConsultarGridMDRPrazos',
                paramsMethod: function () {
                    return {}
                },
            };
            vm.consultaFilial = {
                columnDefs: [{
                    name: 'Código',
                    field: 'id',
                    width: 80,
                    primaryKey: true
                }, {
                    name: 'Nome',
                    field: 'nomeFantasia',
                    width: '*',
                    minWidth: 150
                }, {
                    name: 'Razão social',
                    field: 'razaoSocial',
                    width: '*',
                    minWidth: 150
                }, {
                    name: 'CNPJ',
                    field: 'cnpj',
                    width: '*',
                    minWidth: 150
                }],
                desiredValue: 'id',
                desiredText: 'nomeFantasia',
                url: 'Filial/ConsultarGridFilial',
                paramsMethod: function () {
                    return {}
                },
            };
            vm.consultaCombustivel = {
                columnDefs: [{
                    name: 'Código',
                    field: 'id',
                    width: 80,
                    type: 'number',
                    primaryKey: true
                }, {
                    name: 'Nome',
                    field: 'nome',
                    width: '*',
                    minWidth: 250
                }, {
                    name: 'Unidade de medida',
                    field: 'unidadeMedida',
                    enableGrouping: true,
                    width: 145
                }],
                desiredValue: 'id',
                desiredText: 'nome',
                url: 'Combustivel/ConsultarGridCombustivel',
                paramsMethod: function () {
                    return {}
                }
            };
        };

        vm.buscarEndereco = function (cep) {
            if (cep) {
                limparEndereco();
                BaseService.getEndereco(cep).then(function (response) {
                    if (response && response.erro) {
                        toastr.warning("Endereço não localizado.");
                    }

                    carregarEstados();

                    $timeout(function () {
                        var estado = _.find(vm.estados, ['sigla', response.uf]);

                        if (angular.isDefined(estado))

                            vm.posto.EstadoId = estado.id;
                        carregarCidades(vm.posto.EstadoId);
                        $timeout(function () {
                            var cidade = _.find(vm.cidades, ['descricao', response.localidade]);
                            if (angular.isDefined(cidade))
                                vm.posto.CidadeId = cidade.id;
                        }, 1500);
                    }, 1500);

                    $timeout(function () {
                        if (angular.isDefined(response.logradouro))
                            vm.posto.Endereco = response.logradouro;
                    }, 1500);

                    $timeout(function () {
                        if (angular.isDefined(response.bairro))
                            vm.posto.Bairro = response.bairro;
                    }, 1500);
                });
            }
        };

        function limparEndereco() {
            vm.posto.EstadoId = null;
            vm.posto.CidadeId = null;
            vm.estados = null;
            vm.cidades = null;
            vm.posto.Endereco = null;
            vm.posto.Bairro = null;
            vm.posto.EnderecoNumero = null;
        };

        vm.estadoChange = function (estadoId) {
            vm.cidades = null;
            vm.cidadesDisabled = true;
            vm.posto.CidadeId = null;
            carregarCidades(estadoId);
        }

        function carregarEstados() {
            BaseService.get("Endereco", "ConsultarEstados")
                .then(function (response) {
                    if (response.success) {
                        vm.estados = response.data;
                        vm.estadosDisabled = false;
                    }
                });
        };

        function carregarCidades(EstadoId) {
            BaseService.get("Endereco", "ConsultarCidadesPorEstado", {
                estadoId: EstadoId
            }).then(function (response) {
                if (response.success) {
                    vm.cidades = response.data;
                    vm.cidadesDisabled = false;
                }
            });
        };

        vm.save = function (form) {

            var motivoNulo = 0;

            for (var i = 0; i < vm.posto.documentos.length; i++) {
                if (vm.posto.documentos[i].status == 2) {
                    vm.posto.documentos[i].status = 1
                }
                vm.posto.documentos[i].status == true || vm.posto.documentos[i].status == 1 ? vm.posto.documentos[i].status = 1 : vm.posto.documentos[i].status = 0;
                if (vm.posto.documentos[i].status != 1 && (vm.posto.documentos[i].motivo == "" || vm.posto.documentos[i].motivo == null)) {
                    motivoNulo = 1
                }
            }

            if (motivoNulo == 1) {
                toastr.error('O campo motivo, da aba documentos, deve ser preenchido corretamente com seu respectivo valor');
                return;
            } else if (!form.$valid) {
                toastr.error('Todos os campos obrigatórios devem ser preenchidos corretamente com seus respectivos valores');
                return;
            }

            vm.posto.bancoId = vm.consultaBanco.selectedValue;
            vm.posto.filialId = vm.consultaFilial.selectedValue == 0 ? null : vm.consultaFilial.selectedValue;
            vm.posto.bloqueado = vm.posto.bloqueado == true ? 1 : 0;

            if (vm.isSaving === true) {
                return;
            }

            if (vm.posto.id == "Auto") {
                vm.posto.novo = 1;
                vm.posto.id = "0";
            } else {
                vm.posto.novo = 0;
            }

            vm.isSaving = true;

            BaseService.post('Posto', 'Salvar', vm.posto).then(function (response) {
                vm.isSaving = false;
                if (response.success) {
                    toastr.success(response.message);
                    $state.go('posto.index');
                } else
                    toastr.error(response.message);
            });
        };

        vm.limparMotivoBloqueio = function () {
            if (vm.posto.bloqueado == false) {
                vm.posto.motivoBloqueio = "";
            }
        }

        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.administrador == true;
        };

        function init() {
            carregarModais();
            carregarFuncoesIniciais();
        };

        //Add/Remover contatos
        vm.adicionarContato = function () {
            var permiteAdicionar = false;
            var contato = {
                tipo: vm.tipo,
                nome: vm.nome,
                telefone: vm.telefone,
                telefone2: vm.telefone2,
                celular: vm.celular,
                email: vm.email
            }

            if (!contato.nome || !contato.telefone || !contato.celular || !contato.email)
                return toastr.error("Por favor, informe nome, um telefone, um celular, e-mail.");

            var objetosValidos = _.filter(vm.posto.postoContatos, function (v) {
                return v.nome === contato.nome || v.telefone === contato.telefone || v.celular === contato.celular || v.email === contato.email;
            });

            if (objetosValidos.length > 0) {
                toastr.error("Este contato já foi adicionado.");
                return;
            }

            if (contato != undefined && contato != "" && contato) {
                permiteAdicionar = true;
            }

            if (permiteAdicionar) {
                vm.posto.postoContatos.push(contato);
                vm.clearContatoAdd();
            } else
                toastr.error("Por favor, informe nome, um telefone, um celular, e-mail.");
        };

        vm.clearContatoAdd = function () {
            vm.tipo = 1;
            vm.nome = "";
            vm.telefone = "";
            vm.telefone2 = "";
            vm.celular = "";
            vm.email = "";
        };

        vm.removerContato = function (contato) {
            for (var i = 0; i < vm.posto.postoContatos.length; i++) {
                if (!vm.isNew()) {
                    if (vm.posto.postoContatos[i].id == contato.id) {
                        var index = vm.posto.postoContatos.indexOf((vm.posto.postoContatos[i]));
                        vm.posto.postoContatos.splice(index, 1)
                    }
                } else {
                    if (vm.posto.postoContatos[i] == contato) {
                        var index = vm.posto.postoContatos.indexOf((vm.posto.postoContatos[i]));
                        vm.posto.postoContatos.splice(index, 1)
                    }
                }
            }

            if (vm.posto.postoContatos.length < 1) {
                vm.tipo = "";
                vm.nome = "";
                vm.telefone = "";
                vm.telefone2 = "";
                vm.celular = "";
                vm.email = "";
            }
        };

        //Remover banco
        vm.onClearBanco = function () {
            vm.posto.prazoPagamento = '';
            vm.posto.MDRId = '';
            vm.prazoMdrSelect = undefined;
        }

        //Add/Remover vinculos de combustivel

        vm.adicionarCombustivelProduto = function () {
            var permiteAdicionar = false;

            if (!vm.consultaCombustivel.selectedValue)
                return toastr.error("Nenhum combustível foi selecionado.");

            if (!vm.nfe)
                return toastr.error("Código de produto não informado.");

            var objetosValidos = _.filter(vm.posto.postoCombustivelProduto, function (v) {
                return v.id === vm.consultaCombustivel.selectedValue;
            });

            if (vm.consultaCombustivel.selectedValue != undefined && vm.consultaCombustivel.selectedValue != "" && vm.nfe) {
                permiteAdicionar = true;
            }

            if (permiteAdicionar) {
                var combustivelProduto = {
                    id: vm.posto.postoCombustivelProduto.id > 0 ? vm.posto.postoCombustivelProduto.id : 0,
                    nomeCombustivel: vm.consultaCombustivel.selectedText,
                    combustivelId: vm.consultaCombustivel.selectedValue,
                    codigoProduto: vm.nfe,
                    postoId: vm.posto.id == "Auto" ? 0 : vm.posto.id
                }

                for (var i = 0; i < vm.posto.postoCombustivelProduto.length; i++) {
                    if (vm.posto.postoCombustivelProduto[i].codigoProduto == combustivelProduto.codigoProduto) {
                        return toastr.error("Código de produto já informado para produto!");
                    }
                    vm.clearConsultaCombustivel();
                }

                if (combustivelProduto.id >= 0 && vm.consultaCombustivel.selectedEntity == null) {
                    vm.removerCombustivelProduto(combustivelProduto);
                }

                vm.posto.postoCombustivelProduto.push(combustivelProduto);

                vm.clearConsultaCombustivel();
            } else
                toastr.error("Por favor, informe o combustível/código produto.");
        };

        vm.editarCombustivelProduto = function (combustivelProduto) {

            for (var i = 0; i < vm.posto.postoCombustivelProduto.length; i++) {
                if (vm.posto.postoCombustivelProduto[i].id == combustivelProduto.id) {
                    vm.consultaCombustivel.selectedText = combustivelProduto.nomeCombustivel;
                    vm.consultaCombustivel.selectedValue = combustivelProduto.combustivelId;
                    vm.nfe = combustivelProduto.codigoProduto;
                    for (var i = 0; i < vm.posto.postoCombustivelProduto.length; i++) {
                        if (vm.posto.postoCombustivelProduto[i].id == combustivelProduto.id) {
                            vm.consultaCombustivel.selectedText = combustivelProduto.nomeCombustivel;
                            vm.consultaCombustivel.selectedValue = combustivelProduto.combustivelId;
                            vm.nfe = combustivelProduto.codigoProduto;
                            vm.posto.postoCombustivelProduto.id = combustivelProduto.id;
                            vm.posicaoAntiga = i;
                            vm.id = combustivelProduto.id;

                        }
                    }
                }
            }

            if (vm.posto.postoCombustiveis.length < 1) {
                vm.consultaCombustivel.selectedEntity = undefined;
                vm.consultaCombustivel.selectedValue = undefined;
                vm.consultaCombustivel.selectedText = undefined;
            }
        };

        vm.clearConsultaCombustivel = function () {
            vm.consultaCombustivel.selectedEntity = undefined;
            vm.consultaCombustivel.selectedValue = undefined;
            vm.consultaCombustivel.selectedEntity = null;
            vm.consultaCombustivel.selectedValue = null;
            vm.consultaCombustivel.selectedText = "Combustível";
            vm.nfe = "";
            vm.posto.postoCombustivelProduto.id = null;
            vm.posicaoAntiga = null;
        };

        vm.removerCombustivelProduto = function (combustivelProduto) {
            for (var i = 0; i < vm.posto.postoCombustivelProduto.length; i++) {
                if (vm.posto.postoCombustivelProduto[i].id == combustivelProduto.id
                    && vm.posto.postoCombustivelProduto[i].combustivelId == combustivelProduto.combustivelId
                    && vm.posto.postoCombustivelProduto[i].nfe == combustivelProduto.nfe) {
                    var index = vm.posto.postoCombustivelProduto.indexOf((vm.posto.postoCombustivelProduto[i]));
                    vm.posto.postoCombustivelProduto.splice(index, 1)
                } else {
                    if (vm.posto.postoCombustivelProduto[i] == combustivelProduto) {
                        var index = vm.posto.postoCombustivelProduto.indexOf((vm.posto.postoCombustivelProduto[i]));
                        vm.posto.postoCombustivelProduto.splice(index, 1)
                    }
                }
            }

            if (vm.posto.postoCombustivelProduto.length < 1) {
                vm.consultaCombustivel.selectedEntity = undefined;
                vm.consultaCombustivel.selectedValue = undefined;
                vm.consultaCombustivel.selectedText = undefined;
            }
        };

        vm.carregarDadosDaConta = function (cnpj) {
            if (!cnpj || cnpj.length < 13) return;

            vm.carregandoDadosDaConta = true;
            vm.bancoCarregadoIsBbc = false;
            vm.limparDadosBancarios();

            BaseService.get("Conta", "ConsultarContaAgencia", { cnpj: cnpj })
                .then(function (response) {
                    if (!response || !response.success) {
                        toastr.warning("Não foi possível trazer os dados dessa conta automaticamente para o cadastro. " + response.message);
                        vm.carregandoDadosDaConta = false
                        return;
                    }
                    if (response.success) {
                        toastr.success("Dados da conta bancária carregados automaticamente no cadastro.");
                        vm.posto.agencia = response.data.agencia;
                        vm.posto.agenciaDV = response.data.digitoAgencia;
                        vm.posto.contaCorrente = response.data.conta;
                        vm.posto.contaCorrenteDV = response.data.digitoConta;
                        vm.consultaBanco.selectedValue = response.data.bancoId;
                        vm.consultaBanco.selectedText = response.data.bancoNome;
                        vm.bancoCarregadoIsBbc = response.data.bancoIsBbc > 0;
                    }
                    vm.carregandoDadosDaConta = false
                })
        }

        //Limpa os dados bancários caso o cnpj for mudado
        vm.limparDadosBancarios = function () {
            vm.posto.agencia = undefined;
            vm.posto.agenciaDV = undefined;
            vm.posto.contaCorrente = undefined;
            vm.posto.contaCorrenteDV = undefined;
            vm.bancoCarregadoIsBbc = false;
            vm.posto.prazoPagamento = undefined;
            vm.posto.MDRId = undefined;
            vm.prazoMdrSelect = undefined;
            vm.consultaBanco.selectedValue = '';
            vm.consultaBanco.selectedText = '';
        };

        function dataAtualFormatada(data) {
            var data = new Date(data)
            var dia = data.getDate().toString()
            var diaF = (dia.length == 1) ? '0' + dia : dia
            var mes = (data.getMonth() + 1).toString() //+1 pois no getMonth Janeiro começa com zero.
            var mesF = (mes.length == 1) ? '0' + mes : mes
            var anoF = data.getFullYear();
            return diaF + "/" + mesF + "/" + anoF;
        }

        //Add/Remover documentos        
        vm.aprovarDocumento = function (documento) {
            for (var i = 0; i < vm.posto.documentos.length; i++) {
                if (vm.posto.documentos[i] == documento) {
                    vm.posto.documentos[i].status = 1;
                }
            }
        }

        vm.recusarDocumento = function (documento) {
            for (var i = 0; i < vm.posto.documentos.length; i++) {
                if (vm.posto.documentos[i] == documento) {
                    vm.posto.documentos[i].status = 0;
                }
            }
        }

        vm.baixarDocumento = function (documento) {
            var downloadArchive = document.createElement("a");
            if (documento.tipo == 0) {
                downloadArchive.href = "data:image/png;base64," + documento.foto;
                downloadArchive.download = documento.descricao + ".jpeg";
            } else {
                downloadArchive.href = "data:application/pdf;base64," + documento.foto;
                downloadArchive.download = documento.descricao + ".pdf";
            }

            downloadArchive.click();
        };

        vm.onClickAvancar = function (wizard) {
            wizard.go(wizard.getActivePosition() + 1);
        };

        vm.onClickVoltar = function (wizard) {

            var ativoIndex = wizard.getActivePosition();
            if (ativoIndex == 1)
                $state.go('posto.index');

            wizard.go(ativoIndex - 1);
        };

        vm.limparMotivo = function (doc) {
            if (doc.status == true) {
                doc.motivo = '';
            }
        }

        init();

        $scope.$on('$stateChangeStart', function (arg1, toState) {
            if (toState.name === 'posto.index')
                PersistentDataService.remove('PostoCrudController');
            else
                PersistentDataService.store('PostoCrudController', vm, "Cadastro - Posto", null, "posto.posto-crud", vm.posto.id);
        });

        if (angular.isDefined(selfScope))
            angular.extend(vm, selfScope.data);
        else if (!vm.isNew())
            vm.loadEdit($stateParams.link);
        else {
            //vm.posto = 0;
            vm.modulos = 1;

        }

        $timeout(function () {
            PersistentDataService.remove('PostoController');
        }, 15);
    }
})();