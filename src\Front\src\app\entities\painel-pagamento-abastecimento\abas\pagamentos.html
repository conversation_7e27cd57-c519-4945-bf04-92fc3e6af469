<div class="form-horizontal">
    <hr-label dark="true" title="'Pagamentos'"></hr-label><br><br>
    <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
        <div class="row">
            <div class="col-xs-12 col-md-4">
                <div class="form-group">
                    <label class="col-xs-12 col-md-3 control-label" style="text-align: right; padding-top: 10px;">Período:</label>
                    <div class="input-group col-xs-12 col-md-9">
                        <input date-range-picker class="form-control date-picker" 
                        ui-date-mask="DD/MM/YYYY - DD/MM/YYYY" 
                        type="text" 
                        ng-model="vm.dataPagamento" 
                        options="vm.dateOptions" />
                    </div>
                </div>
            </div>
            <consulta-padrao-modal 
                tabledefinition="vm.consultaEmpresa" 
                label="'Empresa:'" idname="consultaEmpresa" 
                placeholder="'Selecione uma empresa'" 
                directivesizes="'col-xs-12 col-sm-12 col-md-4 col-lg-4'"
                labelsize="'col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label'">
            </consulta-padrao-modal> 
            <div class="col-xs-12 col-sm-12 col-md-4 col-lg-4">
                <div class="form-group">
                    <label class="col-xs-12 col-md-3 control-label" style="text-align: right; padding-top: 10px;">
                        Tipo de operação:
                    </label>
                    <div class="input-group col-xs-12 col-md-9">
                        <ui-select name="TipoOperacao" 
                            ng-model="vm.tipoOperacao" ng-change="vm.AtualizaComboStatus(); vm.setarStatusTodos();">
                            <ui-select-match>
                                <span>{{$select.selected.descricao}}</span>
                            </ui-select-match>
                            <ui-select-choices
                                repeat="ex.id as ex in vm.enumTipoOperacao | propsFilter: {descricao: $select.search}">
                                <div ng-bind-html="ex.descricao | highlight: $select.search"></div>
                            </ui-select-choices>
                        </ui-select>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">           
            <div class="col-xs-12 col-sm-12 col-md-4 col-lg-4" >
                <div class="form-group">
                    <label class="col-xs-12 col-md-3 control-label" style="text-align: right; padding-top: 10px;">
                        Status:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-9 col-lg-9">
                        <ui-select name="status" ats-ui-select-validator validate-on="blur"
                            ng-model="vm.status" ng-disabled="vm.tipoOperacao == 0" ng-change="vm.atualizaTela()">
                            <ui-select-match>
                                <span>{{$select.selected.descricao}}</span>
                            </ui-select-match>
                            <ui-select-choices
                                repeat="ex.id as ex in vm.enumStatus | propsFilter: {descricao: $select.search}">
                                <div ng-bind-html="ex.descricao | highlight: $select.search"></div>
                            </ui-select-choices>
                        </ui-select>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-3 pull-right">
                <button tooltip-placement="top"
                    type='button' ng-click="vm.atualizaTela()"
                    class="btn btn-labeled btn-primary pull-right">
                    <span class="btn-label text-right">
                    </span>
                    <span class="pl-5 ">Consultar</span>
                </button>
            </div>
        </div>
        <div class="row">
            <div class="col-md-3 pull-right">
                <button ng-show="vm.tipoOperacao != '1'" ng-click="vm.inativarAtivarTodasContas()" type='button' class="btn btn-labeled btn-primary pull-right buttons-ativar-desativar-celular">
                    <span class="pl-5">Ativar/Inativar todos</span>
                </button> 
            </div>
        </div>
        <div class="row"></div>
        <hr>
        <div ui-grid="vm.gridOptions" ng-style="{height: vm.gridOptions.getGridHeight()}" class="grid" style="width: 100%;" ui-grid-pinning ui-grid-save-state
            ui-grid-pagination ui-grid-auto-resize ui-grid-resize-columns ui-grid-grouping>
        </div>
        <br>
        <div class="row">
            <div class="col-xs-12 col-md-12 pull-right">
                <button type="button" class="btn btn-success pull-right" ng-disabled="vm.tipoOperacao != 2 || vm.status != 7 && vm.status != 2" ng-click="vm.aprovaPagamento();" >
                    <span class="btn-label">
                        <i class="fa fa-check-circle"></i>
                    </span>
                    Aprovar marcados
                </button>
                <button type="button" class="btn btn-danger pull-right" ng-disabled="vm.tipoOperacao != 2 || vm.status != 7 && vm.status != 2" ng-click="vm.reprovaPagamento();">
                    <span class="btn-label">
                        <i class="fa fa-times-circle"></i>
                    </span>
                    Reprovar marcados
                </button>
                <button type="button" class="btn btn-primary pull-right" ng-disabled="vm.tipoOperacao != 2 || vm.status != 6 && vm.status != 1 && vm.status != 3"
                        ng-click="vm.reenviarPagamentos()"><i class="fa fa-refresh"></i>
                        Reenviar pagamento
                </button>
                <button type="button"
                        class="btn btn-labeled btn-primary pull-right"
                        ng-click="vm.abrirModalRelatorio('gridOptions', 'dataPagamento', vm)">
                    <i class="fa fa-file-pdf-o"></i> Exportar Relatório
                </button>
            </div>
            <div id="exportable-xls">
                <table style="font-family:Arial, Helvetica, sans-serif" ng-show="false" id="exportable"
                       class="table table-bordered" width="100%">
                    <thead>
                    <tr>
                        <th style="text-align: left"
                            ng-repeat="option in vm.modalRelatorioOptions"
                            ng-if="option.enabled && option.field">
                            {{option.name}}
                        </th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr style="text-align: left"
                        ng-repeat="item in vm.dadosRelatorio">
                        <td ng-repeat="option in vm.modalRelatorioOptions"
                            ng-if="option.enabled && option.field">
                            {{option.pipe ? (option.pipe(item[option.field])) : (item[option.field])}}
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>