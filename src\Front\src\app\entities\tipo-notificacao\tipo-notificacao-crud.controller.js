(function () {
    'use strict';

    angular.module('bbcWeb').controller('TipoNotificacaoCrudController', TipoNotificacaoCrudController);

    TipoNotificacaoCrudController.$inject = [
        '$scope',
        'toastr',
        'BaseService',
        'Empresa',
        'Filial',
        '$state',
        '$stateParams',
        '$window',
        'PERFIL_ADMINISTRADOR',
        'PERFIL_EMPRESA',
        '$rootScope',
        'PersistentDataService',
        '$timeout'
    ];

    function TipoNotificacaoCrudController(
        $scope,
        toastr,
        BaseService,
        Empresa,
        Filial,
        $state,
        $stateParams,
        $window,
        PERFIL_ADMINISTRADOR,
        PERFIL_EMPRESA,
        $rootScope,
        PersistentDataService,
        $timeout) {

        //Inicialização dos objetos e arrays
        var vm = this;
        vm.headerItems = [
            { name: '<PERSON><PERSON><PERSON><PERSON>' },
            { name: 'Tipo de Notificação', link: 'gestao-logistica-tipo-notificacao.tiponotificacao' },
            { name: $stateParams.link == 'novo' ? 'Novo' : 'Editar' }
        ];

        vm.tipoNotificacao = {};
        vm.empresas = [];
        vm.filiais = [];
        vm.perfilUsuarioLogado = $rootScope.usuarioLogado.perfil;

        // Variáveis com dados das controller e métodos da api
        var controller = 'TipoNotificacaoAts';
        var method = 'ConsultarPorId';
        var controllerEmpresaAts = 'EmpresaAts';
        var methodTipoEmpresaAts = 'Consultar';
        var controllerFilialAts = 'FilialAts';
        var methodTipoFilialAts = 'ConsultarPorEmpresa';

        vm.loadEdit = function (id) {
            if (vm.isProfileAdmin())
                BaseService.get('TipoNotificacaoAts', method, { idTipoNotificacao: id }).then(function (response) {
                    vm.tipoNotificacao = angular.fromJson(response.data);
                    Empresa.get(controllerEmpresaAts, methodTipoEmpresaAts).then(function (response) {
                        vm.empresas = angular.fromJson(response.data);
                        vm.empresaSelected = vm.tipoNotificacao.IdEmpresa;

                        Filial.get(controllerFilialAts, methodTipoFilialAts, { idEmpresa: vm.empresaSelected }).then(function (response) {
                            vm.filiais = angular.fromJson(response.data);
                            vm.filialSelected = vm.tipoNotificacao.IdFilial;
                        });

                    });
                });
            else if (vm.isProfileEmpresa())
                BaseService.get('TipoNotificacaoAts', method, { idTipoNotificacao: id }).then(function (response) {
                    vm.tipoNotificacao = angular.fromJson(response.data);

                    Filial.get(controllerFilialAts, methodTipoFilialAts).then(function (response) {
                        vm.filiais = angular.fromJson(response.data);
                        vm.filialSelected = vm.tipoNotificacao.IdFilial;
                    });
                });
            else
                BaseService.get('TipoNotificacaoAts', method, { idTipoNotificacao: id }).then(function (response) {
                    vm.tipoNotificacao = angular.fromJson(response.data);
                });
        };

        vm.load = function () {
            if (vm.isProfileAdmin()) {
                Empresa.get(controllerEmpresaAts, methodTipoEmpresaAts).then(function (response) {
                    vm.empresas = angular.fromJson(response.data);
                    //Criar condição para perfil que não é administrador para não buscar filial
                    //Colocar angular is defined para todas propriedades dos objetos que estão aqui
                    vm.empresaSelected = vm.tipoNotificacao.IdEmpresa;
                });
            } else if (vm.isProfileEmpresa()) {
                Filial.get(controllerFilialAts, methodTipoFilialAts).then(function (response) {
                    vm.filiais = angular.fromJson(response.data);
                });
            }
        };

        //Métodos públicos para o escopo vm
        vm.updateFiliais = function () {
            var controller = 'FilialAts';
            var method = 'ConsultarPorEmpresa';

            vm.filialSelected = vm.filiais[0];

            if (angular.isDefined(vm.empresaSelected)) {
                BaseService.get(controller, method, { idEmpresa: vm.empresaSelected }).then(function (response) {
                    var data = angular.fromJson(response.data);
                    vm.filiais = data;
                });
            } else
                vm.filiais = [];
        };

        if ($stateParams.link == 'novo')
            vm.tipoNotificacao.IdTipoNotificacao = 'Auto';
        vm.isNew = function () {
            return $stateParams.link == 'novo';
        };

        vm.isProfileAdmin = function () {
            var perfilUsuarioLogado = $window.localStorage.getItem('perfil');
            if (perfilUsuarioLogado == PERFIL_ADMINISTRADOR) {
                return true;
            }
        };

        vm.isProfileEmpresa = function () {
            return (vm.perfilUsuarioLogado == PERFIL_EMPRESA);
        };

        vm.isVisibled = function (block) {
            if (block == 'blockEmpresa') {
                if (vm.perfilUsuarioLogado == PERFIL_ADMINISTRADOR)
                    return true;
            } else if (block == 'blockFilial') {
                if (vm.perfilUsuarioLogado == PERFIL_ADMINISTRADOR || vm.perfilUsuarioLogado == PERFIL_EMPRESA)
                    return true;
            }
        };

        vm.clear = function ($event) {
            $event.stopPropagation();
            if ($event.currentTarget.id == 'clearEmpresa') {
                vm.empresaSelected = undefined;
                if (angular.isUndefined(vm.empresaSelected) && angular.isDefined(vm.filialSelected)) {
                    vm.updateFiliais();
                } else if (angular.isUndefined(vm.empresaSelected) && angular.isUndefined(vm.filialSelected)) {
                    vm.updateFiliais();
                }
            } else if ($event.currentTarget.id == 'clearFilial') {
                vm.filialSelected = undefined;
            }
        };

        //Salvar e atualizar
        vm.save = function () {
            var model = {};
            model.IdTipoNotificacao = angular.isDefined(vm.tipoNotificacao.IdTipoNotificacao) ? vm.tipoNotificacao.IdTipoNotificacao : null;
            model.Descricao = angular.isDefined(vm.tipoNotificacao.Descricao) ? vm.tipoNotificacao.Descricao : null;
            model.IdEmpresa = angular.isDefined(vm.empresas) ? vm.empresaSelected : null;
            model.IdFilial = angular.isDefined(vm.filiais) ? vm.filialSelected : null;

            BaseService.post('TipoNotificacaoAts', $stateParams.link === 'novo' ? 'Cadastrar' : 'Editar', model).then(function (response) {
                if (response.success) {
                    toastr.success(response.message);
                    $state.go('gestao-logistica-tipo-notificacao.tiponotificacao');
                } else
                    toastr.error(response.message);
                if ($stateParams.link === 'novo')
                    model.IdTipoNotificacao = null;
            });
        };


        // DO NOT TOUCH!!
        $scope.$on('$stateChangeStart', function (_, toState) {
            if (toState.name === 'gestao-logistica-tipo-notificacao.tiponotificacao')
                PersistentDataService.remove('TipoNotificacaoCrudController');
            else PersistentDataService.store('TipoNotificacaoCrudController', vm, "Cadastro - Tipo de notificação", null, "gestao-logistica-tipo-notificacao.tiponotificacao-crud", vm.tipoNotificacao.IdTipoNotificacao);
        });
        var selfScope = PersistentDataService.get('TipoNotificacaoCrudController');
        if (angular.isDefined(selfScope)) {
            angular.extend(vm, selfScope.data);
        } else
            if (!vm.isNew()) vm.loadEdit($stateParams.link);
            else vm.load();

        $timeout(function () { PersistentDataService.remove('TipoNotificacaoController'); }, 15);
        // End
    }
})();