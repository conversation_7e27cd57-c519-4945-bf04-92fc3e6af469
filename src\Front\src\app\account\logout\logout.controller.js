(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('LogoutController', LogoutController);

    LogoutController.$inject = ['$window', '$rootScope', '$uibModalInstance', '$timeout', '$state'];

    function LogoutController($window, $rootScope, $uibModalInstance, $timeout, $state) {
        $timeout(function(){
            $rootScope.usuarioLogado = {}
            $rootScope.menusUsuarioLogado = {}
            $window.localStorage.clear();
            $window.localStorage.setItem('SessionKey', 'invalid-key-value');
            $uibModalInstance.close('ok');
            $state.go('login');
        }, 500)
    }
})();