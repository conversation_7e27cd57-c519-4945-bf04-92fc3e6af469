<div ng-controller="AvaliacaoEmpresaValidarController as vm">
    <form-header items="vm.headerItems" head="'Avaliação de empresa'" state="empresa"></form-header>
    <div class="animated fadeIn filter-position mb-0 pb-0">
        <div class="row">
            <div class="col-lg-12 mb-30">
                <div class="ibox">
                    <div class="ibox-title">
                        <h5><PERSON><PERSON> da Empresa</h5>
                    </div>
                    <div class="ibox-content">
                        <div class="row">
                            <div class="col-md-12">
                                <div form-wizard steps="3">
                                    <div class="form-wizard">
                                        <ol class="row">
                                            <li ng-class="{'active': wizard.active(1)}" class="col-md-4" ng-click="wizard.go(1)">
                                                <h4>Dados da empresa</h4>
                                            </li>
                                            <li ng-class="{'active': wizard.active(2)}" class="col-md-4" ng-click="wizard.go(2)">
                                                    <h4>Representante legal</h4>
                                           </li>
                                            <li ng-class="{'active': wizard.active(3)}" class="col-md-4" ng-click="wizard.go(3)">
                                                <h4>Parecer</h4>
                                            </li>
                                        </ol> 
                                    </div>
                                </div>
                                <hr class="mt-15" />
                                <div>
                                    <div ng-show="wizard.active(1)">
                                        <label class="text-danger">EMPRESA {{vm.empresa.descricaoStatusCadastro | uppercase}}</label>
                                        <div ng-include="'app/entities/empresa/avaliacao/abas-avaliacao-empresa-validar/dados-empresa/dados-empresa.html'" class="form-horizontal"></div>
                                    </div>
                                    <div ng-show="wizard.active(2)">
                                        <div ng-include="'app/entities/empresa/avaliacao/abas-avaliacao-empresa-validar/representanteLegal/representanteLegal.html'" class="form-horizontal"></div>
                                    </div>
                                    <div ng-show="wizard.active(3)">
                                        <div ng-include="'app/entities/empresa/avaliacao/abas-avaliacao-empresa-validar/parecer/parecer.html'" class="form-horizontal"></div>
                                    </div>
                                </div>
                                <!-- <hr class="mt-15" /> -->
                                <div class="row">
                                    <div class="form-group">
                                        <div class="col-md-12 text-right">
                                            <hr />
                                            <button type="button" ng-disabled="vm.saving" ng-click="wizard.getActivePosition() == 1 ? $state.go('empresa-avaliacao.avaliacao')  : wizard.go(wizard.getActivePosition() - 1)" class="btn btn-labeled btn-default">
                                                <span class="btn-label">
                                                    <i class="fa fa-arrow-circle-left"></i>
                                                </span>
                                                Voltar
                                            </button>
                                            <button ng-show="!wizard.active(3)" ng-disabled="vm.saving" ng-click="wizard.go(wizard.getActivePosition() + 1); vm.resize();" type="button" class="btn btn-labeled btn-default">
                                                <span class="btn-label">
                                                    <i class="fa fa-arrow-circle-right"></i>
                                                </span>
                                                Avançar
                                            </button>
                                            <button ng-show="wizard.active(3)" ng-disabled="form-parecer.$invalid" type="button" ng-click="vm.clickSalvar()" class="btn btn-labeled btn-success text-right ladda-button" ladda="loading" data-style="expand-right">
                                                <span class="btn-label">
                                                    <i class="fa fa-check-circle"></i>
                                                </span>
                                                Salvar
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>