(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('PainelPagamentoValePedagioController', PainelPagamentoValePedagioController);

    PainelPagamentoValePedagioController.inject = [
        'BaseService',
        '$rootScope',
        'toastr',
        '$scope',
        'PersistentDataService',
        '$timeout',
        '$state',
        '$stateParams',
        'PERFIL_ADMINISTRADOR',
        'oitozero.ngSweetAlert',
        '$uibModal',
        '$window'
    ];

    function PainelPagamentoValePedagioController(
        BaseService,
        $rootScope,
        toastr,
        $scope,
        PersistentDataService,
        $timeout,
        $uibModal) {

        var vm = this;

        vm.status = 100;
        vm.desabilitarBtnRelatorio = false;
        vm.modalRelatorioOptions = [{}];

        vm.enumStatusLista = [
            { id: 0, descricao: 'Processando' },
            { id: 1, descricao: 'Baixa<PERSON>' },
            { id: 2, descricao: 'Pendente pagamento' },
            { id: 3, descricao: 'Pendente cancelamento' },
            { id: 4, descricao: 'Cancelado' }
        ]
        vm.enumStatus = vm.enumStatusLista;

        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.administrador == true;
        };

        vm.headerItems = [{ name: 'Movimentações' }, { name: 'Painel pagamento vale pedágio' }]

        vm.load = function () {
        }

        vm.atualizaTela = function () {
            vm.gridOptions.dataSource.refresh();
        }

        function filterFormatCNPJ(input) {
            if (!input) return;
            return input.replace(/^(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/, "$1.$2.$3/$4-$5")
        }

        vm.dateOptions = {
            timePicker: false,
            applyClass: 'btn-primary',
            locale: {
                applyLabel: "Aplicar",
                fromLabel: "De",
                format: "DD/MM/YYYY",
                toLabel: "Até",
                cancelLabel: 'Cancelar',
                customRangeLabel: 'Período',
                monthNames: ['Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho', 'Julho', 'Agosto', 'Setembro',
                    'Outubro', 'Novembro', 'Dezembro'
                ],
                daysOfWeek: ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb']
            },
            ranges: {
                'Últimos 1 dias': [moment().add(-1, 'days'), moment()],
                'Últimos 7 dias': [moment().add(-7, 'days'), moment()]
            }
        };

        vm.date = {
            startDate: moment().add(-1, 'days'),
            endDate: moment()
        };

        vm.contas = [];
        vm.clicouNoCheckBoxConta = 0;
        
        $scope.$watch('vm.consultaEmpresa.selectedValue', function (newValue) {
            vm.empresaConsulta = vm.consultaEmpresa.selectedValue;
        });

        vm.gridOptions = {
            data: [],
            onRegisterApi: BaseService.dataGrid.defaultOnRegisterApi,
            urlRelatorio: "Pedagio/ConsultarGridPagamentoValePedagio",
            dataSource: {
                url: "Pedagio/ConsultarGridPagamentoValePedagio",
                params: function () {
                    return {
                        dataInicial: vm.date.startDate.format('DD/MM/YYYY').toString(),
                        dataFinal: vm.date.endDate.format('DD/MM/YYYY').toString(),
                        status: vm.status,
                        EmpresaId: vm.consultaEmpresa.selectedValue
                    }
                },
            },
            columnDefs: [{
                name: 'Ações',
                width: '5%',
                cellTemplate: '<div ng-if="!row.groupHeader" class="ui-grid-cell-contents" align="center" title="TOOLTIP">\
                    <button tooltip-placement="right" uib-tooltip="Visualizar"\
                        type="button" ui-sref="painel-pagamento-vale-pedagio.painel-pagamento-vale-pedagio-crud({link: row.entity.id})"\
                        ng-class="{ \'btn btn-xs btn-info\': true }">\
                        <i class="fa fa-eye"></i>\
                    </button>\
                    </div>\
                </div>'
            },
            {
                name: 'id',
                displayName: 'Código',
                width: 150,
                field: 'id',
                serverField: 'id',
                type: 'number',
                enableFiltering: true
            },
            {
                name: 'DataCadastro',
                displayName: 'Data de Cadastro',
                width: '*',
                minWidth: 150,
                field: 'dataCadastro',
                serverField: 'dataCadastro',
                enableFiltering: false
            },
            {
                name: 'DataBaixa',
                displayName: 'Data de Baixa',
                width: '*',
                minWidth: 150,
                field: 'dataBaixa',
                serverField: 'dataBaixa',
                enableFiltering: false
            },
            {
                name: 'Valor',
                displayName: 'Valor',
                width: 175,
                field: 'valor',
                serverField: 'valor',
                enableFiltering: false
            },
            {
                name: 'ValorComplemento',
                displayName: 'Valor Complemento',
                width: 175,
                field: 'valorComplemento',
                serverField: 'valorComplemento',
                enableFiltering: false
            },
            {
                name: 'ValorTotal',
                displayName: 'Valor Total',
                width: 175,
                field: 'valorTotal',
                serverField: 'valorTotal',
                enableFiltering: false
            },
            {
                name: 'ValorTarifa',
                displayName: 'Valor Tarifa',
                width: 175,
                field: 'valorTarifa',
                serverField: 'valorTarifa',
                enableFiltering: false
            },
            {
                name: 'PagamentoTarifa',
                displayName: 'Pagamento Tarifa',
                width: 170,
                field: 'pagamentoTarifa',
                serverField: 'pagamentoTarifa',
                enableFiltering: false,
                pipe: function (input) {
                    return filterPagamentoTarifaPedagio(input)
                },
                cellTemplate: '<div class="ui-grid-cell-contents" align="left">\
                                        <p ng-show="row.entity.pagamentoTarifa === 0"> Não </p>\
                                        <p ng-show="row.entity.pagamentoTarifa === 1"> Sim </p>\
                                   </div>'
            },
            {
                name: 'NomeContratante',
                displayName: 'Nome Contratante',
                width: 175,
                field: 'nomeContratante',
                serverField: 'nomeContratante',
                enableFiltering: false
            },
            {
                name: 'CNPJContratante',
                displayName: 'CNPJ Contratante',
                width: 175,
                field: 'empresaCnpj',
                serverField: 'empresaCnpj',
                pipe: function (input) {
                    return filterFormatCNPJ(input)
                },
                enableFiltering: true
            },
            {
                name: 'CodigoExterno',
                displayName: 'Código Externo',
                width: 170,
                field: 'codigoExterno',
                serverField: 'pagamentoExternoId',
                type: 'number',
                enableFiltering: true
            },
            {
                name: 'CodigoValePedagio',
                displayName: 'Código Vale Pedágio',
                width: 170,
                field: 'codigoValePedagio',
                serverField: 'codigoValePedagio',
                type: 'number',
                enableFiltering: true
            },
            {
                name: 'FormaPagamento',
                displayName: 'Forma de Pagamento',
                width: 170,
                field: 'formaPagamento',
                serverField: 'formaPagamento',
                enableFiltering: true,
                enum: true,
                enumTipo: 'EFormaPagamentoPedagio',
                pipe: function (input) {
                    return filterFormaPagamentoPedagio(input)
                },
                cellTemplate: '<div class="ui-grid-cell-contents" align="left">\
                                        <p ng-show="row.entity.formaPagamento === 0"> Vale Pedágio BBC </p>\
                                   </div>'
            },
            {
                name: 'Status',
                displayName: 'Status',
                width: 170,
                field: 'status',
                serverField: 'status',
                enableFiltering: false,
                enum: true,
                enumTipo: 'EStatusPagamentoPedagio',
                pipe: function (input) {
                    return filterStatusPagamentoPedagio(input)
                },
                cellTemplate: '<div class="ui-grid-cell-contents" align="left">\
                                        <p ng-show="row.entity.status === 0"> Processando </p>\
                                        <p ng-show="row.entity.status === 1"> Baixado </p>\
                                        <p ng-show="row.entity.status === 2"> Pendente pagamento </p>\
                                        <p ng-show="row.entity.status === 3"> Pendente cancelamento </p>\
                                        <p ng-show="row.entity.status === 4"> Cancelado </p>\
                                   </div>'
            },
            {
                name: 'mensagem',
                displayName: 'Mensagem',
                width: 400,
                field: 'mensagem',
                serverField: 'mensagem',
                enableFiltering: false
            },
            {
                name: 'DataAlteracao',
                displayName: 'Data Alteração',
                width: '*',
                minWidth: 150,
                field: 'dataAlteracao',
                serverField: 'dataAlteracao',
                enableFiltering: false
            },
            {
                name: 'DataCancelamento',
                displayName: 'Data Cancelamento',
                width: '*',
                minWidth: 150,
                field: 'dataCancelamento',
                serverField: 'dataCancelamento',
                enableFiltering: false
            },
            {
                name: 'ContadorReenvio',
                displayName: 'Contador Reenvio',
                width: 170,
                field: 'contadorReenvio',
                serverField: 'contadorReenvio',
                type: 'number',
                enableFiltering: false
            }]
        };

        vm.consultaEmpresa = {
            columnDefs: [{
                name: 'Cód.',
                field: 'id',
                width: 60,
                type: 'number',
                primaryKey: true
            }, {
                name: 'Nome Fantasia',
                field: 'nomeFantasia',
                width: '*'
            }, {
                name: 'Razão Social',
                field: 'razaoSocial',
                width: '*'
            }, {
                name: 'Email',
                field: 'email',
                width: 120
            }],
            desiredValue: 'id',
            desiredText: 'nomeFantasia',
            url: 'Empresa/ConsultarGridEmpresaCombo',
            paramsMethod: function () {
                return {}
            }
        };

        // Controle de aba!!
        $scope.$on('$stateChangeStart', function () {
            PersistentDataService.store('PainelPagamentoValePedagioController', vm, "Painel pagamento vale pedágio", "PainelPagamentoValePedagioController", "painel-pagamento-vale-pedagio.index");
        });

        var selfScope = PersistentDataService.get('PainelPagamentoValePedagioController');

        function exportarEmPdf() {
            vm.desabilitarBtnRelatorio = true;
            vm.gridOptions.dataSource.consultarDadosRelatorio(function (response) {
                vm.dadosRelatorio = response.data.data.items;
                $timeout(function () {
                    BaseService.exportarTabelaEmPdfNovo(
                        "#exportable",
                        "Relatório Pagamento Vale Pedágio",
                        "Relatorio_Pagamento_Vale_Pedágio_" + vm.date.startDate.toDate().toLocaleDateString().replace("/", "-") + "_a_" + vm.date.endDate.toDate().toLocaleDateString().replace("/", "-")
                    )
                }, 500);
                vm.desabilitarBtnRelatorio = false;
            });
        }
        function exportarEmExcel(formatoXls) {
            vm.desabilitarBtnRelatorio = true;
            vm.gridOptions.dataSource.consultarDadosRelatorio(function (response) {
                vm.dadosRelatorio = response.data.data.items;
                $timeout(function () {
                    BaseService.exportarTabelaEmExcel2("exportable-xls", "Relatório Pagamento Vale Pedágio", formatoXls)
                }, 500);
                vm.desabilitarBtnRelatorio = false;
            });
        }

        vm.exportarRelatorio = function (extensao) {
            switch (extensao) {
                case 1: {
                    exportarEmExcel(true)
                    break;
                }
                case 2: {
                    exportarEmPdf()
                    break;
                }
                default:
                    exportarEmPdf()
                    break;
            }
        }

        vm.abrirModalRelatorio = function (controllerPai) {
            $uibModal.open({
                animation: true,
                ariaLabelledBy: 'modal-title',
                ariaDescribedBy: 'modal-body',
                templateUrl: 'app/entities/painel-pagamento-vale-pedagio/modal-relatorios/modal-relatorios.html',
                controller: function ($uibModalInstance, $uibModalStack) {
                    var vm = this;

                    vm.modalRelatorioOptions = [{}];
                    vm.modalRelatorioPagamentoValePedagioOptions = [{}];

                    vm.headerItems = [{ name: 'Transações Financeiras' }];


                    for (var x in controllerPai.gridOptions.columnDefs) {
                        vm.modalRelatorioOptions[x] = {
                            name: controllerPai.gridOptions.columnDefs[x].displayName,
                            field: controllerPai.gridOptions.columnDefs[x].field,
                            pipe: controllerPai.gridOptions.columnDefs[x].pipe,
                            enabled: true
                        }
                    }

                    vm.fechar = function () {
                        controllerPai.modalRelatorioOptions = vm.modalRelatorioOptions;
                        controllerPai.modalRelatorioPagamentoValePedagioOptions = vm.modalRelatorioPagamentoValePedagioOptions;
                        $uibModalStack.dismissAll();
                    }

                    vm.exportarRelatorio = function (extensao) {

                        if (vm.modalRelatorioOptions.filter(function (x) {
                            return x.enabled
                        }).length < 2) {
                            toastr.error("Escolha alguma coluna para gerar o relatório.");
                            return;
                        }

                        if (controllerPai.date.endDate.diff(controllerPai.date.startDate, 'days') > 30) {
                            toastr.error("Escolha um período de no máximo 30 dias para gerar o relatório.")
                            return;
                        }

                        controllerPai.modalRelatorioOptions = vm.modalRelatorioOptions;
                        controllerPai.modalRelatorioPagamentoValePedagioOptions = vm.modalRelatorioPagamentoValePedagioOptions;
                        controllerPai.exportarRelatorio(extensao);

                        $uibModalStack.dismissAll();
                    }

                },
                controllerAs: 'vm',
                keyboard: true,
                size: 'lg'
            }).result.then(function () {
            });
        };

        function filterStatusPagamentoPedagio(input) {
            var correcao;
            switch (input) {
                case 0:
                    correcao = "Processando"
                    break;
                case 1:
                    correcao = "Baixado"
                    break;
                case 2:
                    correcao = "Pendente pagamento"
                    break; 
                case 3:
                    correcao = "Pendente cancelamento"
                    break;
                case 4:
                    correcao = "Cancelado"
                    break;
                default:
                    correcao = input;
            }
            return correcao
        }

        function filterFormaPagamentoPedagio(input) {
            var correcao;
            switch (input) {
                case 0:
                    correcao = "Vale Pedágio BBC"
                    break;
                default:
                    correcao = input;
            }
            return correcao
        }

        function filterPagamentoTarifaPedagio(input) {
            var correcao;
            switch (input) {
                case 0:
                    correcao = "Não"
                    break;
                case 1:
                    correcao = "Sim"
                    break;
                default:
                    correcao = input;
            }
            return correcao
        }
    }
})();