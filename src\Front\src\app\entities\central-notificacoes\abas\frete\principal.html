<div class="form-horizontal">
    <hr />
    <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label">Código:</label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <input type="text" ng-model="vm.notificacao.id" class="form-control" disabled
                            value="{{vm.isNew()}}" />
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-5 col-lg-3 control-label">
                        Descrição:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-7 col-lg-9">
                        <input type="text" disabled ng-model="vm.notificacao.descricao"
                            maxlength="200" name="descricao" class="form-control" />
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-2 col-md-4 col-lg-3">
                        Status:
                    </label>
                    <div class="input-group col-xs-12 col-sm-10 col-md-8 col-lg-9">
                        <input type="text" disabled ng-model="vm.notificacao.status"
                               maxlength="200" name="status" class="form-control" />
                    </div>
                </div>
            </div>  
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-2 col-md-4 col-lg-3">
                        Tipo:
                    </label>
                    <div class="input-group col-xs-12 col-sm-10 col-md-8 col-lg-9">
                        <ui-select 
                            name="tipo"
                            ats-ui-select-validator
                            validate-on="blur"
                            ng-model="vm.notificacao.tipo"
                            ng-disabled="true">
                            <ui-select-match>
                                <span>{{$select.selected.descricao}}</span>
                            </ui-select-match>
                            <ui-select-choices repeat="ex.id as ex in vm.cmbTipo.data | propsFilter: {descricao: $select.search}">
                                <div ng-bind-html="ex.descricao | highlight: $select.search"></div>
                            </ui-select-choices>
                        </ui-select>
                    </div>
                </div>
            </div>  
        </div>
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-5 col-lg-3 control-label">
                        Código transação:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-7 col-lg-9">
                        <input type="text" disabled ng-model="vm.notificacao.pagamentoEventoId"
                            maxlength="200" name="pagamentoEventoId" class="form-control" />
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>