(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('AutorizacaoAbastecimentoController', AutorizacaoAbastecimentoController);

    AutorizacaoAbastecimentoController.inject = ['BaseService', '$rootScope', '$window', 'toastr', '$scope', 'PersistentDataService', '$timeout', '$state', 'PERFIL_ADMINISTRADOR'];

    function AutorizacaoAbastecimentoController(BaseService, $rootScope, toastr, $window, $scope, PersistentDataService, $timeout, $state, PERFIL_ADMINISTRADOR) {
        var vm = this;
        vm.headerItems = [{
            name: 'Administração'
        }, {
            name: 'Autorização de abastecimento' 
        }];

        vm.combustiveisVeiculo = [];
        vm.veiculoList = [];
        vm.cmbConbustivelDesativa = true;
        vm.combustivelObrigatorio = true;
        vm.autorizacaoCombustiveis = [];
        vm.combustivelNomeCmb = "";
        vm.salvandoAutorizacao = false;

        vm.cmbMetodo = {
            data: [{ id: 2, descricao: 'Autorizacao' }, { id: 1, descricao: 'Orçamento' }]
        };

        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.perfil == PERFIL_ADMINISTRADOR;
        };

        $scope.$watch('vm.consultaVeiculo.selectedValue', function () {            
            vm.cmbConbustivelDesativa = true;
            vm.combustiveisVeiculo = [];

            if(!vm.consultaVeiculo.selectedValue ){
                vm.clear();
                return;
            }

            BaseService.get('Veiculo', 'ConsultarVeiculoCombustivel', {
                veiculoId: vm.consultaVeiculo.selectedValue
            }).then(function (response) {
                if (!response.data.sucesso) {
                    toastr.error(response.data.mensagem);
                    return;
                } else if(response.data.data.length == 0){
                    toastr.error("Veículo(s) não possui(em) combustível(eis) cadastrado(s) ou já existe(m) autorização(ões) criada(s) para o(s) mesmo(s)!");
                    vm.clear();
                }
                else {
                    vm.combustiveisVeiculo = response.data.data; 
                    vm.cmbConbustivelDesativa = false;                  
                }
            });

            if(vm.consultaVeiculo.selectedEntity.modelo){
                vm.consultaModelo.selectedValue = vm.consultaVeiculo.selectedEntity.modelo.id;                
                vm.consultaModelo.selectedText = vm.consultaVeiculo.selectedEntity.modelo.nome;
            }                
            vm.metodo = 2;                
            vm.combustivelObrigatorio = true;                             
            vm.consultaModelo.selectedValue = "";                
            vm.consultaModelo.selectedText = "";
        });

        $scope.$watch('vm.consultaFilial.selectedValue', function () {  
            vm.cmbConbustivelDesativa = true;            
            vm.combustiveisVeiculo = [];

            if(!vm.consultaFilial.selectedValue){
                return;
            }

            if(vm.consultaFilial.selectedValue){                
                BaseService.get('Veiculo', 'ConsultarVeiculoFilial', {
                    filialId: vm.consultaFilial.selectedValue,
                    modeloId: vm.consultaModelo.selectedValue ? vm.consultaModelo.selectedValue : 0
                }).then(function (response) {
                    if (!response.data.sucesso) {
                        toastr.error(response.data.mensagem);
                        vm.clear();
                        return;
                    } else if(response.data.data.veiculoCombustiveis.length == 0){
                        toastr.error("Veículo(s) não possui(em) combustível(eis) cadastrado(s) ou já existe(m) autorização(ões) criada(s) para o(s) mesmo(s)!");
                        vm.clear();
                    } else {
                        vm.cmbConbustivelDesativa = false;
                        vm.veiculoList = response.data.data.veiculos;
                        vm.combustiveisVeiculo = response.data.data.veiculoCombustiveis;                       
                        vm.metodo = 1;
                        vm.obrigatorioConbustivel();
                    }
                });
            }else{                              
                vm.clear();
            }
        });

        $scope.$watch('vm.consultaModelo.selectedValue' , function () {  
            vm.cmbConbustivelDesativa = true;
            vm.combustiveisVeiculo = [];

            if(!vm.consultaFilial.selectedValue){
                return;
            }

            if(vm.consultaFilial.selectedValue){                
                BaseService.get('Veiculo', 'ConsultarVeiculoFilial', {
                    filialId: vm.consultaFilial.selectedValue,
                    modeloId: vm.consultaModelo.selectedValue ? vm.consultaModelo.selectedValue : 0
                }).then(function (response) {
                    if (!response.data.sucesso) {
                        toastr.error(response.data.mensagem);
                        vm.clear();
                        return;
                    } else if(response.data.data.veiculoCombustiveis.length == 0){
                        toastr.error("Veículo(s) não possui(em) combustível(eis) cadastrado(s) ou já existe(m) autorização(ões) criada(s) para o(s) mesmo(s)!");
                        vm.clear();
                    } else {
                        vm.cmbConbustivelDesativa = false;
                        vm.veiculoList = response.data.data.veiculos;
                        vm.combustiveisVeiculo = response.data.data.veiculoCombustiveis;                       
                        vm.metodo = 1;
                        vm.obrigatorioConbustivel();
                    }
                });
            }else{                              
                vm.clear();
            }
        });        

        vm.obrigatorioConbustivel = function() {
            vm.combustivelObrigatorio = true;
            if(vm.metodo != 2){
                vm.combustivelObrigatorio = false;          
            }
        }   

        //Add/Remover vinculos de combustivel
        vm.adicionarAutorizacao = function () {
            var permiteAdicionar = false;

            if (!vm.consultaFilial.selectedValue && !vm.consultaVeiculo.selectedValue)
                return toastr.error("Nenhum veículo ou filial foi selecionado.");
            
            if (!vm.combustivel && vm.combustivelObrigatorio)
                return toastr.error("Combustível não informado.");

            if (!vm.litragem)
                return toastr.error("Litragem não informada.");

            if (vm.consultaVeiculo.selectedValue && vm.consultaVeiculo.selectedEntity.tipoAbastecimento != vm.metodo){
                return toastr.error("Veículo não realiza abastecimento do método selecionado.");
            }

            if ((vm.consultaVeiculo.selectedValue  && vm.metodo == 2 && vm.combustivel && vm.litragem) ||
            (vm.consultaVeiculo.selectedValue  && vm.metodo == 1 && (vm.combustivel || !vm.combustivel) && vm.litragem) || 
            (vm.consultaFilial.selectedValue && (vm.combustivel || !vm.combustivel) && vm.litragem)){
                permiteAdicionar = true;
            }

            if (permiteAdicionar && vm.consultaFilial.selectedValue > 0) {
                for (var i = 0; i < vm.veiculoList.length; i++) {

                    var combustivelValidos = _.filter(vm.veiculoList[i].lConbustiveis, function (v) {
                        return v === vm.combustivel.combustivelId;
                    });

                    if (combustivelValidos.length > 0){
                        var combustivel = {
                        combustivelNome: vm.combustivel ? vm.combustivel.combustivelNome : "",
                        placa: vm.veiculoList[i].veiculoPlaca,
                        veiculoId: vm.veiculoList[i].veiculoId,
                        filialId: vm.consultaFilial.selectedValue? vm.consultaFilial.selectedValue : 0,
                        filial: vm.consultaFilial.selectedText,
                        modeloId: vm.consultaModelo.selectedValue? vm.consultaModelo.selectedValue : 0,
                        combustivelId: vm.combustivel ? vm.combustivel.combustivelId : 0,
                        litragem: vm.litragem,
                        metodo: vm.veiculoList[i].metodo == 0 ? 2 : vm.veiculoList[i].metodo
                        }

                        var objetosValidos = _.filter(vm.autorizacaoCombustiveis, function (v) {
                            return v.veiculoId === combustivel.veiculoId;
                        });
            
                        if (objetosValidos.length == 0) {
                            vm.autorizacaoCombustiveis.push(combustivel);
                        }
                        
                    }                    
                                                 
                }
                
                vm.clear();
            }
            else if (permiteAdicionar && vm.consultaVeiculo.selectedValue > 0) {
                var combustivel = {
                    combustivelNome: vm.combustivel ? vm.combustivel.combustivelNome : "",
                    placa: vm.consultaVeiculo.selectedEntity? vm.consultaVeiculo.selectedEntity.placa: null,
                    veiculoId: vm.consultaVeiculo.selectedValue? vm.consultaVeiculo.selectedValue : 0,
                    filialId: vm.consultaFilial.selectedValue? vm.consultaFilial.selectedValue : 0,
                    filial: null,
                    modeloId: vm.consultaModelo.selectedValue? vm.consultaModelo.selectedValue : 0,
                    combustivelId: vm.combustivel ? vm.combustivel.combustivelId : 0,
                    litragem: vm.litragem,
                    metodo: vm.metodo
                }
                
                var objetosValidos = _.filter(vm.autorizacaoCombustiveis, function (v) {
                    return v.veiculoId === combustivel.veiculoId && v.combustivelId === combustivel.combustivelId;
                });
    
                if (objetosValidos.length > 0) {
                    toastr.error("Autorização para este veículo com mesmo combustível já existente.");
                    return;
                }

                vm.autorizacaoCombustiveis.push(combustivel);
                vm.clear();
            }
            else
                toastr.error("Por favor, informe os dados solicitados!");
        };

        vm.clear = function () {
            vm.consultaVeiculo.selectedValue = "";
            vm.consultaVeiculo.selectedText = "";
            vm.consultaVeiculo.selectedEntity = "";
            vm.consultaFilial.selectedValue = "";
            vm.consultaFilial.selectedText = "";
            vm.consultaModelo.selectedValue = "";
            vm.consultaModelo.selectedText = "";
            vm.combustivel = "";
            vm.combustiveisVeiculo = [];
            vm.litragem = "";
        };

        vm.removerCombustivel = function (combustiveis) {
            for (var i = 0; i < vm.autorizacaoCombustiveis.length; i++) {
                if (vm.autorizacaoCombustiveis[i] == combustiveis) {
                    var index = vm.autorizacaoCombustiveis.indexOf((vm.autorizacaoCombustiveis[i]));
                    vm.autorizacaoCombustiveis.splice(index, 1)
                }  
            }
        }; 
        
        vm.save = function () {   
            var lAutorizacao = vm.autorizacaoCombustiveis
            vm.salvandoAutorizacao = true;
            BaseService.post('AutorizacaoAbastecimento', 'Salvar', lAutorizacao).then(function (response) {
                if (response.sucesso) {
                    toastr.success(response.mensagem);
                    vm.autorizacaoCombustiveis = [];
                    vm.gridOptions.dataSource.refresh();
                } else{
                    vm.autorizacaoCombustiveis = [];
                    toastr.error(response.mensagem);
                }
                
                vm.salvandoAutorizacao = false;
                    
            });
        };

        vm.cancelarAutorizacao = function (id) { 
            var request = {autorizacaoId : id}
            BaseService.post('AutorizacaoAbastecimento', 'CancelarAutorizacaoAbastecimento', request).then(function (response) {
                if (response.sucesso) {
                    toastr.success(response.mensagem);
                    vm.gridOptions.dataSource.refresh();
                } else{
                    vm.autorizacaoCombustiveis = [];
                    toastr.error(response.mensagem);
                }
                    
            });
        };

        vm.gridOptions = {
            data: [],
            onRegisterApi: BaseService.dataGrid.defaultOnRegisterApi,
            dataSource: {
                url: "AutorizacaoAbastecimento/ConsultarGridAutorizacaoAbastecimento"
            },
            columnDefs: [{
                name: 'Ações', width: 80, enableColumnMenu: false, cellTemplate: '<div ng-if="!row.groupHeader" class="ui-grid-cell-contents" title="TOOLTIP">\
                    <button ng-if="row.entity.status !== 0" ng-disabled="true" type="button" mwl-confirm class="btn btn-danger btn-xs" \
                            placement="right"\
                            message="Você tem certeza que deseja cancelar esta autorização?"\
                            tooltip-placement="right" uib-tooltip="Cancelar autorização">\
                            <i class="fa fa-trash"></i>\
                    </button>\
                    <button ng-if="row.entity.status === 0" type="button" mwl-confirm class="btn btn-danger btn-xs" \
                            placement="right"\
                            message="Você tem certeza que deseja cancelar esta autorização?"\
                            tooltip-placement="right" uib-tooltip="Cancelar autorização" \
                            confirm-text="Sim"\
                            cancel-text="Não" confirm-button-type="danger"\
                            cancel-button-type="default"\
                            on-confirm="grid.appScope.vm.cancelarAutorizacao(row.entity.id)">\
                            <i class="fa fa-trash"></i>\
                    </button>\
                </div>'
            },{
                name: 'Codigo',
                displayName: 'Código',
                width: 80,
                type: 'number',
                primaryKey: true,
                field: 'id'
            }, {
                name: 'Placa',
                displayName: 'Placa',
                width: '*',
                field: 'placa',
                serverField: 'veiculo.placa'
            }, {
                name: 'LitragemUtilizada',
                displayName: 'Litragem utilizada',
                width: '*',
                field: 'litragemAbastecida',
                cellTemplate: '<div class="ui-grid-cell-contents">\
                                    <input type="text" ng-model="row.entity.litragemAbastecida" readonly\
                                            class="no-borders" style="background: none;" ui-number-mask="3" />\
                               </div>',
                enableFiltering: false,
                enableSorting: false
            }, {
                name: 'Combustivel',
                displayName: 'Combustível',
                width: '*',
                field: 'combustivelNome',
                serverField: 'combustivel.nome'
            }, {
                name: 'LitragemDisponivel',
                displayName: 'Litragem dísponivel',
                width: '*',
                field: 'litragemDisponivel',
                cellTemplate: '<div class="ui-grid-cell-contents">\
                                    <input type="text" ng-model="row.entity.litragemDisponivel" readonly\
                                            class="no-borders" style="background: none;" ui-number-mask="3" />\
                               </div>',
                enableFiltering: false,
                enableSorting: false
            }, {
                name: 'Metodo',
                displayName: 'Método de abastecimento',
                width: '*',
                field: 'metodo',
                enableFiltering: true,
                enum: true,
                enumTipo: 'EMetodoAbastecimento',
                cellTemplate: '<div class="ui-grid-cell-contents" align="left">\
                                        <p ng-show="row.entity.metodo === 2"> Autorização </p>\
                                        <p ng-show="row.entity.metodo === 1"> Orçamento </p>\
                                        <p ng-show="row.entity.metodo === 3"> Extra </p>\
                                   </div>'
            },{
                name: 'Status',
                displayName: 'Status de abastecimento',
                width: '*',
                field: 'status',
                enableFiltering: true,
                enum: true,
                enumTipo: 'EStatusAutorizacaoAbastecimento',
                cellTemplate: '<div class="ui-grid-cell-contents" align="left">\
                                        <p ng-show="row.entity.status === 2"> Cancelada </p>\
                                        <p ng-show="row.entity.status === 1"> Utilizada </p>\
                                        <p ng-show="row.entity.status === 0"> Ativa </p>\
                                   </div>'
            }, {
                name: 'DataCadastro',
                displayName: 'Data cadastro',
                width: '*',
                field: 'dataCadastro',
                enableFiltering: false
            }, {
                name: 'UsuárioCadastro',
                displayName: 'Usuário Cadastro',
                width: '*',
                field: 'usuarioCadastro',
                serverField: 'usuarioCadastro'
            }]
        };

        vm.consultaFilial = {
            columnDefs: [{
                name: 'Código',
                field: 'id',
                width: 80,
                primaryKey: true
            }, {
                name: 'Nome',
                field: 'nomeFantasia',
                width: '*',
                minWidth: 150
            }, {
                name: 'Razão social',
                field: 'razaoSocial',
                width: '*',
                minWidth: 150
            }, {
                name: 'CNPJ',
                field: 'cnpj',
                width: '*',
                minWidth: 150
            }],
            desiredValue: 'id',
            desiredText: 'nomeFantasia',
            url: 'Filial/ConsultarGridFilial',
            paramsMethod: function () {
                return {
                    FiltrarUsuario: true
                }
            },
        };

        vm.consultaVeiculo = {
            columnDefs: [{
                name: 'Código',
                field: 'id',
                width: 80,
                type: 'number',
                primaryKey: true
            }, {
                name: 'Placa',
                field: 'placa',
                width: '*',
                minWidth: 160
            }, {
                name: 'Renavam',
                field: 'renavam',
                enableGrouping: true,
                width: 145
            }],
            desiredValue: 'id',
            desiredText: 'placa',
            url: 'Veiculo/ConsultarGridVeiculo',
            paramsMethod: function () {
                return {
                    FiltrarUsuario: true
                }
            }
        };

        vm.consultaModelo = {
            columnDefs: [{
                name: 'Código',
                field: 'id',
                width: 80,
                type: 'number',
                primaryKey: true
            }, {
                name: 'Nome',
                field: 'nome',
                width: '*',
                minWidth: 160
            }],
            desiredValue: 'id',
            desiredText: 'nome',
            url: 'Modelo/ConsultarGridModelo',
            paramsMethod: function () {
                return {
                    FiltrarUsuario: true,
                    FilialId: vm.consultaFilial.selectedValue
                }
            }
        };

        // Controle de aba!!
        $scope.$on('$stateChangeStart', function () {
            PersistentDataService.store('AutorizacaoAbastecimentoController', vm, "Autorização de abastecimento", "autorizacao-abastecimento.index");
        });

        var selfScope = PersistentDataService.get('AutorizacaoAbastecimentoController');
        if (angular.isDefined(selfScope)) {
            angular.extend(vm, selfScope.data);

            $timeout(function () {
                vm.gridOptions.dataSource.refresh();
            }, 450);
        }
    }
})();