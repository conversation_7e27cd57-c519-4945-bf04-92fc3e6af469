(function () {
    'use strict';

    angular.module('bbcWeb.filial.state', []).config(routeConfig);

    function routeConfig($urlRouterProvider, $stateProvider) {
        $stateProvider.state('filial', {
            abstract: true,
            url: "/filial",
            templateUrl: "app/layout/content.html"
        }).state('filial.index', {
            url: '/index',
            templateUrl: 'app/entities/filial/filial.html'
        }).state('filial.filial-crud', {
            url: '/:link',
            templateUrl: 'app/entities/filial/filial-crud.html'
        });
    }
})();