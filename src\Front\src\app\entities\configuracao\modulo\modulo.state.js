(function() {
    'use strict';

    angular.module('bbcWeb.configuracao.modulo.state', []).config(routeConfig);

    function routeConfig($urlRouterProvider, $stateProvider) {
        $stateProvider
            .state('configuracao.modulo', {
                url: "/modulo",
                templateUrl: "app/entities/configuracao/modulo/modulo.html"
            }).state('configuracao.modulo-crud', {
                url: "/modulo/:link",
                templateUrl: "app/entities/configuracao/modulo/modulo-crud.html"
            })
    }
})();