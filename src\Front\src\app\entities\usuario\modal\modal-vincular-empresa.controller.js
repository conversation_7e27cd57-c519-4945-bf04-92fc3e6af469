(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('ModalVincularEmpresaController', ModalVincularEmpresaController);

    ModalVincularEmpresaController.$inject = ['$uibModalInstance', 'BaseService', 'toastr',  'usuarioId', 'listaEmpresasIds'];

    function ModalVincularEmpresaController($uibModalInstance, BaseService, toastr,  usuarioId, listaEmpresasIds) {
        var vm = this;

        vm.empresasAdicionadas = []; 
        vm.usuarioId = usuarioId;
        vm.listaEmpresasIds = listaEmpresasIds;

        vm.gridOptions = {
            data: [],
            onRegisterApi: BaseService.dataGrid.defaultOnRegisterApi,
            dataSource: {  
                autoBind: true,
                url: "Usuario/ConsultaGridEmpresasVinculadasCombo",
                params: function () {
                    return {
                        UsuarioId: vm.usuarioId,
                        EmpresaId: vm.listaEmpresasIds
                    }
                },
            },
            columnDefs: [{
                name: 'Ações',
                width: '8%',
                cellTemplate: '<div class="ui-grid-cell-contents"">\
                                      <center>\
                                        <button type="button" ng-click="grid.appScope.vm.selectValue(row.entity.id, row.entity.nomeFantasia, row.entity.razaoSocial, row.entity.cnpj)" ng-class="{ \'btn btn-xs btn-secondary btn-secondaryHover\': true }">\
                                            <i class="fa fa-check"></i>\
                                        </button>\
                                      </center>\
                                  </div>'
            },
            {
                name: 'Código',
                field: 'id',
                width: 80,
                type: 'number',
                primaryKey: true
            },
            {
                name: 'Nome fantasia',
                field: 'nomeFantasia',
                width: '*',
                minWidth: 250
            },
            {
                name: 'Razão Social',
                field: 'razaoSocial',
                width: '*',
                minWidth: 250
            },
            {
                name: 'CNPJ',
                field: 'cnpj',
                width: '*',
                minWidth: 250
            }],
        };

        vm.selectValue = function (id, nomeFantasia, razaoSocial, cnpj) {
            var novoItem = {
                id: id,
                nomeFantasia: nomeFantasia,
                razaoSocial: razaoSocial,
                cnpj: cnpj
            };

            var existe = vm.empresasAdicionadas.some(item => item.id === id);

            if (existe) {
                toastr.error('Empresa ja adicinada na lista');
            }

            if (!existe) {
                vm.empresasAdicionadas.push(novoItem);
            }
        };
    
        vm.fechar = function () {
            $uibModalInstance.dismiss('cancel');
        };

        vm.deletar = function (index) {
            vm.empresasAdicionadas.splice(index, 1);
        };

        vm.adicionar = function () {
            if (vm.empresasAdicionadas.length === 0) {
                return toastr.warning("Nenhuma empresa foi adicionada.");
            }
    
            $uibModalInstance.close(vm.empresasAdicionadas);
        };
    }
})();