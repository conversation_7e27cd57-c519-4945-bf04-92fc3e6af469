(function () {
    'use strict';

    angular.module('bbcWeb').controller('DocumentosProrcessoVinculadoCrudController', DocumentosProrcessoVinculadoCrudController);

    DocumentosProrcessoVinculadoCrudController.$inject = ['toastr', '$rootScope', 'BaseService', '$state', '$stateParams', '$window', 'PERFIL_ADMINISTRADOR', '$scope', '$timeout', 'PersistentDataService', 'DefaultsService', '$uibModal'];

    function DocumentosProrcessoVinculadoCrudController(
        toastr, $rootScope,
        BaseService, $state,
        $stateParams, $window,
        PERFIL_ADMINISTRADOR,
        $scope, $timeout,
        PersistentDataService,
        DefaultsService,
        $uibModal
    ) {
        var vm = this;
        vm.documentoProcessoVinculado = {};
        vm.documentoProcessoVinculado.documento = "";
        vm.documentoProcessoVinculado.tipo = 0;
        vm.documentoProcessoVinculado.obrigatorio = 1;
        vm.documentoProcessoVinculado.Ativo = 1;
        vm.documentoProcessoVinculado.processoVinculadoId = 2;
        vm.isSaving = false;
        vm.naoEditavel = false;

        vm.cmbProcessoVinculado = {
            data: [{ id: 0, descricao: 'Usuário' }, 
            { id: 1, descricao: 'Comprovantes' }, 
            { id: 2, descricao: 'Credenciamento' }, 
            { id: 3, descricao: 'Veículos' }]
        };

        vm.cmbTipo = {
            data: [{ id: 1, descricao: 'PDF' }, 
            { id: 0, descricao: 'Imagem' }]
        };

        vm.headerItems = [{
            name: 'Cadastros'
        }, {
            name: 'Documentos',
            link: 'documentos-processo-vinculado.index'
        }, {
            name: $stateParams.link == 'novo' ? 'Novo' : 'Editar'
        }];

        var selfScope = PersistentDataService.get('DocumentosProrcessoVinculadoCrudController');

        if ($stateParams.link == 'novo')
            vm.documentoProcessoVinculado.id = 'Auto';

        vm.isNew = function () {
            return $stateParams.link == 'novo';
        };

        vm.loadEdit = function (id) {
            BaseService.get('DocumentosProcessoVinculado', 'ConsultarPorId', {
                idDocumentosProcessoVinculado: id
            }).then(function (response) {
                if (!response.success) {
                    toastr.error(response.message);
                    return;
                } else {
                    vm.documentoProcessoVinculado = response.data;
                    vm.naoEditavel = response.data.naoEditavel;
                    if(vm.naoEditavel){
                        toastr.warning("Documento já utilizado, só é permitida alteração de obrigatoriedade. ");
                    }
                }
            });
        };

        vm.save = function (form) {            
            if (!form.$valid) {
                toastr.error('Todos os campos obrigatórios devem ser preenchidos corretamente com seus respectivos valores');
                return;
            }
            
            vm.isSaving = true;

            var lDocumentosProcessoVinculadoReq = {
                Id: vm.documentoProcessoVinculado.id == "Auto" ? 0 : vm.documentoProcessoVinculado.id,
                Documento: vm.documentoProcessoVinculado.documento,
                ProcessoVinculadoId: vm.documentoProcessoVinculado.processoVinculadoId,
                Ativo: vm.documentoProcessoVinculado.ativo,
                Obrigatorio: vm.documentoProcessoVinculado.obrigatorio ? 1 : 0,
                Tipo: vm.documentoProcessoVinculado.tipo
            }

            BaseService.post('DocumentosProcessoVinculado', 'Salvar', lDocumentosProcessoVinculadoReq)
            .then(function (response) {
                vm.isSaving = false;
                if (response.sucesso) {
                    toastr.success("Documento cadastrado");
                    $state.go('documentos-processo-vinculado.index');
                } else{
                    vm.autorizacaoCombustiveis = [];
                    toastr.error(response.mensagem);
                }
                    
            });
        };

        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.administrador == true;
        };

        function init() {
            if (vm.isNew()) {
                vm.documentoProcessoVinculado.id = 'Auto';
            }
        }
        var caracteresBloqueados = ['<', '>', '$', '\''];
        
        vm.bloquearCaracteresIndesejados = function(event) {
            var teclaDigitada = String.fromCharCode(event.charCode);
            

            if (caracteresBloqueados.includes(teclaDigitada)) {
                event.preventDefault();
            }
        };
        vm.filtrarColagem = function(event) {
            var textoColado = event.originalEvent.clipboardData.getData('text/plain');
            
            var textoFiltrado = textoColado.split('').filter(function(char) {
                return !caracteresBloqueados.includes(char);
            }).join('');
            
            $scope.vm.documentoProcessoVinculado.documento = textoFiltrado;
            
            event.preventDefault();
        };

        vm.onClickVoltar = function (wizard) {

            var ativoIndex = wizard.getActivePosition();
            if (ativoIndex == 1)
                $state.go('documentos-processo-vinculado.index');

            wizard.go(ativoIndex - 1);
        };
        
        init();

        $scope.$on('$stateChangeStart', function (arg1, toState) {
            if (toState.name === 'documentos-processo-vinculado.index')
                PersistentDataService.remove('DocumentosProrcessoVinculadoCrudController');
            else
                PersistentDataService.store('DocumentosProrcessoVinculadoCrudController', vm, "Cadastro - Documentos", null, "documentos-processo-vinculado.documentos-processo-vinculado-crud", vm.documentoProcessoVinculado.id);
        });

        if (angular.isDefined(selfScope))
            angular.extend(vm, selfScope.data);
        else {
            if (!vm.isNew())
                vm.loadEdit($stateParams.link);
            else {
                init();
            }
        }

        $timeout(function () {
            PersistentDataService.remove('DocumentosProrcessoVinculadoController');
        }, 15);
        
    }
})();
