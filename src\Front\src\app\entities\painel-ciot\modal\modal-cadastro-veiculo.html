<style>
    .modal-top {
        z-index: 9999999999 !important; /* Certifique-se de usar um valor maior */
    }
</style>
<div>
    <form name="formCadastrarVeiculo" novalidate ng-submit="vm.<PERSON>l<PERSON>(formCadastrarVeiculo);" show-validation ats-validator>
        <!-- Modal content-->
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" ng-click="$dismiss()" aria-label="Close">
                    <em class="fa fa-times"></em>
                </button>
                <h4 class="modal-title">Novo Veículo</h4>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="col-sm-4 col-md-4 col-lg-4 control-label-modal"><span
                            class="text-danger mr-5">*</span>Placa</label>
                    <div class="input-group col-sm-8 col-md-8 col-lg-8">
                        <input type="text" style="text-transform: uppercase" name="PlacaVeiculo" ui-mask="*******" class="form-control"
                            ng-model="vm.modal.placa" required-message="'Placa é obrigatório'" ng-required="true" />
                    </div>
                </div>
                <div class="form-group">
	                <label class="col-xs-12 col-sm-12 col-md-4 col-lg-4 control-label-modal"><span
	                        class="text-danger mr-5">*</span>Renavam</label>
	
	                <div class="input-group col-xs-12 col-sm-12 col-md-8 col-lg-8">
	                    <input class="form-control" type="text" name="RenavamVeiculo" placeholder="Informe um Renavam" ats-numeric
	                        onkeypress="return event.charCode >= 48 && event.charCode <= 57" maxlength="11" validate-on="blur"
	                        ng-model="vm.modal.renavam" required-message="'Renavam é obrigatório'" ng-required="true">
	                </div> 
                </div>
                 <div class="form-group">
                    <consulta-padrao-modal ng-show="vm.isAdmin()" tabledefinition="vm.consultaEmpresa"
                        label="'Empresa:'" placeholder="'Selecione uma Empresa'"
                        required-message="'Empresa é obrigatório'" ng-required="vm.isAdmin()"
                        directivesizes="'col-sx-12 col-sm-12 col-md-12 col-lg-12 input-group'"
                        labelsize="'col-sx-12 col-sm-4 col-md-4 col-lg-4 control-label-modal'">
                    </consulta-padrao-modal>
                </div>
            </div>
            <div class="modal-footer">
                <div class="text-right ">
                    <button type="submit"
                        class="btn btn-labeled btn-success text-right">
                        Salvar
                    </button>
                </div>
            </div>
        </div>
    </form>
</div>
