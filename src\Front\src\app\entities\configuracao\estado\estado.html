<div ng-controller="EstadoController as vm">
    <form-header items="vm.headerItems" head="'Estado'"></form-header>
    <div class="wrapper wrapper-content animated fadeIn mt-5">
        <div class="row">
            <div class="col-lg-12">
                <div class="ibox">
                    <div class="ibox-title">
                        <h5>Estados</h5>
                        <div ibox-tools></div>
                    </div>
                    <div class="ibox-content">
                        <div class="pull-right">
                            <button tooltip-placement="top" ng-click="vm.gridOptions.dataSource.refresh();" uib-tooltip="Atualizar" type='button' class="btn btn-labeled btn-default">
                                <i class="fa fa-refresh"></i>
                                <span class="pl-5 ">Atualizar</span>
                            </button>
                            <button tooltip-placement="top" ui-sref="configuracao.estado-crud ({link:'novo'})" uib-tooltip="Cadastrar" type='button'
                                class="btn btn-labeled btn-primary">
                                <span class="btn-label text-right">
                                    <i class="fa fa-plus"></i>
                                </span>
                                <span class="pl-5">Novo</span>
                            </button>
                        </div>
                        <div class="row"></div>
                        <hr>
                        <div ui-grid="vm.gridOptions" class="grid" style="width: 100%;" ng-style="{height: vm.gridOptions.getGridHeight()}" ui-grid-pinning ui-grid-save-state ui-grid-pagination ui-grid-auto-resize ui-grid-resize-columns
                            ui-grid-grouping>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>