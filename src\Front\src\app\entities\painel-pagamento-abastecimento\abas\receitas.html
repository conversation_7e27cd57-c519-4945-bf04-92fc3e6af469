<div class="form-horizontal">
    <hr-label dark="true" title="'Receitas'"></hr-label><br><br>
    <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
        <div class="row">
            <div class="col-xs-12 col-md-4">
                <div class="form-group">
                    <label class="col-xs-12 col-md-3 control-label" style="text-align: right; padding-top: 10px;">Período:</label>
                    <div class="input-group col-xs-12 col-md-9">
                        <input date-range-picker class="form-control date-picker" 
                        ui-date-mask="DD/MM/YYYY - DD/MM/YYYY" 
                        type="text" 
                        ng-model="vm.dataReceita" 
                        options="vm.dateOptions" />
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-4 col-lg-4" >
                <div class="form-group">
                    <label class="col-xs-12 col-md-3 control-label" style="text-align: right; padding-top: 10px;">
                        Status:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-9 col-lg-9">
                        <ui-select name="statusReceita" ats-ui-select-validator validate-on="blur"
                            ng-model="vm.statusReceita" ng-change="vm.atualizaTelaReceita()">
                            <ui-select-match>
                                <span>{{$select.selected.descricao}}</span>
                            </ui-select-match>
                            <ui-select-choices
                                repeat="ex.id as ex in vm.enumStatusReceita | propsFilter: {descricao: $select.search}">
                                <div ng-bind-html="ex.descricao | highlight: $select.search"></div>
                            </ui-select-choices>
                        </ui-select>
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-4 col-lg-4 pull-right">
                <button tooltip-placement="down"
                    type='button' ng-click="vm.atualizaTelaReceita()"
                    class="btn btn-labeled btn-primary pull-right">
                    <span class="btn-label text-right">
                    </span>
                    <span class="pl-5 ">Consultar</span>
                </button>
            </div>
        </div>
        
        <div class="row"></div>
        <hr>
        <div ui-grid="vm.gridReceitaOptions" ng-style="{height: vm.gridOptions.getGridHeight()}" class="grid" style="width: 100%;" ui-grid-pinning ui-grid-save-state
            ui-grid-pagination ui-grid-auto-resize ui-grid-resize-columns ui-grid-grouping>
        </div>
        <br>
        <div class="row">
            <div class="col-xs-12 col-md-12 pull-right px-2">
                <button type="button" class="btn btn-primary pull-right" 
                        ng-disabled = "vm.statusReceita != 6 || vm.disabilitarReenvioReceita"
                        ng-click="vm.reenviarReceitas()"><i class="fa fa-refresh"></i>
                        Reenviar pagamento
                </button>
                <button type="button"
                        class="btn btn-labeled btn-primary pull-right"
                        ng-click="vm.abrirModalRelatorio('gridReceitaOptions', 'dataReceita', vm)">
                    <i class="fa fa-file-pdf-o"></i> Exportar Relatório
                </button>
            </div>
        </div>
        <div id="exportable-xls">
            <table style="font-family:Arial, Helvetica, sans-serif" ng-show="false" id="exportable"
                   class="table table-bordered" width="100%">
                <thead>
                <tr>
                    <th style="text-align: left"
                        ng-repeat="option in vm.modalRelatorioOptions"
                        ng-if="option.enabled && option.field">
                        {{option.name}}
                    </th>
                </tr>
                </thead>
                <tbody>
                <tr style="text-align: left"
                    ng-repeat="item in vm.dadosRelatorio">
                    <td ng-repeat="option in vm.modalRelatorioOptions"
                        ng-if="option.enabled && option.field">
                        {{option.pipe ? (option.pipe(item[option.field])) : (item[option.field])}}
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>