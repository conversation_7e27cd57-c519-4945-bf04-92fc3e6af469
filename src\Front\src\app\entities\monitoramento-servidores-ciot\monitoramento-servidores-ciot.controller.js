(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('MonitoramentoServidoresCiotController', MonitoramentoServidoresCiotController);

    MonitoramentoServidoresCiotController.inject = [
        'BaseService',
        '$rootScope',
        'toastr',
        '$scope',
        'PersistentDataService',
        '$timeout',
        '$state',
        '$stateParams',
        'PERFIL_ADMINISTRADOR',
        'oitozero.ngSweetAlert',
        '$uibModal',
        '$window'
    ];

    function MonitoramentoServidoresCiotController(
        BaseService,
        $rootScope,
        toastr,
        $scope,
        PersistentDataService,
        $timeout,
        $state,
        $stateParams,
        PERFIL_ADMINISTRADOR,
        SweetAlert,
        $uibModal,
        $window) {
        var vm = this;
        vm.firtAcess = true;
        vm.isFullScreen = false;
        vm.headerItems = [{ name: 'CIOT' }, { name: 'Monitoramento de servidores CIOT' }];

        // Adicione $watch para monitorar mudanças em $rootScope.isFullScreen
        $rootScope.$watch('isFullScreen', function(newVal, oldVal) {
            if (newVal != oldVal)
                vm.isFullScreen = newVal;
        });

        vm.load = function () {
        }

        // Controle de aba!!
        $scope.$on('$stateChangeStart', function () {
            PersistentDataService.store('MonitoramentoServidoresCiotController', vm, "Monitoramento de Servidores CIOT", "MonitoramentoServidoresCiotController", "monitoramento-servidores-ciot.index");
        });

        var selfScope = PersistentDataService.get('MonitoramentoServidoresCiotController');
    }
})();