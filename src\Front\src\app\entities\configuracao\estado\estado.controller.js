(function() {
    'use strict';

    angular.module('bbcWeb').controller('EstadoController', EstadoController);

    EstadoController.$inject = [
        '$scope',
        'PersistentDataService',
        'BaseService',
        'toastr',
        '$rootScope',
        '$state',
        '$timeout'
    ];

    function EstadoController(
        $scope,
        PersistentDataService,
        BaseService,
        toastr,
        $rootScope,
        $state,
        $timeout) {
        //Inicialização dos objetos e arrays
        var vm = this;
        vm.headerItems = [{
            name: 'Administração'
        }, {
            name: 'Estado'
        }];
        vm.estado = {};
        vm.estados = [];
        vm.perfilUsuarioLogado = $rootScope.usuarioLogado.perfil;
        // Configurações da grid...
        vm.gridOptions = {
            data: [],
            onRegisterApi: BaseService.dataGrid.defaultOnRegisterApi,
            dataSource: {
                autoBind: true,
                url: "EstadoAts/ConsultarGrid"
            },
            columnDefs: [{
                    name: '<PERSON><PERSON><PERSON>',
                    primaryKey: true,
                    width: 80,
                    field: 'IdEstado',
                    type: 'number'
                },
                {
                    name: '<PERSON><PERSON><PERSON><PERSON>',
                    field: 'Nome',
                    width: '*',
                    minWidth: 250
                },
                {
                    name: 'Pais',
                    field: 'NomePais',
                    serverField: 'Pais.Nome',
                    width: '*',
                    minWidth: 200
                },
                {
                    name: 'Ações',
                    width: 80,
                    cellTemplate: '<div  ng-if="!row.groupHeader" class="ui-grid-cell-contents" title="TOOLTIP">\
                                    <button ng-disabled="!row.entity.Ativo" type="button" ui-sref="configuracao.estado-crud({link: row.entity.IdEstado})"\
                                        ng-class="{ \'btn btn-xs btn-info\': true }">\
                                        <i class="fa fa-edit"></i>\
                                    </button>\
                                    <button type="button" ng-click="grid.appScope.vm.alterarStatus(row.entity.IdEstado, row.entity.Ativo)" ng-class="row.entity.Ativo ? \'btn btn-xs btn-success\' : \'btn btn-xs btn-danger\'">\
                                        <i ng-class="row.entity.Ativo ? \'fa fa-check\' : \'fa fa-times\'"></i>\
                                    </button>\
                                  </div>'
                }
            ]
        };
        // Fim config Grid
        vm.alterarStatus = function(id, ativo) {
            BaseService.post('EstadoAts', ativo ? "Inativar" : "Reativar", {
                idEstado: id
            }).then(function(response) {
                if (response.success)
                    toastr.success(response.message);
                else
                    toastr.error(response.message);

                vm.gridOptions.dataSource.refresh();
            });
        };

        // DO NOT TOUCH!!
        $scope.$on('$stateChangeStart', function(_, toParams) {
            PersistentDataService.store('EstadoController', vm, "Estado", "EstadoCrudController", "configuracao.estado");
        });
        var selfScope = PersistentDataService.get('EstadoController');
        var filho = PersistentDataService.get('EstadoCrudController');
        if (angular.isDefined(filho)) {
            $timeout(function() {
                $state.go('configuracao.estado-crud', {
                    link: filho.data.estado.IdEstado > 0 ? 'editar' : 'novo'
                });
            }, 50);
        } else if (angular.isDefined(selfScope))
            angular.extend(vm, selfScope.data);
        // DO NOT TOUCH!!
    }
})();