<div id="ViagensController" ng-controller="ViagensController as vm">
    <form-header items="vm.headerItems" head="'Viagens'"></form-header>
    <div class="wrapper wrapper-content animated fadeIn mt-5 overflow-hidden" >
        <div class="row">
            <div class="col-lg-12">
                <div class="ibox float-e-margins">
                    <div style="height: 38px;" class="ibox-title">
                        <h5>Viagens</h5>
                        <div ibox-tools></div>
                    </div>
                    <div class="ibox-content">
                        <div class="row">
                             <div class="col-xs-12 col-md-4">
                                <div class="form-group">
                                    <label class="col-xs-12 col-md-4 control-label"
                                        style="text-align: right; padding-top: 10px;">Período:</label>
                                    <div class="input-group col-xs-12 col-md-8">
                                        <input date-range-picker class="form-control date-picker"
                                            ui-date-mask="DD/MM/YYYY - DD/MM/YYYY" type="text" ng-model="vm.date"
                                            options="vm.dateOptions" id="periodoDatePicker" />
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-xs-12 col-sm-12 col-md-4 col-lg-4">
                                <div class="form-group">
                                    <div class="form-group">
                                        <label class="col-xs-12 col-md-4 control-label"
                                             style="text-align: right; padding-top: 10px;">Código viagem interno:</label>
                                        </label>
                                        <div class="input-group col-xs-12 col-md-8">
                                            <input type="text" ng-required="true" name="codViagemInterno" ng-model="vm.codViagemInterno" class="form-control"/>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <consulta-padrao-modal tabledefinition="vm.consultaEmpresa" label="'Empresa:'"
                                idname="consultaEmpresa" placeholder="'Selecione uma empresa'"
                                directivesizes="'col-xs-12 col-sm-12 col-md-4 col-lg-4'"
                                labelsize="'col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label'"
                                ng-show="vm.isAdmin()">
                            </consulta-padrao-modal>


                            <div class="col-xs-12 col-sm-12 col-md-4 col-lg-4 pull-right">
                                <button tooltip-placement="down" type='button' uib-tooltip="Consultar" ng-click="vm.gridOptions.dataSource.refresh();"
                                    class="btn btn-labeled btn-primary pull-right">
                                    <span class="btn-label text-right"></span>
                                    <span class="pl-5 ">Consultar</span>
                                </button>
                            </div>
                        </div>
                        
                        <div class="row"></div>
                        <hr>
                        <div ui-grid="vm.gridOptions" ng-style="{height: vm.gridOptions.getGridHeight()}" class="grid" style="width: 100%;" 
                                                        ui-grid-pinning 
                                                        ui-grid-save-state 
                                                        ui-grid-pagination 
                                                        ui-grid-auto-resize 
                                                        ui-grid-resize-columns 
                                                        ui-grid-grouping>
                                                    </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>