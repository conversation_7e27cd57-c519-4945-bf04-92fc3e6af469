(function () {
    'use strict';

    angular.module('bbcWeb.painel-pedidos-pendentes.state', []).config(routeConfig);

    function routeConfig($stateProvider) {
        $stateProvider
            .state('painel-pedidos-pendentes', {
                url: "/painel-pedidos-pendentes",
                abstract: true,
                templateUrl: "app/layout/content.html"})
            .state('painel-pedidos-pendentes.index', {
                url: '/index',
                templateUrl: 'app/entities/painel-pedidos-pendentes/painel-pedidos-pendentes.html'});
    }
})();