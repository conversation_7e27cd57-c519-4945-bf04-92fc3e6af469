<div class="form-horizontal">
    <form id="formAbaViagem" name="formAbaViagem" novalidate ats-validator
        ng-submit="vm.adicionarViagem(formAbaViagem)">
        <div class="col-sm-12 col-md-12 col-lg-12">
            <div ng-show="vm.painelCiot.tipo!==3">
                <div class="row" ng-show="!vm.isVisualizar()">
                    <consulta-padrao-modal labelsize="'control-label col-xs-12 col-sm-3 col-md-4 col-lg-3'"
                        ng-disabled="((vm.isNew() && vm.disabledAgregado()) || vm.disabledFields())"
                        functionclear="vm.limpaInfosViagem" tabledefinition="vm.consultaViagem" idname="Viagem"
                        idmodel="Viagem" label="'Viagem:'" validate-on="blur" placeholder="'Selecione uma Viagem'">
                    </consulta-padrao-modal>
                </div>
                <div class="row">
                    <consulta-padrao-modal labelsize="'control-label col-xs-12 col-sm-3 col-md-4 col-lg-3'"
                        ng-disabled="((vm.isNew() && vm.disabledAgregado()) || vm.disabledFields())"
                        tabledefinition="vm.consultaCidadeOrigem" idname="Origem" idmodel="Origem"
                        label="'Cidade origem:'"
                        validate-on="blur" placeholder="'Selecione uma Cidade'" ng-required="vm.painelCiot.tipo === 1 && !vm.isVisualizar()"
                        required-message="'Cidade origem é obrigatório'">
                    </consulta-padrao-modal>
                    <consulta-padrao-modal labelsize="'control-label col-xs-12 col-sm-3 col-md-4 col-lg-3'"
                        ng-disabled="((vm.isNew() && vm.disabledAgregado()) || vm.disabledFields())"
                        tabledefinition="vm.consultaCidadeDestino" idname="Destino" idmodel="Destino"
                        label="'Cidade destino:'"
                        placeholder="'Selecione uma Cidade'" ng-required="vm.painelCiot.tipo === 1 && !vm.isVisualizar()"
                        required-message="'Cidade destino é obrigatório'">
                    </consulta-padrao-modal>
                </div>
                <div class="row">
                    <consulta-padrao-modal labelsize="'control-label col-xs-12 col-sm-3 col-md-4 col-lg-3'"
                        ng-disabled="((vm.isNew() && vm.disabledAgregado()) || vm.disabledFields())"
                        tabledefinition="vm.consultaRemetente" idname="Remetente" idmodel="Remetente"
                        label="'Remetente:'"
                        placeholder="'Selecione um Remetente'" function="vm.cadastrarCliente" ngshowadd="true"
                        ng-required="vm.painelCiot.tipo === 1 && !vm.isVisualizar()" required-message="'Remetente é obrigatório'"></consulta-padrao-modal>
                    <consulta-padrao-modal labelsize="'control-label col-xs-12 col-sm-3 col-md-4 col-lg-3'"
                        ng-disabled="((vm.isNew() && vm.disabledAgregado()) || vm.disabledFields())"
                        tabledefinition="vm.consultaConsignatario" idname="Consignatario" idmodel="Consignatario"
                        label="'Consignatário:'" 
                        placeholder="'Selecione um Consignatário'" function="vm.cadastrarCliente" ngshowadd="true"
                        ng-required="vm.painelCiot.tipo === 1 && !vm.isVisualizar()" required-message="'Consignatário é obrigatório'"></consulta-padrao-modal>
                </div>
                <div class="row">
                    <consulta-padrao-modal labelsize="'control-label col-xs-12 col-sm-3 col-md-4 col-lg-3'"
                        ng-disabled="((vm.isNew() && vm.disabledAgregado()) || vm.disabledFields())"
                        tabledefinition="vm.consultaDestinatario" idname="Destinatário" idmodel="Destinatário"
                        label="'Destinatário:'"
                        placeholder="'Selecione um Destinatário'" function="vm.cadastrarCliente" ngshowadd="true"
                        ng-required="vm.painelCiot.tipo === 1 && !vm.isVisualizar()" required-message="'Destinatário é obrigatório'"></consulta-padrao-modal>
                    <consulta-padrao-modal labelsize="'control-label col-xs-12 col-sm-3 col-md-4 col-lg-3'"
                        ng-disabled="((vm.isNew() && vm.disabledAgregado()) || vm.disabledFields() || vm.disableFieldNaturezaCarga)"
                        tabledefinition="vm.consultaNaturezaCarga" idname="Natureza" idmodel="Natureza"
                        label="'Natureza carga:'"
                        placeholder="'Selecione uma Natureza Carga'" ng-required="vm.painelCiot.tipo === 1 && !vm.isVisualizar()"
                        required-message="'Natureza Carga é obrigatório'"></consulta-padrao-modal>
                </div>
                <div class="row">
                    <div class="col-sm-12 col-md-6 col-lg-6">
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label"><span
                                    class="text-danger mr-5"
                                    ng-show="vm.painelCiot.tipo === 1 && !vm.isVisualizar()">*</span>Peso da carga:</label>
                            <div class="input-group col-sm-6 col-md-8 col-lg-6">
                                <span class="input-group-addon">KG</span>
                                <div class="message-required-size">
                                    <input type="text" ats-price maxlength="9"
                                        ng-disabled="((vm.isNew() && vm.disabledAgregado()) || vm.disabledFields() || vm.disableFieldPesoCarga)"
                                        ng-model="vm.valoresViagem.peso" class="form-control" id="PesoCarga"
                                        validate-on="blur" name="PesoCarga"
                                        required-message="'Peso da carga é obrigatório'" ng-required="vm.painelCiot.tipo === 1 && !vm.isVisualizar()" />
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-12 col-md-6 col-lg-6">
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label"><span
                                    class="text-danger mr-5"
                                    ng-show="vm.painelCiot.tipo === 1 && !vm.isVisualizar()">*</span>Valor do frete:</label>
                            <div class="input-group col-sm-6 col-md-8 col-lg-6">
                                <span class="input-group-addon">R$</span>
                                <div class="message-required-size">
                                    <input type="text" ng-model="vm.valoresViagem.valorFrete" ats-price
                                        validate-on="blur"
                                        ng-disabled="((vm.isNew() && vm.disabledAgregado()) || vm.disabledFields() || vm.disableFieldValorFrete)"
                                        class="form-control" id="ValorFrete" name="ValorFrete"
                                        required-message="'Valor do frete é obrigatório'" ng-required="vm.painelCiot.tipo === 1 && !vm.isVisualizar()" />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-12 col-md-6 col-lg-6">
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label">Valor dos impostos:</label>
                            <div class="input-group col-sm-6 col-md-8 col-lg-6">
                                <span class="input-group-addon">R$</span>
                                <input type="text" ng-model="vm.valoresViagem.valorImposto" ats-price validate-on="blur"
                                    ng-disabled="((vm.isNew() && vm.disabledAgregado()) || vm.disabledFields())"
                                    class="form-control" />
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-12 col-md-6 col-lg-6">
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label">Valor das despesas:</label>
                            <div class="input-group col-sm-6 col-md-8 col-lg-6">
                                <span class="input-group-addon">R$</span>
                                <input type="text" ng-model="vm.valoresViagem.valorDespesas" ats-price
                                    ng-disabled="((vm.isNew() && vm.disabledAgregado()) || vm.disabledFields())"
                                    class="form-control" />
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-12 col-md-6 col-lg-6">
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label">Valor do combustível:</label>
                            <div class="input-group col-sm-6 col-md-8 col-lg-6">
                                <span class="input-group-addon">R$</span>
                                <input type="text" ng-model="vm.valoresViagem.valorCombustivel" ats-price
                                    ng-disabled="((vm.isNew() && vm.disabledAgregado()) || vm.disabledFields())"
                                    class="form-control" />
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-12 col-md-6 col-lg-6">
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label">Valor dos pedágios:</label>
                            <div class="input-group col-sm-6 col-md-8 col-lg-6">
                                <span class="input-group-addon">R$</span>
                                <input type="text" ng-model="vm.valoresViagem.valorPedagio" ats-price
                                    ng-disabled="((vm.isNew() && vm.disabledAgregado()) || vm.disabledFields())"
                                    class="form-control" />
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row" ng-show="vm.mostraListaViagens">
                    <div class="ibox-title" style="margin-bottom: 11px;">
                        <div class="pull-left">
                            <button type="submit"
                                ng-disabled="((vm.isNew() && vm.disabledAgregado()) || (vm.isRetificar() && !vm.disabledAgregado()) || vm.disabledFields())"
                                class="mr-5 btn-labeled btn btn-info">
                                <span class="btn-label text-left"><i class="fa fa-plus"></i></span>
                                <span class="pl-5">Adicionar Viagem</span>
                            </button>
                        </div>
                        <div class="form-group" style="padding-left: 135px;">
                            <button ng-click="vm.limpar()"
                                ng-disabled="((vm.isNew() && vm.disabledAgregado()) || vm.disabledFields())"
                                type='button' class="mr-5 btn-labeled btn btn-danger">
                                <span class="pl-3">Limpar</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row" ng-show="vm.mostraListaViagens">
                <div class="table-responsive">
                    <table class="table table-striped col-xs-12">
                        <thead>
                            <tr>
                                <th scope="col">Cidade origem</th>
                                <th scope="col">Cidade destino</th>
                                <th scope="col" ng-show="!vm.isVisualizar() && vm.painelCiot.tipo != 3">Remetente</th>
                                <th scope="col" ng-show="!vm.isVisualizar() && vm.painelCiot.tipo != 3">Consignatário</th>
                                <th scope="col" ng-show="!vm.isVisualizar() && vm.painelCiot.tipo != 3">Destinatário</th>
                                <th scope="col">Natureza da carga</th>
                                <th scope="col">Peso da carga</th>
                                <th scope="col" ng-show="!vm.isVisualizar() && vm.painelCiot.tipo != 3">Valor do frete</th>
                                <th scope="col" ng-show="!vm.isVisualizar() && vm.painelCiot.tipo != 3">Valor do impostos</th>
                                <th scope="col" ng-show="!vm.isVisualizar() && vm.painelCiot.tipo != 3">Valor da despesas</th>
                                <th scope="col" ng-show="!vm.isVisualizar() && vm.painelCiot.tipo != 3">Valor do combustível</th>
                                <th scope="col" ng-show="!vm.isVisualizar() && vm.painelCiot.tipo != 3">Valor do pedágio</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr ng-repeat="viagem in vm.painelCiot.viagensList">
                                <td>{{viagem.nomeOrigem ? viagem.nomeOrigem : viagem.cidadeOrigem.nome}}</td>
                                <td>{{viagem.nomeDestino ? viagem.nomeDestino : viagem.cidadeDestino.nome}}</td>
                                <td ng-show="!vm.isVisualizar() && vm.painelCiot.tipo != 3">{{viagem.nomeRemetente}}</td>
                                <td ng-show="!vm.isVisualizar() && vm.painelCiot.tipo != 3">{{viagem.nomeConsignatario}}</td>
                                <td ng-show="!vm.isVisualizar() && vm.painelCiot.tipo != 3">{{viagem.nomeDestinatario}}</td>
                                <td>{{viagem.codigoNaturezaCarga}}</td>
                                <td>{{viagem.peso}}</td>
                                <td ng-show="!vm.isVisualizar() && vm.painelCiot.tipo != 3">{{viagem.valorFrete}}</td>
                                <td ng-show="!vm.isVisualizar() && vm.painelCiot.tipo != 3">{{viagem.valorImposto}}</td>
                                <td ng-show="!vm.isVisualizar() && vm.painelCiot.tipo != 3">{{viagem.valorDespesas}}</td>
                                <td ng-show="!vm.isVisualizar() && vm.painelCiot.tipo != 3">{{viagem.valorCombustivel}}</td>
                                <td ng-show="!vm.isVisualizar() && vm.painelCiot.tipo != 3">{{viagem.valorPedagio}}</td>
                                <td class="text-center" style="vertical-align: middle">
                                    <button type="button"
                                        ng-disabled="((vm.isNew() && vm.disabledAgregado()) || vm.disabledFields())"
                                        uib-tooltip="Remover" class="btn btn-xs btn-danger"
                                        ng-click="vm.removerViagem(this)">
                                        <i class="fa fa-trash-o"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </form>
</div>