(function () {
    'use strict';

    angular.module('bbcWeb.cliente.state', []).config(routeConfig);

    function routeConfig($urlRouterProvider, $stateProvider) {
        $stateProvider.state('cliente', {
            abstract: true,
            url: "/cliente",
            templateUrl: "app/layout/content.html"
        }).state('cliente.index', {
            url: '/index',
            templateUrl: 'app/entities/cliente/cliente.html'
        }).state('cliente.cliente-crud', {
            url: '/:link',
            templateUrl: 'app/entities/cliente/cliente-crud.html'
        });
    }
})();