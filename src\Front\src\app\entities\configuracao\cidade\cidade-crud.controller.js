(function() {
    'use strict';

    angular.module('bbcWeb')
        .controller('CidadeCrudController', CidadeCrudController);

    CidadeCrudController.$inject = [
        '$scope',
        'PersistentDataService',
        'toastr',
        'BaseService',
        'TipoCarreta',
        '$state',
        '$stateParams',
        '$window',
        '$rootScope',
        '$timeout'
    ];

    function CidadeCrudController(
        $scope,
        PersistentDataService,
        toastr,
        BaseService,
        TipoCarreta,
        $state,
        $stateParams,
        $window,
        $rootScope,
        $timeout) {

        //Inicialização dos objetos e arrays
        var vm = this;
        vm.cidade = {};
        vm.perfilUsuarioLogado = $rootScope.usuarioLogado.perfil;

        if ($stateParams.link == 'novo')
            vm.cidade.IdCidade = 'Auto';
        vm.isNew = function() {
            return $stateParams.link == 'novo';
        };
        vm.headerItems = [{
                name: 'Administração'
            },
            {
                name: 'Cidade',
                link: 'configuracao.cidade'
            },
            {
                name: $stateParams.link == 'novo' ? 'Novo' : 'Editar'
            }
        ];

        vm.loadEdit = function(id) {
            vm.carregarPaises(function() {
                BaseService.get('CidadeAts', 'ConsultarPorId', {
                    idCidade: id
                }).then(function(response) {
                    vm.cidade = angular.fromJson(response.data);
                    vm.carregarEstados(vm.cidade.IdPais);
                });
            });
        };

        vm.load = function() {
            vm.carregarPaises();
        };

        //Selects de endereço
        vm.carregarPaises = function(callback) {
            vm.selectEstados = [];
            BaseService.get('PaisAts', 'Consultar').then(function(response) {
                if (response.success)
                    vm.selectPaises = response.data;

                if (angular.isFunction(callback))
                    callback();
            });
        };

        vm.carregarEstados = function(idPais){
            BaseService.get('EstadoAts', 'Consultar', {IdPais : idPais}).then(function(response){
                vm.selectEstados = response.data;
            });
        }

        vm.onChangePais = function(idPais){
            vm.cidade.IdEstado = undefined;
            vm.carregarEstados(idPais);
        }

        //Salvar e atualizar
        vm.save = function() {
            BaseService.post('CidadeAts', $stateParams.link === 'novo' ? 'Cadastrar' : 'Editar', vm.cidade)
                .then(function(response) {
                    if (response.success) {
                        toastr.success(response.message);
                        $state.go('configuracao.cidade');
                    } else {
                        toastr.error(response.message);
                    }
                });
        };

        // DO NOT TOUCH!! 666
        $scope.$on('$stateChangeStart', function(_, toState) {
            if (toState.name === 'configuracao.cidade')
                PersistentDataService.remove('CidadeCrudController');
            else
                PersistentDataService.store('CidadeCrudController', vm, "Cidade", null, "configuracao.cidade-crud", vm.cidade.IdCidade);
        });
        var selfScope = PersistentDataService.get('CidadeCrudController');
        if (angular.isDefined(selfScope)) {
            angular.extend(vm, selfScope.data);
        } else {
            if (!vm.isNew())
                vm.loadEdit($stateParams.link);
            else
                vm.load();
        }

        $timeout(function() {
            PersistentDataService.remove('CidadeController');
        }, 15);
    }
})();