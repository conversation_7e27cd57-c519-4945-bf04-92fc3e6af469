<div>
    <style>
        .custom-modal-width .modal-dialog {
            width: 60%; /* Largura desejada da modal */
            max-width: none; /* Para garantir que a largura personalizada funcione corretamente */
        }

        textarea:hover {
            cursor: pointer;
        }
    </style>
    <form name="formPortador" novalidate show-validation>
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" ng-click="$dismiss()" aria-label="Close">
                    <em class="fa fa-times"></em>
                </button>
                <h4 class="modal-title">Detalhes da Transação</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label"
                                style="text-align: right;">C<PERSON>digo da Transacao:</label>
                            </label>
                            <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                                <input type="text" ng-disabled="true" ng-model="vm.idTransacao" class="form-control" />
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label"
                                style="text-align: right;">Código Response Dock:</label>
                            </label>
                            <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                                <input type="text" ng-disabled="true" ng-model="vm.responseCodeDock"
                                    class="form-control" />
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label"
                                style="text-align: right;">Data/Hora Requisição:</label>
                            <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                                <input type="text" ng-disabled="true" ng-model="vm.dataHoraRequisicao"
                                    class="form-control" />
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label"
                                style="text-align: right;">Data/Hora Resposta:</label>
                            <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                                <input type="text" ng-disabled="true" ng-model="vm.dataHoraResposta"
                                    class="form-control" />
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-xs-12 col-md-6">
                        <div class="form-group">
                            <label class="control-label col-xs-12 col-md-3">Json envio Dock:</label>
                            <div class="input-group col-xs-12 col-md-9">
                                <a ng-click="vm.jsonEnvioDock ? vm.copiar(1) : null">
                                    <textarea style="resize: none;" type="text" ng-disabled="!vm.jsonEnvioDock"
                                        tooltip-placement="left" uib-tooltip="{{vm.jsonEnvioDock ? 'Copiar json envio' : ''}}"
                                        validate-on="blur" multiple maxlength="10000" readonly name="jsonEnvioDock" 
                                        rows="25"
                                        class="form-control ng-pristine ng-valid ng-empty ng-valid-maxlength ng-touched user-success">{{ vm.jsonEnvioDock ? (vm.prettyPrint(vm.jsonEnvioDock) || '') : '' }}
                                    </textarea>
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12 col-md-6">
                        <div class="form-group">
                            <label class="control-label col-xs-12 col-md-3">Json resposta Dock:</label>
                            <div class="input-group col-xs-12 col-md-9">
                                <a ng-click="vm.jsonRespostaDock ? vm.copiar(2) : null">
                                    <textarea style="resize: none;" type="text" ng-disabled="!vm.jsonRespostaDock"
                                        tooltip-placement="left" uib-tooltip="{{vm.jsonRespostaDock ? 'Copiar json resposta' : ''}}"
                                        validate-on="blur" multiple maxlength="10000" readonly name="jsonRespostaDock"
                                        rows="25"
                                        class="form-control ng-pristine ng-valid ng-empty ng-valid-maxlength ng-touched user-success">{{ vm.jsonRespostaDock ? (vm.prettyPrint(vm.jsonRespostaDock) || '') : '' }}
                                    </textarea>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>