<div ng-controller="NotificacaoPushCrudController as vm">
    <form-header items="vm.headerItems" head="'Notificação Push'"></form-header>
    <div class="wrapper wrapper-content animated fadeIn mb-0 pb-0">
        <div class="row">
            <div class="col-lg-12">
                <div class="ibox">
                    <div class="ibox-title">
                        <h5>{{vm.isNew() ? 'Nova' : 'Editar'}} notificação de push</h5>
                        <div ibox-tools></div>
                    </div>
                    <div class="ibox-content wizard">
                        <form name="formNotificacaoPush" role="form" novalidate ng-submit="vm.save()" show-validation>
                            <div form-wizard steps="2">
                                <div class="form-wizard">
                                    <ol class="row">
                                        <li ng-class="{'active':wizard.active(1)}" ng-click="wizard.go(1)" class="col-sm-6 col-md-6 col-lg-6">
                                            <h4>Principal</h4>
                                        </li>
                                        <li ng-class="{'active':wizard.active(2)}" ng-click="wizard.go(2)" class="col-sm-6 col-md-6 col-lg-6">
                                            <h4>IMEIS</h4>
                                        </li>
                                    </ol>
                                    <div ng-show="wizard.active(1)">
                                        <div class="form-horizontal">
                                            <hr/>
                                            <div class="col-sm-12">
                                                <div class="row">
                                                    <div class="col-sm-12 col-md-6 col-lg-6">
                                                        <div class="form-group">
                                                            <label class="col-sm-3 col-md-4 col-lg-3 control-label">Código:</label>
                                                            <div class="input-group col-sm-2 col-md-2 col-lg-2">
                                                                <input type="text" ng-model="vm.notificacaoPush.IdNotificacaoPush" class="form-control" disabled value="{{vm.isNew()}}" />
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div id="blockEmpresa" class="col-sm-12 col-md-6 col-lg-6" ng-if="vm.componenteVisivel('blockEmpresa')">
                                                        <div class="form-group">
                                                            <label class="col-sm-3 col-md-4 col-lg-3 control-label"><span class="text-danger mr-5">*</span>Empresa:</label>
                                                            <div class="input-group col-sm-9 col-md-8 col-lg-9">
                                                                <ui-select id="empresa" name="empresa" ng-model="vm.empresaSelected" ng-change="vm.forEachFunc(['onEmpresaChange','updateFiliais','updateTiposNotificacao','updateGruposUsuario','updateHardwaresIMEI', 'resetarGridIMEIS']);"
                                                                    required>
                                                                    <ui-select-match placeholder="Selecione a empresa">
                                                                        <span>{{$select.selected.RazaoSocial}}</span>
                                                                        <div id="clearEmpresa" class="fa fa-close m-l-n ui-select-vas" ng-click="vm.clear($event)"></div>
                                                                    </ui-select-match>
                                                                    <ui-select-choices repeat="empresa.IdEmpresa as empresa in vm.empresas | propsFilter: {RazaoSocial: $select.search, CNPJ: $select.search}">
                                                                        <div ng-bind-html="empresa.RazaoSocial | highlight: $select.search"></div>
                                                                    </ui-select-choices>
                                                                </ui-select>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div id="blockFilial" class="col-sm-12 col-md-6 col-lg-6" ng-if="vm.componenteVisivel('blockFilial')">
                                                        <div class="form-group">
                                                            <label class="col-sm-3 col-md-4 col-lg-3 control-label">Filial:</label>
                                                            <div class="input-group col-sm-9 col-md-8 col-lg-9">
                                                                <ui-select id="filial" name="filial" ng-model="vm.filialSelected" ng-change="vm.forEachFunc(['onFilialChange','updateTiposNotificacao','updateHardwaresIMEI']);">
                                                                    <ui-select-match placeholder="Selecione a filial">
                                                                        <span>{{$select.selected.NomeFantasia}}</span>
                                                                        <div id="clearFilial" class="fa fa-close m-l-n ui-select-vas" ng-click="vm.clear($event)"></div>
                                                                    </ui-select-match>
                                                                    <ui-select-choices repeat="filial.IdFilial as filial in vm.filiais | propsFilter: {NomeFantasia: $select.search}">
                                                                        <div ng-bind-html="filial.NomeFantasia | highlight: $select.search"></div>
                                                                    </ui-select-choices>
                                                                </ui-select>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div id="blockGrupoUsuario" class="col-sm-12 col-md-6 col-lg-6" ng-if="vm.componenteVisivel('blockGrupoUsuario')">
                                                        <div class="form-group">
                                                            <label class="col-sm-3 col-md-4 col-lg-3 control-label"><span class="text-danger mr-5">*</span>Grupo de usuário:</label>
                                                            <div class="input-group col-sm-9 col-md-8 col-lg-9">
                                                                <ui-select multiple id="grupoUsuario" name="grupoUsuario" ng-model="vm.grupoUsuarioSelected" required>
                                                                    <ui-select-match placeholder="Selecione o grupo de usuário">
                                                                        <span>{{$item.Descricao}}</span>
                                                                    </ui-select-match>
                                                                    <ui-select-choices repeat="grupoUsu in vm.gruposUsuario | propsFilter: {Descricao: $select.search}" ui-disable-choice="vm.grupoUsuarioJaSelecionado(grupoUsu.IdGrupoUsuario);">
                                                                        <div ng-bind-html="grupoUsu.Descricao | highlight: $select.search"></div>
                                                                    </ui-select-choices>
                                                                </ui-select>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div id="blockTipoNotificacao" class="col-sm-12 col-md-6 col-lg-6" ng-if="vm.componenteVisivel('blockTipoNotificacao')">
                                                        <div class="form-group">
                                                            <label class="col-sm-3 col-md-4 col-lg-3 control-label"><span class="text-danger mr-5">*</span>Tipo de notificação:</label>
                                                            <div class="input-group col-sm-9 col-md-8 col-lg-9">
                                                                <ui-select ng-model="vm.tipoNotificacaoSelected" required>
                                                                    <ui-select-match placeholder="Selecione o tipo de notificação">
                                                                        <span>{{$select.selected.Descricao}}</span>
                                                                        <div id="clearTipoNotificacao" class="fa fa-close m-l-n ui-select-vas" ng-click="vm.clear($event)"></div>
                                                                    </ui-select-match>
                                                                    <ui-select-choices repeat="tpNot.IdTipoNotificacao as tpNot in vm.tiposNotificacao | propsFilter: {Descricao: $select.search}">
                                                                        <div ng-bind-html="tpNot.Descricao | highlight: $select.search"></div>
                                                                    </ui-select-choices>
                                                                </ui-select>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-sm-12 col-md-6 col-lg-6">
                                                        <div class="form-group">
                                                            <label class="col-sm-3 col-md-4 col-lg-3 control-label"><span class="text-danger mr-5">*</span>Mensagem notificação:</label>
                                                            <div class="input-group col-sm-9 col-md-8 col-lg-9">
                                                                <textarea style="height: 85px" type="text" ng-model="vm.notificacaoPush.DescricaoMensagem" class="form-control" required/>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-sm-12 col-md-6 col-lg-6">
                                                        <div class="form-group">
                                                            <label class="col-sm-3 col-md-4 col-lg-3 control-label"><span class="text-danger mr-5">*</span>Descrição:</label>
                                                            <div class="input-group col-sm-9 col-md-8 col-lg-9">
                                                                <input type="text" id="Descricao" maxlength="100" name="Descricao" ng-model="vm.notificacaoPush.Descricao" class="form-control"
                                                                    required/>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-sm-12 col-md-6 col-lg-6">
                                                        <div class="form-group">
                                                            <label class="col-sm-3 col-md-4 col-lg-3 control-label"><span class="text-danger mr-5">*</span>Execução:</label>
                                                            <div class="input-group col-sm-9 col-md-8 col-lg-9">
                                                                <ui-select ng-model="vm.notificacaoPush.MomentoExecucao" ng-change="vm.onChangeMomentoExecucao();" required>
                                                                    <ui-select-match>
                                                                        <span>{{$select.selected.Descricao}}</span>
                                                                    </ui-select-match>
                                                                    <ui-select-choices repeat="ex.IdExecucao as ex in vm.momentosExecucao | propsFilter: {Descricao: $select.search}">
                                                                        <div ng-bind-html="ex.Descricao | highlight: $select.search"></div>
                                                                    </ui-select-choices>
                                                                </ui-select>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-sm-12 col-md-6 col-lg-6">
                                                        <div class="form-group">
                                                            <label class="col-sm-3 col-md-4 col-lg-3 control-label"><span class="text-danger mr-5">*</span>SQL(Select):</label>
                                                            <div class="input-group col-sm-9 col-md-8 col-lg-9">
                                                                <textarea style="height: 250px" type="text" ng-model="vm.notificacaoPush.Sql" class="form-control" required/>
                                                            </div>
                                                            <div class="btn-group btnAcoesNotificacaoPush" uib-dropdown>
                                                                <button type="button" class="btn btn-primary" uib-dropdown-toggle>
                                                                    Testar <span class="caret"></span>
                                                                </button>
                                                                <ul role="menu" uib-dropdown-menu="">
                                                                    <li><a href="" ng-click="vm.btnAcoesTestar.mensagem();">Mensagem</a></li>
                                                                    <li><a href="" ng-click="vm.btnAcoesTestar.sql();">SQL</a></li>
                                                                    <li ng-if="!vm.isNew()"><a href="" ng-click="vm.btnAcoesTestar.push();">Push</a></li>
                                                                </ul>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row"></div>
                                            <hr/>
                                            <div class="form-group">
                                                <div class="col-md-12 col-lg-12 text-right">
                                                    <button type="button" ui-sref="gestao-logistica-notificacao-push.notificacao" class="btn btn-labeled btn-default">
                                                      <span class="btn-label"><i class="fa fa-arrow-circle-left"></i></span>
                                                      Voltar
                                                    </button>
                                                    <button focusable type="button" ng-click="wizard.go(2);" class="btn btn-labeled btn-default text-right">
                                                        Avançar
                                                        <span class="btn-label"><i class="fa fa-arrow-circle-right"></i></span>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div ng-show="wizard.active(2)">
                                        <div class="form-horizontal">
                                            <div class="row">
                                                <div class="col-sm-12 col-md-12 col-lg-12">
                                                    <div class="form-group">
                                                        <div class="col-sm-4">
                                                            <label class="col-sm-2 control-label">IMEI:</label>
                                                            <div class="input-group col-sm-10">
                                                                <ui-select ng-model="vm.hardwareIMEISelected" ng-change="">
                                                                    <ui-select-match placeholder="Selecione o IMEI">
                                                                        <span>{{$select.selected.Identificacao}}</span>
                                                                        <div id="clearHardwaresIMEI" class="fa fa-close m-l-n ui-select-vas" ng-click="vm.clear($event)"></div>
                                                                    </ui-select-match>
                                                                    <ui-select-choices ui-disable-choice="vm.gridIMEISJaExisteImei(hw.Identificacao)" repeat="hw.IdHardware as hw in vm.hardwaresIMEI | propsFilter: {Identificacao: $select.search}">
                                                                        <div ng-bind-html="hw.Identificacao | highlight: $select.search"></div>
                                                                    </ui-select-choices>
                                                                </ui-select>
                                                            </div>
                                                        </div>
                                                        <div class="col-sm-4">
                                                            <label class="col-sm-3 control-label">Apelido:</label>
                                                            <div class="input-group col-sm-9">
                                                                <input type="text" ng-model="vm.hardwareIMEISelectedApelido" class="form-control" />
                                                            </div>
                                                        </div>
                                                        <div class="col-xs-1">
                                                            <button type="button" ng-click="vm.gridIMEISAdd();" ng-disabled="vm.hardwareIMEISelected === undefined || vm.hardwareIMEISelectedApelido === ''"
                                                                class="btn btn-labeled btn-primary btn-sm">
                                                                <i class="fa fa-plus"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-sm-12">
                                                <div class="row">
                                                    <div class="col-sm-12 col-md-6 col-lg-6">
                                                        <div class="form-group">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <ul class="todo-list col-lg-12">
                                                <li ng-repeat="item in vm.gridImeisApelidos">
                                                    <span>{{item.IMEI}} - {{item.Apelido}}</span>
                                                    <button type="button" class="btn btn-danger btn-xs floatRight" ng-click="vm.deletarImeiTabela($index)">
                                                        <span class="btn-label"><i class="fa fa-close"></i></span>
                                                    </button>
                                                </li>
                                            </ul>
                                            <div class="row"></div>
                                            <hr/>
                                            <div class="form-group">
                                                <div class="col-md-12 col-lg-12 text-right">
                                                    <button type="button" ng-click="wizard.go(1);" class="btn btn-labeled btn-default">
                                                      <span class="btn-label"><i class="fa fa-arrow-circle-left"></i></span>
                                                      Voltar
                                                    </button>

                                                    <button type="submit" ng-disabled="formNotificacaoPush.$invalid" class="btn btn-labeled btn-success text-right">
                                                        <span class="btn-label"><i class="fa fa-check-circle"></i></span>
                                                        Salvar
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>