<div ng-controller="MenuCrudController as vm">
    <form-header ng-submit="return" items="[{name: 'Cadastro<PERSON>'}, {name: '<PERSON>u',link: 'menu.index'}, {name: vm.isNew() ? 'Novo' : 'Editar'}]" head="'Menu'"></form-header>
    <div class="wrapper wrapper-content animated fadeIn mb-0 pb-0">
        <div class="row">
            <div class="col-lg-12">
                <div class="ibox animated fadeIn">
                    <div class="ibox-title">
                        <h5>{{vm.isNew() ? 'Novo' : 'Editar'}} menu</h5>
                        <div ibox-tools></div>
                    </div>
                    <div class="ibox-content wizard">
                        <form name="frmMenuCadastro" novalidate ats-validator role="form" ng-submit="vm.save(frmMenuCadastro)" show-validation>
                            <div form-wizard steps="4">
                                <div class="form-wizard">
                                    <ol class="row">
                                        <div class="form-horizontal">
                                            <hr />
                                            <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
                                                <div class="row">
                                                    <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                                                        <div class="form-group">
                                                            <label class="col-xs-3 col-sm-2 col-md-4 col-lg-3 control-label">Código:</label>
                                                            <div class="input-group col-xs-8 col-sm-10 col-md-8 col-lg-9">
                                                                <input type="text" ng-model="vm.menu.Id" class="form-control" disabled value="{{vm.isNew()}}" />
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                                                        <div class="form-group">
                                                            <label class="col-xs-3 col-sm-2 col-md-4 col-lg-3 control-label">Link:</label>
                                                            <div class="input-group col-xs-8 col-sm-10 col-md-8 col-lg-9">
                                                                <input type="text" ng-model="vm.menu.Link" name="Link" class="form-control" validate-on="blur"/>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                                                        <div class="form-group">
                                                            <label class="col-xs-3 col-sm-2 col-md-4 col-lg-3 control-label">
                                                                <span class="text-danger mr-5">*</span>Descrição:</label>
                                                            <div class="input-group col-xs-8 col-sm-10 col-md-8 col-lg-9">
                                                                <input type="text" ng-disabled="!vm.isNew()" ng-model="vm.menu.Descricao" required-message="'Descrição é obrigatória'" name="Descricao" class="form-control" validate-on="blur" required/>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    
                                                    <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                                                        <div class="form-group">
                                                            <label class="col-xs-3 col-sm-2 col-md-4 col-lg-3 control-label">Menu pai:</label>
                                                            <div class="input-group col-xs-8 col-sm-10 col-md-8 col-lg-9">
                                                                <input type="text" maxlength="100" ng-model="vm.menu.MenuPai" name="Menu pai" class="form-control" validate-on="blur"/>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                                                        <div class="form-group">
                                                            <label class="col-xs-3 col-sm-2 col-md-4 col-lg-3 control-label">
                                                                <span class="text-danger mr-5">*</span>Módulo:</label>
                                                            <div class="input-group col-xs-8 col-sm-10 col-md-8 col-lg-9">
                                                                <input type="text" maxlength="100" ng-model="vm.menu.Modulo" required-message="'Módulo é obrigatório'" name="Razão social" class="form-control" validate-on="blur" required/>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                                                        <div class="form-group">
                                                            <label class="col-xs-3 col-sm-2 col-md-4 col-lg-3 control-label">Menu Pai:</label>
                                                            <div class="input-group col-xs-8 col-sm-10 col-md-8 col-lg-9">
                                                                <toggle-switch on-label="Sim" off-label="Não" ng-model="vm.menu.IsMenuPai" class="switch-small">
                                                                </toggle-switch>
                                                            </div>
                                                        </div>
                                                        
                                                    </div>
                                                </div>        
                                            </div>
                                        </div>
                                    </ol>
                                </div>                            
                                <hr />
                                <div ng-show="vm.carregandoEdit">
                                    <div class="spiner-example">
                                        <div class="sk-spinner sk-spinner-wave">
                                            <div class="sk-rect1"></div>
                                            <div class="sk-rect2"></div>
                                            <div class="sk-rect3"></div>
                                            <div class="sk-rect4"></div>
                                            <div class="sk-rect5"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="form-group">
                                    <div class="col-md-12 col-lg-12 text-right">
                                        <hr />
                                        <button type="button" ng-disabled="vm.saving" ng-click="wizard.getActivePosition() == 1 ? $state.go('autorizadora.index')  : wizard.go(wizard.getActivePosition() - 1)"
                                            class="btn btn-labeled btn-default">
                                            <span class="btn-label">
                                                <i class="fa fa-arrow-circle-left"></i>
                                            </span>
                                            Voltar
                                        </button>
                                        <button ng-disabled="vm.saving" type="submit" class="btn btn-labeled btn-success text-right ladda-button" ladda="loading" data-style="expand-right">
                                            <span class="btn-label">
                                                <i class="fa fa-check-circle"></i>
                                            </span>
                                            Salvar
                                        </button>
                                    </div>
                                </div>
                            </div>                            
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
</div>