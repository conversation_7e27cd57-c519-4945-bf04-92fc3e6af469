<div class="form-horizontal">
    <hr />
    <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-12 col-md-4 col-lg-3">
                        Código:
                    </label>
                    <div class="input-group col-xs-12 col-sm-12 col-md-3 col-lg-3">
                        <input type="text" ng-model="vm.usuario.id" class="form-control" disabled
                            value="{{vm.isNew()}}" />
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6 pull-right">
                <div>
                    <center>
                        <img ng-show="!vm.imagemUpload.filename" style="height: 128px;width: 128px;"
                            ng-src="data:image/png;base64,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" />
                        <img ng-show="vm.imagemUpload.filename" style="height: 128px;width: 128px;"
                            ng-src="data:{{ vm.imagemUpload.filetype }};base64,{{ vm.imagemUpload.base64 }}" />
                        <div style="margin-bottom: 10px;">
                            <label style="margin-top: 2%;" tooltip-placement="left"
                                uib-tooltip="Apenas arquivos nos formatos JPG, JPEG,PNG e GIF são aceitos!"
                                title="{{vm.imagemSelecionadaInput == null ? 'Selecione (Máximo 512Kb)' : vm.imagemSelecionadaInput }}"
                                for="Foto" class="sllipse btn btn-warning btn-sm">Selecione
                                <input id="Foto" name="foto" type="file" ng-model="vm.imagemUpload"
                                    do-not-parse-if-oversize base-sixty-four-input onload="vm.onLoadImageUploader"
                                    ng-show="false" accept="image/png, image/jpeg, image/gif">
                            </label>
                            <button type="button" style="margin-top: 2%;" tooltip-placement="right"
                                uib-tooltip="Remover imagem"
                                ng-click="vm.imagemUpload = undefined; vm.usuario.foto = undefined; vm.usuario.imagemPerfilB64 = undefined; vm.imagemSelecionadaInput = undefined;"
                                class="btn btn-default btn-sm">
                                <i class="fa fa-close"></i>
                            </button>
                        </div>
                    </center>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="row">
                    <div class="col-xs-12">
                        <div class="form-group">
                            <label class="control-label col-xs-3 col-sm-2 col-md-4 col-lg-3">
                                <span class="text-danger mr-5">*</span>Nome:
                            </label>
                            <div class="input-group col-xs-12 col-sm-10 col-md-8 col-lg-9">
                                <input type="text" name="nome" maxlength="200" class="form-control" validate-on="blur"
                                    ng-model="vm.usuario.nome" required-message="'Nome é obrigatório'" required />
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-xs-12">
                        <div class="form-group">
                            <label class="control-label col-xs-3 col-sm-2 col-md-4 col-lg-3">
                                <span class="text-danger mr-5">*</span>CPF:
                            </label>
                            <div class="input-group col-xs-12 col-sm-10 col-md-8 col-lg-9">
                                <input type="text" name="CPF" class="form-control" ui-br-cpf-mask validate-on="blur"
                                    ng-disabled="!vm.isNew()" ng-model="vm.usuario.cpf"
                                    required-message="'CPF é obrigatório'" required />
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-xs-12">
                        <div class="form-group">
                            <label class="control-label col-xs-12 col-sm-2 col-md-4 col-lg-3">
                                <span class="text-danger mr-5">*</span>E-mail:
                            </label>
                            <div class="input-group col-xs-12 col-sm-10 col-md-8 col-lg-9">
                                <input type="email" name="Email" id="email" maxlength="200" class="form-control"
                                    validate-on="blur" ng-blur="vm.preencherLogin()" ng-model="vm.usuario.email"
                                    required-message="'E-mail é obrigatório'" required
                                    invalid-message="'E-mail é inválido'" />
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-xs-12">
                        <div class="form-group">
                            <label class="control-label col-xs-3 col-sm-2 col-md-4 col-lg-3">
                                <span class="text-danger mr-5">*</span>Login:
                            </label>
                            <div class="input-group col-xs-12 col-sm-10 col-md-8 col-lg-9">
                                <input type="text" name="Login" maxlength="100" class="form-control" validate-on="blur"
                                    ng-disabled="!vm.isNew()" ng-model="vm.usuario.login"
                                    required-message="'Login é obrigatório'" required />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row" ng-show="!vm.isNew()">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="control-label col-xs-3 col-sm-2 col-md-4 col-lg-3">
                        <span class="text-danger mr-5" ng-show="vm.isNew()">*</span>Senha:
                    </label>
                    <div class="input-group col-xs-12 col-sm-10 col-md-8 col-lg-9">
                        <input type="password" autocomplete="new-password" name="Senha" maxlength="100"
                            class="form-control" validate-on="blur" ng-blur="vm.validarSenha()"
                            ng-model="vm.usuario.senha" />
                        <span class="input-group-addon">
                            <img class="imgPassCombination" tooltip-placement="left" ng-show="vm.validacaoSenhaSucesso"
                                uib-tooltip="Senha válida!"
                                src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAACEElEQVQ4T6WUTWsTURSG33MnmQQ063ZG/EJL6q5IKbgUf4AKpkGl1JUVxUIyBbf9AaaLlhaX4kKkMXbhzr9gt5IMfjSmmHRhRagp+Wju0Ttm0mQ6yTQ6y3vO+8x7zrn3EHy+c1uL0VptL8ng6yCaFJCGSpMQFTBvCkEbET2WLZ5frHnl5D0w89ZtJmQAOJB+H6NVBkKpnfGn6905h0BOaIZ9dhXguUEgH0dr5XhpHpRtqVgHaBQWng0Lc+EErJXHM486wHaZL4dx5s1lUFKVT2oA9frel6CeBf1MSnyLhRsXaDRvzRLheZCgO35Pn8Lbgw/YlVWPjGdoJJ9+I4huHhf4JHoN0/oE3h+U8GA/65FRjkYKVkkAp73Ak4jgF+o9xy7su6xibn8dRfnDI5NfySikmoAIdUfu6Jcxq085DrbkrhMKhqmLLxu+QCt6FQqqeqSgCX3CKbO/s792HGC/kl1oEy2EoQXC2hUWBw7FhQY5O2yXfE2Gbc2A8aLflO9HruBd0/YZgI+C6a5zsWv1n58Jmnncq+OXJ4HtmNYYc96yaaeTzPTqf4BgvlW5tJTrLAezYK0y8PBfoExY2Yln5nu2DTihmfaZ5eGhvFyJb6ePrC/X2WhhYZolLwmBU4Pcqp4J5pQqszvvyMZWwYsfH0eqrXACEDeA1qRsb28BUQbkJljbOBGq5z6NrfS+zT/i37yU582LPf+hAAAAAElFTkSuQmCC" />
                            <img class="imgPassCombination bgError" tooltip-placement="left"
                                ng-show="!vm.validacaoSenhaSucesso" uib-tooltip="{{ vm.validacaoSenhaMensagem }}"
                                src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAABsUlEQVQ4T62UzytEURTHv+c+jadkjxT5ce9b2Pk7/ChM0mSjFEX8HxQiCwtZSBjK32GnzH0ZDMJeMn7Mu0fvlmnMeM9g7vKecz7nfM+95xC+OVft7e6L6yYBDBBzHzM3h25E9MBEJ0x01JDP73fkci/l4VR+kVFqDMYsQggLiToGuHeY55Xv75X6FIEMOFqpNQBTcaCKipjXpe/PEhBYFZ8OGaU2omBOb691C05Pv81FzOvK92eKQCsT2ImqrOn42Joe+/sjiyfmZCif7AMkEpdxPasGaIC7RKHQSRmlJgBsxfWtGqCVy5wKgYcAhmoBNECatJQ3TNRWCyCMuaZMT887hKirEfCt9sBqJDdubloBT5OTsX/eALmqHuUXk3NAZ56XIubtuKCfJqUkdtx+7GfXvRBAyz8n5dYJgm47y2eelyTm3Shgw9ycNeWXl+OEDHtap4vLQUu5xkTTv+hXqeuqp/Xsl20Tri9fypU/QFeU1gsV6+sznZZyNCBaEkBrXLXEfMtE86HMUr+KjR0az7u66gPHGQmIBoUxfQDs9jZC3AvgBMBRXaGQ7s5mX8uTfgDCabpOjteIBgAAAABJRU5ErkJggg==" />
                        </span>
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-2 col-md-4 col-lg-3">
                        <span class="text-danger mr-5" ng-show="vm.isNew()">*</span>Confirmar senha:
                    </label>
                    <div class="input-group col-xs-12 col-sm-10 col-md-8 col-lg-9">
                        <input type="password" autocomplete="new-password" name="Confirmar senha" maxlength="100"
                            class="form-control" validate-on="blur" ng-model="vm.usuario.confirmarSenha" />
                        <span class="input-group-addon">
                            <img class="imgPassCombination" ng-show="vm.iconeErroSenha()" tooltip-placement="left"
                                uib-tooltip="Senha confirmada!"
                                src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAACEElEQVQ4T6WUTWsTURSG33MnmQQ063ZG/EJL6q5IKbgUf4AKpkGl1JUVxUIyBbf9AaaLlhaX4kKkMXbhzr9gt5IMfjSmmHRhRagp+Wju0Ttm0mQ6yTQ6y3vO+8x7zrn3EHy+c1uL0VptL8ng6yCaFJCGSpMQFTBvCkEbET2WLZ5frHnl5D0w89ZtJmQAOJB+H6NVBkKpnfGn6905h0BOaIZ9dhXguUEgH0dr5XhpHpRtqVgHaBQWng0Lc+EErJXHM486wHaZL4dx5s1lUFKVT2oA9frel6CeBf1MSnyLhRsXaDRvzRLheZCgO35Pn8Lbgw/YlVWPjGdoJJ9+I4huHhf4JHoN0/oE3h+U8GA/65FRjkYKVkkAp73Ak4jgF+o9xy7su6xibn8dRfnDI5NfySikmoAIdUfu6Jcxq085DrbkrhMKhqmLLxu+QCt6FQqqeqSgCX3CKbO/s792HGC/kl1oEy2EoQXC2hUWBw7FhQY5O2yXfE2Gbc2A8aLflO9HruBd0/YZgI+C6a5zsWv1n58Jmnncq+OXJ4HtmNYYc96yaaeTzPTqf4BgvlW5tJTrLAezYK0y8PBfoExY2Yln5nu2DTihmfaZ5eGhvFyJb6ePrC/X2WhhYZolLwmBU4Pcqp4J5pQqszvvyMZWwYsfH0eqrXACEDeA1qRsb28BUQbkJljbOBGq5z6NrfS+zT/i37yU582LPf+hAAAAAElFTkSuQmCC" />
                            <img class="imgPassCombination bgError" ng-show="!vm.iconeErroSenha()"
                                tooltip-placement="left"
                                uib-tooltip="Os valores para senha e a confirmação devem ser os mesmos!"
                                src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAABsUlEQVQ4T62UzytEURTHv+c+jadkjxT5ce9b2Pk7/ChM0mSjFEX8HxQiCwtZSBjK32GnzH0ZDMJeMn7Mu0fvlmnMeM9g7vKecz7nfM+95xC+OVft7e6L6yYBDBBzHzM3h25E9MBEJ0x01JDP73fkci/l4VR+kVFqDMYsQggLiToGuHeY55Xv75X6FIEMOFqpNQBTcaCKipjXpe/PEhBYFZ8OGaU2omBOb691C05Pv81FzOvK92eKQCsT2ImqrOn42Joe+/sjiyfmZCif7AMkEpdxPasGaIC7RKHQSRmlJgBsxfWtGqCVy5wKgYcAhmoBNECatJQ3TNRWCyCMuaZMT887hKirEfCt9sBqJDdubloBT5OTsX/eALmqHuUXk3NAZ56XIubtuKCfJqUkdtx+7GfXvRBAyz8n5dYJgm47y2eelyTm3Shgw9ycNeWXl+OEDHtap4vLQUu5xkTTv+hXqeuqp/Xsl20Tri9fypU/QFeU1gsV6+sznZZyNCBaEkBrXLXEfMtE86HMUr+KjR0az7u66gPHGQmIBoUxfQDs9jZC3AvgBMBRXaGQ7s5mX8uTfgDCabpOjteIBgAAAABJRU5ErkJggg==" />
                        </span>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="control-label col-xs-3 col-sm-2 col-md-4 col-lg-3">
                        <span class="text-danger mr-5">*</span>Telefone:
                    </label>
                    <div class="input-group col-xs-12 col-sm-10 col-md-8 col-lg-9">
                        <input type="text" name="Telefone" class="form-control" validate-on="blur"
                            ng-model="vm.usuario.telefone" data-mask="(00) 0000-0000"
                            required-message="'Telefone é obrigatório'" required />
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="control-label col-xs-3 col-sm-2 col-md-4 col-lg-3">Celular:</label>
                    <div class="input-group col-xs-12 col-sm-10 col-md-8 col-lg-9">
                        <input type="text" name="Celular" class="form-control" ng-model="vm.usuario.celular"
                            ui-br-phone-number />
                    </div>
                </div>
            </div>
        </div>
        <!--
        <consulta-padrao-modal idname="EmpresaUser" idmodel="EmpresaUser" ng-show="vm.isAdmin()"
            tabledefinition="vm.consultaEmpresa" label="'Empresa:'" placeholder="'Selecione uma Empresa'"
            ng-disabled="vm.consultaGrupoEmpresa.selectedValue" ng-required="vm.usuario.empresasVinculadas.length > 0" required-message="'Empresa é obrigatória'">
        </consulta-padrao-modal>
        -->
        <div class="row">
            <div ng-if="vm.isAdmin()">
                <consulta-padrao-modal tabledefinition="vm.consultaGrupoEmpresa"
                    label="'Grupo de empresa:'" placeholder="'Selecione um Grupo de empresa'"
                    ng-disabled="vm.usuario.empresasVinculadas.length != 0">
                </consulta-padrao-modal>
            </div>
        </div>
        <hr ng-if="vm.isAdmin() || vm.isGrupoEmpresa()">
        <div class="row">
            <div class="col-xs-12 col-md-6" ng-if="vm.isAdmin() || vm.isGrupoEmpresa()">
                <div class="form-group form-horizontal">
                    <label class="control-label col-xs-3 col-sm-2 col-md-4 col-lg-3">
                        Empresas vinculadas:</label>
                    <p class="input-group">
                        <input type="text" ng-disabled="true" placeholder="Empresas" class="form-control" />
                        <span class="input-group-btn">
                            <button type="button" ng-disabled="vm.consultaGrupoEmpresa.selectedValue"
                                class="btn btn-default" ng-click="vm.vincularEmpresa()">
                                <i class="fa fa-search"></i>
                            </button>
                        </span>
                    </p>
                </div>
            </div>
            <div class="row">
                <consulta-padrao-modal tabledefinition="vm.consultaGrupoUsuario" label="'Grupo de usuário:'"
                    placeholder="'Selecione um Grupo de usuário'" required-message="'Grupo de usuário é obrigatório'"
                    ng-required="true">
                </consulta-padrao-modal>
            </div>
        </div>
        <div ng-if="vm.isAdmin() || vm.isGrupoEmpresa()">
            <button style="margin-left: 21px;" type="button" class="mr-5 btn-labeled btn btn-danger"
                tooltip-placement="top" uib-tooltip="Excluir itens"
                ng-disabled="vm.usuario.empresasVinculadas.length == 0" ng-click="vm.excluirItens()">
                <span class="pl-3">Limpar todos</span>
            </button>
            <div class="ibox-content" style="border-style: none !important;">
                <div class="table-responsive tableFixHead">
                    <table class="table table-sm table-bordered">
                        <thead>
                            <tr style="width: 100% !important">
                                <th style="width: 10% !important;">Código</th>
                                <th style="width: 35% !important;">Nome Fantasia</th>
                                <th style="width: 35% !important;">Razão social</th>
                                <th style="width: 10% !important;">CNPJ</th>
                                <th style="width: 10% !important;">Ação</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr ng-repeat="empresaVinculada in vm.usuario.empresasVinculadas">
                                <td>{{empresaVinculada.id}}</td>
                                <td>{{empresaVinculada.nomeFantasia}}</td>
                                <td>{{empresaVinculada.razaoSocial}}</td>
                                <td>{{empresaVinculada.cnpj}}</td>
                                <td>
                                    <button type="button" mwl-confirm class="btn btn-danger btn-xs" placement="top"
                                        message="Você tem certeza que deseja excluir esta empresa?"
                                        ng-disabled="false" tooltip-placement="top"
                                        uib-tooltip="Excluir empresa" confirm-text="Sim" cancel-text="Não"
                                        confirm-button-type="danger" cancel-button-type="default"
                                        on-confirm="vm.excluirEmpresaVinculada(empresaVinculada)">
                                        <i class="fa fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>