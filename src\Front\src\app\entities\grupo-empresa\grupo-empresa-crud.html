<style>
    #GrupoEmpresaCrudController .fixLRpg {
        padding-left: 4px !important;
        padding-right: 4px !important;
        text-align: -webkit-center;
    }

    .widthHF {
        width: 25px;
    }

    .colorGreen {
        color: green;
    }

    .form-wizard>ol>li {
        min-height: 50px;
        padding-top: 10px;
        padding-bottom: 7px;
        background-color: #eeeeee;
        border-color: #eaeaea;
        border-style: solid;
        cursor: pointer;
        border-width: 2px;
    }

    #GrupoEmpresaCrudController .imgPassCombination {
        position: relative;
        top: 5px;
        right: -37px;
    }

    #GrupoEmpresaCrudController .ui-select-bootstrap .ui-select-toggle>a.btn {
        position: absolute !important;
        height: 10px !important;
        right: 10px !important;
        margin-top: 0px !important;
    }

    #GrupoEmpresaCrudController .imgCliente,
    .imgClienteFile {
        position: absolute;
        margin: -20px 105px;
        margin-left: 50%;
        height: 128px;
        width: 128px;
        border-radius: 65%;
        object-fit: cover;
        object-position: center;
    }

    #GrupoEmpresaCrudController .inputUploadImg {
        position: absolute;
        right: 0;
        top: 117px;
    }

    .form-wizard>ol>li {
        min-height: 33px;
        padding-top: 2px;
        padding-bottom: 0px;
        background-color: #eeeeee;
        border-color: #eaeaea;
        border-style: solid;
        cursor: pointer;
        border-width: 2px;
    }
</style>
<div id="GrupoEmpresaCrudController" ng-controller="GrupoEmpresaCrudController as vm">
    <form-header ng-submit="return" items="vm.headerItems" head="'Grupo de empresas'"></form-header>
    <div class="wrapper-content animated overflow-hidden fadeIn mb-0 pb-0">
        <div class="row">
            <div class="col-lg-12">
                <div class="ibox">
                    <div class="ibox-title">
                        <h5>{{vm.isNew() ? 'Novo' : 'Editar'}} grupo de empresa</h5>
                        <div ibox-tools></div>
                    </div>
                    <div class="ibox-content wizard">
                        <form name="formEmpresaCrud" role="form" novalidate ats-validator ng-submit="vm.save(formEmpresaCrud)" show-validation>
                            <div form-wizard steps="{{vm.isNew() ? 3 : 4}}">
                                <div class="form-wizard">
                                    <ol class="row" style="display: flex; flex-wrap: wrap;">
                                        <li ng-click="wizard.go(1)" class="fixLRpg" ng-class="{'active': wizard.active(1), 'col-sm-6': vm.isNew(), 'col-sm-4': !vm.isNew()}">
                                            <h4>Principal</h4>
                                        </li>
                                        <li ng-click="wizard.go(2)" class="fixLRpg" ng-class="{'active': wizard.active(2), 'col-sm-6': vm.isNew(), 'col-sm-4': !vm.isNew()}">
                                            <h4>Tarifas</h4>
                                        </li>
                                        <li ng-click="wizard.go(3)"  class="fixLRpg" ng-class="{'active': wizard.active(3), 'col-sm-6': vm.isNew(), 'col-sm-4': !vm.isNew()}">
                                            <h4>Parâmetros</h4>
                                        </li>
                                        <li ng-click="wizard.go(4)"  ng-if="!vm.isNew()" class="fixLRpg" ng-class="{'active': wizard.active(4), 'col-sm-6': vm.isNew(), 'col-sm-4': !vm.isNew()}">
                                            <h4>Empresas</h4>
                                        </li>
                                    </ol>
                                    <br/>
                                </div>
                                <div id="activateTab1" ng-show="wizard.active(1)">
                                    <div ng-include="'app/entities/grupo-empresa/abas/principal.html'" class="form-horizontal"> </div>
                                </div>
                                <div id="activateTab2" ng-show="wizard.active(2)">
                                    <div ng-include="'app/entities/grupo-empresa/abas/tarifas.html'" class="form-horizontal"> </div>
                                </div>
                                <div id="activateTab4" ng-show="wizard.active(3)">
                                    <div ng-include="'app/entities/grupo-empresa/abas/parametro.html'" class="form-horizontal"> </div>
                                </div>
                                <div id="activateTab3" ng-show="wizard.active(4)"  ng-if="!vm.isNew()">
                                    <div ng-include="'app/entities/grupo-empresa/abas/empresas.html'" class="form-horizontal"> </div>
                                </div>
                            </div>
                            <div class="row clearfix"> </div>
                            <hr-label dark="true"></hr-label>
                            <br/>
                            <div class="row">
                                <div class="form-group">
                                    <div class="col-md-12 col-lg-12 text-right">
                                        <button type="button" ng-disabled="vm.isSaving" ng-click="vm.onClickVoltar(wizard)" class="btn btn-labeled
                                            btn-default">
                                            <span class="btn-label">
                                                <i class="fa fa-arrow-circle-left"></i>
                                            </span>
                                            Voltar
                                        </button>
                                        <button type="button" ng-show="!vm.isNew() ? !wizard.active(4) : !wizard.active(3)" ng-disabled="vm.saving"
                                            ng-click="wizard.go(wizard.getActivePosition() + 1);"
                                            class="btn btn-labeled btn-default">
                                            <span class="btn-label">
                                                <i class="fa fa-arrow-circle-right"></i>
                                            </span>
                                            Avançar
                                        </button>
                                        <button type="submit" ng-disabled="vm.isSaving" ng-show="wizard.active(vm.isNew() ? 3 : 4)" class="btn btn-labeled btn-success text-right" disabled="disabled">
                                            <span class="btn-label">
                                                <i class="fa fa-check-circle"></i>
                                            </span>
                                            Salvar
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>