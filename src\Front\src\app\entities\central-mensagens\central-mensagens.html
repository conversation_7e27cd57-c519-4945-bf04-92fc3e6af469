﻿<div id="CentralMensagensController" ng-controller="CentralMensagensController as vm">
    <form-header items="vm.headerItems" head="'Central de mensagens'"></form-header>
    <div class="wrapper wrapper-content animated fadeIn mt-5 overflow-hidden">
        <div class="row">
            <div class="col-lg-12">
                <div class="ibox float-e-margins">
                    <div style="height: 38px;" class="ibox-title">
                        <h5>Central de mensagens</h5>
                        <div ibox-tools></div>
                    </div>
                    <div class="ibox-content">
                        <div class="row">
                            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-3">
                                <div class="form-group">
                                    <label class="col-xs-12 col-md-4 col-md-3 control-label" style="text-align: right; padding-top: 10px;">
                                        Status:
                                    </label>
                                    <div class="input-group col-xs-12 col-sm-12 col-md-8 col-lg-9">
                                        <ui-select name="status" ng-model="vm.status" ats-ui-select-validator validate-on="blur">
                                            <ui-select-match> <span>{{$select.selected.descricao}}</span>
                                            </ui-select-match>
                                            <ui-select-choices
                                                repeat="ex.id as ex in vm.enumStatus | propsFilter: {descricao: $select.search}">
                                                <div ng-bind-html="ex.descricao | highlight: $select.search"></div>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-7">
                                <div class="form-group">
                                    <label class="col-xs-12 col-md-4 col-md-3 control-label"
                                        style="text-align: right; padding-top: 10px;">Mensagem:</label>
                                    </label>
                                    <div class="input-group col-xs-12 col-md-8">
                                        <input type="text" ng-required="true" name="mensagem"
                                            ng-model="vm.mensagem" class="form-control" />
                                    </div>
                                </div>
                            </div>
                            <div class="pull-right" style="text-align: right; margin-right: 13px;">
                                <button type="button" class="btn btn-labeled btn-primary"
                                    ng-click="vm.atualizaTela();">Consultar
                                </button>
                            </div>
                        </div>
                        <hr />
                        <div class="pull-right">
                            <button tooltip-placement="top" ng-click="vm.atualizaTela();"
                                uib-tooltip="Atualizar" type='button' class="btn btn-labeled btn-default">
                                <i class="fa fa-refresh"></i>
                                <span class="pl-5 ">Atualizar</span>
                            </button>
                            <button tooltip-placement="top"
                                ui-sref="central-mensagens.central-mensagens-crud({link: 'novo'})"
                                uib-tooltip="Cadastrar " type='button' class="btn btn-labeled btn-primary ">
                                <span class="btn-label text-right">
                                    <i class="fa fa-plus"></i>
                                </span>
                                <span class="pl-5">Novo</span>
                            </button>
                        </div>
                        <div class="row"></div>
                        <br>
                        <div ui-grid="vm.gridOptions" ng-style="{height: 410}" class="grid" style="width: 100%;" ui-grid-pinning
                            ui-grid-save-state ui-grid-pagination ui-grid-auto-resize ui-grid-resize-columns ui-grid-grouping>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>