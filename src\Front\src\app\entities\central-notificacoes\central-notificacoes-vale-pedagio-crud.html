<div id="CentralNotificacaoValePedagioController" ng-controller="CentralNotificacaoValePedagioCrudController as vm">
    <form-header ng-submit="return" items="vm.headerItems" head="'Notificação'"></form-header>
    <div class="wrapper-content animated overflow-hidden fadeIn mb-0 pb-0">
        <div class="row">
            <div class="col-lg-12">
                <div class="ibox">
                    <div class="ibox-title">
                        <h5>Notificação</h5>
                        <div ibox-tools></div>
                    </div>
                    <div class="ibox-content wizard">
                        <div>
                            <div class="form-wizard">
                                <ol class="row">
                                    <li class="fixLRpg col-sm-12" ng-class="{'active': true}">
                                        <h4>Principal</h4>
                                    </li>
                                </ol>
                                <br/>
                            </div>
                            <div>
                                <div ng-include="'app/entities/central-notificacoes/abas/vale-pedagio/principal.html'" class="form-horizontal"></div>
                            </div>
                        </div>
                        <div class="row clearfix"></div>
                        <hr-label dark="true"></hr-label>
                        <br/>
                        <div class="row">
                            <div class="form-group">
                                <div class="col-md-12 col-lg-12 text-right">
                                    <button type="button" ng-click="vm.onClickVoltar()" class="btn btn-labeled btn-default">
                                            <span class="btn-label">
                                                <i class="fa fa-arrow-circle-left"></i>
                                            </span>
                                        Voltar
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>