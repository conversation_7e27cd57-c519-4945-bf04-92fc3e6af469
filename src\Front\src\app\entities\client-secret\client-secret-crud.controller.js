(function () {
    'use strict';

    angular.module('bbcWeb').controller('ClientSecretCrudController', ClientSecretCrudController);

    ClientSecretCrudController.$inject = ['toastr', '$rootScope', 'BaseService', '$state', '$stateParams', '$window', 'PERFIL_ADMINISTRADOR', '$scope', '$timeout', 'PersistentDataService', 'DefaultsService', '$uibModal'];

    function ClientSecretCrudController(
        toastr, $rootScope,
        BaseService, $state,
        $stateParams, $window,
        PERFIL_ADMINISTRADOR,
        $scope, $timeout,
        PersistentDataService,
        DefaultsService,
        $uibModal
    ) {
        var vm = this;

        vm.isNew = function () {
            return $stateParams.link === 'novo';
        };

        vm.loadEdit = function (id) {
            id = parseInt(id);
            BaseService.get('ClientSecret', 'ConsultarPorId', {
                idClientSecret: id
            }).then(function (response) {
                if (!response.success) {
                    toastr.error(response.message);
                    return;
                }
                vm.clientSecret = response.data;

                vm.temGrupoEmpresa = !!response.data.grupoEmpresaId
                vm.consultaGrupoEmpresa.selectedText = response.data.nomeGrupoEmpresa;
                vm.consultaGrupoEmpresa.selectedValue = response.data.grupoEmpresaId;

                vm.temEmpresa = !!response.data.empresaId
                vm.consultaEmpresa.selectedText = response.data.nomeEmpresa;
                vm.consultaEmpresa.selectedValue = response.data.empresaId;
                setDataFim(response.data.dataExpiracao);
                
                if(!vm.consultaEmpresa.selectedValue) return;

                BaseService.get('Empresa', 'ConsultarPorId', {
                    idEmpresa: vm.consultaEmpresa.selectedValue
                }).then(function (res) {
                    vm.habilitarSenhaApi = !res.data.grupoEmpresaId;
                    vm.consultaGrupoEmpresa.selectedText = res.data.grupoEmpresaNome;
                    vm.consultaGrupoEmpresa.selectedValue = res.data.grupoEmpresaId;
                })
            });
        };

        vm.save = function (form) {
            if (!form.$valid) {
                toastr.error('Todos os campos obrigatórios devem ser preenchidos corretamente com seus respectivos valores');
                return;
            }

            if (vm.isSaving) return;

            vm.isSaving = true;

            var saveClientSecret = {
                idEmpresa: vm.consultaEmpresa.selectedValue,
                grupoEmpresaId: vm.consultaGrupoEmpresa.selectedValue,
                descricao: vm.clientSecret.descricao,
                senha: vm.clientSecret.senhaApi,
                dataExpiracao: vm.clientSecret.dataExpiracao ? new Date(vm.clientSecret.dataExpiracao.getTime() - (vm.clientSecret.dataExpiracao.getTimezoneOffset() * 60000)).toJSON() : null,
                id: vm.clientSecret.id === "Auto" ? 0 : vm.clientSecret.id
            }

            if (vm.isNew()) {
                abrirModal(saveClientSecret)
                vm.isSaving = false;
            } else {
                BaseService.post('ClientSecret', 'SaveClientSecret', saveClientSecret).then(function (response) {
                    vm.isSaving = false;
                    if (!response) {
                        toastr.error('Erro de comunicação com o servidor, tente novamente em alguns minutos.');
                        return
                    }

                    if (!response.success) {
                        toastr.error(response.message);
                        return
                    }

                    toastr.success(response.message);
                    $state.go('client-secret.index');
                });
            }
        };

        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.administrador === true;
        };

        function setDataFim(data) {
            if (!data) return
            vm.clientSecret.dataExpiracao = new Date(data);
        }

        function abrirModal(saveClientSecret) {
            $uibModal.open({
                animation: true,
                ariaLabelledBy: 'modal-title',
                ariaDescribedBy: 'modal-body',
                templateUrl: 'app/entities/client-secret/modal/client-secret-modal.html',
                controller: function ($uibModalInstance, toastr, BaseService, saveClientSecret, $rootScope) {
                    var vm = this;
                    vm.carregandoEdit = true;

                    BaseService.post('ClientSecret', 'SaveClientSecret', saveClientSecret).then(function (response) {
                        vm.carregandoEdit = false;
                        if (response.success) {
                            toastr.success(response.message);
                            vm.clientSecret = response.data
                        } else {
                            $uibModalInstance.close();
                            toastr.error(response.message);
                        }

                    });

                    vm.copiar = function () {
                        unsecuredCopyToClipboard(vm.clientSecret)

                        toastr.success("Client Secret copiado para área de transferência")
                    }

                    function unsecuredCopyToClipboard(text) {
                        var textArea = document.createElement("textarea");
                        textArea.value = text;
                        document.body.appendChild(textArea);
                        textArea.focus();
                        textArea.select();
                        try {
                            document.execCommand('copy');
                        } catch (err) {
                            console.error('Unable to copy to clipboard', err);
                        }
                        document.body.removeChild(textArea);
                    }

                    vm.closeModal = function () {
                        $uibModalInstance.close();
                        $state.go('client-secret.index')
                    }
                },
                controllerAs: 'vm',
                keyboard: true,
                size: 'md',
                resolve: {
                    saveClientSecret: saveClientSecret
                }
            }).result.then(function () {
            });
        }

        vm.temGrupoEmpresa = true
        vm.temEmpresa = true
        vm.habilitarSenhaApi = false;

        vm.headerItems = [{
            name: 'Cadastros'
        }, {
            name: 'Client secret',
            link: 'client-secret.index'
        }, {
            name: $stateParams.link === 'novo' ? 'Novo' : 'Editar'
        }];

        vm.clientSecret = {
            ativo: 1
        };

        vm.optionsDatePicker = {
            minDate: vm.isNew() ? new Date() : new Date - 1
        };

        vm.consultaEmpresa = {
            columnDefs: [{
                name: 'Código',
                field: 'id',
                width: 80,
                type: 'number',
                primaryKey: true
            }, {
                name: 'CNPJ',
                field: 'cnpj',
                width: '*',
                minWidth: 250,
                serverField: 'cnpj'
            }, {
                name: 'Nome Fantasia',
                field: 'nomeFantasia',
                serverField: 'nomeFantasia',
                width: '*',
                minWidth: 250
            }, {
                name: 'Grupo De Empresa',
                field: 'grupoDeEmpresa',
                width: '*',
                minWidth: 200
            }],
            desiredValue: 'id',
            desiredText: 'nomeFantasia',
            url: 'Empresa/ConsultarGridEmpresaComboClientSecret',
            executeAfterSelection: function () {
                vm.consultaGrupoEmpresa.selectedValue = undefined;
                vm.consultaGrupoEmpresa.selectedText = '';
                BaseService.get('Empresa', 'ConsultarPorId', {
                    idEmpresa: vm.consultaEmpresa.selectedValue
                }).then(function (response) {
                    if (vm.isNew()) vm.clientSecret.senhaApi = ''
                    vm.habilitarSenhaApi = !response.data.grupoEmpresaId;
                    vm.consultaGrupoEmpresa.selectedText = response.data.grupoEmpresaNome;
                    vm.consultaGrupoEmpresa.selectedValue = response.data.grupoEmpresaId;
                })
            },
            clearFunction: function () {
                vm.habilitarSenhaApi = false;
                vm.clientSecret.senhaApi = '';
                if (vm.temGrupoEmpresa) return
                vm.consultaGrupoEmpresa.selectedText = '';
                vm.consultaGrupoEmpresa.selectedValue = undefined;
            },
            paramsMethod: function () {
                return vm.consultaGrupoEmpresa.selectedValue ? {
                    GrupoEmpresaId: vm.consultaGrupoEmpresa.selectedValue
                } : null;
            }
        };

        vm.consultaGrupoEmpresa = {
            columnDefs: [{
                name: 'Código',
                field: 'id',
                width: 80,
                type: 'number',
                primaryKey: true
            }, {
                name: 'CNPJ',
                field: 'cnpj',
                width: '*',
                minWidth: 200
            }, {
                name: 'Razão Social',
                field: 'razaosocial',
                width: '*',
                minWidth: 250
            }],
            desiredValue: 'id',
            desiredText: 'razaosocial',
            url: 'GrupoEmpresa/ConsultarModalGrupoEmpresa',
            executeAfterSelection: function () {
                vm.habilitarSenhaApi = true;
                vm.clientSecret.senhaApi = '';
                vm.consultaEmpresa.selectedValue = undefined;
                vm.consultaEmpresa.selectedText = '';
            },
            clearFunction: function () {
                vm.habilitarSenhaApi = false;
                vm.clientSecret.senhaApi = '';
                vm.consultaEmpresa.selectedValue = undefined;
                vm.consultaEmpresa.selectedText = '';
            },
            paramsMethod: function () {
                return {}
            }
        };

        // -------------------------- Inicialiazação da Tela -------------------------- //

        var selfScope = PersistentDataService.get('ClientSecretCrudController');

        if ($stateParams.link === 'novo')
            vm.clientSecret.id = 'Auto';

        $scope.$on('$stateChangeStart', function (arg1, toState) {
            if (toState.name === 'client-secret.index')
                PersistentDataService.remove('ClientSecretCrudController');
            else
                PersistentDataService.store('ClientSecretCrudController', vm, "Cadastro - Grupos de client secret", null, "client-secret.client-secret-crud", vm.clientSecret.id);
        });

        if (angular.isDefined(selfScope)) {
            angular.extend(vm, selfScope.data);
        } else if (!vm.isNew()) {
            vm.loadEdit($stateParams.link);
        } else {
            vm.clientSecret.id = "Auto";
            BaseService.get('GrupoEmpresa', 'ConsultarGrupoEmpresaUsuario')
                .then(function (response) {
                    if (!response) return

                    if (!response.success) return

                    if (!response.data) {
                        vm.temEmpresa = false
                        vm.temGrupoEmpresa = false
                        return
                    }

                    vm.temGrupoEmpresa = !!response.data.grupoEmpresaId
                    vm.consultaGrupoEmpresa.selectedText = response.data.grupoEmpresaNome;
                    vm.consultaGrupoEmpresa.selectedValue = response.data.grupoEmpresaId;

                    vm.temEmpresa = !!response.data.empresaId
                    vm.consultaEmpresa.selectedText = response.data.empresaNome;
                    vm.consultaEmpresa.selectedValue = response.data.empresaId;

                    if(!vm.consultaEmpresa.selectedValue) return;

                    BaseService.get('Empresa', 'ConsultarPorId', {
                        idEmpresa: vm.consultaEmpresa.selectedValue
                    }).then(function (res) {
                        if (vm.isNew()) vm.clientSecret.senhaApi = ''
                        vm.habilitarSenhaApi = !res.data.grupoEmpresaId;
                        vm.consultaGrupoEmpresa.selectedText = res.data.grupoEmpresaNome;
                        vm.consultaGrupoEmpresa.selectedValue = res.data.grupoEmpresaId;
                    })
                })
        }

        $timeout(function () {
            PersistentDataService.remove('ClientSecretController');
        }, 15);

        // -------------------------- Inicialiazação da Tela -------------------------- //
    }
})();