<div class="form-horizontal">
    <hr />
    <div class="row">
        <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
            <div class="form-group">
                <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label">
                    Código:
                </label>
                <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                    <input type="text" ng-model="vm.pagamentoValePedagio.id" class="form-control" ng-disabled="true" />
                </div>
            </div>
        </div>
        <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
            <div class="form-group">
                <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label">
                    Status:
                </label>
                <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                    <input type="text" disabled ng-model="vm.pagamentoValePedagio.status" name="status" class="form-control"/>
                    <!-- 
                    <ui-select name="status" ats-ui-select-validator validate-on="blur" ng-model="vm.status">
                        <ui-select-match>
                            <span>{{$select.selected.descricao}}</span>
                        </ui-select-match>
                        <ui-select-choices
                            repeat="ex.id as ex in vm.enumStatus | propsFilter: {descricao: $select.search}">
                            <div ng-bind-html="ex.descricao | highlight: $select.search"></div>
                        </ui-select-choices>
                    </ui-select>
                    -->
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-xs-12 col-md-6">
            <div class="form-group">
                <label class="control-label col-xs-12 col-md-3">
                    <span class="text-danger mr-5">*</span>Json envio vale pedágio:
                </label>
                <div class="input-group col-xs-12 col-md-9">
                    <textarea style="resize: none;" value="" type="text" validate-on="blur" disabled value="true"
                        multiple maxlength="500" ng-model="vm.pagamentoValePedagio.jsonEnvio" name="jsonEnvio"
                        rows="7"
                        class="form-control ng-pristine ng-valid ng-empty ng-valid-maxlength ng-touched user-success"> </textarea>
                </div>
            </div>
        </div>
        <div class="col-xs-12 col-md-6">
            <div class="form-group">
                <label class="control-label col-xs-12 col-md-3">
                    <span class="text-danger mr-5">*</span>Json retorno vale pedágio:
                </label>
                <div class="input-group col-xs-12 col-md-9">
                    <textarea style="resize: none;" value="" type="text" validate-on="blur" multiple maxlength="500"
                        disabled value="true" ng-model="vm.pagamentoValePedagio.jsonRetorno" name="jsonRetorno"
                        rows="7"
                        class="form-control ng-pristine ng-valid ng-empty ng-valid-maxlength ng-touched user-success"> </textarea>
                </div>
            </div>
        </div>
    </div>
    <br><br>
    <hr-label class="mb-15" dark="true" title="'Transações'"></hr-label>
    <br><br>
    <div class="row">
        <div ui-grid="vm.gridOptions" ng-style="{height: vm.gridOptions.getGridHeight()}" class="grid" style="width: 100%;" ui-grid-pinning
            ui-grid-save-state ui-grid-pagination ui-grid-auto-resize ui-grid-resize-columns ui-grid-grouping> </div>
    </div>

</div>