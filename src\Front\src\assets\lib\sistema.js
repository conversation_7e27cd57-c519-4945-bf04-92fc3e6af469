﻿Sistema = window.Sistema || {
    Msg: {
        alert: function (aMsg) {
            window.alert(aMsg);
        },

        alertError: function (aMsg) {
            this.show("Erro: " + aMsg);
        },

        showDialog: function (aTitle, aMsg, aButtons) {
            var lDiv = '<div id="dialog-confirm" title="' + Sistema.String.toHtmlString(aTitle) + '">' +
                '<p><span class="ui-icon ui-icon-alert" style="vertical-align: middle;margin-right: 5px;"></span>' + Sistema.String.toHtmlString(aMsg) + '</p>' +
                '</div>';

            $(lDiv).dialog(
                {
                    resizable: false,
                    //height: 140,
                    width: 600,
                    modal: true,
                    buttons: aButtons,
                    closeOnEscape: false,
                    open: function (event, ui) {
                        $(this).parent().children().children('.ui-dialog-titlebar-close').hide();
                    }
                });
        },

        show: function (aMsg, aCallbackEvent) {
            this.showDialog("Atenção", aMsg, {
                "OK": function () {
                    if (aCallbackEvent != null) {
                        aCallbackEvent();
                    }
                    $(this).dialog("close");
                }
            });
        },

        confirm: function (aMsg, aTrueEvent, aFalseEvent) {
            this.showDialog("Confirmação", aMsg,
                {
                    "Sim": function () {
                        if (aTrueEvent != null) {
                            aTrueEvent();
                        }
                        $(this).dialog("close");
                    },
                    "Não": function () {
                        if (aFalseEvent) {
                            aFalseEvent();
                        }
                        $(this).dialog("close");
                    }
                },
                aFalseEvent);
        },

        error: function (aMsg, aCallbackEvent) {
            this.showDialog("Erro", aMsg, {
                "OK": function () {
                    if (aCallbackEvent != null) {
                        aCallbackEvent();
                    }
                    $(this).dialog("close");
                }
            });
        },

        growl: function (aTitle, aMsg) {
            $.growlUI(aTitle, aMsg);

            // http://malsup.com/jquery/block/#overview
            /*
            div.growlUI { background: url(check48.png) no-repeat 10px 10px }
            div.growlUI h1, div.growlUI h2 {
                color: white; padding: 5px 5px 5px 75px; text-align: left
            }
            */
        }

    },

    String: {
        toHtmlString: function (aValue) {
            return aValue
                .replace("\n", "<br>")
                .replace(" ", "&nbsp;");
        }
    },

    UI: {
        block: function (aMsg) {
            if (aMsg == null)
                aMsg = "Processando operação, por favor aguarde";
            $.blockUI({ message: '<h4><img src="/Images/Loading01.gif" />' + aMsg + '</h4>' });
        },

        unblock: function () {
            $.unblockUI();
        }
    }
}

window.ATS = {};
angular.isDefinedNotNull = function (obj) {
    return angular.isDefined(obj) && obj !== null;
};

window.ATS.randomString = function () {
    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
};

angular.isDefinedNotNullNotEmpty = function (obj) {
    return angular.isDefined(obj) && obj !== null && obj.toString() != "" && obj.toString().length > 0;
};

angular.isUndefinedOrNull = function (obj) {
    return angular.isUndefined(obj) || obj === null;
};

angular.isUndefinedOrNullOrEmpty = function (obj) {
    return angular.isUndefined(obj) || obj === null || obj == "";
};
angular.isBool = function (val) {
    return typeof val == "boolean";
};

$(document).ready(function () {
    webshims.setOptions('forms-ext', {
        types: 'date'
    });
    webshims.polyfill('forms forms-ext');
    $.circleProgress.defaults.size = 100;

});

Array.prototype.unique = function () {
    return this.filter(function (value, index, self) {
        return self.indexOf(value) === index;
    });
};

String.prototype.toPlacaFormat = function () {
    var placa = this;
    if (placa === undefined || placa === null || placa.length < 4)
        return "Não informada";
    if (this.length == 7)
        placa = this.substr(0, 3) + "-" + this.substr(3, 7);
    return placa;
};

String.prototype.escapeSpecialChars = function () {
    return this.replace(/\\n/g, "\\n")
        .replace(/\\'/g, "\\'")
        .replace(/\\"/g, '\\"')
        .replace(/\\&/g, "\\&")
        .replace(/\\r/g, "\\r")
        .replace(/\\t/g, "\\t")
        .replace(/\\b/g, "\\b")
        .replace(/\\f/g, "\\f");
};

String.prototype.toFixedType = function () {
    var v = this.toString();
    if (v === "null")
        return null;
    if (v === "undefined")
        return undefined;
    if (v === null)
        return null;
    if (v === "true")
        return true;
    if (v === false)
        return false;
    var parsedVal = parseInt(v);
    if (!isNaN(parsedVal))
        return parsedVal;

    return v;
};

forEachObjProperty = function (obj, callback) {
    for (var prop in obj) {
        if (obj.hasOwnProperty(prop) && typeof obj[prop] !== "function") {
            callback(prop, obj[prop]);
        }
    }
};

function fixLightBoxButtons() {
    setTimeout(function () {
        $("[ng-if='Lightbox.images.length > 1']").find('a').each(function (index, el) {
            if (index === 0) $(el).html('< Anterior');
            if (index === 1) $(el).remove();
            if (index === 2) $(el).html('Próximo >');
        });
    }, 5000);
};

Date.prototype.addDays = function (days) {
    var dat = new Date(this.valueOf())
    dat.setDate(dat.getDate() + days);
    return dat;
};

function __lessOrEqual(dtA, dtB) {
    var a = new Date(dtA.getFullYear(), dtA.getMonth() - 1, dtA.getDate(), 0, 0, 0);
    var b = new Date(dtB.getFullYear(), dtB.getMonth() - 1, dtB.getDate(), 0, 0, 0);

    return a <= b;
};

function getStartEndBetweenDates(startDate, stopDate) {
    var dateArray = new Array();
    var currentDate = new Date(startDate.getTime());

    while (__lessOrEqual(currentDate, stopDate)) {
        var isFirst = currentDate.getTime() == startDate.getTime();
        var isLastDate = currentDate.toLocaleDateString() == stopDate.toLocaleDateString();

        if (startDate.toDateString() == stopDate.toDateString()) {
            var dtItem = {
                start: new Date(startDate),
                end: new Date(stopDate)
            };
            dateArray.push(dtItem);
        } else {

            if (isFirst) {
                var dtItem = {
                    start: new Date(currentDate),
                    end: null
                };

                currentDate.setHours(23);
                currentDate.setMinutes(59);
                currentDate.setSeconds(59);
                dtItem.end = currentDate;

                dateArray.push(dtItem);
            }

            if (!isFirst && !isLastDate) {
                var dataMeiaNoite = currentDate;
                dataMeiaNoite.setHours(0);
                dataMeiaNoite.setMinutes(0);
                dataMeiaNoite.setSeconds(0);

                var dtItem = {
                    start: new Date(dataMeiaNoite),
                    end: null
                };

                var data23e59 = dataMeiaNoite;
                dataMeiaNoite.setHours(23);
                dataMeiaNoite.setMinutes(59);
                dataMeiaNoite.setSeconds(59);
                dtItem.end = data23e59;

                dateArray.push(dtItem);
            }

            if (isLastDate) {
                var dataMeiaNoite = currentDate;
                dataMeiaNoite.setHours(0);
                dataMeiaNoite.setMinutes(0);
                dataMeiaNoite.setSeconds(0);

                var dtItem = {
                    start: new Date(dataMeiaNoite),
                    end: stopDate
                };

                dateArray.push(dtItem);
            }
        }
        currentDate = currentDate.addDays(1);
    }

    return dateArray;
};

angular.distanceBetweenElems = function distanceBetweenElems(elem1, elem2) {
    var e1Rect = elem1.getBoundingClientRect();
    var e2Rect = elem2.getBoundingClientRect();
    var dx = (e1Rect.left + (e1Rect.right - e1Rect.left) / 2) - (e2Rect.left + (e2Rect.right - e2Rect.left) / 2);
    var dy = (e1Rect.top + (e1Rect.bottom - e1Rect.top) / 2) - (e2Rect.top + (e2Rect.bottom - e2Rect.top) / 2);
    var dist = Math.sqrt(dx * dx + dy * dy);
    return dist;
};

/* FileSaver.js
 * A saveAs() FileSaver implementation.
 * 1.3.6
 * 2018-03-16 14:39:40
 *
 * By Eli Grey, https://eligrey.com
 * License: MIT
 *   See https://github.com/eligrey/FileSaver.js/blob/master/LICENSE.md
 */

/*global self */
/*jslint bitwise: true, indent: 4, laxbreak: true, laxcomma: true, smarttabs: true, plusplus: true */

/*! @source http://purl.eligrey.com/github/FileSaver.js/blob/master/src/FileSaver.js */

var saveAs = saveAs || (function (view) {
    "use strict";
    // IE <10 is explicitly unsupported
    if (typeof view === "undefined" || typeof navigator !== "undefined" && /MSIE [1-9]\./.test(navigator.userAgent)) {
        return;
    }
    var
        doc = view.document
        // only get URL when necessary in case Blob.js hasn't overridden it yet
        ,
        get_URL = function () {
            return view.URL || view.webkitURL || view;
        },
        save_link = doc.createElementNS("http://www.w3.org/1999/xhtml", "a"),
        can_use_save_link = "download" in save_link,
        click = function (node) {
            var event = new MouseEvent("click");
            node.dispatchEvent(event);
        },
        is_safari = /constructor/i.test(view.HTMLElement) || view.safari,
        is_chrome_ios = /CriOS\/[\d]+/.test(navigator.userAgent),
        setImmediate = view.setImmediate || view.setTimeout,
        throw_outside = function (ex) {
            setImmediate(function () {
                throw ex;
            }, 0);
        },
        force_saveable_type = "application/octet-stream"
        // the Blob API is fundamentally broken as there is no "downloadfinished" event to subscribe to
        ,
        arbitrary_revoke_timeout = 1000 * 40 // in ms
        ,
        revoke = function (file) {
            var revoker = function () {
                if (typeof file === "string") { // file is an object URL
                    get_URL().revokeObjectURL(file);
                } else { // file is a File
                    file.remove();
                }
            };
            setTimeout(revoker, arbitrary_revoke_timeout);
        },
        dispatch = function (filesaver, event_types, event) {
            event_types = [].concat(event_types);
            var i = event_types.length;
            while (i--) {
                var listener = filesaver["on" + event_types[i]];
                if (typeof listener === "function") {
                    try {
                        listener.call(filesaver, event || filesaver);
                    } catch (ex) {
                        throw_outside(ex);
                    }
                }
            }
        },
        auto_bom = function (blob) {
            // prepend BOM for UTF-8 XML and text/* types (including HTML)
            // note: your browser will automatically convert UTF-16 U+FEFF to EF BB BF
            if (/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(blob.type)) {
                return new Blob([String.fromCharCode(0xFEFF), blob], {
                    type: blob.type
                });
            }
            return blob;
        },
        FileSaver = function (blob, name, no_auto_bom) {
            if (!no_auto_bom) {
                blob = auto_bom(blob);
            }
            // First try a.download, then web filesystem, then object URLs
            var
                filesaver = this,
                type = blob.type,
                force = type === force_saveable_type,
                object_url, dispatch_all = function () {
                    dispatch(filesaver, "writestart progress write writeend".split(" "));
                }
                // on any filesys errors revert to saving with object URLs
                ,
                fs_error = function () {
                    if ((is_chrome_ios || (force && is_safari)) && view.FileReader) {
                        // Safari doesn't allow downloading of blob urls
                        var reader = new FileReader();
                        reader.onloadend = function () {
                            var url = is_chrome_ios ? reader.result : reader.result.replace(
                                /^data:[^;]*;/, 'data:attachment/file;');
                            var popup = view.open(url, '_blank');
                            if (!popup) view.location.href = url;
                            url = undefined; // release reference before dispatching
                            filesaver.readyState = filesaver.DONE;
                            dispatch_all();
                        };
                        reader.readAsDataURL(blob);
                        filesaver.readyState = filesaver.INIT;
                        return;
                    }
                    // don't create more object URLs than needed
                    if (!object_url) {
                        object_url = get_URL().createObjectURL(blob);
                    }
                    if (force) {
                        view.location.href = object_url;
                    } else {
                        var opened = view.open(object_url, "_blank");
                        if (!opened) {
                            // Apple does not allow window.open, see https://developer.apple.com/library/safari/documentation/Tools/Conceptual/SafariExtensionGuide/WorkingwithWindowsandTabs/WorkingwithWindowsandTabs.html
                            view.location.href = object_url;
                        }
                    }
                    filesaver.readyState = filesaver.DONE;
                    dispatch_all();
                    revoke(object_url);
                };
            filesaver.readyState = filesaver.INIT;

            if (can_use_save_link) {
                object_url = get_URL().createObjectURL(blob);
                setImmediate(function () {
                    save_link.href = object_url;
                    save_link.download = name;
                    click(save_link);
                    dispatch_all();
                    revoke(object_url);
                    filesaver.readyState = filesaver.DONE;
                }, 0);
                return;
            }

            fs_error();
        },
        FS_proto = FileSaver.prototype,
        saveAs = function (blob, name, no_auto_bom) {
            return new FileSaver(blob, name || blob.name || "download", no_auto_bom);
        };

    // IE 10+ (native saveAs)
    if (typeof navigator !== "undefined" && navigator.msSaveOrOpenBlob) {
        return function (blob, name, no_auto_bom) {
            name = name || blob.name || "download";

            if (!no_auto_bom) {
                blob = auto_bom(blob);
            }
            return navigator.msSaveOrOpenBlob(blob, name);
        };
    }

    save_link.target = "_blank";

    FS_proto.abort = function () { };
    FS_proto.readyState = FS_proto.INIT = 0;
    FS_proto.WRITING = 1;
    FS_proto.DONE = 2;

    FS_proto.error =
        FS_proto.onwritestart =
        FS_proto.onprogress =
        FS_proto.onwrite =
        FS_proto.onabort =
        FS_proto.onerror =
        FS_proto.onwriteend =
        null;

    return saveAs;
}(
    typeof self !== "undefined" && self ||
    typeof window !== "undefined" && window ||
    this
));

//teste login

function login(versao, usuario, senha, dominio) {
    var args_ = {
        'func': 'login',
        'usuario': usuario,
        'senha': senha,
        'dominio': dominio
    };
    var sk = window.localStorage.getItem('SessionKey');
    if (sk == null || sk === 'invalid-key-value') {
        var urlLogin = window.location.origin + "/index.html#/login";
        window.location = urlLogin;
    }
};

function deslogar(versao) {
    var args_ = {
        'func': 'deslogar'
    };
    if (versao == 1)
        document.getElementById("sistemaB").contentWindow.postMessage(args_, '*');
    else
        document.getElementById("sistemaA").contentWindow.postMessage(args_, '*');

    setTimeout(function () {
        $("#sistemaA").show();
        $("#sistemaB").hide();
    }, 250);
};

function onMessage(event) {
    var data = event.data;
    if (data.func !== undefined && data.func.toLowerCase() === 'login')
        window.login(data.versao, data.usuario, data.senha, data.dominio);
    else if (data.func !== undefined && data.func.toLowerCase() === 'deslogar')
        window.deslogar(data.versao);
    else if (data.func !== undefined && data.func.toLowerCase() === 'abrirlinkabanova')
        window.open(data.link, '_blank');
    else if (data.func !== undefined && data.func.toLowerCase() === 'isonsistemainfodomain') {
        window.login('', '', '', '');
    }
};

if (window.addEventListener)
    window.addEventListener("message", onMessage, false);
else if (window.attachEvent) {
    window.attachEvent("onmessage", onMessage, false);
}
