'use strict';

var conf = require('./conf');
var gulp = require('gulp');
var fullPath = [conf.paths.dist + "/**", "!" + conf.paths.dist + "/*.config"];
var path = [conf.paths.dist + "/**", "!" + conf.paths.dist + "/*.config" , "!" + conf.paths.dist + "/app/**", "!" + conf.paths.dist + "/assets/**", "!" + conf.paths.dist + "/fonts/**", "!" + conf.paths.dist + "/scripts/shims/**"];
var site = "BBC - BBC Controle";
var dest_dev = `\\\\sw19-142\\c$\\sites\\BBC\\DEV\\${site}`;
var dest_hml = `\\\\sw19-142\\c$\\sites\\BBC\\HML\\${site}`;
var dest_prod = `\\\\sw19-142\\c$\\sites\\BBC\\PROD\\${site}`;

gulp.task('publish-dev', function() {
    return gulp.src(path).pipe(gulp.dest(dest_dev));
});

gulp.task('publish-hml', function() {
    return gulp.src(path).pipe(gulp.dest(dest_hml));
});

gulp.task('publish-prod-sim', function() {
    return gulp.src(path).pipe(gulp.dest(dest_prod));
});

gulp.task('publish-dev-full', function() {
    return gulp.src(fullPath).pipe(gulp.dest(dest_dev));
});

gulp.task('publish-hml-full', function() {
    return gulp.src(fullPath).pipe(gulp.dest(dest_hml));
});

gulp.task('publish-prod-sim-full', function() {
    return gulp.src(fullPath).pipe(gulp.dest(dest_prod));
});