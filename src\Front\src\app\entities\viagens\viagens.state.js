(function () {
    'use strict';

    angular.module('bbcWeb.viagens.state', []).config(routeConfig);

    function routeConfig($urlRouterProvider, $stateProvider) {
        $stateProvider.state('viagens', {
            abstract: true,
            url: "/viagens",
            templateUrl: "app/layout/content.html"
        })
        .state('viagens.index', {
            url: '/index',
            templateUrl: 'app/entities/viagens/viagens.html'
        })
        .state('viagens.pagamentos-viagem', {
            url: '/:link',
            templateUrl: 'app/entities/viagens/pagamentos/pagamentos-viagem.html'
        })
        .state('viagens.transacoes-pagamento', {
            url: '/pagamentos/:viagem/:pagamento',
            templateUrl: 'app/entities/viagens/transacoes/transacoes-pagamento.html'
        })
        .state('viagens.transacoes-pagamento-historico', {
            url: '/pagamentos/:viagem/:pagamentoHistorico/:pagamentoEvento',
            templateUrl: 'app/entities/viagens/transacoes/transacoes-pagamento-historico.html'
        });;
    }
})();