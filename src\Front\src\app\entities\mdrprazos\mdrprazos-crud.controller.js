(function () {
    'use strict';

    angular.module('bbcWeb').controller('MDRPrazosCrudController', MDRPrazosCrudController);

    MDRPrazosCrudController.$inject = ['toastr', '$rootScope', 'BaseService', '$state', '$stateParams', '$window', 'PERFIL_ADMINISTRADOR', '$scope', '$timeout', 'PersistentDataService', 'DefaultsService', '$uibModal'];

    function MDRPrazosCrudController(
        toastr, $rootScope,
        BaseService, $state,
        $stateParams, $window,
        PERFIL_ADMINISTRADOR,
        $scope, $timeout,
        PersistentDataService,
        DefaultsService,
        $uibModal
    ) {
        var vm = this;
        vm.mdrPrazos = [];
        vm.menusPai = [];

        vm.headerItems = [{
            name: 'Cadastros'
        }, {
            name: 'MDR/Prazos',
            link: 'mdrprazos.index'
        }, {
            name: $stateParams.link == 'novo' ? 'Novo' : 'Editar'
        }];

        var selfScope = PersistentDataService.get('MDRPrazosCrudController');

        if ($stateParams.link == 'novo')
            vm.mdrPrazos.id = 'Auto';

        vm.isNew = function () {
            return $stateParams.link == 'novo';
        };

        vm.loadEdit = function (id) {
            BaseService.get('MDRPrazos', 'ConsultarPorId', {
                idMDRPrazos: id
            }).then(function (response) {
                if (!response.success) {
                    toastr.error(response.message);
                    return;
                } else {
                    vm.mdrPrazos = response.data;
                    vm.consultaBanco.selectedValue = vm.mdrPrazos.bancoId;
                    vm.consultaBanco.selectedText = vm.mdrPrazos.banco.nome;
                }
            });
        };

        vm.save = function (form) {
            if (!form.$valid) {
                toastr.error('Todos os campos obrigatórios devem ser preenchidos corretamente com seus respectivos valores');
                return;
            }

            if (vm.isSaving === true)
                return;

            var mdrPrazos = {}

            mdrPrazos.Id = vm.mdrPrazos.id == "Auto" ? 0 : vm.mdrPrazos.id;
            mdrPrazos.BancoId = vm.consultaBanco.selectedValue;
            mdrPrazos.MDR = vm.mdrPrazos.mdr.toString().replace(".", ",");
            mdrPrazos.Prazo = vm.mdrPrazos.prazo;
            mdrPrazos.Ativo = vm.mdrPrazos.ativo;
            mdrPrazos.Descricao = vm.mdrPrazos.descricao;

            vm.isSaving = true;

            BaseService.post('MDRPrazos', 'Salvar', mdrPrazos).then(function (response) {
                vm.isSaving = false;

                if (response && !response.sucesso)
                    return toastr.error(response.mensagem);
                else {
                    toastr.success('Prazo salvo com sucesso!');
                    $state.go('mdrprazos.index');
                }
            });
        };

        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.administrador == true;
        };

        function init() {
            if (vm.isNew()) {
                vm.mdrPrazos.id = 'Auto';
            }
        };

        vm.onClickAvancar = function (wizard) {
            wizard.go(wizard.getActivePosition() + 1);
        };

        vm.onClickVoltar = function (wizard) {

            var ativoIndex = wizard.getActivePosition();
            if (ativoIndex == 1)
                $state.go('mdrprazos.index');

            wizard.go(ativoIndex - 1);
        };

        init();

        $scope.$on('$stateChangeStart', function (arg1, toState) {
            if (toState.name === 'mdrprazos.index')
                PersistentDataService.remove('MDRPrazosCrudController');
            else
                PersistentDataService.store('MDRPrazosCrudController', vm, "Cadastro - MDR/Prazos", null, "mdrprazos.mdrprazos-crud", vm.mdrPrazos.id);
        });

        if (angular.isDefined(selfScope))
            angular.extend(vm, selfScope.data);
        else {
            if (!vm.isNew())
                vm.loadEdit($stateParams.link);
            else {
                vm.modulos = 1;
            }
        }

        $timeout(function () {
            PersistentDataService.remove('MDRPrazosController');
        }, 15);

        vm.consultaBanco = {
            columnDefs: [{
                name: 'Código',
                field: 'id',
                width: 80,
                primaryKey: true
            }, {
                name: 'Nome',
                field: 'nome',
                width: '*',
                minWidth: 150
            }],
            desiredValue: 'id',
            desiredText: 'nome',
            url: 'Banco/ConsultarGridBancoCombo',
            paramsMethod: function () {
                return {}
            },
        };
    }
})();
