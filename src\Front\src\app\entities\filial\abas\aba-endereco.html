<div class="form-horizontal">
        <hr />
        <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
            <div class="row">
                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                    <div class="form-group">
                        <label class="control-label col-xs-12 col-sm-2 col-md-4 col-lg-3">
                            <span class="text-danger mr-5">*</span>CEP:
                        </label>
                        <div class="input-group col-xs-12 col-sm-10 col-md-8 col-lg-9">
                                <input
                                    type="text"
                                    name="CEP"
                                    ui-br-cep-mask
                                    class="form-control"
                                    validate-on="blur"
                                    ng-model="vm.filial.Cep"
                                    ng-blur="vm.buscarEndereco(vm.filial.Cep)"
                                    required-message="'CEP é obrigatório'"
                                    required/>
                        </div>
                    </div>
                </div>
                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                    <div class="form-group">
                        <label class="control-label col-xs-12 col-sm-2 col-md-4 col-lg-3">
                            <span class="text-danger mr-5">*</span>Estado:
                        </label>
                        <div class="input-group col-xs-12 col-sm-10 col-md-8 col-lg-9">
                            <ui-select
                                name="Estado"
                                ats-ui-select-validator
                                validate-on="blur"
                                ng-model="vm.filial.EstadoId"
                                ng-change="vm.estadoChange(vm.filial.EstadoId)"
                                required-message="'Estado é obrigatório'"
                                required>
                                <ui-select-match>
                                    <span>{{$select.selected.descricao}}</span>
                                </ui-select-match>
                                <ui-select-choices repeat="ex.id as ex in vm.estados | propsFilter: {descricao: $select.search}">
                                    <div ng-bind-html="ex.descricao | highlight: $select.search"></div>
                                </ui-select-choices>
                            </ui-select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                    <div class="form-group">
                        <label class="control-label col-xs-12 col-sm-2 col-md-4 col-lg-3">
                            <span class="text-danger mr-5">*</span>Cidade:
                        </label>
                        <div class="input-group col-xs-12 col-sm-10 col-md-8 col-lg-9">
                            <ui-select 
                                name="Cidade"
                                ats-ui-select-validator
                                validate-on="blur"
                                ng-model="vm.filial.CidadeId"
                                ng-disabled="vm.cidadesDisabled"
                                required-message="'Cidade é obrigatória'"
                                required>
                                <ui-select-match>
                                    <span>{{$select.selected.descricao}}</span>
                                </ui-select-match>
                                <ui-select-choices repeat="ex.id as ex in vm.cidades | propsFilter: {descricao: $select.search}">
                                    <div ng-bind-html="ex.descricao | highlight: $select.search"></div>
                                </ui-select-choices>
                            </ui-select>
                        </div>
                    </div>
                </div>
                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                    <div class="form-group">
                        <label class="control-label col-xs-12 col-sm-2 col-md-4 col-lg-3">
                            <span class="text-danger mr-5">*</span>Endereço:
                        </label>
                        <div class="input-group col-xs-12 col-sm-10 col-md-8 col-lg-9">
                            <input
                                type="text"
                                name="Endereço"
                                maxlength="200"
                                class="form-control"
                                validate-on="blur"
                                ng-model="vm.filial.Endereco"
                                required-message="'Endereço é obrigatório'"
                                required/>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                    <div class="form-group">
                        <label class="control-label col-xs-12 col-sm-2 col-md-4 col-lg-3">
                            <span class="text-danger mr-5">*</span>Bairro:
                        </label>
                        <div class="input-group col-xs-12 col-sm-10 col-md-8 col-lg-9">
                            <input
                                type="text"
                                name="Bairro"
                                maxlength="100"
                                class="form-control"
                                validate-on="blur"
                                ng-model="vm.filial.Bairro"
                                required-message="'Bairro é obrigatório'"
                                required/>
                        </div>
                    </div>
                </div>
                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                    <div class="form-group">
                        <label class="control-label col-xs-12 col-sm-2 col-md-4 col-lg-3">Número:</label>
                        <div class="input-group col-xs-12 col-sm-10 col-md-8 col-lg-9">
                            <input
                                type="text"
                                maxlength="10"
                                ats-numeric
                                ng-model="vm.filial.EnderecoNumero"
                                name="Número"
                                class="form-control"/>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                    <div class="form-group">
                        <label class="control-label col-xs-12 col-sm-2 col-md-4 col-lg-3">Complemento:</label>
                        <div class="input-group col-xs-12 col-sm-10 col-md-8 col-lg-9">
                            <textarea
                                style="resize: none;"
                                type="text"
                                maxlength="100"
                                ng-model="vm.filial.Complemento"
                                name="Complemento"
                                class="form-control">
                            </textarea>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>