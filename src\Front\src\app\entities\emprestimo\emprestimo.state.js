(function () {
    'use strict';

    angular.module('bbcWeb.emprestimo.state', []).config(routeConfig);

    function routeConfig($urlRouterProvider, $stateProvider) {
        $stateProvider.state('emprestimo', {
            abstract: true,
            url: "/emprestimo",
            templateUrl: "app/layout/content.html"
        }).state('emprestimo.index', {
            url: '/index',
            templateUrl: 'app/entities/emprestimo/consulta/emprestimo.html'
        })
        .state('emprestimo.crud', {
            url: '/:link',
            templateUrl: 'app/entities/emprestimo/cadastro/emprestimo-crud.html'
        });
    }
})();