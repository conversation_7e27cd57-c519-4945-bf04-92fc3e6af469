(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('DocumentosProrcessoVinculadoController', DocumentosProrcessoVinculadoController);

        DocumentosProrcessoVinculadoController.inject = ['BaseService', '$rootScope', 'toastr', '$scope', 'PersistentDataService', '$timeout', '$state', 'PERFIL_ADMINISTRADOR'];

    function DocumentosProrcessoVinculadoController(BaseService, $rootScope, toastr, $scope, PersistentDataService, $timeout, $state, PERFIL_ADMINISTRADOR) {
        var vm = this;
        vm.headerItems = [{
            name: 'Cadastro<PERSON>'
        }, {
            name: 'Documentos' 
        }];

        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.perfil == PERFIL_ADMINISTRADOR;
        };

        vm.gridOptions = {
            data: [],
            onRegisterApi: BaseService.dataGrid.defaultOnRegisterApi,
            dataSource: {
                url: "DocumentosProcessoVinculado/ConsultarGridDocumentosProcessoVinculado"
            },
            columnDefs: [{
                name: 'Ações', 
                width: 80, 
                enableColumnMenu: false, 
                cellTemplate: '<div ng-if="!row.groupHeader" class="ui-grid-cell-contents" title="TOOLTIP">\
                        <button ng-disabled="row.entity.ativo===0" tooltip-placement="right" uib-tooltip="Editar" type="button" ui-sref="documentos-processo-vinculado.documentos-processo-vinculado-crud({link: row.entity.id})"\
                            ng-class="{ \'btn btn-xs btn-info\': true }">\
                            <i class="fa fa-edit"></i>\
                        </button>\
                        <button type="button" tooltip-placement="right" uib-tooltip-html="row.entity.ativo===0 ? \'Ativar\' : \'Inativar\'" \
                            ng-click="grid.appScope.vm.alterarStatus(row.entity.id, row.entity.ativo)" \
                            ng-class="row.entity.ativo===1 ? \'btn btn-xs btn-success\' : \'btn btn-xs btn-danger\'"> \
                            <i ng-class="row.entity.ativo===1 ? \'fa fa-check\' : \'fa fa-times\'"></i>\
                        </button>\
                    </div>'
            },{
                name: 'Codigo',
                displayName: 'Código',
                width: 80,
                type: 'number',
                primaryKey: true,
                field: 'id'
            }, {
                name: 'ProcessoVinculado',
                displayName: 'Processo vinculado',
                width: '*',
                field: 'processoVinculadoId',
                serverField: 'processoVinculadoId',
                enableGrouping: false,
                enum: true,
                enumTipo: 'EProcessoVinculados',
                cellTemplate: '<div class="ui-grid-cell-contents" align="left">\
                                        <p ng-show="row.entity.processoVinculadoId === 0"> Usuário </p>\
                                        <p ng-show="row.entity.processoVinculadoId === 1"> Comprovantes </p>\
                                        <p ng-show="row.entity.processoVinculadoId === 2"> Credenciamento </p>\
                                        <p ng-show="row.entity.processoVinculadoId === 3"> Veículos </p>\
                                   </div>',
                enableFiltering: true,
            }, {
                name: 'Documento',
                displayName: 'Documento',
                width: '*',
                field: 'documento',
                serverField: 'documento',
                enableFiltering: true
            }, {
                name: 'Obrigatório',
                displayName: 'Obrigatório',
                width: '*',
                field: 'obrigatorio',
                cellTemplate: '<div class="ui-grid-cell-contents" align="left">\
                                        <p ng-show="row.entity.obrigatorio === 1"> Sim </p>\
                                        <p ng-show="row.entity.obrigatorio === 0"> Não </p>\
                                   </div>',
                enableFiltering: false
            }, {
                name: 'Formato',
                displayName: 'Tipo do arquivo',
                width: '*',
                field: 'tipo',
                cellTemplate: '<div class="ui-grid-cell-contents" align="left">\
                                        <p ng-show="row.entity.tipo === 1"> PDF </p>\
                                        <p ng-show="row.entity.tipo === 0"> Imagem </p>\
                                   </div>',
                enableFiltering: false
            }, {
                name: 'DataCadastro',
                displayName: 'Data cadastro',
                width: '*',
                field: 'dataCadastro',
                enableFiltering: false
            }]
        };

        vm.alterarStatus = function (id, ativo) {
            BaseService.post('DocumentosProcessoVinculado', 'AlterarStatus', {
                id: id
            }).then(function (response) {
                response.success ? 
                (ativo === 1 ? 
                    toastr.success('Documento inativado com sucesso!') 
                    : toastr.success('Documento reativado com sucesso!')) 
                : toastr.error(response.message);
                
                vm.gridOptions.dataSource.refresh();
            });
        };

        // Controle de aba!!
        $scope.$on('$stateChangeStart', function () {
            PersistentDataService.store('DocumentosProrcessoVinculadoController', vm, "Lista de documentos", "DocumentosProrcessoVinculadoController", "documentos-processo-vinculado.index");
        });

        var selfScope = PersistentDataService.get('DocumentosProrcessoVinculadoController');
        var filho = PersistentDataService.get('DocumentosProrcessoVinculadoCrudController');
        if (angular.isDefined(filho)) {
            $timeout(function () {
                $state.go('documentos-processo-vinculado.documentos-processo-vinculado-crud', {
                    link: filho.data.documentosProcessoVinculado.IdDocumentosProcessoVinculado > 0 ? 'editar' : 'novo'
                });
            }, 50);
        } else if (angular.isDefined(selfScope)) {
            angular.extend(vm, selfScope.data);

            $timeout(function () {
                vm.gridOptions.dataSource.refresh();
            }, 450);
        }
    }
})();