(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('PainelPagamentoAbastecimentoController', PainelPagamentoAbastecimentoController);

        PainelPagamentoAbastecimentoController.inject = [
        'BaseService',
        '$rootScope',
        'toastr',
        '$scope',
        'PersistentDataService',
        '$timeout',
        '$state',
        '$stateParams',
        'PERFIL_ADMINISTRADOR',
        'oitozero.ngSweetAlert', 
        '$uibModal',
        '$window'
    ];

    function PainelPagamentoAbastecimentoController(
        BaseService, 
        $rootScope, 
        toastr, 
        $scope, 
        PersistentDataService, 
        $timeout, 
        $state, 
        $stateParams, 
        PERFIL_ADMINISTRADOR, 
        SweetAlert,
        $uibModal,
        $window) {

        var vm = this;

        //#region "Área responsavel pela operações da aba pagamentos"
        vm.status = 0;
        vm.tipoOperacao = 0;
        vm.ativarTodasConta = false;
        vm.pagamentosPendente = [];
        vm.pagamentosPendenteId = [];

        vm.enumStatusLista = [
            { id: 0, descricao: 'Todos'}, 
            { id: 1, descricao: 'Processando'}, 
            { id: 2, descricao: 'Autorização de remessa'}, 
            { id: 3, descricao: 'Em fila de pagamento'},
            { id: 4, descricao: 'Pagamento realizado'},
            { id: 5, descricao: 'Cancelado'}, 
            { id: 6, descricao: 'Não efetivado'},
            { id: 7, descricao: 'Reprovado'}
        ]

        vm.enumTipoOperacaoLista = [
            { id: 0, descricao: 'Todos' }, 
            { id: 1, descricao: 'Retenção' }, 
            { id: 2, descricao: 'Pagamento' }
        ]

       
        vm.enumStatus = vm.enumStatusLista;
        vm.enumTipoOperacao = vm.enumTipoOperacaoLista;

        vm.headerItems = [{ name: 'Movimentações' }, { name: 'Painel pagamento abastecimento' }];

        vm.load = function () {
        }

        vm.atualizaTela = function () {
            vm.gridOptions.dataSource.refresh();
            vm.AtualizaComboStatus();
        }       

        vm.AtualizaComboStatus = function(){
            vm.enumStatus = [];

            switch(vm.tipoOperacao) {
                case 1:
                    vm.enumStatus.push(vm.enumStatusLista[4],vm.enumStatusLista[5],vm.enumStatusLista[6],vm.enumStatusLista[1],vm.enumStatusLista[0]);      
                  break;
                case 2:
                    vm.enumStatus.push(vm.enumStatusLista[3],vm.enumStatusLista[2],vm.enumStatusLista[6],vm.enumStatusLista[7],vm.enumStatusLista[1],vm.enumStatusLista[0],vm.enumStatusLista[4]);           
                  break;
                default:
                    vm.enumStatus = vm.enumStatusLista;
              }
        }

        vm.setarStatusTodos =  function(){
            vm.status = 0;
        }
        
        vm.dateOptions = {
            timePicker: false,
            applyClass: 'btn-primary',
            locale: {
                applyLabel: "Aplicar",
                fromLabel: "De",
                format: "DD/MM/YYYY",
                toLabel: "Até",
                cancelLabel: 'Cancelar',
                customRangeLabel: 'Período',
                monthNames: ['Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho', 'Julho', 'Agosto', 'Setembro',
                    'Outubro', 'Novembro', 'Dezembro'
                ],
                daysOfWeek: ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb']
            },
            ranges: {
                'Últimos 1 dias': [moment().add(-1, 'days'), moment()],
                'Últimos 7 dias': [moment().add(-7, 'days'), moment()]
            }
        };

        vm.dataPagamento = {
            startDate: moment().add(-1, 'days'),
            endDate: moment()
        };

        vm.contas = [];
        vm.clicouNoCheckBoxConta = 0;

        vm.onClickMarcarConta = function (row) {
            row.IsChecked = true;
            vm.clicouNoCheckBoxConta = 1;

            var newItem = {
                Id: row.id,
                dtInicial: vm.dataPagamento.startDate.toDate(),
                dtFinal: vm.dataPagamento.endDate.toDate(),
                tipoOperacao: vm.tipoOperacao,
                status: vm.status,
                EmpresaId: vm.consultaEmpresa.selectedValue,
                Ativo: false
            };
            vm.contas.push(newItem);
        };

        vm.isContaMarcada = function (row) {
            var idxOf = vm.contas.indexOf(row.Id);
            if (idxOf > -1 || row.IsChecked)
                return true;
            return row.ativo;
        };

        vm.onClickDesmarcarConta = function (row) {
            vm.clicouNoCheckBoxConta = 1;

            if  (vm.ativarTodasConta){
                var newItem = {
                    Id: row.id,
                    dtInicial: vm.dataPagamento.startDate.toDate(),
                    dtFinal: vm.dataPagamento.endDate.toDate(),
                    tipoOperacao: vm.tipoOperacao,
                    status: vm.status,
                    EmpresaId: vm.consultaEmpresa.selectedValue,
                    Ativo: false
                };
                vm.contas.push(newItem);
            }else{
                vm.contas = vm.contas.filter(function(el) { 
                    return el.Id !== row.id; 
                }); 
            }

            row.IsChecked = false;
        };

        vm.inativarAtivarTodasContas = function(){
            vm.ativarTodasConta = !vm.ativarTodasConta;           
            
            for(var i=0;i< this.gridApi.grid.rows.length;i++)
                this.gridApi.grid.rows[i].entity.ativo = vm.ativarTodasConta;
             
        }

        $scope.$watch('vm.consultaEmpresa.selectedValue', function (newValue) {
            vm.empresaConsulta = vm.consultaEmpresa.selectedValue;
        });

        vm.gridOptions = {
            data: [],
            onRegisterApi: BaseService.dataGrid.defaultOnRegisterApi,
            urlRelatorio: "PagamentoAbastecimento/ConsultarGridPagamentoAbastecimento",
            dataSource: {
                autoBind: false,
                url: "PagamentoAbastecimento/ConsultarGridPagamentoAbastecimento",
                params: function () {
                    return {
                        dataInicial: vm.dataPagamento.startDate.format('DD/MM/YYYY').toString(),
                        dataFinal: vm.dataPagamento.endDate.format('DD/MM/YYYY').toString(),
                        tipoOperacao: vm.tipoOperacao,
                        status: vm.status,
                        EmpresaId: vm.consultaEmpresa.selectedValue,
                        AtivarTodasConta : vm.ativarTodasConta
                    }
                },
            },
            columnDefs: [{
                name: ' ',
                width: 40,
                cellTemplate: '<input type=\"checkbox\" ng-model=\"row.entity.ativo\" ng-disabled="grid.appScope.vm.tipoOperacao != 2" ng-click="grid.appScope.vm.onClickDesmarcarConta(row.entity)" ng-false-value=\'false\' />\
                    <input type=\"checkbox\" ng-show="!grid.appScope.vm.isContaMarcada(row.entity)" ng-model=\"row.entity.ativo\"  ng-click="grid.appScope.vm.onClickMarcarConta(row)" ng-true-value=\'true\'/>'
            },{
                name: 'Ações',
                width: '4%',
                cellTemplate: '<div ng-if="!row.groupHeader" class="ui-grid-cell-contents" align="center" title="TOOLTIP">\
                                    <div class="container-btn-action" ng-show="grid.appScope.vm.tipoOperacao == 2 && grid.appScope.vm.status == 6 || grid.appScope.vm.status == 1 || grid.appScope.vm.status == 3">\
                                        <button tooltip-placement="right" uib-tooltip="Reenviar pagamento" type="button" ng-click="grid.appScope.vm.reenviarPagamento(row.entity)" ng-class="row.entity.status===\'Aberto\' ? \'btn btn-xs btn-danger\' : \'btn btn-xs btn-danger\'">\
                                            <i ng-class="\'fa fa-refresh\'"></i>\
                                        </button>\
                                    </div>\
                                    <div class="container-btn-action" ng-show="row.entity.tipoOperacao == \'Pagamento\'">\
                                        <button tooltip-placement="right" uib-tooltip="Exibir abastecimentos" type="button" ng-click="grid.appScope.vm.consultarLote(row.entity.lotePagamentoId)" ng-class="row.entity.status===\'Aberto\' ? \'btn btn-xs btn-info\' : \'btn btn-xs btn-info\'">\
                                            <i ng-class="\'fa fa-eye\'"></i>\
                                        </button>\
                                    </div>\
                                </div>'
            },
            {
                name: 'Codigo transação',
                displayName: 'Código transação',
                width: 100,
                field: 'id',
                serverField: 'id',
                type: 'number',
                enableFiltering: true
            },
            {
                name: 'Razão Social',
                displayName: 'Razão Social',
                width: 135,
                field: 'razaoSocial',
                serverField: 'razaoSocial',
                enableFiltering: false,
                enableSorting: false
            },
            {
                name: 'Posto CNPJ',
                displayName: 'Posto CNPJ',
                width: 135,
                field: 'postoCnpj',
                serverField: 'postoCnpj',
                enableFiltering: false,
                enableSorting: false
            },
            {
                name: 'Código(s) abastecimento(s)',
                displayName: 'Código abastecimento',
                width: 135,
                field: 'abastecimentos',
                serverField: 'abastecimentos',
                type: 'number',
                enableFiltering: false,
                enableSorting: false
            },
            {
                name: 'Valor abastecimento',
                displayName: 'Valor abastecimento',
                width: 120,
                field: 'valorAbastecimento',
                serverField: 'valorAbastecimento',
                cellTemplate: '<div class="ui-grid-cell-contents">\
                                    <input type="text" ng-model="row.entity.valorAbastecimento" readonly\
                                            class="no-borders" style="background: none;" ui-money-mask="3" />\
                               </div>',
                enableFiltering: false,
                enableSorting: false
            },
            {
                name: 'Valor pagamento',
                displayName: 'Valor pagamento',
                width: 120,
                field: 'valorAbastecimentoDesconto',
                serverField: 'valorAbastecimentoDesconto',
                cellTemplate: '<div class="ui-grid-cell-contents">\
                                    <input type="text" ng-model="row.entity.valorAbastecimentoDesconto" readonly\
                                            class="no-borders" style="background: none;" ui-money-mask="3" />\
                               </div>',
                enableFiltering: false,
                enableSorting: false
            },
            {
                name: 'Valor com taxa',
                displayName: 'Valor com taxa',
                width: 120,
                field: 'valorAbastecimentoAcrescimoTaxa',
                serverField: 'valorAbastecimentoAcrescimoTaxa',
                cellTemplate: '<div class="ui-grid-cell-contents">\
                                    <input type="text" ng-model="row.entity.valorAbastecimentoAcrescimoTaxa" readonly\
                                            class="no-borders" style="background: none;" ui-money-mask="3" />\
                               </div>',
                enableFiltering: false,
                enableSorting: false
            },
            {
                name: '% Empresa',
                displayName: '% Empresa',
                width: 140,
                field: 'valorTaxaEmpresa',
                serverField: 'valorTaxaEmpresa',
                enableFiltering: false,
                cellTemplate: '<div class="ui-grid-cell-contents">\
                                    <input type="text" ng-model="row.entity.valorTaxaEmpresa" readonly\
                                            class="no-borders" style="background: none;" ui-money-mask="3" />\
                               </div>',
                enableSorting: false
            },
            {
                name: 'MDR',
                displayName: 'MDR',
                width: 140,
                field: 'descontoMDR',
                serverField: 'descontoMDR',
                enableFiltering: false,
                cellTemplate: '<div class="ui-grid-cell-contents">\
                                    <input type="text" ng-model="row.entity.descontoMDR" readonly\
                                            class="no-borders" style="background: none;" ui-money-mask="3" />\
                               </div>',
                enableSorting: false
            },
            {
                name: 'Previsão de pagamento',
                displayName: 'Previsão de pagamento',
                width: 140,
                field: 'dataPrevisaoPagamento',
                serverField: 'dataPrevisaoPagamento',
                enableFiltering: false,
                enableSorting: false
            },
            {
                name: 'Status',
                displayName: 'Status',
                width: 170,
                field: 'status',
                serverField: 'status',
                enableFiltering: false,
                cellTemplate: '<div class="ui-grid-cell-contents" align="left">\
                                        <p ng-show="row.entity.status === \'Processando\'"> Processando </p>\
                                        <p ng-show="row.entity.status === \'PendenteAprovacao\'"> Autorização de remessa </p>\
                                        <p ng-show="row.entity.status === \'Aprovado\'"> Em fila de pagamento </p>\
                                        <p ng-show="row.entity.status === \'Baixado\'"> Pagamento realizado </p>\
                                        <p ng-show="row.entity.status === \'Cancelado\'"> Cancelado </p>\
                                        <p ng-show="row.entity.status === \'Erro\'"> Não efetivado </p>\
                                        <p ng-show="row.entity.status === \'Reprovado\'"> Reprovado </p>\
                                   </div>',
                enableSorting: false
            },
            {
                name: 'Codigo Lote',
                displayName: 'Código Lote',
                width: '90',
                field: 'lotePagamentoId',
                serverField: 'lotePagamentoId',
                enableFiltering: false
            },
            {
                name: 'Conta origem',
                displayName: 'Conta origem',
                width: 140,
                field: 'contaOrigem',
                serverField: 'contaOrigem',
                type: 'number',
                enableFiltering: false,
                enableSorting: false
            },
            {
                name: 'Conta destino',
                displayName: 'Conta destino',
                width: 140,
                field: 'contaDestino',
                serverField: 'contaDestino',
                type: 'number',
                enableFiltering: false,
                enableSorting: false
            },
            {
                name: 'Data alteração',
                displayName: 'Data alteração',
                width: 140,
                field: 'dataAlteracao',
                serverField: 'dataAlteracao',
                enableFiltering: false,
                enableSorting: false
            },
            {
                name: 'Tipo operação',
                displayName: 'Tipo operação',
                width: 170,
                field: 'tipoOperacao',
                serverField: 'tipoOperacao',
                enableFiltering: false,
                cellTemplate: '<div class="ui-grid-cell-contents" align="left">\
                                        <p ng-show="row.entity.tipoOperacao === \'Retencao\'"> Retenção </p>\
                                        <p ng-show="row.entity.tipoOperacao === \'Pagamento\'"> Pagamento </p>\
                                   </div>',
                enableSorting: false
            },
            {
                name: 'Usuario de cadastro',
                displayName: 'Usuario de cadastro',
                width: 80,
                type: 'number',
                field: 'usuarioCadastroId',
                serverField: 'usuarioCadastroId',
                enableFiltering: false,
                enableSorting: false
            }]
        };

        vm.consultaEmpresa = {
            columnDefs: [{
                name: 'Cód.',
                field: 'id',
                width: 60,
                type: 'number',
                primaryKey: true
            }, {
                name: 'Nome Fantasia',
                field: 'nomeFantasia',
                width: '*'
            }, {
                name: 'Razão Social',
                field: 'razaoSocial',
                width: '*'
            }, {
                name: 'Email',
                field: 'email',
                width: 120
            }],
            desiredValue: 'id',
            desiredText: 'nomeFantasia',
            url: 'Empresa/ConsultarGridEmpresaCombo',
            paramsMethod: function () {
                return {}
            }
        };

        vm.reprovaPagamento = function () { 
            vm.pagamentosPendenteId = [];
            vm.pagamentosPendente = [];
            
            //So os marcados
            for(var i=0;i< this.gridApi.grid.rows.length;i++)
            {
                if(this.gridApi.grid.rows[i].entity.ativo)
                    vm.pagamentosPendente.push(this.gridApi.grid.rows[i].entity); 
            }

            for(var i=0;i<vm.pagamentosPendente.length;i++)
            {
                vm.pagamentosPendenteId.push({IdPagamento: vm.pagamentosPendente[i].id });

                if(vm.pagamentosPendente[i].status != 'PendenteAprovacao'){
                    toastr.info('Apenas itens com status de aguardando pendente de são permitidos para este processo.');
                    vm.pagamentosPendenteId = [];
                    vm.pagamentosPendente = [];
                    return;
                }
            }

            if(vm.pagamentosPendenteId.length <= 0){
                toastr.info('Selecione pelo menos um item para realizar a operação!');
                return;
            }

            BaseService.post('PagamentoAbastecimento', 'ReprovarPagamento', vm.pagamentosPendenteId
            ).then(function (response) {
                if (response.success) {
                    toastr.success(response.message);
                    vm.pagamentosPendenteId = [];
                    vm.gridOptions.dataSource.refresh();
                } else{
                    toastr.error(response.message);
                    vm.pagamentosPendenteId = [];
                    vm.gridOptions.dataSource.refresh();
                }
                    
            });
        };

        vm.aprovaPagamento = function () { 
            vm.pagamentosPendenteId = [];
            vm.pagamentosPendente = [];
            
            //So os marcados
            for(var i=0;i< this.gridApi.grid.rows.length;i++)
            {
                if(this.gridApi.grid.rows[i].entity.ativo)
                    vm.pagamentosPendente.push(this.gridApi.grid.rows[i].entity); 
            }

            for(var i=0;i<vm.pagamentosPendente.length;i++)
            {
                vm.pagamentosPendenteId.push({IdPagamento: vm.pagamentosPendente[i].id });
            }

            if(vm.pagamentosPendenteId.length <= 0){
                toastr.info('Selecione pelo menos um item para realizar a operação!');
                return;
            }

            BaseService.post('PagamentoAbastecimento', 'AprovarPagamento', vm.pagamentosPendenteId
            ).then(function (response) {
                if (response.success) {
                    toastr.success(response.message);
                    vm.pagamentosPendenteId = [];
                    vm.gridOptions.dataSource.refresh();
                } else{
                    toastr.error(response.message);
                    vm.pagamentosPendenteId = [];
                    vm.gridOptions.dataSource.refresh();
                }
                    
            });
        };

        vm.consultarLote = function (lote) { 
            $uibModal.open({
                animation: true,
                ariaLabelledBy: 'modal-title',
                ariaDescribedBy: 'modal-body',
                templateUrl: 'app/entities/painel-pagamento-abastecimento/modal-abastecimentos/modal-abastecimentos.html',
                controller: function ($uibModalInstance, $uibModalStack, $scope, BaseService, lote) {
                    var vm = this;

                    vm.lotePagamento = lote;

                    vm.headerItems = [{
                        idPosto: 0,
                        name: 'Lote de abastecimentos'
                    }];

                    vm.gridLoteOptions = {
                        data: [],
                        onRegisterApi: BaseService.dataGrid.getGridApiRegistration("gridLoteOptions"),
                        dataSource: {
                            autoBind: true,
                            url: "Abastecimento/ConsultarGridLoteAbastecimento",
                            params: function () {
                                return {                                    
                                    idPosto: 0,
                                    LotePagamento: vm.lotePagamento
                                }
                            },
                        },
                        columnDefs: [{
                            name: 'Codigo',
                            displayName: 'Código',
                            width: 80,
                            field: 'id',
                            serverField: 'id',
                            type: 'number',
                            enableFiltering: true
                        },
                        {
                            name: 'Valor abastecimento',
                            displayName: 'Valor abastecimento',
                            width: 140,
                            field: 'valorAbastecimento',
                            serverField: 'valorAbastecimento',
                            cellTemplate: '<div class="ui-grid-cell-contents">\
                                                <input type="text" ng-model="row.entity.valorAbastecimento" readonly\
                                                        class="no-borders" style="background: none;" ui-money-mask="3" />\
                                        </div>',
                            enableFiltering: false,
                            enableSorting: false
                        },
                        {
                            name: 'Combustivel',
                            displayName: 'Combustível',
                            width: '*',
                            field: 'combustivel',
                            serverField: 'combustivel',
                            enableFiltering: false
                        },
                        {
                            name: 'Nota Fiscal',
                            displayName: 'Nota Fiscal',
                            width: '*',
                            field: 'notaXml',
                            serverField: 'notaXml',
                            enableFiltering: false
                        },
                        {
                            name: 'Protocolo',
                            displayName: 'Protocolo',
                            width: '*',
                            field: 'protocoloAbastecimentoId',
                            serverField: 'protocoloAbastecimentoId',
                            enableFiltering: false
                        }]
                    };
                
                    vm.fechar = function () {
                        $uibModalStack.dismissAll();
                    }
                    
                },
                controllerAs: 'vm',
                keyboard: true,
                size: 'lg',
                resolve: {
                    lote: lote
                }
            }).result.then(function () {
            });
        };

        vm.reenviarPagamento = function () { 
            vm.pagamentosPendenteId = [];
            vm.pagamentosPendente = [];
            //So os marcados
            for(var i=0;i< this.gridApi.grid.rows.length;i++)
            {
                if(this.gridApi.grid.rows[i].entity.ativo)
                    vm.pagamentosPendente.push(this.gridApi.grid.rows[i].entity); 
            }

            for(var i=0;i<vm.pagamentosPendente.length;i++)
            {
                vm.pagamentosPendenteId.push({IdPagamento: vm.pagamentosPendente[i].id });
            }

            if(vm.pagamentosPendenteId.length <= 0){
                toastr.info('Selecione pelo menos um item para realizar a operação!');
                return;
            }

            BaseService.post('PagamentoAbastecimento', 'ReenviarPagamento', vm.pagamentosPendenteId
            ).then(function (response) {
                if (response.success) {
                    toastr.warning (response.message);
                    vm.pagamentosPendenteId = [];
                    vm.gridOptions.dataSource.refresh();
                } else{
                    toastr.error(response.message);
                    vm.pagamentosPendenteId = [];
                    vm.gridOptions.dataSource.refresh();
                }
                    
            });
        };
        //#endregion "Área responsavel pela operações da aba pagamentos"
        
        //#region "Área responsavel pela operações da aba receitas"

        vm.statusReceita = 0;
        vm.receitaPendente = [];
        vm.receitaPendenteId = [];
        vm.disabilitarReenvioReceita = false;

        vm.enumStatusReceita = [
            { id: 2, descricao: 'Autorização de remessa'}, 
            { id: 6, descricao: 'Não efetivado'},
            { id: 3, descricao: 'Em fila de pagamento'},
            { id: 4, descricao: 'Pagamento realizado'}
        ]

        vm.dataReceita = {
            startDate: moment().add(-1, 'days'),
            endDate: moment()
        };
        
        vm.atualizaTelaReceita = function () {
            vm.gridReceitaOptions.dataSource.refresh();
        }

        vm.receitas = [];
        vm.clicouNoCheckBoxReceita = 0;

        vm.gridReceitaOptions = {
            data: [],
            onRegisterApi: BaseService.dataGrid.getGridApiRegistration("gridReceitaOptions"),
            urlRelatorio: "PagamentoAbastecimento/ConsultarGridReceitaAbastecimento",
            dataSource: {
                autoBind: false,
                url: "PagamentoAbastecimento/ConsultarGridReceitaAbastecimento",
                params: function () {
                    return {
                        dataInicial: vm.dataReceita.startDate.format('DD/MM/YYYY').toString(),
                        dataFinal: vm.dataReceita.endDate.format('DD/MM/YYYY').toString(),
                        status: vm.statusReceita,
                        NumeroNota : vm.notaReceita,
                        NumeroProtocolo : vm.protocoloReceita
                    }
                },
            },
            columnDefs: [{
                name: ' ',
                width: 40,
                cellTemplate: '<input type=\"checkbox\" ng-model=\"row.entity.ativo\" ng-disabled="row.entity.status != \'Erro\' || grid.appScope.vm.statusReceita != 6" ng-false-value=\'false\' />\
                    <input type=\"checkbox\" ng-show="!grid.appScope.vm.isReceitaMarcada(row.entity)" ng-model=\"row.entity.ativo\"  ng-true-value=\'true\'/>'
            },
            {
                name: 'Codigo',
                displayName: 'Código',
                width: '90',
                field: 'id',
                serverField: 'id',
                enableFiltering: false
            },
            {
                name: 'Codigo Lote',
                displayName: 'Código Lote',
                width: '100',
                field: 'loteReceitaId',
                serverField: 'loteReceitaId',
                enableFiltering: false
            },
            {
                name: 'Conta origem',
                displayName: 'Conta origem',
                width: 140,
                field: 'contaOrigem',
                serverField: 'contaOrigem',
                type: 'number',
                enableFiltering: false,
                enableSorting: false
            },
            {
                name: 'Conta destino',
                displayName: 'Conta destino',
                width: 140,
                field: 'contaDestino',
                serverField: 'contaDestino',
                type: 'number',
                enableFiltering: false,
                enableSorting: false
            },
            {
                name: 'Valor receita',
                displayName: 'Valor receita',
                width: 120,
                field: 'valorReceita',
                serverField: 'valorReceita',
                cellTemplate: '<div class="ui-grid-cell-contents">\
                                    <input type="text" ng-model="row.entity.valorReceita" readonly\
                                            class="no-borders" style="background: none;" ui-money-mask="3" />\
                               </div>',
                enableFiltering: false,
                enableSorting: false
            },
            {
                name: 'Data receita',
                displayName: 'Data receita',
                width: 140,
                field: 'dataBaixaReceita',
                serverField: 'dataBaixaReceita',
                enableFiltering: false,
                enableSorting: false
            },
            {
                name: 'Tipo',
                displayName: 'Tipo',
                width: 170,
                field: 'tipoOperacao',
                serverField: 'tipoOperacao',
                enableFiltering: false,
                cellTemplate: '<div class="ui-grid-cell-contents" align="left">\
                                        <p ng-show="row.entity.tipoOperacao === \'ReceitaEmpresa\'"> % Empresa </p>\
                                        <p ng-show="row.entity.tipoOperacao === \'ReceitaMDR\'"> MDR </p>\
                                   </div>',
                enableSorting: false
            },
            {
                name: 'Status',
                displayName: 'Status',
                width: "*",
                field: 'status',
                serverField: 'status',
                enableFiltering: false,
                cellTemplate: '<div class="ui-grid-cell-contents" align="left">\
                                        <p ng-show="row.entity.status === \'PendenteAprovacao\'"> Autorização de remessa </p>\
                                        <p ng-show="row.entity.status === \'Erro\'"> Não efetivado </p>\
                                        <p ng-show="row.entity.status === \'Aprovado\'"> Em fila de pagamento </p>\
                                        <p ng-show="row.entity.status === \'Baixado\'"> Pagamento realizado </p>\
                                   </div>',
                enableSorting: false
            }]
        };

        vm.abrirModalRelatorio = function (gridName, dateName, controllerPai) {
            $uibModal.open({
                animation: true,
                ariaLabelledBy: 'modal-title',
                ariaDescribedBy: 'modal-body',
                templateUrl: 'app/entities/painel-pagamento-abastecimento/modal-relatorios/modal-relatorios.html',
                controller: function ($uibModalInstance, $uibModalStack) {
                    var vm = this;

                    vm.modalRelatorioOptions = [{}];

                    vm.headerItems = [{name: 'Transações Financeiras'}];

                    for (var x in controllerPai[gridName].columnDefs) {
                        vm.modalRelatorioOptions[x] = {
                            name: controllerPai[gridName].columnDefs[x].displayName,
                            field: controllerPai[gridName].columnDefs[x].field,
                            pipe: controllerPai[gridName].columnDefs[x].pipe,
                            enabled: true
                        }
                    }

                    vm.fechar = function () {
                        controllerPai.modalRelatorioOptions = vm.modalRelatorioOptions;
                        $uibModalStack.dismissAll();
                    }

                    vm.exportarRelatorio = function (extensao) {

                        if (vm.modalRelatorioOptions.filter(function(x){return x.enabled}).length <= 2) {
                            toastr.error("Escolha alguma coluna para gerar o relatório.");
                            return;
                        }

                        if (controllerPai[dateName].endDate.diff(controllerPai[dateName].startDate, 'days') > 30) {
                            toastr.error("Escolha um período de no máximo 30 dias para gerar o relatório.")
                            return;
                        }

                        controllerPai.modalRelatorioOptions = vm.modalRelatorioOptions;
                        controllerPai.exportarRelatorio(gridName, dateName, extensao);

                        $uibModalStack.dismissAll();
                    }

                },
                controllerAs: 'vm',
                keyboard: true,
                size: 'lg'
            }).result.then(function () {
            });
        };

        vm.exportarRelatorio = function (gridName, dateName, extensao) {
            switch (extensao){
                case 1: {
                    exportarEmExcel(gridName, true)
                    break;
                }
                case 2: {
                    exportarEmPdf(gridName, dateName)
                    break;
                }
                case 3: {
                    exportarEmTxt(gridName)
                    break;
                }
                case 4: {
                    exportarEmExcel(gridName, false)
                    break;
                }
                default: exportarEmPdf(gridName, dateName)
                    break;
            }
        };

        //todo fazer as grids aqui usarem o msm objeto (gridOptions)
        //todo ou fazer um metodo no baseService que recebe qlq gridOptions pra pegar o urlRelatorio
        
        function exportarEmPdf(gridName, dateName) {
            vm.desabilitarBtnRelatorio = true;
            vm[gridName].dataSource.consultarDadosRelatorio(function (response) {
                vm.dadosRelatorio = response.data.data.items;
                $timeout(function () {
                    BaseService.exportarTabelaEmPdfNovo(
                        "#exportable",
                        "Relatório de Pagamentos de Abastecimentos",
                        "BBC_Relatorio_Pagamento_Abastecimento_" + vm[dateName].startDate.toDate().toLocaleDateString().replace("/","-") + "_a_" + vm[dateName].endDate.toDate().toLocaleDateString().replace("/","-")
                    )
                }, 500);
                vm.desabilitarBtnRelatorio = false;
            }, gridName);
        }

        function exportarEmExcel(gridName, formatoXls) {
            vm.desabilitarBtnRelatorio = true;
            vm[gridName].dataSource.consultarDadosRelatorio(function (response) {
                vm.dadosRelatorio = response.data.data.items;
                $timeout(function () {
                    BaseService.exportarTabelaEmExcel("exportable-xls", "Transacoes", formatoXls)
                }, 500);
                vm.desabilitarBtnRelatorio = false;
            }, gridName);
        }

        function exportarEmTxt(gridName) {
            vm.desabilitarBtnRelatorio = true;
            vm[gridName].dataSource.consultarDadosRelatorio(function (response) {
                vm.dadosRelatorio = response.data.data.items;
                $timeout(function () {
                    BaseService.exportarTabelaEmTxt("exportable", "Transacoes")
                }, 500);
                vm.desabilitarBtnRelatorio = false;
            }, gridName);
        }

        vm.reenviarReceitas = function () { 

            vm.disabilitarReenvioReceita = true;
            
            vm.receitaPendente = [];
            //So os marcados
            for(var i=0;i< vm.gridReceitaOptions.data.length;i++)
            {
                if(vm.gridReceitaOptions.data[i].ativo){                    
                    vm.receitaPendente.push({IdLoteReceita: vm.gridReceitaOptions.data[i].loteReceitaId}); 
                }
            }

            if(vm.receitaPendente.length <= 0){
                toastr.info('Selecione pelo menos um item para realizar a operação!');
                return;
            }

            BaseService.post('PagamentoAbastecimento', 'ReenviarReceitas', vm.receitaPendente
            ).then(function (response) {
                toastr.info (response.message);
                    vm.ReceitaPendenteId = [];
                    vm.disabilitarReenvioReceita = false;
                    vm.gridOptions.dataSource.refresh();
                    
            });
        };

        //#endregion "Área responsavel pela operações da aba receitas"

        // Controle de aba!!
        $scope.$on('$stateChangeStart', function () {
            PersistentDataService.store('PainelPagamentoAbastecimentoController', vm, "Painel pagamento abastecimento", "PainelPagamentoAbastecimentoController", "painel-pagamento-abastecimento.index");
        });

        var selfScope = PersistentDataService.get('PainelPagamentoAbastecimentoController');
       
    }
})();