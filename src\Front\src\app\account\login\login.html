
<style class="ng-scope">


    #view-login {
        height: 100%;
    }

    .body-login {
        margin-left: 0;
        margin-right: 0;
    }

    .login-cover {
        /* background-color: #ffc107; */
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
    }

    .center-screen {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
    }

    .center-body {
        width: 100%;
        background-color: rgba(255, 255, 255, 1);
        display: block;
        text-align: center;
    }

    .center-content {
        padding-top: 3%;
        padding-bottom: 3%;
        width: 30%;
        display: inline-block;
        text-align: left !important;
    }

    @media only screen and (max-width: 600px) {
        .center-content {
            width: 50%;
        }
    }

    .input-form {
        /* background: rgba(0, 0, 0, 0.5); */
        border: none;
        /* color: black; */
        height: 46px;
        padding: 10px 16px;
        line-height: 1.3333333;
        border-radius: 6px !important;
    }

    .btn-login {
        width: 100%;
        font-size: 1.2em;
        /* background-color: rgb(255, 91, 87); */
    }

    .input-borderfocus:focus{
        border-style: solid !important;
        border-color: rgb(255,205,0) !important;
        border-radius: 6px;
        border-width: thin;
    }

    .input-bordebottom {
        border: lightgray;
        border-style: solid;
        border-width: thin;
    }
</style>

<div class="background-login" id="background">
    <div class="login-cover">
        <div class="center-screen">
            <div class="center-body">
                <br><br><br><br>
                <center> <img class="img-responsive logo" id="" src="assets/images/BBC_LOGO_DIGITAL_PANTONE.png" style="margin-top: 30px;"/>
                    <div class="center-content">
                        <spinner-loader ng-show="vm.isLoading"></spinner-loader>
    
                        <div class="row" ng-show="vm.isLoading">
                            <h3 class="m-t-none m-b" style="text-align: center; color: rgb(0, 0, 0);">Conectando, aguarde...
                            </h3>
                        </div>
    
                        <div ng-show="!vm.isLoading" class=" col-xs-12 col-sm-12 col-md-12 col-lg-12">
                            <form autocomplete="off" name="loginFrm" role="form">
    
                                <div class="form-group">
                                    <input type="text" autofocus required placeholder="Usuário"
                                        class="form-control input-form input-borderfocus input-bordebottom" ng-minlength="3" ng-model="vm.login.Usuario">
                                </div>
    
                                <div class="form-group">
                                    <input type="password" required placeholder="Senha" class="form-control input-form input-borderfocus input-bordebottom"
                                        ng-minlength="3" ng-model="vm.login.Senha">
                                </div>
    
                                <button class="btn  btn-primary btn-lg btn-login"
                                    ng-disabled="loginFrm.$invalid || vm.isLoading" ng-click="vm.submit()">Entrar</button>
                                <center>
                                    <div class="m-t-20 esqueceu-senha">
                                        Esqueceu sua senha? Clique <span style="color: #FEC726" id="esqueceuSenhaBtn"
                                            ng-click="vm.recuperarSenha();">aqui</span> para recuperá-la.
                                    </div>
    
                                    <div class="m-t-20 esqueceu-senha">
                                        <br>
                                        <a style="color: #FEC726" ng-click="vm.redirecionarCadastreSe()"
                                            title="Clique aqui para realizar o seu cadastro">Ainda não tenho cadastro</a>
                                    </div>
                                </center>
    
                            </form>
                        </div>
                    </div>
                </center>
                <br><br><br><br>
            </div>
    
        </div>
    </div>
</div>