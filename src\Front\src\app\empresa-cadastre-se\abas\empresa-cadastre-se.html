<div style="height: 100%;">
    <div class="col-lg-6 mb-30" style="padding-left: 0px; padding-right: 6px;">
        <div class="ibox animated fadeInRight">
            <div class="ibox-title" style="background-color: #056233; border-radius: 14px;">
                <h5 style="color: white">Dados da empresa</h5>
            </div>
            <div style="line-height: 20px; padding-top: 15px;">
                <div class="col-xm-12 col-sm-12 col-md-12 col-lg-12">
                    <div class="row">
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-12 col-md-3 col-lg-3 control-label mt-4"
                                style="text-align: left;"><span class="text-danger">*</span>
                                Razão social:</label>
                            <div class="intput-group col-xs-12 col-sm-12 col-md-9 col-lg-9">
                                <input class="form-control" type="text" ng-model="vm.empresa.razaoSocial"
                                    focus-me="shouldBeOpen" required-message="'Razão social é obrigatório'"
                                    maxlength="200" validate-on="blur" name="RazaoSocial" ng-required="true" />
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xm-12 col-sm-12 col-md-12 col-lg-12">
                    <div class="row">
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-12 col-md-3 col-lg-3 control-label mt-4"
                                style="text-align: left;"><span class="text-danger">*</span>
                                Nome fantasia:</label>
                            <div class="intput-group col-xs-12 col-sm-12 col-md-9 col-lg-9">
                                <input class="form-control" type="text" ng-model="vm.empresa.nomeFantasia"
                                    focus-me="shouldBeOpen" required-message="'Nome fantasia é obrigatório'"
                                    maxlength="200" validate-on="blur" name="NomeFantasia" ng-required="true" />
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xm-12 col-sm-12 col-md-12 col-lg-12">
                    <div class="row">
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-12 col-md-3 col-lg-3 control-label mt-4"
                                style="text-align: left;"><span class="text-danger">*</span>
                                E-mail:</label>
                            <div class="intput-group col-xs-12 col-sm-12 col-md-9 col-lg-9">
                                <input class="form-control" type="text" ng-model="vm.empresa.email"
                                    focus-me="shouldBeOpen" required-message="'E-mail é obrigatório'" maxlength="100"
                                    validate-on="blur" name="EmailEmpresa" ng-required="true" />
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xm-12 col-sm-12 col-md-12 col-lg-12">
                    <div class="row">
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-12 col-md-3 col-lg-3 control-label mt-4"
                                style="text-align: left;"><span class="text-danger">*</span>
                                CNPJ:</label>
                            <div class="intput-group col-xs-12 col-sm-12 col-md-9 col-lg-9">
                                <input class="form-control" type="text" ng-model="vm.empresa.cnpj"
                                    focus-me="shouldBeOpen" ui-mask="99.999.999/9999-99" required-message="'CNPJ é obrigatório'"
                                    validate-on="blur" name="Cnpj" ng-required="true" />
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xm-12 col-sm-12 col-md-12 col-lg-12">
                    <div class="row">
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-12 col-md-3 col-lg-3 control-label mt-4"
                                style="text-align: left;">RNTRC:</label>
                            <div class="intput-group col-xs-12 col-sm-12 col-md-9 col-lg-9">
                                <input class="form-control" type="text" ng-model="vm.empresa.rntrc" name="Rntrc" required-message="'RNTRC é obrigatório'"
                                validate-on="blur" maxlength="8" />
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xm-12 col-sm-12 col-md-12 col-lg-12">
                    <div class="row">
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-12 col-md-3 col-lg-3 control-label mt-4"
                                style="text-align: left;"><span class="text-danger">*</span>
                                Telefone:</label>
                            <div class="intput-group col-xs-12 col-sm-12 col-md-9 col-lg-9">
                                <input class="form-control" ui-br-phone-number type="text"
                                    ng-model="vm.empresa.telefone" focus-me="shouldBeOpen"
                                    required-message="'Telefone é obrigatório'" validate-on="blur"
                                    name="TelefoneEmpresa" ng-required="true" />
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xm-12 col-sm-12 col-md-12 col-lg-12">
                    <div class="row">
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-12 col-md-3 col-lg-3 control-label mt-4"
                                style="text-align: left;"><span class="text-danger">*</span>
                                Celular:</label>
                            <div class="intput-group col-xs-12 col-sm-12 col-md-9 col-lg-9">
                                <input class="form-control" ui-br-phone-number type="text" ng-model="vm.empresa.celular"
                                    focus-me="shouldBeOpen" required-message="'Celular é obrigatório'"
                                    validate-on="blur" name="CelularEmpresa" ng-required="true" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-6 mb-30" style="padding-right: 0px; padding-left: 6px;">
        <div class="ibox animated fadeInRight">
            <div class="ibox-title" style="background-color: #056233; border-radius: 14px;">
                <h5 style="color: white">Endereço</h5>
            </div>
            <div style="line-height: 20px; padding-top: 15px; padding-left: 8px;">

                <div class="col-xm-12 col-sm-12 col-md-12 col-lg-12">
                    <div class="row">
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-12 col-md-3 col-lg-3 control-label mt-4"
                                style="text-align: left;"><span class="text-danger">*</span>
                                CEP:</label>
                            <div class="intput-group col-xs-12 col-sm-12 col-md-9 col-lg-9">
                                <input class="form-control" ui-br-cep-mask type="text" ng-model="vm.empresa.cep"
                                    focus-me="shouldBeOpen" required-message="'CEP é obrigatório'" validate-on="blur"
                                    name="CepEmpresa" ng-required="true" ng-blur="vm.buscarEndereco(vm.empresa.cep)" />
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xm-12 col-sm-12 col-md-12 col-lg-12">
                    <div class="row">
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-12 col-md-3 col-lg-3 control-label mt-4"
                                style="text-align: left;"><span class="text-danger">*</span>
                                Endereço:</label>
                            <div class="intput-group col-xs-12 col-sm-12 col-md-9 col-lg-9">
                                <input class="form-control" type="text" ng-model="vm.empresa.endereco"
                                    focus-me="shouldBeOpen" required-message="'Endereço é obrigatório'" maxlength="200"
                                    validate-on="blur" name="EndereçoEmpresa" ng-required="true" />
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xm-12 col-sm-12 col-md-12 col-lg-12">
                    <div class="row">
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-12 col-md-3 col-lg-3 control-label mt-4"
                                style="text-align: left;"><span class="text-danger">*</span>
                                Bairro:</label>
                            <div class="intput-group col-xs-12 col-sm-12 col-md-9 col-lg-9">
                                <input class="form-control" type="text" ng-model="vm.empresa.bairro"
                                    focus-me="shouldBeOpen" required-message="'Bairro é obrigatório'" maxlength="100"
                                    validate-on="blur" name="BairroEmpresa" ng-required="true" />
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xm-12 col-sm-12 col-md-12 col-lg-12">
                    <div class="row">
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-12 col-md-3 col-lg-3 control-label mt-4"
                                style="text-align: left;"><span class="text-danger">*</span>
                                Número:</label>
                            <div class="intput-group col-xs-12 col-sm-12 col-md-9 col-lg-9">
                                <input class="form-control" type="text" ng-model="vm.empresa.enderecoNumero" ats-numeric
                                    focus-me="shouldBeOpen" required-message="'Número é obrigatório'" maxlength="10"
                                    validate-on="blur" name="NúmeroEmpresa" ng-required="true" />
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xm-12 col-sm-12 col-md-12 col-lg-12">
                    <div class="row">
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-12 col-md-3 col-lg-3 control-label mt-4"
                                style="text-align: left;">Complemento:</label>
                            <div class="intput-group col-xs-12 col-sm-12 col-md-9 col-lg-9">
                                <input class="form-control" type="text" ng-model="vm.empresa.complemento"
                                    focus-me="shouldBeOpen" maxlength="100" name="ComplementoEmpresa"
                                    ng-required="false" />
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xm-12 col-sm-12 col-md-12 col-lg-12">
                    <div class="row">
                        <div class="form-group"> 
                            <label class="col-xs-12 col-sm-12 col-md-3 col-lg-3 control-label mt-4"
                                style="text-align: left;"><span class="text-danger">*</span>
                                Estado:</label>
                            <div class="intput-group col-xs-12 col-sm-12 col-md-9 col-lg-9">
                                <ui-select name="EstadoEmpresa" ats-ui-select-validator validate-on="blur"
                                    ng-model="vm.empresa.estadoId" ng-change="vm.estadoChange(vm.empresa.estadoId)"
                                    required-message="'Estado é obrigatório'" required>
                                    <ui-select-match>
                                        <span>{{$select.selected.descricao}}</span>
                                    </ui-select-match>
                                    <ui-select-choices
                                        repeat="ex.id as ex in vm.estados | propsFilter: {descricao: $select.search}">
                                        <div ng-bind-html="ex.descricao | highlight: $select.search"></div>
                                    </ui-select-choices>
                                </ui-select>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xm-12 col-sm-12 col-md-12 col-lg-12">
                    <div class="row">
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-12 col-md-3 col-lg-3 control-label mt-4"
                                style="text-align: left;"><span class="text-danger">*</span>
                                Cidade:</label>
                            <div class="intput-group col-xs-12 col-sm-12 col-md-9 col-lg-9">
                                <ui-select name="EstadoEmpresa" ats-ui-select-validator validate-on="blur"
                                    ng-model="vm.empresa.cidadeId" ng-disabled="vm.cidadesDisabled"
                                    required-message="'Cidade é obrigatório'" required>
                                    <ui-select-match>
                                        <span>{{$select.selected.descricao}}</span>
                                    </ui-select-match>
                                    <ui-select-choices
                                        repeat="ex.id as ex in vm.cidades | propsFilter: {descricao: $select.search}">
                                        <div ng-bind-html="ex.descricao | highlight: $select.search"></div>
                                    </ui-select-choices>
                                </ui-select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>