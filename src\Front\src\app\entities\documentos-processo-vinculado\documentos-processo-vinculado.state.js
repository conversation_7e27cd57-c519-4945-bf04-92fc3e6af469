(function () {
    'use strict';

    angular.module('bbcWeb.documentos-processo-vinculado.state', []).config(routeConfig);

    function routeConfig($urlRouterProvider, $stateProvider) {
        $stateProvider.state('documentos-processo-vinculado', {
            abstract: true,
            url: "/documentos-processo-vinculado",
            templateUrl: "app/layout/content.html"
        }).state('documentos-processo-vinculado.index', {
            url: '/index',
            templateUrl: 'app/entities/documentos-processo-vinculado/documentos-processo-vinculado.html'
        }).state('documentos-processo-vinculado.documentos-processo-vinculado-crud', {
            url: '/:link',
            templateUrl: 'app/entities/documentos-processo-vinculado/documentos-processo-vinculado-crud.html'
        });
    }
})();