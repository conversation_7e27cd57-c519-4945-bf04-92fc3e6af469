(function() {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('AvaliacaoEmpresaController', AvaliacaoEmpresaController);

    AvaliacaoEmpresaController.inject = [
        '$rootScope',
        'BaseService',
        'AvaliacaoEmpresaService'
    ];

    function AvaliacaoEmpresaController($rootScope, BaseService, AvaliacaoEmpresaService) {
        var vm = this;

        const EMPRESA_CONTROLLER = "Empresa";
        vm.headerItems = [{ name: 'Empresa' }, { name: 'Avaliação' }];

        vm.consultarGrid = function () {
            consultarGrid();
        };

        function consultarGrid() {
            vm.gridOptions.dataSource.refresh();
        }

        function getDataSourceGrid() {
            return { autoBind: true, url: EMPRESA_CONTROLLER + "/ConsultarDadosGridAvaliacaoEmpresa", }
        }

        vm.gridOptions = {
            data: [],
            onRegisterApi: BaseService.dataGrid.defaultOnRegisterApi,
            dataSource: getDataSourceGrid(),
            columnDefs: [    
                {name: 'Ações', width: 80, enableColumnMenu: false, cellTemplate: AvaliacaoEmpresaService.getGridTemplate()},
                {name: 'Código', width: 80, primaryKey: true, type : 'number', field: 'id'},
                {name: 'Razão social', field: 'razaoSocial', width: '*', minWidth: 230, enableGrouping: false},
                {displayName: 'CNPJ', field: 'cnpj', width: 130, enableGrouping: false},
                {name: 'Telefone', field: 'telefone', width: 100, enableGrouping: false},
                {name: 'Status', field: 'statusCadastro', width: 160, enableGrouping: false, enum: true, enumTipo: 'EStatusCadastros'}
            ]
        }
    }
})();