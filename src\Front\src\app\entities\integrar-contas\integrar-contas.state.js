(function () {
    'use strict';

    angular.module('bbcWeb.integrar-contas.state', []).config(routeConfig);

    function routeConfig($urlRouterProvider, $stateProvider) {
        $stateProvider.state('integrar-conta', {
            abstract: true,
            url: "/integrar-conta",
            templateUrl: "app/layout/content.html"
        }).state('integrar-conta.index', {
            url: '/index',
            templateUrl: 'app/entities/integrar-contas/integrar-contas.html'
        });
    }
})();