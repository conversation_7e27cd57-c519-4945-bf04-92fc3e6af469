(function () {
    'use strict';

    angular.module('bbcWeb.tipo-empresa.state', []).config(routeConfig);

    function routeConfig($urlRouterProvider, $stateProvider) {
        $stateProvider.state('tipo-empresa', {
            abstract: true,
            url: "/tipo-empresa",
            templateUrl: "app/layout/content.html"
        }).state('tipo-empresa.index', {
            url: '/index',
            templateUrl: 'app/entities/tipo-empresa/tipo-empresa.html'
        }).state('tipo-empresa.tipo-empresa-crud', {
            url: '/:link',
            templateUrl: 'app/entities/tipo-empresa/tipo-empresa-crud.html'
        });
    }
})();