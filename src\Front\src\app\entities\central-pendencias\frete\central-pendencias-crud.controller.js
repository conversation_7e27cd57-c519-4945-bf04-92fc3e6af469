(function () {
    'use strict';

    angular.module('bbcWeb').controller('CentralPendenciaCrudController', CentralPendenciaCrudController);

    CentralPendenciaCrudController.$inject = ['toastr', '$rootScope', 'BaseService', '$state', '$stateParams', '$window', 'PERFIL_ADMINISTRADOR', '$scope', '$timeout', 'PersistentDataService', 'DefaultsService', '$uibModal'];

    function CentralPendenciaCrudController(
        toastr, $rootScope,
        BaseService, $state,
        $stateParams, $window,
        PERFIL_ADMINISTRADOR,
        $scope, $timeout,
        PersistentDataService,
        DefaultsService,
        $uibModal
    ) {
        var vm = this;
        vm.pendencia = [];
        vm.menusPai = [];
        vm.status = "";

        vm.cmbStatus = {
            data: [{ id: 1, descricao: 'Aberta' }, { id: 2, descricao: 'Pendente' }, { id: 3, descricao: 'Erro' }, { id: 0, descricao: 'Fechada' }]
        };

        vm.cmbTipo = {
            data: [{ id: 0, descricao: 'Adiantamento' }, { id: 1, descricao: 'Saldo' }, { id: 2, descricao: 'Complemento' }, { id: 3, descricao: 'Avulso' }, { id: 4, descricao: 'Tarifa ANTT' }]
        };

        vm.headerItems = [{
            name: 'Movimentações'
        }, {
            name: 'Central de pendências',
            link: 'central-pendencias.index'
        }, {
            name: 'Visualizar'
        }];

        var selfScope = PersistentDataService.get('CentralPendenciaCrudController');

        if ($stateParams.link == 'novo')
            vm.pendencia.id = 'Auto';

        vm.isNew = function () {
            return $stateParams.link == 'novo';
        };
        
        vm.loadEdit = function (id) {
            BaseService.get('CentralPendencias', 'ConsultarPorId', {
                idCentralPendencias: id
            }).then(function (response) {
                if (!response.success) {
                    toastr.error(response.message);
                    return;
                } else {
                    vm.pendencia = response.data;
                    vm.pendencia.dataBaixa = response.data.dataBaixa == null ? null : new Date(response.data.dataBaixa);
                    switch (response.data.tipo) {
                        case 0:
                            vm.tipo = "Adiantamento"
                            break;
                        case 1:
                            vm.tipo = "Saldo"
                            break;
                        case 2:
                            vm.tipo = "Complemento"
                            break;
                        case 3:
                            vm.tipo = "Avulso"
                            break;
                        case 4:
                            vm.tipo = "Tarifa ANTT"
                            break;
                        default:
                            break;
                    }
                    switch (response.data.status) {
                        case 0:
                            vm.status = "Fechado"
                            break;
                        case 1:
                            vm.status = "Aberta"
                            break;
                        case 2:
                            vm.status = "Pendente"
                            break;
                        case 3:
                            vm.status = "Erro"
                            break;
                        case 4:
                            vm.status = "Cancelado"
                            break;
                        case 5:
                            vm.status = "Processando"
                            break;
                        case 6:
                            vm.status = "Não Concluído"
                            break;
                        default:
                            break;
                    }
                }
            });
        }

        vm.save = function (form) {
            if (!form.$valid) {
                toastr.error('Todos os campos obrigatórios devem ser preenchidos corretamente com seus respectivos valores');
                return;
            }

            if (vm.isSaving === true)
                return;

            var pendencia = {}

            pendencia.Id = vm.pendencia.id;
            pendencia.Status = vm.pendencia.status;

            vm.isSaving = true;

            BaseService.post('CentralPendencias', 'Salvar', pendencia).then(function (response) {
                vm.isSaving = false;

                if (response && !response.sucesso)
                    return toastr.error(response.mensagem);
                else {
                    toastr.success('Pendencia atualizada com sucesso!');
                    $state.go('central-pendencias.index');
                }
            });
        };

        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.administrador == true;
        };

        function init() {
            if (vm.isNew()) {
                vm.pendencia.id = 'Auto';
            }
        };

        vm.onClickAvancar = function (wizard) {
            wizard.go(wizard.getActivePosition() + 1);
        };

        vm.onClickVoltar = function (wizard) {

            var ativoIndex = wizard.getActivePosition();
            if (ativoIndex == 1)
                $state.go('central-pendencias.index');

            wizard.go(ativoIndex - 1);
        };

        init();

        $scope.$on('$stateChangeStart', function (arg1, toState) {
            if (toState.name === 'central-pendencias.index')
                PersistentDataService.remove('CentralPendenciaController');
            else
                PersistentDataService.store('CentralPendenciaCrudController', vm, "Movimentações - Central de pendencias", null, "central-pendencias.central-pendencias-crud", vm.pendencia.id);
        });

        if (angular.isDefined(selfScope))
            angular.extend(vm, selfScope.data);
        else {
            if (!vm.isNew())
                vm.loadEdit($stateParams.link);
            else {
                vm.modulos = 1;
            }
        }

        $timeout(function () {
            PersistentDataService.remove('CentralPendenciaController');
        }, 15);

        vm.consultaEmpresa = {
            columnDefs: [{
                name: 'Cód.',
                field: 'id',
                width: 60,
                type: 'number',
                primaryKey: true
            }, {
                name: 'Nome Fantasia',
                field: 'nomeFantasia',
                width: '*'
            }, {
                name: 'Razão Social',
                field: 'razaoSocial',
                width: '*'
            }, {
                name: 'Email',
                field: 'email',
                width: 120
            }],
            desiredValue: 'id',
            desiredText: 'nomeFantasia',
            url: 'Empresa/ConsultarGridEmpresaCombo',
            paramsMethod: function () {
                return {}
            }
        }
    }
})();
