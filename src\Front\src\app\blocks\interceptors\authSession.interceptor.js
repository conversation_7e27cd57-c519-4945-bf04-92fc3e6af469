(function () {
    angular.module('bbcWeb')
        .factory('authSessionInterceptor', authSessionInterceptor);

    authSessionInterceptor.$inject = ['$window', '$q', '$rootScope', '$injector'];

    function authSessionInterceptor($window, $q, $rootScope, $injector) {
        return {
            request: function (request) {
                request.headers = request.headers || {};

                // Ignora endereços do cdn.raw...
                if (request.url.indexOf('cdn.rawgit.com') > -1 || request.url.indexOf('maps.googleapis') > -1 || request.url.indexOf('cep.republicavirtual') > -1)
                    return request || $q.when(request);

                if ($window.localStorage.getItem('SessionKey'))
                    request.headers.SessionKey = $window.localStorage.getItem('SessionKey');

                else
                    $rootScope.goToState("login.sessao-expirada-login");

                return request || $q.when(request);
            },
            //response: function(response) {
            //    var validErros = [
            //        '[ERR_SESSION_KEY_HEADER_NOT_FOUND]',
            //        '[ERR_SIS_INVALID_SESSION_KEY]',
            //        '[ERR_SIS_USUARIO_INATIVO]',
            //        '[ERR_SIS_TOKEN_EXPIRED]'
            //    ];
            //    if (response.data !== null && angular.isDefined(response.data) && validErros.indexOf(response.data.error) > -1) 
            //    {
            //        if($window.location.href.includes("login")){
            //            return  response || $q.when(response);
            //        }
            //        $rootScope.goToState("login");
            //        return response || $q.when(response);
            //    } else
            //        return response || $q.when(response);
            //},
            responseError: function (response) {
                if (response.status !== 401) {
                    return response || $q.when(response);
                }
                var toastr = $injector.get('toastr')
                if($window.localStorage.getItem('SessionKey') !== 'invalid-key-value')
                    toastr.error('Sessão do usuário expirada. Faça o login novamente.')
                $window.localStorage.setItem('SessionKey', 'invalid-key-value')
                $rootScope.goToState("login")
                return response || $q.when(response)
            }
        };
    }

    // Register the previously created AuthInterceptor.
    angular.module('bbcWeb').config(function ($httpProvider) {
        $httpProvider.interceptors.push('authSessionInterceptor');
        $httpProvider.defaults.timeout = 60000;
    });
})();