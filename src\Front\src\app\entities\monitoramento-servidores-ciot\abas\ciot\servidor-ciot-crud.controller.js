(function () {
    'use strict';

    angular.module('bbcWeb').controller('ServidorCiotCrudController', ServidorCiotCrudController);

    ServidorCiotCrudController.$inject = ['toastr', '$rootScope', 'BaseService', '$state', '$stateParams', '$window', 'PERFIL_ADMINISTRADOR', '$scope', '$timeout', 'PersistentDataService', 'DefaultsService', '$uibModal'];

    function ServidorCiotCrudController(
        toastr, $rootScope,
        BaseService, $state,
        $stateParams, $window,
        PERFIL_ADMINISTRADOR,
        $scope, $timeout,
        PersistentDataService,
        DefaultsService,
        $uibModal
    ) {
        var vm = this;
        vm.servidor = [];

        vm.headerItems = [{
            name: 'Cadastros'
        }, {
            name: 'Servidor',
            link: 'monitoramento-servidores-ciot.index'
        }, {
            name: $stateParams.id == 'novo' ? 'Novo' : 'Editar'
        }];

        if ($stateParams.id == 'novo')
            vm.servidor.id = 'Auto';

        vm.isNew = function () {
            return $stateParams.link === 'novo';
        };  

        vm.comboTipoServidor = {
            data: [{ id: 0, descricao: 'Servidor velho XML' }, { id: 1, descricao: 'Servidor novo JSON' }]
        };
        
        vm.loadEdit = function (id, nome, link, tipoServidor) {
            vm.servidor.id = id;
            vm.servidor.nome = nome;
            vm.servidor.link = link;
            vm.servidor.tipoServidor = parseInt(tipoServidor, 10);
        };
        
        
        
        vm.save = function (form) {
            if (!form.$valid) {
                toastr.error('Todos os campos obrigatórios devem ser preenchidos corretamente com seus respectivos valores');
                return;
            }

            if (vm.isSaving === true)
                return;
         
            vm.isSaving = true;

            var saveServidor = {};

            saveServidor.Id = vm.servidor.id == "Auto" ? 0 : vm.servidor.id;
            saveServidor.Nome = vm.servidor.nome;
            saveServidor.Link = vm.servidor.link;
            saveServidor.TipoServidor = vm.servidor.tipoServidor;

       
            BaseService.post('MonitoramentoCiot', 'SaveServidorCiot', saveServidor).then(function (response) {
                vm.isSaving = false;
                if (response.success) {
                    toastr.success(response.message);
                    $state.go('monitoramento-servidores-ciot.index');
                } else
                    toastr.error(response.message);
                });
        };
        
        
        function init() {
            vm.loadEdit();
        };

        vm.onClickAvancar = function (wizard) {
            wizard.go(wizard.getActivePosition() + 1);
        };

        vm.onClickVoltar = function (wizard) {

            var ativoIndex = wizard.getActivePosition();
            if (ativoIndex == 1)
                $state.go('monitoramento-servidores-ciot.index');

            wizard.go(ativoIndex - 1);
        };
        init();
        
        if (!vm.isNew()) {
            vm.loadEdit($stateParams.id, $stateParams.nome, $stateParams.link, $stateParams.tipoServidor);
        }
        else{
            vm.servidor.id = "Auto";
        }

        $timeout(function () {
            PersistentDataService.remove('ServidorCiotCrudController');
        }, 15);

    }
})();
