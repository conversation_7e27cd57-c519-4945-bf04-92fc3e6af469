<div ng-controller="MenuController as vm">
    <form-header items="vm.headerItems" head="'Menu'" state="menu"></form-header>
    <div class="animated fadeIn filter-position">
        <div class="row">
            <div class="col-lg-12">
                <div class="ibox animated fadeIn">
                    <div class="ibox-title">
                        <h5>Menu</h5>
                    </div>
                    <div class="ibox-content">
                        <div class="row">
                            <div class="col-lg-12 text-right" >
                                <button tooltip-placement="top" ng-click="vm.gridOptions.dataSource.refresh();" uib-tooltip="Atualizar" type='button' class="btn btn-labeled btn-default">
                                    <i class="fa fa-refresh"></i>
                                    <span class="pl-5 ">Atualizar</span>
                                </button>
                                 <button tooltip-placement="top" ui-sref="menu.crud({link:'novo'})" uib-tooltip="Cadastrar" type='button' class="btn btn-labeled btn-primary fixAddbtn">
                                    <span class="btn-label text-right">
                                        <i class="fa fa-plus"></i>
                                    </span>
                                    <span class="pl-5">Novo</span>
                                </button> 
                                <hr />
                            </div>                            
                            <div class="col-lg-12">
                                <div ui-grid="vm.gridOptions" ng-style="{height: vm.gridOptions.getGridHeight()}" class="grid" style="width: 100%;" ui-grid-pinning ui-grid-save-state ui-grid-pagination ui-grid-auto-resize ui-grid-resize-columns ui-grid-grouping></div>
                            </div>
                        </div>
                        <div style="shape-margin: 10px" class="row">
                            <br />
                            <div class="col-lg-12 text-right">
                                <!-- <div class="form-header">
                                    <button type="button" ng-disabled="vm.disableButtons"  class="btn btn-xs btn-primary" ng-click="vm.gerarRalatorio('xlsx')"><i class="fa fa-file-excel-o"></i> Exportar em Excel</button>
                                    <button type="button" ng-disabled="vm.disableButtons"  class="btn btn-xs btn-primary" ng-click="vm.gerarRalatorio('pdf')"><i class="fa fa-file-pdf-o"></i> Exportar em PDF</button>
                                </div> -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>