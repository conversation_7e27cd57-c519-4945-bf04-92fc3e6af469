(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('PortadorController', PortadorController);

    PortadorController.inject = ['BaseService', '$rootScope', 'toastr', '$scope', 'PersistentDataService', '$timeout', '$state', 'PERFIL_ADMINISTRADOR','oitozero.ngSweetAlert'];

    function PortadorController(BaseService, $rootScope, toastr, $scope, PersistentDataService, $timeout, $state, PERFIL_ADMINISTRADOR, SweetAlert) {
        var vm = this;
        vm.headerItems = [{
            name: '<PERSON><PERSON><PERSON><PERSON>'
        }, {
            name: 'Portador'
        }];
        
        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.perfil == PERFIL_ADMINISTRADOR;
        };

        vm.criarNovaSenha = function (id, ativo, mobile){

            BaseService.post('Portador', "CriarNovaSenha", {
                id: id,
                mobile: mobile
            }).then(function (response) {
                if(response.success){
                    vm.alterarStatus(id, ativo);
                    toastr.success('Portador atualizado com sucesso!')
                }else{
                    toastr.error(response.message)
                }
                vm.gridOptions.dataSource.refresh();
            });
        }
      
        vm.alterarStatus = function (id, ativo) {
            BaseService.post('Portador', "AlterarStatus", {
                id: id
            }).then(function (response) {
                response.success ? (ativo === 1 ? toastr.success('Portador inativado com sucesso!') : toastr.success('Portador reativado com sucesso!')) : toastr.error(response.message);
                vm.gridOptions.dataSource.refresh();
            });
        };

        vm.reativarPortador =function (id, ativo) {
            Sistema.Msg.confirm("Deseja realmente reativar o portador?", function () {
                Sistema.Msg.confirm('Deseja gerar um nova senha para o portador ' + id +'?', function () {
                    Sistema.Msg.confirm('Por padrão a senha sera enviada por e-mail, caso desejar enviar por SMS clique em "sim".', function () {
                        vm.criarNovaSenha(id, ativo, true);
                    }, function () {
                        vm.criarNovaSenha(id, ativo, false);
                    });
                }, function () {
                    vm.alterarStatus(id, ativo);
                });
            })
        }

        vm.gridOptions = {
            data: [],  
            onRegisterApi: BaseService.dataGrid.defaultOnRegisterApi,
            parametrosExtras: {
                IdEmpresa: $rootScope.usuarioLogado.idEmpresa          
            },
            dataSource: {
                url: "Portador/ConsultarGridPortador"
            },
            columnDefs: [{
                name: 'Ações',
                width: 80,
                cellTemplate:  '<div ng-if="!row.groupHeader" class="ui-grid-cell-contents" title="TOOLTIP">\
                    <button ng-disabled="row.entity.ativo===0" tooltip-placement="right" uib-tooltip="Editar" type="button" ui-sref="portador.portador-crud({link: row.entity.id})"\
                        ng-class="{ \'btn btn-xs btn-info\': true }">\
                        <i class="fa fa-edit"></i>\
                    </button>\
                    <button type="button" tooltip-placement="right"\
                        uib-tooltip="Ativar / Inativar"\
                        ng-click="row.entity.ativo === 1 ? grid.appScope.vm.alterarStatus(row.entity.id, row.entity.ativo)\
                         : row.entity.atividade !== \'Frota\' ? grid.appScope.vm.alterarStatus(row.entity.id, row.entity.ativo)\
                         : grid.appScope.vm.reativarPortador(row.entity.id, row.entity.ativo)" \
                         ng-class="row.entity.ativo===1 ? \'btn btn-xs btn-success\' : \'btn btn-xs btn-danger\'">\                                            <i ng-class="row.entity.ativo===1 ? \'fa fa-check\' : \'fa fa-times\'"></i>\
                    </button>\
                </div>'
            }, {
                name: 'Código',
                width: 80,
                primaryKey: true,
                field: 'id',
                type : 'number',
                enableFiltering: true
            }, {
                name: 'Nome',
                width: '*',
                minWidth: 160,
                field: 'nome',
                serverField: 'nome',
                enableFiltering: true
            }, {
                name: 'CPF/CNPJ',
                displayName: 'CPF/CNPJ',
                width: 150,
                field: 'cpfCnpj',
                serverField: 'cpfCnpj',
                enableFiltering: true
            },
            {
                name: 'Telefone',
                displayName: 'Telefone',
                width: 120,
                field: 'telefone',
                serverField: 'telefone',
                enableFiltering: true
            }]
        };

         // Controle de aba!!
        $scope.$on('$stateChangeStart', function () {
            PersistentDataService.store('PortadorController', vm, "Portadores", "PortadorController", "portador.index");
        });

        var selfScope = PersistentDataService.get('PortadorController');
        var filho = PersistentDataService.get('PortadorCrudController');
        if (angular.isDefined(filho)) {
            $timeout(function () {
                $state.go('portador.portador-crud', {
                    link: filho.data.portador.id > 0 ? 'editar' : 'novo'
                });
            }, 50);
        } else if (angular.isDefined(selfScope)) {
            angular.extend(vm, selfScope.data);

            $timeout(function () {
                vm.gridOptions.dataSource.refresh();
            }, 450);
        }
    }
})();