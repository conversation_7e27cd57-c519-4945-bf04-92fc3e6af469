<div class="col-md-12">
    <div class="form-horizontal">
        <div class="row">
            <div class="col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-sm-3 col-md-4 col-lg-3 control-label">Código:</label>
                    <div class="input-group col-sm-3 col-md-3 col-lg-3">
                        <input type="text" ng-model="vm.emprestimo.id" class="form-control" disabled />
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-sm-3 col-md-4 col-lg-3 control-label"><span class="text-danger mr-5">*</span>
                        Agência:</label>
                    <div class="input-group col-sm-3 col-md-3 col-lg-9">
                        <input type="text" ng-model="vm.emprestimo.agencia" ats-numeric maxlength="10"
                            onkeypress="return event.charCode >= 48 && event.charCode <= 57" class="form-control"
                            ng-disabled="vm.desabilitarCampos" required-message="'Agência é obrigatório'" ng-required="true" id="Agencia"
                            name="Agencia" />
                    </div>
                </div>
            </div>
            <div class="col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-sm-3 col-md-4 col-lg-3 control-label"><span class="text-danger mr-5">*</span>
                        Conta:</label>
                    <div class="input-group col-sm-3 col-md-3 col-lg-9">
                        <input type="text" ng-model="vm.emprestimo.conta" maxlength="15" ats-numeric
                            onkeypress="return event.charCode >= 48 && event.charCode <= 57" class="form-control"
                            ng-disabled="vm.desabilitarCampos" required-message="'Conta é obrigatório'" ng-required="true" id="Conta"
                            name="Conta" />
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <consulta-padrao-modal tabledefinition="vm.consultaPortador" label="'Portador:'"
                ng-disabled="vm.disabledPortador" placeholder="'Selecione um Portador'"
                labelsize="'col-sm-3 col-md-4 col-lg-3 control-label'" required-message="'Beneficiário é obrigatório'"
                ng-required="vm.isNew()" ngshowasterisc="vm.isNew()"></consulta-padrao-modal>
            <div class="col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-sm-3 col-md-4 col-lg-3 control-label">
                        CPF/CNPJ:</label>
                    <div class="input-group col-sm-3 col-md-3 col-lg-9">
                        <input type="text" ui-mask="{{vm.emprestimo.cpfCnpjPortador.length > 14 ? 99.999.999/9999-99 : 999.999.999-99}}" name="CPF/CNPJ" maxlength="18" ng-model="vm.emprestimo.cpfCnpjPortador" class="form-control" disabled
                        required-message="'CPF/CNPJ é obrigatório'" id="CPF/CNPJ" />
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-sm-3 col-md-4 col-lg-3 control-label"><span class="text-danger mr-5">*</span>
                        Status:</label>
                    <div class="input-group col-sm-3 col-md-3 col-lg-9">
                        <select class="form-control" ng-model="vm.emprestimo.status" required-message="'Status é obrigatório'" ng-required="true" id="Status"
                        name="Status">
                            <option ng-repeat="option in vm.enumStatus | orderBy:'descricao'" value="{{option.id}}">
                                {{option.descricao}}</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-sm-3 col-md-4 col-lg-3 control-label">Taxa
                        retenção:</label>
                    <div class="input-group col-sm-9 col-md-8 col-lg-9">
                        <span class="input-group-addon">%</span>
                        <input type="text" maxlength="5" ats-price ng-model="vm.emprestimo.taxaRetencao"
                            class="form-control" ng-disabled="vm.desabilitarCampos"/>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-sm-3 col-md-4 col-lg-3 control-label">Valor
                        aquisição:</label>
                    <div class="input-group col-sm-9 col-md-8 col-lg-9">
                        <span class="input-group-addon">R$</span>
                        <input type="text" maxlength="14" ats-price ng-model="vm.emprestimo.valorAquisicao"
                            name="Valor" class="form-control" ng-disabled="vm.desabilitarCampos"/>
                    </div>
                </div>
            </div>
            <div class="col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-sm-3 col-md-4 col-lg-3 control-label">Valor
                        pago:</label>
                    <div class="input-group col-sm-9 col-md-8 col-lg-9">
                        <span class="input-group-addon">R$</span>
                        <input type="text" maxlength="14" ats-price ng-model="vm.emprestimo.valorPago" name="Valor"
                            class="form-control" ng-disabled="vm.desabilitarCampos"/>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-12 col-md-6 col-lg-6" ng-if="!vm.isNew()">
                <div class="form-group">
                    <label class="col-sm-3 col-md-4 col-lg-3 control-label">Código State:</label>
                    <div class="input-group col-sm-3 col-md-3 col-lg-9">
                        <input type="text" ng-model="vm.emprestimo.idState" class="form-control"
                            ng-disabled="vm.disabledState"/>
                    </div>
                </div>
            </div>
            <!-- <div class="col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-sm-3 col-md-4 col-lg-3 control-label"></label>
                    <div class="input-group col-sm-3 col-md-3 col-lg-9">
                        <button type="button" class="btn btn-success" ng-click="vm.sinalizaClienteEmprestimo()"
                            ng-show="!vm.isNew()" ng-disabled="vm.sinalizando">Sinalizar cliente empréstimo</button>
                    </div>
                </div>
            </div> -->
        </div>
    </div>
</div>
<div class="row"></div>