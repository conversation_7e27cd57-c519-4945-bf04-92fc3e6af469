(function () {
    'use strict';

    angular.module('bbcWeb.empresa.state', []).config(routeConfig);

    function routeConfig($urlRouterProvider, $stateProvider) {
        $stateProvider.state('empresa', {
            abstract: true,
            url: "/empresa",
            templateUrl: "app/layout/content.html"
        })
        .state('empresa.index', {
            url: '/index',
            templateUrl: 'app/entities/empresa/consulta/empresa.html'
        })
        .state('empresa.crud', {
            url: '/:link',
            templateUrl: 'app/entities/empresa/cadastro/empresa-crud.html'
        });
    }
})();