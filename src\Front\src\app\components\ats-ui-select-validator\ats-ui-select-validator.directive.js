(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .directive('atsUiSelectValidator', atsUiSelectValidator);

    function atsUiSelectValidator() {

        var directive = {
            bindToController: true,
            require: 'uiSelect',
            link: link,
            restrict: 'A'
        };
        return directive;

        function link(scope, element, attrs) {
            var searchInput = element.querySelectorAll('input.ui-select-search');
            if (searchInput.length !== 1) return;
            searchInput[0].setAttribute("name", attrs.name);
            //searchInput[0].setAttribute("validate-on", attrs);
            searchInput[0].required = attrs.required;
            if (angular.isDefined(attrs.validateOn))
                searchInput[0].setAttribute("validate-on", attrs.validateOn);
            if (angular.isDefined(attrs.requiredMessage))
                searchInput[0].setAttribute("required-message", attrs.requiredMessage);
            if (angular.isDefined(attrs.invalidMessage))
                searchInput[0].setAttribute("invalid-message", attrs.invalidMessage);
        }
    }

})();