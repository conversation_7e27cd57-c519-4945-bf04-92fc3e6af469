(function () {
    'use strict';

    angular.module('bbcWeb').directive('hrLabel', function () {
        return {
            bindToController: true,
            controller: function () {
                var vm = this;
            },
            controllerAs: 'vm',
            templateUrl: 'app/components/hrLabel/hr-label.html',
            restrict: 'AE',
            scope: {
                title: '=title',
                dark: '&'
            }
        };
    });
})();