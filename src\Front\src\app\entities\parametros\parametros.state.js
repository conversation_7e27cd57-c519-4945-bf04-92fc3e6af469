(function () {
    'use strict';

    angular.module('bbcWeb.parametros.state', []).config(routeConfig);

    function routeConfig($urlRouterProvider, $stateProvider) {
        $stateProvider.state('parametros', {
            abstract: true,
            url: "/parametros",
            templateUrl: "app/layout/content.html"
        }).state('parametros.index', {
            url: '/index',
            templateUrl: 'app/entities/parametros/parametros.html'
        }).state('parametros.parametros-crud', {
            url: '/:link',
            templateUrl: 'app/entities/parametros/parametros-crud.html'
        }).state('parametros.configuracao-sla', {
            url: '/configuracao/configuracaoSla',
            templateUrl: 'app/entities/parametros/configuracao-sla/configuracao-sla.html'
        }).state('parametros.configuracao-atualizacao-automatica-preco-combustivel', {
            url: '/configuracao/configuracao-atualizacao-automatica-preco-combustivel',
            templateUrl: 'app/entities/parametros/configuracao-atualizacao-automatica-preco-combustivel/configuracao-atualizacao-automatica-preco-combustivel.html'
        }).state('parametros.configuracao-monitoramento-ciot', {
            url: '/configuracao/configuracao-monitoramento-ciot',
            templateUrl: 'app/entities/parametros/configuracao-monitoramento-ciot/configuracao-monitoramento-ciot.html'
        }).state('parametros.configuracao-vale-pedagio', {
            url: '/configuracao/configuracao-vale-pedagio',
            templateUrl: 'app/entities/parametros/configuracao-vale-pedagio/configuracao-vale-pedagio.html'
        }).state('parametros.configuracao-telao-saldo', {
            url: '/configuracao/configuracao-telao-saldo',
            templateUrl: 'app/entities/parametros/configuracao-telao-saldo/configuracao-telao-saldo.html'
        }).state('parametros.configuracao-qualificacao-transacao', {
            url: '/configuracao/configuracao-qualificacao-transacao',
            templateUrl: 'app/entities/parametros/configuracao-qualificacao-transacao/configuracao-qualificacao-transacao.html'
        })
        ;
    }
})();