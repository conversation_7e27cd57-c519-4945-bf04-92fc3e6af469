﻿<div class="row">
    <div>
        <div class="col-xs-12 col-md-4">
            <label class="col-xs-12 col-md-4 col-lg-4 control-label mt-4"
                   style="margin-top: 6px;text-align: left;">Período:</label>
            <div class="input-group col-xs-12 col-md-8">
                <input date-range-picker class="form-control date-picker" type="text"
                       ng-model="vm.dataFrete" options="vm.dateOptions"
                       style="background-color: white !important;" readonly/>
            </div>
        </div>
        <consulta-padrao-modal tabledefinition="vm.consultaEmpresa" idname="consultaEmpresa" label="'Empresa:'"
                               placeholder="'Selecione uma Empresa'" required-message="'Empresa é obrigatória'"
                               ng-show="vm.usuAdm"
                               ng-required="vm.isAdmin()" directivesizes="'col-xs-12 col-sm-12 col-md-4 col-lg-6'"
                               labelsize="'col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label'">
        </consulta-padrao-modal>
        <div class="col-md-2" style="text-align: right;">
            <button type="button" class="btn btn-labeled btn-primary"
                    ng-click="vm.atualizaTelaFrete();">Consultar
            </button>
        </div>
    </div>
</div>
<div class="row"></div>
<br>
<div ui-grid="vm.gridFreteOptions" ng-style="{height: vm.gridFreteOptions.getGridHeight()}" class="grid" style="width: 100%;"
     ui-grid-pinning ui-grid-save-state ui-grid-pagination ui-grid-auto-resize
     ui-grid-resize-columns ui-grid-grouping>
</div>
<div class="row"></div>
</div>    