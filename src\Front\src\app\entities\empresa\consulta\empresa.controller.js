(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('EmpresaController', EmpresaController);

    EmpresaController.inject = [
        '$scope',
        'toastr',
        'BaseService',
        'PersistentDataService',
        '$rootScope',
        '$timeout',
        '$uibModal',
        '$state'
    ];

    function EmpresaController(
        $scope,
        toastr,
        BaseService,
        PersistentDataService,
        $rootScope,
        $timeout,
        $uibModal,
        $state) {

        var vm = this;
        vm.consultaEmpresa = {};
        const EMPRESA_CONTROLLER = "Empresa";

        vm.headerItems = [{
            name: '<PERSON><PERSON><PERSON><PERSON>'
        }, {
            name: 'Empresa'
        }];

        vm.alterarStatus = function (entity, ativo) {
            BaseService.post('Empresa', "AtivarInativarEmpresa", entity).then(function (response) {
                response.success ? (ativo === 1 ? toastr.success('Empresa inativada com sucesso!') : toastr.success('Empresa reativada com sucesso!')) : toastr.error(response.message);
                vm.gridOptions.dataSource.refresh();
            });
        };

        vm.atualizaTela = function () {
            vm.gridOptions.dataSource.refresh();
        }

        vm.isAdministrador = function () {
            return isAdministrador();
        }

        function getDataSourceGrid() {
            return { autoBind: true, url: EMPRESA_CONTROLLER + "/ConsultarDadosGridEmpresa", }
        }

        function isAdministrador() {
            return $rootScope.usuarioLogado.administrador;
        }
        

        vm.gridOptions = {
            data: [],
            onRegisterApi: BaseService.dataGrid.defaultOnRegisterApi,
            urlRelatorio: EMPRESA_CONTROLLER + "/ConsultarDadosGridEmpresa",
            dataSource: getDataSourceGrid(),
            columnDefs: [
                {
                    name: 'Ações', 
                    width: 80, 
                    enableColumnMenu: false, 
                    cellTemplate: '<div ng-if="!row.groupHeader" class="ui-grid-cell-contents" title="TOOLTIP">\
                                        <button ng-disabled="row.entity.ativo===0" title="Editar" type="button" ui-sref="empresa.crud({link: row.entity.id})"\
                                                ng-class="{ \'btn btn-xs btn-info\': true }">\<i class="fa fa-edit"></i>\
                                        </button>\
                                        <button ng-if="grid.appScope.vm.isAdministrador()" type="button" title="Ativar/Desativar" ng-click="grid.appScope.vm.alterarStatus(row.entity, row.entity.ativo)" ng-class="row.entity.ativo===1 ? \'btn btn-xs btn-success\' : \'btn btn-xs btn-danger\'">\
                                            <i ng-class="row.entity.ativo===1 ? \'fa fa-check\' : \'fa fa-times\'"></i>\
                                        </button>\
                                    </div>'
                }, { 
                    displayName: 'Código',
                    name: 'Código', 
                    width: 80, 
                    primaryKey: true, 
                    type: 'number', 
                    field: 'id' 
                }, { 
                    displayName: 'CNPJ', 
                    name: 'CNPJ', 
                    field: 'cnpj', 
                    minWidth: 75 
                }, { 
                    displayName: 'Razão social', 
                    name: 'Razão social', 
                    field: 'razaoSocial', 
                    width: '*', 
                    minWidth: 160 
                }, { 
                    displayName: 'Grupo de empresa', 
                    name: 'Grupo de empresa', 
                    field: 'grupoEmpresa',
                    serverField: 'grupoEmpresa.razaoSocial', 
                    width: '*', 
                    minWidth: 160 
                }, { 
                    displayName: 'CNPJ grupo de empresa', 
                    name: 'CNPJ grupo de empresa', 
                    field: 'grupoEmpresaCnpj', 
                    serverField: 'grupoEmpresa.cnpj', 
                    width: '*', 
                    minWidth: 80 
                }
            ]
        };          


        vm.abrirModalRelatorio = function (gridName, controllerPai) {
            $uibModal.open({
                animation: true,
                ariaLabelledBy: 'modal-title',
                ariaDescribedBy: 'modal-body',
                templateUrl: 'app/entities/empresa/consulta/modal-relatorios/modal-relatorios-empresa.html',
                controller: function ($uibModalInstance, $uibModalStack) {
                    var vm = this;

                    vm.modalRelatorioOptions = [{}];

                    vm.headerItems = [{name: 'Empresas'}];

                    for (var x in controllerPai[gridName].columnDefs) {
                        vm.modalRelatorioOptions[x] = {
                            name: controllerPai[gridName].columnDefs[x].displayName,
                            field: controllerPai[gridName].columnDefs[x].field,
                            pipe: controllerPai[gridName].columnDefs[x].pipe,
                            enabled: true
                        }
                    }

                    vm.fechar = function () {
                        controllerPai.modalRelatorioOptions = vm.modalRelatorioOptions;
                        $uibModalStack.dismissAll();
                    }

                    vm.exportarRelatorio = function (extensao) {

                        if (vm.modalRelatorioOptions.filter(function(x){return x.enabled}).length <= 2) {
                            toastr.error("Escolha alguma coluna para gerar o relatório.");
                            return;
                        }

                        controllerPai.modalRelatorioOptions = vm.modalRelatorioOptions;
                        controllerPai.exportarRelatorio(gridName, Date(), extensao);

                        $uibModalStack.dismissAll();
                    }

                },
                controllerAs: 'vm',
                keyboard: true,
                size: 'lg'
            }).result.then(function () {
            });
        };

        

        vm.exportarRelatorio = function (gridName, dateName, extensao) {
            switch (extensao){
                case 1: {
                    exportarEmExcel(gridName, true)
                    break;
                }
                case 2: {
                    exportarEmPdf(gridName, dateName)
                    break;
                }
                case 3: {
                    exportarEmTxt(gridName)
                    break;
                }
                case 4: {
                    exportarEmExcel(gridName, false)
                    break;
                }
                default: exportarEmPdf(gridName, dateName)
                    break;
            }
        };

        //todo fazer as grids aqui usarem o msm objeto (gridOptions)
        //todo ou fazer um metodo no baseService que recebe qlq gridOptions pra pegar o urlRelatorio
        
        function currentDateExportar() {
           return new Date().toLocaleString().replace(/[\/\-:,]/g, "_").replace(/\s/g, "");
        }

        function exportarEmPdf(gridName, dateName) {
            vm.desabilitarBtnRelatorio = true;
            vm[gridName].dataSource.consultarDadosRelatorio(function (response) {
                vm.dadosRelatorio = response.data.data.items;
                $timeout(function () {
                    BaseService.exportarTabelaEmPdfAutoSize(
                        "#exportable",
                        "Relatório de Empresas",
                        "BBC_Relatorio_Empresas_" + currentDateExportar()
                    )
                }, 500);
                vm.desabilitarBtnRelatorio = false;
            }, gridName);
        }

        function exportarEmExcel(gridName, formatoXls) {
            vm.desabilitarBtnRelatorio = true;
            vm[gridName].dataSource.consultarDadosRelatorio(function (response) {
                vm.dadosRelatorio = response.data.data.items;
                $timeout(function () {
                    BaseService.exportarTabelaEmExcel2("exportable-xls", "BBC_Relatorio_Empresas_" +  currentDateExportar())
                }, 500);
                vm.desabilitarBtnRelatorio = false;
            }, gridName);
        }

        function exportarEmTxt(gridName) {
            vm.desabilitarBtnRelatorio = true;
            vm[gridName].dataSource.consultarDadosRelatorio(function (response) {
                vm.dadosRelatorio = response.data.data.items;
                $timeout(function () {
                    BaseService.exportarTabelaEmTxt("exportable", "BBC_Relatorio_Empresas_" + currentDateExportar())
                }, 500);
                vm.desabilitarBtnRelatorio = false;
            }, gridName);
        }

        // Controle de aba!!
        $scope.$on('$stateChangeStart', function () {
            PersistentDataService.store('EmpresaController', vm, "Empresa", "EmpresaCrudController", "empresa.index");
        });

        var selfScope = PersistentDataService.get('EmpresaController');
        var filho = PersistentDataService.get('EmpresaCrudController');
        if (angular.isDefined(filho)) {
            $timeout(function () {
                $state.go('empresa.crud', {
                    link: filho.data.empresa.id > 0 ? 'editar' : 'novo'
                });
            }, 50);
        } else if (angular.isDefined(selfScope)) {
            angular.extend(vm, selfScope.data);

            $timeout(function () {
                vm.gridOptions.dataSource.refresh();
            }, 450);
        }
    }
})();