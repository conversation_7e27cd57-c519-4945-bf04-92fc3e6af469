<div class="form-horizontal">
    <hr-label dark="true"></hr-label>
    <br>
    <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
        <div class="row">
            <div class="col-xs-12 col-md-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-md-3">Aberto 24h</label>
                    <div class="input-group col-xs-12 col-md-9">
                        <toggle-switch on-label="Sim" off-label="Não" ng-model="vm.posto.aberto24h"class="switch-mini"></toggle-switch>
                    </div>
                </div>
            </div>        
            <div class="col-xs-12 col-md-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-md-3">Restaurante</label>
                    <div class="input-group col-xs-12 col-md-9">
                        <toggle-switch on-label="Sim" off-label="Não" ng-model="vm.posto.possuiRestaurante"class="switch-mini"></toggle-switch>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12 col-md-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-md-3">Area de descanso</label>
                    <div class="input-group col-xs-12 col-md-9">
                        <toggle-switch on-label="Sim" off-label="Não" ng-model="vm.posto.possuiAreaDescanso"class="switch-mini"></toggle-switch>
                    </div>
                </div>
            </div>        
            <div class="col-xs-12 col-md-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-md-3">Borracharia</label>
                    <div class="input-group col-xs-12 col-md-9">
                        <toggle-switch on-label="Sim" off-label="Não" ng-model="vm.posto.possuiBorracharia"class="switch-mini"></toggle-switch>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12 col-md-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-md-3">Vestiário</label>
                    <div class="input-group col-xs-12 col-md-9">
                        <toggle-switch on-label="Sim" off-label="Não" ng-model="vm.posto.possuiVestiario"class="switch-mini"></toggle-switch>
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-md-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-md-3">Computador</label>
                    <div class="input-group col-xs-12 col-md-9">
                        <toggle-switch on-label="Sim" off-label="Não" ng-model="vm.posto.possuiComputador"class="switch-mini"></toggle-switch>
                    </div>
                </div>
            </div>

        </div>
        <div class="row">        
            <div class="col-xs-12 col-md-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-md-3">Veículo de apoio</label>
                    <div class="input-group col-xs-12 col-md-9">
                        <toggle-switch on-label="Sim" off-label="Não" ng-model="vm.posto.possuiVeiculoApoio"class="switch-mini"></toggle-switch>
                    </div>
                </div>
            </div>

        </div>
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-3 col-md-4 col-lg-3">Informações:</label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <textarea
                            style="resize: none;"
                            type="text"
                            maxlength="200"
                            ng-model="vm.posto.informacoes"
                            name="Complemento"
                            class="form-control">
                        </textarea>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>