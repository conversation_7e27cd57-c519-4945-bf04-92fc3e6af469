<style>
    .panel-heading {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .icon-container {
        display: flex;
        align-items: center;
        gap: 10px; /* Espaço entre os ícones */
    }

    .icon-container i {
        font-size: 16px;
        cursor: pointer;
    }

    .active {
        color: red;
    }

    .tgactive {
        font-weight: bold;
    }

    td {
        font-weight: bold;
    }

    .card {
        margin: 0 auto 15px;
        height: 160px;
    }

    .fade-element-in.ng-enter {
        transition: 0.4s linear all;
        opacity: 0;
    }

    .fade-element-in-init .fade-element-in.ng-enter {
        opacity: 1;
    }

    .fade-element-in.ng-enter.ng-enter-active {
        opacity: 1;
    }

    .fade-element-in.ng-leave {
        transition: 0.2s linear all;
        opacity: 1;
    }

    .fade-element-in.ng-leave.ng-leave-active {
        opacity: 0;
    }

    .fullscreen {
        position: fixed;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        overflow: auto;
    }

    /* WILSON PASSOU AQUI */
.empresa-imagem-container {
    transition: all 0.5s ease;
}

.empresa-imagem-container {
    width: 100%;
    max-height: 800px;
    overflow: hidden;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: height 0.3s ease;
}

.empresa-imagem-container-mini {
    width: 30px;
    max-height: 25px;
    overflow: hidden;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: height 0.3s ease;
}

.empresa-imagem-container img {
    width: 100%;
    height: auto; 
    object-fit: cover;
    border-radius: 10px;
}

.empresa-imagem-container-mini img {
    width: 100%;
    height: auto;
    object-fit: cover;
    border-radius: 10px;
}

@media (max-width: 991px) {
    .empresa-imagem-container {
        display: none;
    }

    .empresa-imagem-container img {
        display: none; 
    }

    .empresa-imagem-container img {
        display: none; 
    }

    .empresa-imagem-container-mini {
        display: block !important;
        width: 30px; /* Ajuste conforme necessário */
        height: 25px;
    }

    .empresa-imagem-container-mini img {
        display: block !important;
        width: 30px; /* Ajuste conforme necessário */
        height: 25px;
    }

    .header-container {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
}

.empresa-imagem-container-mini {
    display: none;
}

.empresa-imagem-container-mini img {
    display: none; 
}

.texto-com-fundo {
    background-color: white;
    padding-right: 8px;
    padding-left: 8px;
    padding-top: 2px;
    padding-bottom: 2px;
    border-radius: 5px;
}

</style>
<div ng-controller="PainelSaldoController as vm">
    <form-header items="vm.headerItems" head="'Painel de saldo'"></form-header>
    <div class="animated fadeIn filter-position">
        <div class="row">
            <div class="col-lg-12">
                <div class="ibox">
                    <div class="ibox-content p-10" style="background-color: lightgray;">
                        <div class="container-fluid">
                            <div ng-repeat="empresa in vm.empresasHabilitados" class="col-md-12 panel panel-default"
                                 style="background-color: white; padding: 0;">
                                <div class="panel-heading">
                                    <h3 class="panel-title">
                                        <strong>{{empresa.nome}}</strong>
                                    </h3>

                                    <div class="icon-container">

                                        <strong>Data Correspondente:</strong>
                                        <strong class="texto-com-fundo"
                                            class="form-control"
                                            uib-datepicker-popup="dd/MM/yyyy"
                                            ng-model="empresa.dataCorrespondente"
                                            is-open="empresa.calendarioAberto"
                                            current-text="Hoje" 
                                            clear-text="Limpar"
                                            close-text="Fechar"
                                            datepicker-options="{ startingDay: 1, maxDate: vm.hoje, initDate: empresa.dataCorrespondente }"
                                            ng-change="vm.selecionarSemana(empresa)"
                                            ng-click="empresa.calendarioAberto = true"
                                        >{{ empresa.dataConsulta | date:'dd/MM/yyyy' }}</strong>
                                        
    
                                        <i ng-click="empresa.calendarioAberto = true" class="fa fa-calendar link-nav"></i>
                                        

                                        <i ng-class="{ active: !empresa.graficoOn }" ng-click="empresa.graficoOn = !empresa.graficoOn; vm.fixGridHeight()" class="fa fa-th-list link-nav"></i>
                                        <i ng-class="{ active: empresa.graficoOn }" ng-click="empresa.graficoOn = !empresa.graficoOn; vm.fixGridHeight()" class="fa fa-chart-line link-nav"></i>
                                    </div>
                                </div>
                                <div class="panel-body">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="row">
                                                <div class="col-md-12">
                                                    <div class="table-responsive tableFixHead">
                                                        <table class="table table-sm table-bordered">
                                                            <thead>
                                                            <th>Descrição</th>
                                                            <th>Valor</th>
                                                            </thead>
                                                            <tbody>
                                                            <tr ng-repeat="linha in empresa.resumoLinhas">
                                                                <td ng-style="{{linha.styleCss}}">{{linha.descricao}}</td>
                                                                <td>{{linha.valorFormatted}}</td>
                                                            </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                             
                                            </div>
                                            <img class="empresa-imagem-container fade-element-in"
                                            src="data:image/gif;base64,{{empresa.base64Img}}" alt="">
                                            <!-- <div class="row">
                                                <div class="col-md-12">
                                                    <img class="empresa-imagem-container fade-element-in"
                                                         src="data:image/gif;base64,{{empresa.base64Img}}" alt="">
                                                </div>
                                            </div> -->
                                        </div>
                                        <div class="col-md-8">
                                            <chart-directive ng-show="empresa.graficoOn" class="fade-element-in"
                                                             chart-type="line"
                                                             chart-data="empresa.chartData"
                                                             chart-options="vm.chartOptions">
                                            </chart-directive>
                                            <div ng-show="!empresa.graficoOn"
                                                 class="table-responsive tableFixHead totalizador fade-element-in">
                                                <table style="overflow-x:scroll; overflow-y: scroll; display: block; margin-bottom: 0px;"
                                                       class="table table-sm table-bordered"
                                                       ng-style="{'max-height': vm.gridStyle+px}">
                                                    <thead>
                                                    <th style="min-width: 100px;">Detalhamento carga</th>
                                                    <th style="min-width: 100px;">Segunda</th>
                                                    <th style="min-width: 100px;">Terça</th>
                                                    <th style="min-width: 100px;">Quarta</th>
                                                    <th style="min-width: 100px;">Quinta</th>
                                                    <th style="min-width: 100px;">Sexta</th>
                                                    <th style="min-width: 100px;">Sábado</th>
                                                    <th style="min-width: 100px;">Domingo</th>
                                                    <th style="min-width: 100px;">Total Semana</th>
                                                    <th style="min-width: 100px;">Total Mês</th>
                                                    </thead>
                                                    <tbody>
                                                    <tr ng-repeat="linha in empresa.pagamentosDiaGrid">
                                                        <td role="gridcell">{{linha.categoria}}</td>
                                                        <td role="gridcell">{{linha.segundaFormatted}}</td>
                                                        <td role="gridcell">{{linha.tercaFormatted}}</td>
                                                        <td role="gridcell">{{linha.quartaFormatted}}</td>
                                                        <td role="gridcell">{{linha.quintaFormatted}}</td>
                                                        <td role="gridcell">{{linha.sextaFormatted}}</td>
                                                        <td role="gridcell">{{linha.sabadoFormatted}}</td>
                                                        <td role="gridcell">{{linha.domingoFormatted}}</td>
                                                        <td role="gridcell">{{linha.totalSemanaFormatted}}</td>
                                                        <td role="gridcell">{{linha.totalMesFormatted}}</td>
                                                    </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                            <!-- Texto abaixo do gráfico e da tabela -->
                                            <div ng-show="empresa.carregouDados" class="text-center" style="margin-top: 15px;">
                                                <p><strong>Semana:</strong> {{ empresa.dataInicialSemana | date:'dd/MM/yyyy' }} - {{ empresa.dataFinalSemana | date:'dd/MM/yyyy' }}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>