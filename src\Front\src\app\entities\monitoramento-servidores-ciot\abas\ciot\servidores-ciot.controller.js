(function () {
    'use strict';

    angular
        .module('bbcWeb').filter('secondsToMinutes', function () {
            return function (seconds) {
                var minutes = Math.floor(seconds / 60);
                var remainingSeconds = seconds % 60;
                return minutes + ":" + (remainingSeconds < 10 ? "0" : "") + remainingSeconds;
            };
        })
        .controller('ServidoresCiotController', ServidoresCiotController);

    ServidoresCiotController.inject = [
        'BaseService',
        '$rootScope',
        'toastr',
        '$scope',
        'PersistentDataService',
        '$timeout',
        '$state',
        '$stateParams',
        'PERFIL_ADMINISTRADOR',
        'oitozero.ngSweetAlert',
        '$uibModal',
        '$window'
    ];

    function ServidoresCiotController(
        BaseService,
        $rootScope,
        toastr,
        $scope,
        PersistentDataService,
        $timeout,
        $state,
        $stateParams,
        PERFIL_ADMINISTRADOR,
        SweetAlert,
        $uibModal,
        $window) {
        var vm = this;

        
        // Função para monitorar a tecla Esc
        function monitorEscKey(event) {
            console.log('Key pressed:', event.key);
            // Verifica se a tecla pressionada é a tecla Esc (código 27)
            if (event.key === 'Escape' || event.key === 'Esc' || event.keyCode === 27 || event.which === 27) {
                // Chama o método toggleFullScreen() quando a tecla Esc é pressionada
                if (vm.isFullScreen) {
                    vm.isFullScreen = false
                    $rootScope.isFullScreen = false;
                }
            }
        }

        // Adiciona um ouvinte de evento para a tecla Esc no documento
        angular.element(document).on('keyup', monitorEscKey);
        angular.element(document).on('keydown', monitorEscKey);

        // Remove o ouvinte de evento quando o controlador é destruído para evitar vazamentos de memória
        $scope.$on('$destroy', function () {
            angular.element(document).off('keydown', monitorEscKey);
            angular.element(document).off('keyup', monitorEscKey);
        });

        document.addEventListener('fullscreenchange', function() {
            // Código para manipular a mudança para o modo de tela cheia aqui
            changeStatusScreen();
        });
        
        document.addEventListener('webkitfullscreenchange', function() {
            // Código para manipular a mudança para o modo de tela cheia no Webkit (Safari, Chrome) aqui
            changeStatusScreen();
        });
        
        document.addEventListener('mozfullscreenchange', function() {
            // Código para manipular a mudança para o modo de tela cheia no Mozilla (Firefox) aqui
            changeStatusScreen();
        });

        vm.status = 2;
        vm.counter = 300;

        vm.headerItems = [{ name: 'CIOT' }, { name: 'Monitoramento de servidores CIOT' }];

        vm.load = function () { }

        vm.atualizaTela = function () {
            vm.gridOptions.dataSource.refresh();
            vm.counter = 300;
        }

        function changeStatusScreen() {
            vm.isFullScreen = !vm.isFullScreen;
            $rootScope.isFullScreen = vm.isFullScreen;
            vm.minimalize();
        }

        vm.timer = function () {
            vm.onTimeout = function () {
                vm.counter--;
                if (vm.counter === 0) {
                    if ($state.current.name === "monitoramento-servidores-ciot.index") {
                        vm.atualizaTela();
                        vm.counter = 300;
                    }
                    
                }
                mytimeout = $timeout(vm.onTimeout, 1000);
            }
            var mytimeout = $timeout(vm.onTimeout, 1000);

            vm.stop = function () {
                $timeout.cancel(mytimeout);
            }
        }
        
        vm.timer();

        vm.minimalize = function(){
            angular.element('body').toggleClass('mini-navbar');
                if (!angular.element('body').hasClass('mini-navbar') ||
                    angular.element('body').hasClass('body-small')) {
                    angular.element('#side-menu').hide(0);
                    angular.element('#side-menu').fadeIn(0);
                } else
                    angular.element('#side-menu').removeAttr('style');

                $timeout(function() {
                    angular.element($window).trigger('resize');
                }, 400)
        } 

        vm.toggleFullScreen = function() {
            var elem = document.documentElement; // Seleciona a div que deseja tornar em tela cheia
            // Verifica se a API de tela cheia é suportada
            if (document.fullscreenEnabled || document.webkitFullscreenEnabled || document.mozFullScreenEnabled || document.msFullscreenEnabled) {
                if (!document.fullscreenElement && !document.webkitFullscreenElement && !document.mozFullScreenElement && !document.msFullscreenElement) {
                    // Se nenhum elemento estiver em tela cheia, entra em tela cheia
                    if (elem.requestFullscreen) {
                        elem.requestFullscreen();
                    } else if (elem.webkitRequestFullscreen) {
                        elem.webkitRequestFullscreen();
                    } else if (elem.mozRequestFullScreen) {
                        elem.mozRequestFullScreen();
                    } else if (elem.msRequestFullscreen) {
                        elem.msRequestFullscreen();
                    }
                } else {
                    // Se já estiver em tela cheia, sai da tela cheia
                    if (document.exitFullscreen) {
                        document.exitFullscreen();
                    } else if (document.webkitExitFullscreen) {
                        document.webkitExitFullscreen();
                    } else if (document.mozCancelFullScreen) {
                        document.mozCancelFullScreen();
                    } else if (document.msExitFullscreen) {
                        document.msExitFullscreen();
                    }
                }
            } else {
                console.log("API de tela cheia não é suportada.");
            }
        };

        
        vm.alterarStatus = function (dadosServidor) {
            BaseService.post('MonitoramentoCiot', "AtivarDesativarServidor", dadosServidor).then(function (response) {
                $timeout(function() { 
                    response.success ? toastr.success('Sucesso!') : toastr.error(response.message);
                    vm.atualizaTela();
                }, 1500);
            });
        };

        vm.alterarStatusContigencia = function (dadosServidor) {
            BaseService.post('MonitoramentoCiot', "AtivarDesativarContigencia", dadosServidor).then(function (response) {
                $timeout(function() {
                    response.success ? toastr.success('Sucesso!') : toastr.error(response.message);
                    vm.atualizaTela();
                }, 1500);
            });
        };

        vm.dialogoAlterarStatusContingencia = function (id, statusServidor) {
            Sistema.Msg.confirm(statusServidor === 1 ? "Deseja tirar de contingência?" : "Deseja colocar em contingência?", function () {
                vm.alterarStatusContigencia(id);
            });
        }


        // ng-disabled="row.entity.ativo===0"
        vm.gridOptions = {
            data: [],
            enableFiltering: true,
            onRegisterApi: BaseService.dataGrid.defaultOnRegisterApi,
            dataSource: {
                url: "MonitoramentoCiot/ConsultarGridServidoresCiot"
            },
            columnDefs: [{
                name: 'Ações',
                width: 200,
                cellTemplate: '<div ng-if="!row.groupHeader" class="ui-grid-cell-contents" title="TOOLTIP">\
                               <button tooltip-placement="right" uib-tooltip="Editar" ng-disabled="row.entity.ativo===0" type="button" ui-sref="monitoramento-servidores-ciot.servidor-ciot-crud({id: row.entity.id, nome: row.entity.nome, link: row.entity.link, tipoServidor: row.entity.tipoServidor})"\
                                  ng-class="{ \'btn btn-xs btn-info\': true }">\
                                  <i class="fa fa-edit"></i>\
                               </button>\
                                 \
                                <button type="button" title="Ativar/Desativar" ng-click="grid.appScope.vm.alterarStatus(row.entity)" ng-class="row.entity.ativo===1 ? \'btn btn-xs btn-success\' : \'btn btn-xs btn-danger\'">\
                                    <i ng-class="row.entity.ativo===1 ? \'fa fa-check\' : \'fa fa-times\'"></i>\
                                </button>\
                                <button type="button" title="Ativar/Desativar" ng-click="grid.appScope.vm.dialogoAlterarStatusContingencia(row.entity, row.entity.status)" ng-disabled="row.entity.status === 2 || row.entity.status === 3"\
                                ng-class="{\'btn btn-xs btn-danger\': row.entity.status === 1 || row.entity.status === 2, \'btn btn-xs btn-success\': row.entity.status === 0 || row.entity.status === 4, \'btn btn-xs btn-gray-dark\': row.entity.status === 3\}">\
                                {{ row.entity.status === 1 || row.entity.status === 2 ? \'Em contingência\' : \'Contingência\' }}\</button>\
            </div>'
            }, 
            {
                name: 'Status servidor',
                displayName: 'Status servidor',
                width: 200,
                field: 'status',
                enum: true,
                enumTipo: 'EStatusServidorCiot',
                enableFiltering: true,
                pipe: function (input) {
                    return filterStatus(input)
                },
                cellTemplate: '<div class="ui-grid-cell-contents" align="center">\
                                        <p ng-show="row.entity.status === 0" style="background-color: green;">\
                                            <a style="color: white; font-weight: bold">OK</a>\
                                        </p>\
                                        <p ng-show="row.entity.status === 1" style="background-color: red;">\
                                            <a style="color: white; font-weight: bold">CONTINGÊNCIA FORÇADA</a>\
                                        </p>\
                                        <p ng-show="row.entity.status === 2" style="background-color: #FCBF00;">\
                                            <a style="color: white; font-weight: bold">CONTINGÊNCIA AUTOMÁTICA</a>\
                                        </p>\
                                        <p ng-show="row.entity.status === 3" style="background-color: gray;">\
                                            <a style="color: white; font-weight: bold">OFFLINE</a>\
                                        </p>\
                                        <p ng-show="row.entity.status === 4" style="background-color: #E65100;">\
                                            <a style="color: white; font-weight: bold">VERIFICANDO</a>\
                                        </p>\
                                </div>'
            }, 
            {
                name: 'Consulta externa',
                displayName: 'Consulta externa',
                width: 200,
                field: 'statusConsulta',
                enum: true,
                cellTemplate:  '<div class="ui-grid-cell-contents" align="center">\
                                        <p ng-show="row.entity.statusConsulta === 0" style="background-color: green;">\
                                            <a style="color: white; font-weight: bold">OK</a>\
                                        </p>\
                                        <p ng-show="row.entity.statusConsulta === 1" style="background-color: red;">\
                                            <a style="color: white; font-weight: bold">CONTINGÊNCIA FORÇADA</a>\
                                        </p>\
                                        <p ng-show="row.entity.statusConsulta === 2" style="background-color: #FCBF00;">\
                                            <a style="color: white; font-weight: bold">CONTINGÊNCIA AUTOMÁTICA</a>\
                                        </p>\
                                        <p ng-show="row.entity.statusConsulta === 3" style="background-color: gray;">\
                                            <a style="color: white; font-weight: bold">OFFLINE</a>\
                                        </p>\
                                        <p ng-show="row.entity.statusConsulta === 4" style="background-color: #E65100;">\
                                            <a style="color: white; font-weight: bold">VERIFICANDO</a>\
                                        </p>\
                                </div>'
            },
            {
                name: 'Comunicação com a ANTT',
                displayName: 'Comunicação com a ANTT',
                width: 200,
                field: 'statusComunicacaoAntt',
                enum: true,
                cellTemplate: '<div class="ui-grid-cell-contents" align="left">\
                                     <p ng-show="row.entity.statusComunicacaoAntt == 0" style="color"> Ok </p>\
                                     <p ng-show="row.entity.statusComunicacaoAntt == 1"> Sem Comunicação </p>\
                               </div>',
            }, {
                name: 'Servidor',
                displayName: 'Servidor',
                minWidth: 300,
                type: 'text',
                width: '*',
                field: 'nome'
            }, {
                name: 'Tipo de Servidor',
                displayName: 'Tipo de Servidor',
                minWidth: 150,
                width: '*',
                field: 'tipoServidor',
                enum: true,
                enableFiltering: true,
                enumTipo: 'ETipoServidor',
                pipe: function (input) {
                    return filterTipoServidor(input)
                },
                cellTemplate: '<div class="ui-grid-cell-contents" align="left">\
                                    <p ng-show="row.entity.tipoServidor == 0"> Servidor velho XML </p>\
                                    <p ng-show="row.entity.tipoServidor == 1"> Servidor novo JSON </p>\
                              </div>',
            }]
        };
        // Contingencia manual é tratada quando é clicado no botão (Contingencia forçada para o cliente)
        // Contingencia forcada é tratada quando alguma ação ocorre
        function filterStatus(input) {
            var correcao;
            switch (input) {
                case 0:
                    correcao = "Verde"
                    break;
                case 1:
                    correcao = "Vermelho"
                    break;
                case 2:
                    correcao = "Amarelo"
                    break;
                //case 3:
                //    correcao = "Offline"
                //    break;
                //case 4:
                //    correcao = "Verificando"
                //    break;
                default:
                    correcao = input;
            }
            return correcao
        }

        function filterTipoServidor(input) {
            var correcao;
            switch (input) {
                case 0:
                    correcao = "Servidor velho XML"
                    break;
                case 1:
                    correcao = "Servidor novo JSON"
                    break;
                default:
                    correcao = input;
            }
            return correcao
        }


    }
})();