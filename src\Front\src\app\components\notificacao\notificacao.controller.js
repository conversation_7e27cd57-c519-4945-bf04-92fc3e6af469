(function() {
    'use strict';

    angular.module('bbcWeb')
        .controller('NotificacaoController', NotificacaoController);

    NotificacaoController.$inject = ['$rootScope', '$interval', 'BaseService', '$timeout'];

    function NotificacaoController($rootScope, $interval, BaseService, $timeout) {
        var vm = this;
        //Inicialização dos objetos e arrays
        vm.notificacoes = {};
        vm.notificacoes.naoLidas = [];
        vm.notificacoes.totalNotificacoes = 0;
        var inprogres = false;
        $rootScope.recarregarNotificacoes = false;
        //Variáveis privadas da controller angular e dados api
        var idUsuario = $rootScope.usuarioLogado.idUsuario;

        vm.verMais = function($event) {
            $event.stopPropagation();
            buscarNotificacoes(false);
        };

        vm.marcarNotificacaoLida = function($event, notificacao) {
            $event.stopPropagation();
            if (notificacao.Lida)
                return;
            var params = { idNotificacao: notificacao.IdNotificacao };
            BaseService.post("NotificacaoAts", "MarcarNotificacaoLida", params)
                .then(function(response) {
                    notificacao.Lida = true;
                    vm.notificacoes.totalNotificacoes--;
                })
        };

        function servicoNotificacoes() {
            if ($rootScope.recarregarNotificacoes) {
                if (inprogres) return;
                inprogres = true;
                $rootScope.recarregarNotificacoes = false;
                buscarNotificacoes(true);
            }

            if (inprogres) return;
            inprogres = true;
            BaseService.get("NotificacaoAts", "GetNotificacoes").then(function(response) {
                if (angular.isDefined(response.data) && angular.isDefined(response.data.notificacoes) && response.data.notificacoes.length > 0)
                    vm.notificacoes.naoLidas = response.data.notificacoes.concat(vm.notificacoes.naoLidas);
                vm.notificacoes.totalNotificacoes = response.data.count;
                inprogres = false;
                $timeout(function() { angular.element('[data-toggle="tooltip"]').tooltip(); }, 50);
            });

        };

        function buscarNotificacoes(force) {
            BaseService.get("NotificacaoAts", "GetAll", { take: 20, skip: force ? 0 : vm.notificacoes.naoLidas.length }).then(function(response) {
                if (response.data) {
                    vm.notificacoes.naoLidas = force ? response.data.notificacoes : vm.notificacoes.naoLidas.concat(response.data.notificacoes || []);
                    vm.notificacoes.totalNotificacoes = response.data.count;
                }
                inprogres = false;
                $timeout(function() { angular.element('[data-toggle="tooltip"]').tooltip(); }, 50);
            });
        }

        vm.getDate = function(notificacao) {
            return new Date(parseInt(notificacao.DataHoraEnvio.replace("/Date(", "").replace(")/", ""), 10));
        };

        function iniciarServicoNotificacao() {
            $interval(function() {
                servicoNotificacoes();
            }, 10 * 60 * 1000); // 5 minutos - Original ATS 15000
        }

        iniciarServicoNotificacao();
        servicoNotificacoes();
        buscarNotificacoes(false);
    }
})();