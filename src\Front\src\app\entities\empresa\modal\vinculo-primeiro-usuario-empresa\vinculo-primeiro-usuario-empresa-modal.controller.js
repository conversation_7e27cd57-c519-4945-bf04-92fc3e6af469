(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('VinculoPrimeiroUsuarioEmpresaModalController', VinculoPrimeiroUsuarioEmpresaModalController);

    VinculoPrimeiroUsuarioEmpresaModalController.inject = [
        '$rootScoope',
        '$scope',
        '$state',
        'BaseService',
        'empresa',
        'isFromEmpresa',
        'isFromAvaliacaoEmpresa',
        'isBloqueadoStatusAtualEmpresa',
        'toastr',
        '$timeout',
        '$uibModalInstance'
    ];

    function VinculoPrimeiroUsuarioEmpresaModalController($rootScope, $scope, $state, BaseService, empresa, isFromEmpresa, isFromAvaliacaoEmpresa, isBloqueadoStatusAtualEmpresa, toastr, $timeout, $uibModalInstance) {
        var vm = this;
        const ESTADO_CONTROLLER = "Estado";
        const CIDADE_CONTROLLER = "Cidade";
        const EMPRESA_CONTROLLER = "Empresa";
        const GRUPO_USUARIO_CONTROLLER = "GrupoUsuario";
        const USUARIO_CONTROLLER = "Usuario";
        const FILIAL_CONTROLLER = "Filial";

        vm.validacaoSenha = 1;

        vm.loader = false;

        vm.estados = [];
        vm.cidades = [];

        vm.usuario = {};
        vm.empresa = empresa;

        consultarEstados();

        vm.load = function () {
            consultarEstados();
        };

        vm.estadoChange = function (id) {
            vm.cidades = [];
            consultarCidades(id);
        };

        vm.buscarEndereco = function (cep) {
            if (cep) {
                limparEndereco();
                BaseService.getEndereco(cep).then(function (response) {
                    if (response && response.erro) {
                        toastr.warning("Endereço não localizado.");
                    }

                    consultarEstados();

                    $timeout(function () {
                        var estado = _.find(vm.estados, ['sigla', response.uf]);

                        if (angular.isDefined(estado))

                            vm.usuario.EstadoId = estado.id;
                        consultarCidades(vm.usuario.EstadoId);
                        $timeout(function () {
                            var cidade = _.find(vm.cidades, ['descricao', response.localidade]);
                            if (angular.isDefined(cidade))
                                vm.usuario.CidadeId = cidade.id;
                        }, 1500);
                    }, 1500);

                    $timeout(function () {
                        if (angular.isDefined(response.logradouro))
                            vm.usuario.Endereco = response.logradouro;
                    }, 1500);

                    $timeout(function () {
                        if (angular.isDefined(response.bairro))
                            vm.usuario.Bairro = response.bairro;
                    }, 1500);

                });
            }
        };

        function limparEndereco() {
            vm.usuario.EstadoId = null;
            vm.usuario.CidadeId = null;
            vm.estados = null;
            vm.cidades = null;
            vm.usuario.Endereco = null;
            vm.usuario.Bairro = null;
            vm.usuario.EnderecoNumero = null;
            vm.usuario.Complemento = null;
            consultarEstados();
        };

        // Ao confirmar os dados o processo deverá cadastrar um grupo de usuário, um usuário vinculando o grupo anteriormente cadastrado,
        // cadastrar uma filial, e após o sucesso de tudo atualizar o status da empresa
        vm.clickConfirmar = function () {
            iniciarLoader();

            if (vm.validacaoSenha !== 3) {
                toastr.warning("O campo Senha não obedece as regras de senha.");
                finalizarLoader()
                return;
            }

            confirmar();
        };

        vm.clickCancelar = function () {
            if (isFromEmpresa) {
                toastr.success("Operação realizada com sucesso");
                $state.go("empresa.index");
            }
            else {
                $uibModalInstance.close();
            }
        };

        $scope.$watchGroup(['vm.usuario.Senha', 'vm.usuario.SenhaConfirmacao'], function (values) {
            var senha = values[0];
            var confirmacaoSenha = values[1];
            if (angular.isDefined(senha) && angular.isDefined(confirmacaoSenha)) {
                if (senha === "" || senha === null) vm.validacaoSenha = 1;
                if (confirmacaoSenha === "" || confirmacaoSenha === null) vm.validacaoSenha = 1

                if (senha === confirmacaoSenha) {
                    BaseService.post(USUARIO_CONTROLLER, 'ValidarSenha', { senhaValidacao: senha }).then(function (response) {
                        if (!response.success) {
                            vm.validacaoSenha = 2;
                            toastr.warning("Senha inválida. Informe uma senha com no mínimo 6 caracteres, que possua ao menos uma letra, um número e um caracter especial. Sequências e/ou nome da empresa não são permitidos.");
                        } else {
                            vm.validacaoSenha = 3;
                        }
                    });
                } else {
                    vm.validacaoSenha = 2;
                    //toastr.warning("Senha inválida. Informe uma senha com no mínimo 6 caracteres, que possua ao menos uma letra, um número e um caracter especial. Sequências e/ou nome da empresa não são permitidos.");

                }
            } else {
                vm.validacaoSenha = 1;
            }
        });

        function confirmar() {
            realizarCadastroGrupoUsuario(function (sucesso, mensagem, id) {
                if (!sucesso) {
                    finalizarLoader();
                    toastr.error(mensagem);
                    return;
                }

                vm.usuario.grupoUsuarioId = id;
                vm.usuario.empresaId = vm.empresa.id;

                realizarCadastroUsuario(function (sucesso, mensagem) {
                    if (!sucesso) {
                        finalizarLoader();
                        toastr.error(mensagem);
                        return;
                    }

                    realizarCadastroFilial(function (sucesso, mensagem) {
                        if (!sucesso) {
                            finalizarLoader();
                            toastr.error(mensagem);
                            return;
                        }

                        if (isFromEmpresa) {
                            toastr.success("Operação realizada com sucesso");
                            $state.go("empresa.index");
                        }
                        else if (isFromAvaliacaoEmpresa) {
                            realizarAtualizacaoEmpresa();
                        }
                    });
                });
            });
        };

        function consultarEstados() {
            BaseService.get(ESTADO_CONTROLLER, 'ConsultarEstados', {}).then(function (response) {
                if (response.success)
                    vm.estados = response.data;
                else
                    toastr.error(response.message);
            });
        };

        function consultarCidades(id) {
            BaseService.get(CIDADE_CONTROLLER, 'ConsultarCidadesPorEstado', { estadoId: id }).then(function (response) {
                if (response.success)
                    vm.cidades = response.data;
                else
                    toastr.error(response.message);
            });
        };

        function realizarCadastroGrupoUsuario(callback) {
            var obj = { Id: 0, Descricao: "Grupo de usuário " + vm.empresa.nomeFantasia, EmpresaId: vm.empresa.id };

            BaseService.post(GRUPO_USUARIO_CONTROLLER, "CadastrarGrupoUsuarioParaEmpresa", obj).then(function (response) {
                if (response.success)
                    callback(true, null, response.data);
                else
                    callback(false, response.message, null);
            });
        };

        function realizarCadastroUsuario(callback) {
            BaseService.post(USUARIO_CONTROLLER, 'CadastrarPrimeiroUsuarioEmpresa', vm.usuario).then(function (response) {
                if (response.success)
                    callback(true, null);
                else
                    callback(false, response.message);
            });
        };

        function realizarCadastroFilial(callback) {
            var obj = arrangeDadosFilial();
            BaseService.post(FILIAL_CONTROLLER, 'CadastrarFilialParaEmpresa', obj).then(function (response) {
                if (response.success)
                    callback(true, null);
                else
                    callback(false, response.message);
            });
        };

        function realizarAtualizacaoEmpresa() {
            if (parseInt(vm.empresa.statusCadastro) === 0) {
                vm.empresa.usuarioBloqueioId = $rootScope.usuarioLogado.idUsuario;
                vm.empresa.dataBloqueio = new Date();
                vm.empresa.usuarioValidacaoId = $rootScope.usuarioLogado.idUsuario;
                vm.empresa.dataValidacao = new Date();
                vm.empresa.Ativo = 0;
            }

            if (isBloqueadoStatusAtualEmpresa && parseInt(vm.empresa.statusCadastro) === 1) {
                vm.empresa.usuarioDesbloqueioId = $rootScope.usuarioLogado.idUsuario;
                vm.empresa.dataDesbloqueio = new Date();
                vm.empresa.ativo = 1;
            }

            if (parseInt(vm.empresa.statusCadastro) === 1) {
                vm.empresa.usuarioValidacaoId = $rootScope.usuarioLogado.idUsuario;
                vm.empresa.dataValidacao = new Date();
            }

            BaseService.post(EMPRESA_CONTROLLER, 'Cadastrar', vm.empresa).then(function (response) {
                finalizarLoader();
                if (response.success) {
                    enviarEmailValidacao();
                    toastr.success("Operação realizada com sucesso");
                    $state.go('empresa-avaliacao.avaliacao');
                } else {
                    toastr.error(response.message);
                }
            });
        };

        function enviarEmailValidacao() {
            BaseService.get(EMPRESA_CONTROLLER, 'EnviarEmailValidacaoEmpresa', {
                destinatario: vm.empresa.email,
                statusCadastro: vm.empresa.statusCadastro,
                parecerExterno: vm.empresa.parecerExterno,
                usuario: vm.usuario.Login,
                senha: vm.usuario.Senha
            }).then(function (response) {
                if (!response.success)
                    toastr.error("Não foi possível enviar e-mail ao usuário: " + response.message);
            });
        };

        function arrangeDadosFilial() {
            return { NomeFantasia: vm.empresa.nomeFantasia, Cnpj: vm.empresa.cnpj, Email: vm.empresa.email, Endereco: vm.empresa.endereco, CidadeId: vm.empresa.cidadeId, Cep: vm.empresa.cep, EnderecoNumero: vm.empresa.enderecoNumero, Telefone: vm.empresa.telefone, Celular: vm.empresa.celular, Bairro: vm.empresa.bairro, Complemento: vm.empresa.complemento, RazaoSocial: vm.empresa.razaoSocial, EmpresaId: vm.empresa.id };
        };

        function iniciarLoader() {
            vm.loader = true;
        };

        function finalizarLoader() {
            vm.loader = false;
        };

        vm.load();
    }
})();