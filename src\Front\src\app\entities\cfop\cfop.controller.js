(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('CfopController', CfopController);

        CfopController.inject = ['BaseService', '$rootScope', 'toastr', '$scope', 'PersistentDataService', '$timeout', '$state', 'PERFIL_ADMINISTRADOR'];

    function CfopController(BaseService, $rootScope, toastr, $scope, PersistentDataService, $timeout, $state, PERFIL_ADMINISTRADOR) {
        var vm = this;
        vm.headerItems = [{
            name: 'Cadastros'
        }, {
            name: 'CFOP' 
        }];

        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.perfil == PERFIL_ADMINISTRADOR;
        };

        vm.gridOptions = {
            data: [],
            onRegisterApi: BaseService.dataGrid.defaultOnRegisterApi,
            dataSource: {
                url: "CFOP/ConsultarGridCFOP"
            },
            columnDefs: [{
                name: '<PERSON><PERSON><PERSON>',
                width: 80,
                type: 'number',
                primaryKey: true,
                field: 'id'
            }, {
                name: 'CFOP',
                displayName: 'CFOP',                
                width: 100,
                type: 'number',
                field: 'cfop'
            }, {
                name: 'Descrição',
                width: '*',
                field: 'descricao',
                enableFiltering: false
            }]
        };

        vm.alterarStatus = function (id, ativo) {
            BaseService.post('Cfop', "AlterarStatus", {
                id: id
            }).then(function (response) {
                response.success ? ativo ? toastr.success('CFOP inativado com sucesso!') : toastr.success('CFOP reativado com sucesso!') : toastr.error(response.message);
                vm.gridOptions.dataSource.refresh();
            });
        };

        // Controle de aba!!
        $scope.$on('$stateChangeStart', function () {
            PersistentDataService.store('CfopController', vm, "Cfop", "CfopCrudController", "Cfop.index");
        });

        var selfScope = PersistentDataService.get('CfopController');
        var filho = PersistentDataService.get('CfopCrudController');
        if (angular.isDefined(filho)) {
            $timeout(function () {
                $state.go('Cfop.Cfop-crud', {
                    link: filho.data.Cfop.IdCfop > 0 ? 'editar' : 'novo'
                });
            }, 50);
        } else if (angular.isDefined(selfScope)) {
            angular.extend(vm, selfScope.data);

            $timeout(function () {
                vm.gridOptions.dataSource.refresh();
            }, 450);
        }
    }
})();