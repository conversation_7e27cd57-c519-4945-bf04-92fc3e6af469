(function() {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('PainelCiotModalCancelCrudController', PainelCiotModalCancelCrudController);

        PainelCiotModalCancelCrudController.$inject = ['$uibModalInstance', 'toastr', 'BaseService', 'CiotId', 'Ciot', 'DtEmissao', 'DtFim', 'CpfCnpjCliente'];

    function PainelCiotModalCancelCrudController($uibModalInstance,toastr, BaseService, CiotId, Ciot, DtEmissao, DtFim, CpfCnpjCliente) {
        var vm = this;
        vm.saving = false;
        
        vm.cancelar = function() {
            var request = {
                Id: CiotId,
                Motivo: vm.motivoCancelamento,
                Ciot: Ciot,
                DataEmissao: DtEmissao,
                DataFim: DtFim,
                CpfCnpjClienteAdmOrCompanyGroup: CpfCnpjCliente
            };
            vm.saving = true;

            BaseService.post('PainelCiot', "AlterarStatusCancelado", request).then(function (response) {
                if (response.success) {
                    toastr.success('CIOT cancelado com sucesso!');
                    $uibModalInstance.close();
                } else {
                    toastr.error(response.message);
                    vm.saving = false;
                };
            });
        };
    } 
})();