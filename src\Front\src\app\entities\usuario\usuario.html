<div ng-controller="UsuarioController as vm">
    <form-header
        items="vm.headerItems"
        head="'Usuário'"
        state="usuario">
    </form-header>
    <div class=" wrapper-content animated fadeIn filter-position overflow-hidden" >
        <div class="row">
            <div class="col-lg-12">
                <div class="ibox animated fadeIn">
                    <div class="ibox-title">
                        <h5>Usuário</h5>
                        <div ibox-tools></div>
                    </div>
                    <div class="ibox-content">
                        <div class="row">
                            <div class="col-lg-12 text-right" >
                                <button
                                    type='button'
                                    class="btn btn-labeled btn-default"
                                    ng-click="vm.gridOptions.dataSource.refresh();"
                                    uib-tooltip="Atualizar"
                                    tooltip-placement="top">
                                    <i class="fa fa-refresh"></i>
                                    <span class="pl-5 ">Atualizar</span>
                                </button>
                                <button
                                    tooltip-placement="top"
                                    ui-sref="usuario.crud({link:'novo'})"
                                    uib-tooltip="Cadastrar"
                                    type='button'
                                    class="btn btn-labeled btn-primary fixAddbtn">
                                    <span class="btn-label text-right">
                                        <i class="fa fa-plus"></i>
                                    </span>
                                    <span class="pl-5">Novo</span>
                                </button> 
                                <hr />
                            </div>
                            <div class="col-lg-12">
                                <div
                                    class="grid" style="width: 100%;"
                                    ui-grid="vm.gridOptions"
                                    ng-style="{height: vm.gridOptions.getGridHeight()}"
                                    ui-grid-auto-resize
                                    ui-grid-grouping
                                    ui-grid-pagination
                                    ui-grid-pinning
                                    ui-grid-resize-columns
                                    ui-grid-save-state>
                                </div>
                            </div>
                        </div>
                        <div style="shape-margin: 10px" class="row">
                            <br /><div class="col-lg-12 text-right"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>