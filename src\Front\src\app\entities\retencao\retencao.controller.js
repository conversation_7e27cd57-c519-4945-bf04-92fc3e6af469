(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('RetencaoController', RetencaoController);

    RetencaoController.inject = ['BaseService', '$rootScope', 'toastr', '$scope', 'PersistentDataService', '$timeout', '$state', 'PERFIL_ADMINISTRADOR', 'oitozero.ngSweetAlert'];

    function RetencaoController(BaseService, $rootScope, toastr, $scope, PersistentDataService, $timeout, $state, PERFIL_ADMINISTRADOR, SweetAlert) {
        var vm = this;

        vm.headerItems = [{ name: 'Movimentações' }, { name: 'Retenção' }];
        vm.retencaoRelatorio = [];
        vm.desabilitarBtnRelatorio = false;

        vm.consultarDadosRelatorio = function (extensao) {
            if (extensao === 1)
                exportarEmExcel();

            if (extensao === 2)
                exportarEmPdf();
        };

        function exportarEmPdf() {
            vm.desabilitarBtnRelatorio = true;
            vm.gridOptions.dataSource.consultarDadosRelatorio(function (response) {
                vm.retencaoRelatorio = response.data.data;
                $timeout(function () { BaseService.exportarTabelaEmPdf("#exportable", "Retenção", "Retenção"); }, 500);
                vm.desabilitarBtnRelatorio = false;
            });
        }

        function exportarEmExcel() {
            vm.desabilitarBtnRelatorio = true;
            vm.gridOptions.dataSource.consultarDadosRelatorio(function (response) {
                vm.retencaoRelatorio = response.data.data;
                $timeout(function () { BaseService.exportarTabelaEmExcel("exportable-xls", "Retenção") }, 500);
                vm.desabilitarBtnRelatorio = false;
            });
        }

        vm.atualizaTela = function () {
            vm.gridOptions.dataSource.refresh();
        }

        vm.date = {
            startDate: moment().add(-1, 'days'),
            endDate: moment()
        };

        vm.dateOptions = {
            timePicker: true,
            timePicker24Hour: true,
            applyClass: 'btn-primary',
            locale: {
                applyLabel: "Aplicar",
                fromLabel: "De",
                format: "DD/MM/YYYY",
                toLabel: "Até",
                cancelLabel: 'Cancelar',
                customRangeLabel: 'Período',
                monthNames: ['Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho', 'Julho', 'Agosto', 'Setembro',
                    'Outubro', 'Novembro', 'Dezembro'],
                daysOfWeek: ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb']
            },
            ranges: {
                'Últimos 1 dias': [moment().add(-1, 'days'), moment()],
                'Últimos 7 dias': [moment().add(-7, 'days'), moment()]
            }
        };

        vm.gridOptions = {
            data: [],
            onRegisterApi: BaseService.dataGrid.defaultOnRegisterApi,
            urlRelatorio: "Retencao/DadosRelatorioGridRetencao",
            params: function () {
                return {
                    dtInicial: vm.date.startDate.toDate(),
                    dtFinal: vm.date.endDate.toDate()
                }
            },
            dataSource: {
                autoBind: false,
                url: "Retencao/ConsultarGridRetencao",
                params: function () {
                    return {
                        dtInicial: vm.date.startDate.toDate(),
                        dtFinal: vm.date.endDate.toDate()
                    }
                }
            },
            columnDefs: [
                {
                    name: 'Cód.',
                    width: 70,
                    primaryKey: true,
                    field: 'id',
                    type: 'number',
                    enableGrouping: false,
                    enableFiltering: true
                }, 
                { 
                    name: 'Portador',
                    width: '*',
                    minWidth: 150,
                    field: 'nome',
                    serverField: 'Pagamento.Portador.Nome',
                    enableGrouping: false,
                    enableFiltering: true,
                },
                {
                    name: 'CPF/CNPJ Portador',
                    displayName: 'CPF/CNPJ Portador',
                    width: 150,
                    field: 'cpfCnpj',
                    serverField: 'Pagamento.Portador.cpfCnpj',
                    enableGrouping: false,
                    enableFiltering: true
                },
                {
                    name: 'Pagamento',
                    width: 100,
                    field: 'pagamentoId',
                    serverField: 'PagamentoId',
                    enableGrouping: false,
                    enableFiltering: true
                },
                {
                    name: 'Status',
                    width: 100,
                    field: 'status',
                    serverField: 'Status',
                    enableGrouping: false,
                    enableFiltering: true
                },
                {
                    name: 'Valor',
                    width: 100,
                    field: 'valor',
                    serverField: 'Valor',
                    enableGrouping: false,
                    enableFiltering: true
                },
                {
                    name: 'DataIntegracao',
                    displayName: 'Data Integração',
                    width: 145,
                    type: 'date',
                    field: 'dataIntegracao',
                    serverField: 'DataIntegracao',
                    enableGrouping: false,
                    enableFiltering: true
                },
                {
                    name: 'DataCadastro',
                    displayName: 'Data Cadastro',
                    width: 145,
                    field: 'dataCadastro',
                    type: 'date',
                    serverField: 'DataCadastro',
                    enableGrouping: false,
                    enableFiltering: true
                }]
        };

        $timeout(function () {
            vm.atualizaTela();
        }, 1000);

        // Controle de aba!!
        $scope.$on('$stateChangeStart', function () {
            PersistentDataService.store('RetencaoController', vm, "Retenção", "RetençãoCrudController", "retencao.index");
        });
    }
})();
