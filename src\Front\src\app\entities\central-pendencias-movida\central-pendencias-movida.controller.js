﻿(function () {
    'use strict'

    angular
        .module('bbcWeb')
        .controller('CentralPendenciaMovidaController', CentralPendenciaMovidaController)

    CentralPendenciaMovidaController.inject = [
        'BaseService',
        'toastr'
    ]

    function CentralPendenciaMovidaController(BaseService, toastr) {
        var vm = this

        vm.headerItems = [{ name: 'Movimentações' }, { name: 'Central de pendências - gestão de combustível' }]
        vm.desabilitarBtnRelatorio = false
        vm.reenviandoAbastecimento = false

        vm.date = {
            startDate: moment().add(-7, 'days'),
            endDate: moment()
        }

        vm.dateOptions = {
            timePicker: false,
            timePicker24Hour: false,
            applyClass: 'btn-primary',
            locale: {
                applyLabel: "Aplicar",
                fromLabel: "De",
                format: "DD/MM/YYYY",
                toLabel: "Até",
                cancelLabel: 'Cancelar',
                customRangeLabel: 'Perío<PERSON>',
                monthNames: ['Janeiro', '<PERSON><PERSON>', 'Mar<PERSON><PERSON>', 'Abril', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', 'Agos<PERSON>', 'Setem<PERSON>',
                    'Outubro', 'Novembro', 'Dezembro'],
                daysOfWeek: ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb']
            },
            ranges: {
                'Últimos 1 dias': [moment().add(-1, 'days'), moment()],
                'Últimos 7 dias': [moment().add(-7, 'days'), moment()],
                'Último mês': [moment().subtract(1, 'months').startOf('month'), moment().subtract(1, 'months').endOf('month')]
            }
        }

        vm.gridOptions = {
            data: [],
            onRegisterApi: BaseService.dataGrid.defaultOnRegisterApi,
            urlRelatorio: "CentralPendencias/ConsultarGridCentralPendenciasMovida",
            params: function () {
                var inicio = vm.date.startDate.toDate()
                var fim = vm.date.endDate.toDate()
                return {
                    dtInicial: new Date(inicio.getTime() - (inicio.getTimezoneOffset() * 60000)).toJSON(),
                    dtFinal: new Date(fim.getTime() - (fim.getTimezoneOffset() * 60000)).toJSON(),
                    empresaId: vm.consultaEmpresa.selectedValue ? vm.consultaEmpresa.selectedValue : null
                }
            },
            dataSource: {
                url: "CentralPendencias/ConsultarGridCentralPendenciasMovida",
                params: function () {
                    var inicio = vm.date.startDate.toDate()
                    var fim = vm.date.endDate.toDate()
                    return {
                        dtInicial: new Date(inicio.getTime() - (inicio.getTimezoneOffset() * 60000)).toJSON(),
                        dtFinal: new Date(fim.getTime() - (fim.getTimezoneOffset() * 60000)).toJSON(),
                        empresaId: vm.consultaEmpresa.selectedValue ? vm.consultaEmpresa.selectedValue : null
                    }
                }
            },
            columnDefs: [
                {
                    name: 'Ações',
                    width: '*',
                    minWidth: 120,
                    cellTemplate:
                        '<div ng-if="!row.groupHeader" class="ui-grid-cell-contents" title="TOOLTIP">\
                            <div class="container-btn-action">\
                            <button tooltip-placement="right" uib-tooltip="Reenviar à Movida"\
                                type="button" ng-click="grid.appScope.vm.reenviarAbastecimento(row.entity.id)"\
                                class="btn btn-xs btn-info" ng-disabled="grid.appScope.vm.reenviandoAbastecimento"\
                                ng-if="row.entity.integracaoMovida === 2">\
                                <i ng-class="\'fa fa-paper-plane-o\'"></i>\
                            </button>\
                        </div>'
                },
                {
                    name: 'IntegracaoMovida',
                    displayName: 'Status',
                    width: '*',
                    minWidth: 150,
                    field: 'integracaoMovida',
                    serverField: 'integracaoMovida',
                    enableFiltering: false,
                    cellTemplate:
                        '<div class="ui-grid-cell-contents" align="left">\
                        <p ng-show="row.entity.integracaoMovida === 0"> Falha </p>\
                        <p ng-show="row.entity.integracaoMovida === 1"> Sucesso </p>\
                        <p ng-show="row.entity.integracaoMovida === 2"> Não Concluído </p>\
                    </div>'
                },
                //{
                //    name: 'Status',
                //    displayName: 'Status Abastecimento',
                //    width: '*',
                //    minWidth: 150,
                //    field: 'status',
                //    serverField: 'status',
                //    enableFiltering: false,
                //    cellTemplate:
                //        '<div class="ui-grid-cell-contents" align="left">\
                //        <p ng-show="row.entity.status === 0"> Cancelado </p>\
                //        <p ng-show="row.entity.status === 1"> Aprovado </p>\
                //        <p ng-show="row.entity.status === 2"> Reprovado </p>\
                //        <p ng-show="row.entity.status === 3"> Aguardando Aprovação </p>\
                //    </div>'
                //},
                {
                    name: 'EmpresaNomeFantasia',
                    displayName: 'Empresa',
                    width: '*',
                    minWidth: 150,
                    field: 'empresaNomeFantasia',
                    serverField: 'empresa.NomeFantasia',
                    enableFiltering: true
                },
                {
                    name: 'Placa',
                    displayName: 'Placa',
                    width: '*',
                    minWidth: 150,
                    field: 'placa',
                    serverField: 'veiculo.Placa',
                    enableFiltering: true
                },
                {
                    name: 'DataCadastro',
                    displayName: 'Data de Abastecimento',
                    width: '*',
                    minWidth: 150,
                    field: 'dataCadastro',
                    serverField: 'dataCadastro',
                    enableFiltering: true
                },
                {
                    name: 'Litragem',
                    displayName: 'Litragem Abastecida',
                    width: '*',
                    minWidth: 150,
                    field: 'litragem',
                    serverField: 'litragem',
                    enableFiltering: true
                },
                {
                    name: 'CombustivelNome',
                    displayName: 'Combustível',
                    width: '*',
                    minWidth: 150,
                    field: 'combustivelNome',
                    serverField: 'combustivel.Nome',
                    enableFiltering: true
                },
                {
                    name: 'PostoCnpj',
                    displayName: 'Posto CNPJ',
                    width: '*',
                    minWidth: 150,
                    field: 'postoCnpj',
                    serverField: 'posto.Cnpj',
                    enableFiltering: true
                },
                {
                    name: 'PostoRazaoSocial',
                    displayName: 'Posto Razão Social',
                    width: '*',
                    minWidth: 150,
                    field: 'postoRazaoSocial',
                    serverField: 'posto.RazaoSocial',
                    enableFiltering: true
                },
                {
                    name: 'ValorAbastecimento',
                    displayName: 'Valor de Abastecimento',
                    width: '*',
                    minWidth: 150,
                    field: 'valorAbastecimento',
                    serverField: 'valorAbastecimento',
                    enableFiltering: true
                },
                {
                    name: 'AutorizacaoAbastecimentoId',
                    displayName: 'Integração Id',
                    width: '*',
                    minWidth: 150,
                    field: 'autorizacaoAbastecimentoId',
                    serverField: 'autorizacaoAbastecimentoId',
                    enableFiltering: true
                },
                {
                    name: 'Id',
                    displayName: 'Abastecimento Id',
                    width: '*',
                    minWidth: 150,
                    field: 'id',
                    serverField: 'id',
                    enableFiltering: true
                },
                {
                    name: 'ValorTaxaBbc',
                    displayName: 'Valor Taxa Bbc',
                    width: '*',
                    minWidth: 150,
                    field: 'valorTaxaBbc',
                    serverField: 'empresa.TaxaAbastecimento',
                    enableFiltering: true
                },
                {
                    name: 'FuncionarioCpf',
                    displayName: 'Funcionário CPF',
                    width: '*',
                    minWidth: 150,
                    field: 'funcionarioCpf',
                    serverField: 'usuarioCadastro.Cpf',
                    enableFiltering: true
                },
                {
                    name: 'OdometroInformado',
                    displayName: 'Odômetro Informado',
                    width: '*',
                    minWidth: 150,
                    field: 'odometroInformado',
                    serverField: 'odometro',
                    enableFiltering: true
                },
                {
                    name: 'MotivoPendenciaMovida',
                    displayName: 'Motivo Pendência',
                    width: '*',
                    minWidth: 150,
                    field: 'motivoPendenciaMovida',
                    serverField: 'motivoPendenciaMovida',
                    enableFiltering: false,
                    enableGrouping: false,
                    enableSorting: false
                },
                {
                    name: 'ContadorIntegracaoMovida',
                    displayName: 'Reenvios',
                    width: '*',
                    minWidth: 90,
                    field: 'contadorIntegracaoMovida',
                    serverField: 'contadorIntegracaoMovida',
                    enableFiltering: true
                }
            ]
        }

        vm.consultaEmpresa = {
            columnDefs: [{
                name: 'Cód.',
                field: 'id',
                width: 60,
                type: 'number',
                primaryKey: true
            }, {
                name: 'Nome Fantasia',
                field: 'nomeFantasia',
                width: '*'
            }, {
                name: 'Razão Social',
                field: 'razaoSocial',
                width: '*'
            }, {
                name: 'Email',
                field: 'email',
                width: 120
            }],
            desiredValue: 'id',
            desiredText: 'nomeFantasia',
            url: 'Empresa/ConsultarGridEmpresaCombo',
            paramsMethod: function () {
                return {}
            },
            executeAfterSelection: function () {
                vm.atualizarTela()
            },
            clearFunction: function () {
                vm.atualizarTela()
            }
        }

        vm.atualizarTela = function () {
            vm.gridOptions.dataSource.refresh()
        }

        vm.reenviarAbastecimento = function (id) {
            vm.reenviandoAbastecimento = true
            BaseService.post('CentralPendencias', 'ReenviarAbastecimentoMovida', { id: id })
                .then(function (response) {
                    vm.reenviandoAbastecimento = false
                    vm.atualizarTela()

                    if (!response) {
                        toastr.error('Erro de comunicação com o servidor, tente novamente em alguns minutos.')
                        return
                    }
                    if (!response.success) {
                        toastr.error(response.message)
                        return
                    }

                    toastr.success(response.message)
                })
        }
    }
})();