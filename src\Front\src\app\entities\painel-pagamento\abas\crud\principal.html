<div class="form-horizontal">
    <hr />
    <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label">
                        Código:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <input type="text" ng-model="vm.painelPagamento.id" class="form-control" disabled
                            value="{{vm.isNew()}}" />
                    </div>
                </div>
            </div>
            <consulta-padrao-modal tabledefinition="vm.consultaBeneficiario" idname="Beneficiario" idmodel="Beneficiario" label="'Beneficiário:'"
                placeholder="'Selecione um Beneficiário'" labelsize="'col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label'"
                functionclear="vm.beneficiarioLimpaCampos"
                ng-disabled="true">
            </consulta-padrao-modal>
        </div>
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label" style="word-wrap: break-word; margin-top: -7px">
                       Forma de pagamento:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <ui-select name="FormaPagamento" ng-model="vm.painelPagamento.formaPagamento"
                            ats-ui-select-validator ng-change="vm.FormaPagamento(vm.painelPagamento.formaPagamento)"
                            ng-disabled="true"
                            validate-on="blur">
                            <ui-select-match>
                                <span>{{$select.selected.descricao}}</span>
                            </ui-select-match>
                            <ui-select-choices
                                repeat="ex.id as ex in vm.comboFormaPag.data | propsFilter: {descricao: $select.search}">
                                <div ng-bind-html="ex.descricao | highlight: $select.search"></div>
                            </ui-select-choices>
                        </ui-select>
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label">
                        Tipo:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <ui-select name="Tipo" ng-model="vm.painelPagamento.tipo" ats-ui-select-validator
                            ng-disabled="true"
                            validate-on="blur" >
                            <ui-select-match>
                                <span>{{$select.selected.descricao}}</span>
                            </ui-select-match>
                            <ui-select-choices
                                repeat="ex.id as ex in vm.comboTipo.data | propsFilter: {descricao: $select.search}">
                                <div ng-bind-html="ex.descricao | highlight: $select.search"></div>
                            </ui-select-choices>
                        </ui-select>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label">
                        Valor:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        R$
                        <div class="message-required-size">
                            <input type="text" maxlength="100" ats-price
                            ng-disabled="true"
                            ng-model="vm.painelPagamento.valor" name="Valor" class="form-control" validate-on="blur"
                            />
                        </div> 
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label">CIOT:</label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <ui-select name="CIOT" ng-model="vm.painelPagamento.ciotId"
                            ng-disabled="true">
                            <ui-select-match>
                               {{$select.selected.ciot}} {{$select.selected.verificador}}
                            </ui-select-match>
                            <ui-select-choices repeat="ex.id as ex in vm.CIOTS | filter: $select.search">
                                <span ng-bind-html="ex.ciot | highlight: $select.search"></span>
                                <span ng-bind-html="ex.verificador | highlight: $select.search"></span>
                            </ui-select-choices>
                        </ui-select>
                    </div>
                </div>
            </div>
        </div>
        <div class="row" ng-show="vm.painelPagamento.formaPagamento === 0">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs12 col-sm-3 col-md-4 col-lg-3 control-label">Agência:</label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <input type="text" ng-pattern="/^[0-9]*$/" maxlength="10" ng-model="vm.painelPagamento.agencia"
                            ng-disabled="true"
                            name="Agencia" class="form-control" validate-on="blur" />
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label">Conta:</label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <input type="text" maxlength="10" ng-pattern="/^[0-9]*$/" ng-model="vm.painelPagamento.conta" name="Conta"
                            class="form-control" validate-on="blur" 
                            ng-disabled="true"/>
                        <span class="input-group-addon">-</span>
                        <input type="text" maxlength="10" ng-model="vm.painelPagamento.verificadorConta"
                            name="Verificador"
                            ng-disabled="true"
                            class="form-control" style="width: 50px;" validate-on="blur" />
                    </div>
                </div>
            </div>
        </div>
        <div class="row" ng-show="vm.painelPagamento.formaPagamento === 0">
            <consulta-padrao-modal
                ng-disabled="true"
                tabledefinition="vm.consultaBanco" label="'Banco:'" placeholder="'Selecione um Banco'"
                >
            </consulta-padrao-modal>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label">Tipo Conta:</label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <input type="text" maxlength="10" ng-model="vm.painelPagamento.tipoConta"
                            ng-disabled="true"
                            name="TipoConta" class="form-control" validate-on="blur" />
                    </div>
                </div>
            </div>
        </div>
        <div class="row" ng-show="vm.painelPagamento.formaPagamento === 1">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label">
                       Previsão pagamento:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <input type="text" name="DataPrevisaoPagamento" validate-on="blur" class="form-control"
                            ng-disabled="true"
                            ng-model="vm.painelPagamento.DataPrevisaoPagamento" uib-datepicker-popup="dd/MM/yyyy"
                            is-open="vm.datePickerOpen" ng-click="vm.datePickerOpen = !vm.datePickerOpen"
                            current-text="Hoje" clear-text="Limpar" close-text="Fechar" datepicker-append-to-body="true"
                            datepicker-options="vm.optionsDatePicker" />
                        <span class="input-group-btn">
                            <button type="button" class="btn btn-default"
                                ng-click="vm.datePickerOpen = !vm.datePickerOpen">
                                <i class="fa fa-calendar"></i>
                            </button>
                        </span>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <consulta-padrao-modal ng-show="vm.isAdmin()"
                ng-disabled="true" functionclear="vm.empresaLimpaCampos"
                tabledefinition="vm.consultaEmpresa" idname="Empresa" idmodel="Empresa" label="'Empresa:'" labelsize="'col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label'" placeholder="'Selecione uma Empresa'">
            </consulta-padrao-modal>
        </div>
        <div class="row" ng-show="vm.painelPagamento.formaPagamento === 1">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label">Conta origem:</label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <ui-select name="ContaOrigem" ng-model="vm.painelPagamento.contaOrigem"
                            ng-disabled="true"
                            ats-ui-select-validator validate-on="blur">
                            <ui-select-match>
                                <span>{{$select.selected.descricao}}</span>
                            </ui-select-match>
                            <ui-select-choices
                                repeat="ex.id as ex in vm.painelPagamento.contasOrigem | filter: $select.search">
                                <div ng-bind-html="ex.descricao | highlight: $select.search"></div>
                                <small ng-bind-html="ex.cpfCnpj | highlight: $select.search"></small>
                            </ui-select-choices>
                        </ui-select>
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label">Conta destino:</label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <ui-select name="ContaDestino" ng-model="vm.painelPagamento.contaDestino"
                            ng-disabled="true"
                            ats-ui-select-validator validate-on="blur">
                            <ui-select-match>
                                <span>{{$select.selected.descricao}}</span>
                            </ui-select-match>
                            <ui-select-choices
                                repeat="ex.id as ex in vm.painelPagamento.contasDestino | filter: $select.search">
                                <div ng-bind-html="ex.descricao | highlight: $select.search"></div>
                                <small ng-bind-html="ex.cpfCnpj | highlight: $select.search"></small>
                            </ui-select-choices>
                        </ui-select>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>