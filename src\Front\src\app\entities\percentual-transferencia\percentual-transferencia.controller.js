(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('PercentualTransferenciaController', PercentualTransferenciaController);

    PercentualTransferenciaController.inject = [
        'BaseService',
        '$rootScope',
        'toastr',
        '$scope',
        'PersistentDataService',
        '$timeout',
        '$state',
        'PERFIL_ADMINISTRADOR'
    ];

    function PercentualTransferenciaController(
        BaseService,
        $rootScope,
        toastr,
        $scope,
        PersistentDataService,
        $timeout,
        $state,
        PERFIL_ADMINISTRADOR
    ) {
        var vm = this;
        vm.headerItems = [{
            name: '<PERSON><PERSON><PERSON><PERSON>'
        }, {
            name: 'Percentual de Transferência Automática'
        }];

        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.perfil === PERFIL_ADMINISTRADOR;
        };

        vm.gridOptions = {
            data: [],
            onRegisterApi: BaseService.dataGrid.defaultOnRegisterApi,
            dataSource: {
                url: "PercentualTransferencia/ConsultarGridPercentualTransferencia"
            },
            columnDefs: [{
                name: 'A<PERSON>õ<PERSON>',
                width: 80,
                cellTemplate: '<div ng-if="!row.groupHeader" class="ui-grid-cell-contents" title="TOOLTIP">\
                                    <button tooltip-placement="right" uib-tooltip="Editar" type="button" \
                                    ui-sref="percentual-transferencia.percentual-transferencia-crud({link: row.entity.id})"\
                                        ng-class="{ \'btn btn-xs btn-info\': true }">\
                                        <i class="fa fa-edit"></i>\
                                    </button>\
                                    \
                                </div>'
            }, {
                name: 'Código',
                width: 80,
                type: 'number',
                primaryKey: true,
                field: 'id'
            }, {
                name: 'Nome',
                displayName: 'Proprietário',
                field: 'nome',
                width: '350',
                minWidth: 180,
                enableFiltering: true
            }, {
                name: 'CpfCnpj',
                displayName: 'CPF/CNPJ',
                field: 'cpfCnpj',
                width: '*',
                minWidth: 180,
                enableFiltering: true
            }]
        };

        // Controle de aba!!
        $scope.$on('$stateChangeStart', function () {
            PersistentDataService.store('PercentualTransferenciaController', vm, "PercentualTransferencia", "PercentualTransferenciaCrudController", "percentual-transferencia.index");
        });

        var selfScope = PersistentDataService.get('PercentualTransferenciaController');
        var filho = PersistentDataService.get('PercentualTransferenciaCrudController');
        if (angular.isDefined(filho)) {
            $timeout(function () {
                $state.go('percentual-transferencia.percentual-transferencia-crud', {
                    link: filho.data.percentualTransferencia.proprietarioId
                });
            }, 50);
        } else if (angular.isDefined(selfScope)) {
            angular.extend(vm, selfScope.data);

            $timeout(function () {
                vm.gridOptions.dataSource.refresh();
            }, 450);
        }
    }
})();