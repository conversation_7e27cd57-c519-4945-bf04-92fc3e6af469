<div class="form-horizontal">
    <hr />
    <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3 col-lg-3">
                        Código:
                    </label>
                    <div class="input-group col-xs-3 col-sm-3 col-md-3 col-lg-3">
                        <input type="text" ng-model="vm.liberacaoBloqueioSpd.Id" class="form-control" disabled value="{{vm.isNew()}}" />
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-3 col-lg-3 control-label">
                        <span class="text-danger mr-5">*</span>Código SPD:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-9 col-lg-9">
                        <input type="text" ui-number-mask = "0" ui-hide-group-sep required ng-model="vm.liberacaoBloqueioSpd.Codigo" required-message="'Código SPD é obrigatório'" maxlength="3" validate-on="blur" name="codigoSPD" class="form-control"
                        />
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-3 col-lg-3 control-label">
                        <span class="text-danger mr-5">*</span>Descrição:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-9 col-lg-9">
                        <input type="text" required ng-model="vm.liberacaoBloqueioSpd.Descricao" required-message="'Descrição é obrigatória'" maxlength="100" validate-on="blur" name="Descricao" class="form-control"
                        />
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <consulta-padrao-modal tabledefinition="vm.consultaEmpresa" label="'Empresa:'" placeholder="'Selecione uma Empresa'" required-message="'Empresa é obrigatório'" ng-required="vm.isAdmin()" ng-show="vm.isAdmin()"></consulta-padrao-modal>
        </div>
      
    </div>
</div>