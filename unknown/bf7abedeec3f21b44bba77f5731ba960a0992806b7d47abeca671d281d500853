<style>
    .fixLabelFile {
        float: right;
        right: -14px;
    }

    .borderSettings {
        border: 1px solid #dcdada;
        padding-top: 3px;
        margin-right: -2px;
        text-align: right;
        border-radius: 7px;
        margin-top: 8px;
        min-height: 69px;
        max-height: 69px;
        height: 69px;
    }

    .borderSettings:hover {
        background-color: whitesmoke;
        /* color: #D74655; */
    }

    #GrupoUsuarioCrudController .ats-switch.switch-small {
        min-width: 30px;
        height: 10px;
    }

    @media (min-width: 600px) {
        #GrupoUsuarioCrudController .xsFix {
            margin-right: 0;
            left: 0;
        }
    }

    @media (min-width: 1200px) {
        .col-lg-4:focus {
            outline: none;
        }

        #GrupoUsuarioCrudController .xsFix {
            margin-right: -49px;
            left: 49px;
        }

    }
</style>
<div class="row">
    <div ng-class="{'colorGreen': modulo.selectedMenus.length > 0}" class="col-xs-12 col-sm-12 col-md-4 col-lg-4 xsFix"
        ng-repeat="modulo in vm.modulos" ng-click="vm.clicouCard(modulo)">
        <div class="col-sm-11 borderSettings" ng-style="{ 'cursor': modulo.menus.length > 0 ? 'pointer' : 'no-drop' }">
            <div class="col-xs-3">
                <i style="font-size: 60px;" ng-style="{ 'cursor': modulo.menus.length > 0 ? 'pointer' : 'no-drop' }"
                    class="{{modulo.classIcon || 'fa window-close'}}" aria-hidden="true"></i>
            </div>
            <div class="col-xs-9" style="font-size: initial"
                ng-style="{ 'cursor': modulo.menus.length > 0 ? 'pointer' : 'no-drop' }">
                <div class=" form-group ">
                    <label ng-style="{ 'cursor': modulo.menus.length > 0 ? 'pointer' : 'no-drop' }"
                        class="col-xs-12 col-sm-12 col-md-12 col-lg-12 control-label ">
                        {{modulo.descricao}}
                        <img src="assets/images/loading-bar-cor-vermelha.gif" height="15px" ng-if="modulo.searching"
                            class="" />
                        <br />
                        <span
                            ng-if="(modulo.menus.length == 0 || modulo.menus == null || angular.isUndefined(modulo.menus)) && !modulo.searching"
                            style="font-weight: 400; font-size: 10px ">Nenhum menu disponível</span>
                        <span ng-if="modulo.menus.length > 0"
                            style="font-weight: 400; font-size: 10px ">{{modulo.selectedMenus.length || 0}}/{{modulo.menus.length || 0}}
                            menu(s) selecionado(s)</span>
                    </label>
                </div>
            </div>
        </div>
    </div>
</div>
<br />