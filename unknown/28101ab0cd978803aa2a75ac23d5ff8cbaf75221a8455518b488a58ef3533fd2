(function() {
    'use strict';

    angular
        .module('bbcWeb')
        .directive('consultaPadraoMultipleModal', consultaPadraoMultipleModal);

    function consultaPadraoMultipleModal() {

        var directive = {
            bindToController: true,
            link: link,
            templateUrl: 'app/components/consulta-padrao-multiple/consulta-padrao-modal-multiple-directive.html',
            controller: consultaPadraoMultipleModalDirectiveController,
            controllerAs: 'vm',
            restrict: 'AE',
            scope: {
                tableDefinition: '=tabledefinition',
                itensGrid: '=itensGrid',
                label: '=',
                placeholder: '=',
                ngRequired: '&',
                idmodel: '@',
                idname: '@',
                ngDisabled: '&',
                labelsize: '=labelsize',
                directivesizes: '=directivesizes',
                asteriscclass: '=asteriscclass'
            }
        };
        return directive;

        function link(scope, element, attrs) {
            if (angular.isDefined(attrs.validateOn))
                scope.vm.validateOn = attrs.validateOn;
            if (angular.isDefined(attrs.requiredMessage))
                scope.vm.requiredMessage = attrs.requiredMessage;
        }

    }
    /* @ngInject */
    function consultaPadraoMultipleModalDirectiveController($scope, $uibModal, $window) {
        var vm = this;

        vm.show = function() {
            return !this.tableDefinition.applyMultiDomainCompany;
        }

        if (this.tableDefinition.applyMultiDomainCompany === true) {
            var idEmpresaBase = $window.localStorage.getItem('idEmpresaBase').toFixedType();
            if (angular.isDefined(idEmpresaBase) && idEmpresaBase !== '' && idEmpresaBase !== null)
                this.tableDefinition.selectedValue = parseInt(idEmpresaBase);

        }

        vm.open = function() {
            var modalInstance = $uibModal.open({
                animation: true,
                ariaLabelledBy: 'modal-title',
                ariaDescribedBy: 'modal-body',
                templateUrl: 'app/components/consulta-padrao-multiple/consulta-padrao-multiple-modal.html',
                controller: 'ConsultaPadraoMultipleModalController',
                controllerAs: 'vm',
                windowClass: 'master-supreme-infinity-z-index-but-the-login-z-index-is-higher',
                size: 'lg',
                resolve: {
                    tableDefinition: this.tableDefinition
                }
            });
        };

        vm.clear = function() {
            vm.tableDefinition.selectedText = "";
            vm.tableDefinition.selectedValue = null;
            vm.tableDefinition.itensGrid = [];
        }

        vm.deletar = function($index){
            itensGrid.splice($index, 1);
        }

        vm.asteriscClass = angular.isDefined(this.asteriscclass) ? this.asteriscclass : '';
        vm.tamanhoDiv = angular.isDefined(this.directivesizes) ? this.directivesizes : "col-sm-12 col-md-6 col-lg-6";
        vm.tamanhoLabel = angular.isDefined(this.labelsize) ? this.labelsize : "col-sm-3 col-md-3 col-lg-3 control-label";

        vm.onChangeInput = function() {
            if (angular.isUndefined(vm.selectedText) || vm.selectedText === "")
                vm.tableDefinition.selectedValue = undefined;
        }
    }
})();