<style>
    .master-supreme-infinity-z-index-but-the-login-z-index-is-higher {
        z-index: 9999999 !important;
    }
</style>
<div class="{{vm.tamanhoDiv}}" ng-show="vm.show()">
    <div class="form-group form-horizontal">
        <!--<label style="text-align: right;margin-top: 7px;" class="{{vm.tamanhoLabel}}"><span ng-show="vm.ngRequired()" class="text-danger mr-5">*</span> {{vm.label}}</label>-->
        <label class="{{vm.tamanhoLabel}}">
            <span ng-show="vm.ngRequired()" class="text-danger mr-5 {{vm.asteriscClass}}">*</span> {{vm.label}}</label>
        <p class="input-group">
            <input type="text" name="{{vm.idname ? vm.idname : 'inputBuscar'}}" id="{{vm.idmodel ? vm.idmodel : 'inputBuscar'}}" ng-required="vm.ngRequired()" required-message="{{vm.requiredMessage}}" ng-disabled="true" ng-model="vm.tableDefinition.selectedText"
                ng-change="vm.onChangeInput()" placeholder="{{vm.placeholder}}" class="form-control" />
            <span class="input-group-btn">
                    <button type="button" ng-disabled="vm.tableDefinition.selectedText == null || vm.tableDefinition.selectedText.length == 0 || vm.ngDisabled()"
                        class="btn btn-default" ng-click="vm.clear()">
                        <i class="fa fa-close"></i>
                    </button>
                    <button type="button" ng-disabled="vm.ngDisabled()" class="btn btn-default" ng-click="vm.open()">
                        <i class="fa fa-search"></i>
                    </button>
                </span>
        </p>
    </div>
</div>