(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('ClientSecretController', ClientSecretController);

    ClientSecretController.inject = ['BaseService', '$rootScope', 'toastr', '$scope', 'PersistentDataService', '$timeout', '$state', 'PERFIL_ADMINISTRADOR'];

    function ClientSecretController(BaseService, $rootScope, toastr, $scope, PersistentDataService, $timeout, $state, PERFIL_ADMINISTRADOR) {
        var vm = this;
        vm.headerItems = [{
            name: '<PERSON><PERSON><PERSON><PERSON>'
        }, {
            name: 'Client secret'
        }];

      

        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.perfil == PERFIL_ADMINISTRADOR;
        };
        
        vm.gridOptions = {
            data: [],  
            onRegisterApi: BaseService.dataGrid.defaultOnRegisterApi,
            dataSource: {
                url: "ClientSecret/ConsultarGridClientSecret"
            },
            columnDefs: [{
                name: 'A<PERSON>õ<PERSON>',
                width: 70,
                cellTemplate: '<div ng-if="!row.groupHeader" class="ui-grid-cell-contents" title="TOOLTIP">\
                                    <button tooltip-placement="right" uib-tooltip="Editar" ng-disabled="row.entity.ativo===0" type="button" ui-sref="client-secret.client-secret-crud({link: row.entity.id})"\
                                        ng-class="{ \'btn btn-xs btn-info\': true }">\
                                        <i class="fa fa-edit"></i>\
                                    </button>\
                                    \
                                    <button type="button" title="Ativar/Desativar" ng-click="grid.appScope.vm.alterarStatus(row.entity)"  ng-disabled="row.entity.ativo===0" ng-class="row.entity.ativo===1 ? \'btn btn-xs btn-success\' : \'btn btn-xs btn-danger\'">\                                            <i ng-class="row.entity.ativo===1 ? \'fa fa-check\' : \'fa fa-times\'"></i>\
                                    </button>\
                                </div>'
            }, {
                name: 'Código',
                width: 80,
                type : 'number',
                primaryKey: true,
                field: 'id',
                serverField: 'id'
            }, {
                name: 'Descrição',
                width: '*',
                minWidth: 180,
                field: 'descricao',
                serverField: 'descricao'
            },, {
                name: 'Usuário cadastro',
                width: '*',
                minWidth: 180,
                field: 'usuarioCadastro',
                serverField: 'usuarioCadastro'
            }, {
                name: 'Client secret',
                width: '*',
                minWidth: 180,
                field: 'secretKey',
                serverField: 'secretKeySearch'
            }, {
                name: 'CNPJ',
                width: '*',
                minWidth: 180,
                type: 'text',
                field: 'cnpj',
                serverField:'cnpjSearch'
            }, {
                name: 'Grupo Empresa',
                width: '*',
                minWidth: 180,
                field: 'grupoEmpresa',
                serverField:'grupoEmpresa'
            }, {
                name: 'Empresa',
                width: '*',
                minWidth: 180,
                field: 'empresa',
                serverField: 'empresa'
            }, {
                name: 'Data cadastro',
                width: '*',
                minWidth: 180,
                field: 'dataCadastro',
                serverField: 'dataCadastro',
                enableFiltering: false
            }, {
                name: 'Data expiracao',
                width: '*',
                minWidth: 180,
                field: 'dataExpiracao',
                enableFiltering: false
            }, {
                name: 'Data desativação',
                width: '*',
                minWidth: 180,
                field: 'dataDesativacao',
                enableFiltering: false
            }, {
                name: 'Status',
                width: '*',
                minWidth: 180,
                field: 'ativo',
                enableFiltering: false,
                cellTemplate: '<div class="ui-grid-cell-contents" align="left">\
                                        <p ng-show="row.entity.ativo === 0"> Inativo </p>\
                                        <p ng-show="row.entity.ativo === 1"> Ativo </p>\
                                   </div>'
            }
          ]
        };
        vm.gridOptions.minimumColumnSize = 100;

        
        
        vm.alterarStatus = function (entity) {
            var alterarStatus = {
                id : entity.id
            } 
            BaseService.post('ClientSecret', "AlterarStatus", alterarStatus).then(function (response) {
                response.success ?  toastr.success('Client secret inativada com sucesso!') : toastr.error(response.message);
                vm.gridOptions.dataSource.refresh();
            });
        };
       
        // Controle de aba!!
        $scope.$on('$stateChangeStart', function () {
            PersistentDataService.store('ClientSecretController', vm, "Client secret", "ClientSecretCrudController", "client-secret.index");
        });

        var selfScope = PersistentDataService.get('ClientSecretController');
        var filho = PersistentDataService.get('ClientSecretCrudController');
        if (angular.isDefined(filho)) {
            $timeout(function () {
                $state.go('client-secret.client-secret-crud', {
                    link: filho.data.grupoUsuario.IdGrupoUsuario > 0 ? 'editar' : 'novo'
                });
            }, 50);
        } else if (angular.isDefined(selfScope)) {
            angular.extend(vm, selfScope.data);

            $timeout(function () {
                vm.gridOptions.dataSource.refresh();
            }, 450);
        }
    }
})();