<style>
    .input-group-custom {
    display: flex;
    align-items: center;
    }

    .custom-input {
        flex: 1;
        margin-right: 10px; /* Espaçamento entre o input e o toggle switch */
    }

    .custom-toggle {
        flex-shrink: 0;
    }

    span.switch-right {
        background: blue !important;
    }

    span.switch-left {
        background: green !important;
    }

    .input-group-custom {
        position: relative;
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
        }
        to {
            opacity: 1;
        }
    }

    @keyframes fadeOut {
        from {
            opacity: 1;
        }
        to {
            opacity: 0;
        }
    }

    .single-input {
        position: absolute;
        width: 100%;
        top: 0;
        left: 0;
        transition: opacity 0.3s ease-in-out;
    }

    .ng-enter {
        animation: fadeIn 0.3s forwards;
    }

    .ng-leave {
        animation: fadeOut 0.3s forwards;
    }

</style>

<div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
    <div class="row">
        <div class="col-xs-12 col-md-6">
            <div class="form-group">
                <label class="control-label col-xs-12 col-md-4">Conta tarifa Vale Pedágio:</label>
                <div class="input-group col-xs-12 col-md-8">
                    <input type="text" 
                        ng-model="vm.configuracaoValePedagio.contaTarifaValePedagio" 
                        name="ContaTarifaValePedagio"
                        ng-required="true"
                        required-message="'Conta tarifa é obrigatória'"
                        class="form-control ng-pristine ng-valid ng-empty ng-touched user-success custom-input single-input" />
                </div>
            </div>
        </div>
        <div class="col-xs-12 col-md-6">
            <div class="form-group">
                <label class="control-label col-xs-12 col-md-4"> {{ vm.tipoValorMaximoComplemento ? '% Valor máximo para pagamento de complemento:' : 'Valor máximo para pagamento de complemento:' }}</label>
                <div class="input-group input-group-custom col-xs-12 col-md-8">
                    <!-- Input para números inteiros quando o toggle estiver no "%" -->
                    <input type="text" 
                        ng-if="vm.tipoValorMaximoComplemento" 
                        ng-model="vm.configuracaoValePedagio.valorMaximoPagamentoComplemento" 
                        name="ValorMaximoPagamentoComplemento" 
                        ui-number-mask="2"
                        class="form-control ng-pristine ng-valid ng-empty ng-touched user-success custom-input single-input"
                        placeholder="Digite um percentual" />

                    <!-- Input para valores decimais quando o toggle estiver no "$" -->
                    <input type="text" 
                        ng-if="!vm.tipoValorMaximoComplemento" 
                        ui-money-mask="2" 
                        ng-model="vm.configuracaoValePedagio.valorMaximoPagamentoComplemento" 
                        name="ValorMaximoPagamentoComplemento" 
                        class="form-control ng-pristine ng-valid ng-empty ng-touched user-success custom-input single-input"
                        placeholder="Digite um valor" />
        
                    <span class="custom-toggle">
                        <toggle-switch on-label="%" off-label="R$" ng-model="vm.tipoValorMaximoComplemento" ng-change="vm.resetValueValorMaximoComp()"></toggle-switch>
                    </span>
                </div>
            </div>
        </div>
    
    </div>
    <div class="row">
        <div class="col-xs-12 col-md-6">
            <div class="form-group">
                <label class="control-label col-xs-12 col-md-4">Conta Vale Pedágio:</label>
                <div class="input-group col-xs-12 col-md-8">
                    <input type="text" ng-model="vm.configuracaoValePedagio.contaValePedagio" name="ContaValePedagio" required required-message="'Conta vale pedágio é obrigatória'"
                        class="form-control ng-pristine ng-valid ng-empty ng-touched user-success" />
                </div>
            </div>
        </div>
        <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
            <div class="form-group">
                <label class="col-xs-12 col-sm-3 col-md-5 col-lg-4 control-label">
                    Valor máximo para pagamento de vale pedágio:
                </label>
                <div class="input-group col-xs-12 col-sm-9 col-md-7 col-lg-8">
                    <input type="text" ui-money-mask="2" ng-model="vm.configuracaoValePedagio.valorMaximoPagamentoValePedagio" class="form-control" required/>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-xs-12 col-md-6">
            <div class="form-group">
                <label class="control-label col-xs-12 col-md-4">Configuração de tentativa de reenvio vale pedágio:</label>
                <div class="input-group col-xs-12 col-md-8">
                    <input type="text" ng-model="vm.configuracaoValePedagio.configuracaoTentativaReenvioValePedagio" name="ConfiguracaoTentativaReenvioValePedagio" required required-message="'Configuração de tentativa de reenvio é obrigatória'"
                        class="form-control ng-pristine ng-valid ng-empty ng-touched user-success" />
                </div>
            </div>
        </div>

<!--        <div class="col-xs-12 col-md-6">-->
<!--            <div class="form-group">-->
<!--                <label class="control-label col-xs-12 col-md-4">Configuração de tentativa de reenvio vale pedágio:</label>-->
<!--                <div class="input-group col-xs-12 col-md-8">-->
<!--                    <input type="text" ng-model="vm.configuracaoValePedagio.configuracaoTentativaReenvioValePedagio" name="ConfiguracaoTentativaReenvioValePedagio" required required-message="'Configuração de tentativa de reenvio é obrigatória'"-->
<!--                        class="form-control ng-pristine ng-valid ng-empty ng-touched user-success" />-->
<!--                </div>-->
<!--            </div>-->
<!--        </div>-->

<!--        <div class="col-xs-12 col-md-6">-->
<!--            <div class="form-group">-->
<!--                <label class="control-label col-xs-12 col-md-4">Configuração de cancelamento (Dias):</label>-->
<!--                <div class="input-group col-xs-12 col-md-8">-->
<!--                    <input type="text" ng-model="vm.configuracaoValePedagio.configuracaoTentativaReenvioValePedagio" name="ConfiguracaoTentativaReenvioValePedagio" required required-message="'Configuração de tentativa de reenvio é obrigatória'"-->
<!--                        class="form-control ng-pristine ng-valid ng-empty ng-touched user-success" />-->
<!--                </div>-->
<!--            </div>-->
<!--        </div>-->
        <div class="col-xs-12 col-md-6">
            <div class="form-group">
                <label class="control-label col-xs-12 col-md-4">Prazo máximo para cancelamento de pagamentos de vale pedágio (Dias):</label>
                <div class="input-group col-xs-12 col-md-8">
                    <input type="text" ng-model="vm.configuracaoValePedagio.configuracaoCancelamentoPagamentoPedagio" name="ConfiguracaoCancelamentoPagamentoPedagio" required required-message="'Configuração de cancelamento pagamento é obrigatória'"
                        class="form-control ng-pristine ng-valid ng-empty ng-touched user-success" />
                </div>
            </div>
        </div>
        
        <div class="col-xs-12 col-md-6">
            <div class="form-group">
                <label class="control-label col-xs-12 col-md-4">Configuração de tentativa de reenvio vale pedágio:</label>
                <div class="input-group col-xs-12 col-md-8">
                    <input type="text" ng-model="vm.configuracaoValePedagio.configuracaoTentativaReenvioValePedagio" name="ConfiguracaoTentativaReenvioValePedagio" required required-message="'Configuração de tentativa de reenvio é obrigatória'"
                        class="form-control ng-pristine ng-valid ng-empty ng-touched user-success" />
                </div>
            </div>
        </div>

<!--        <div class="col-xs-12 col-md-6">-->
<!--            <div class="form-group">-->
<!--                <label class="control-label col-xs-12 col-md-4">Configuração de cancelamento (Dias):</label>-->
<!--                <div class="input-group col-xs-12 col-md-8">-->
<!--                    <input type="text" ng-model="vm.configuracaoValePedagio.configuracaoTentativaReenvioValePedagio" name="ConfiguracaoTentativaReenvioValePedagio" required required-message="'Configuração de tentativa de reenvio é obrigatória'"-->
<!--                        class="form-control ng-pristine ng-valid ng-empty ng-touched user-success" />-->
<!--                </div>-->
<!--            </div>-->
<!--        </div>-->
    </div>
<!--    <div class="row">-->
<!--       -->
<!--        -->
<!--    </div>-->
</div>