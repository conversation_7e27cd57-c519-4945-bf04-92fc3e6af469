(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('ConsultaPadraoMultipleModalController', ConsultaPadraoMultipleModalController);

    function ConsultaPadraoMultipleModalController($uibModalInstance, tableDefinition) {
        var vm = this;

        vm.tableDefinition = tableDefinition;
        

        vm.onSelectValue = function () {
            var objAdd = {
                Id: vm.tableDefinition.selectedValue, 
                Descricao: vm.tableDefinition.selectedText
            };
            for (var i = 0; i < vm.tableDefinition.itensGrid.length; i++) {
                const item = vm.tableDefinition.itensGrid[i];

                if(item.Id == vm.tableDefinition.selectedValue){
                    vm.tableDefinition.selectedText = vm.tableDefinition.itensGrid.length + " " + vm.tableDefinition.textoItensSelecionados
                    return;
                }
                
            }
            vm.tableDefinition.itensGrid.push(objAdd);
            //vm.tableDefinition.selectedText = vm.tableDefinition.itensGrid.length + " " + vm.tableDefinition.textoItensSelecionados
            
        };

        vm.deletar = function ($index) {
            vm.tableDefinition.itensGrid.splice($index, 1);
            //vm.tableDefinition.selectedText = vm.tableDefinition.itensGrid.length + " " + vm.tableDefinition.textoItensSelecionados
            /*
            if(vm.tableDefinition.itensGrid.length == 0)
                vm.tableDefinition.selectedText = "";
            */
        };

        vm.confirmar = function ($index) {
            $uibModalInstance.close();
        };
        
    }
})();