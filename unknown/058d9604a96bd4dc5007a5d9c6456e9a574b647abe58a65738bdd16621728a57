(function () {
  'use strict';

  angular
    .module('bbcWeb')
    .controller('PainelSaldoController', PainelSaldoController);

  PainelSaldoController.$inject = [
    'BaseService','$interval','toastr','$rootScope'
  ];

  function PainelSaldoController(BaseService, $interval, toastr, $rootScope) {

    var vm = this;
    vm.parametros = {};
    vm.hoje = new Date();

    vm.headerItems = [{
      name: 'Movimentações'
    }, {
      name: 'Painel de Saldo'
    }];

    function init() {
      ConsultaParametrosTela();    
    }

    function ConsultaParametrosTela() {
      BaseService.get("Saldo", "ConsultaParametrosConfiguracaoTelaoSaldo")
        .then(function (response) {
          if (response.success) {
             vm.parametros = response.data;
             ConsultarEmpresasHabilitadas();
          }else{
            toastr.Error("Não foi possivel consultar os paramêtros da tela.")
          }
        });
    }
    
    vm.fixGridHeight = function(){
      var gridStyle = angular.element('canvas')[0].offsetHeight;
      if(gridStyle > 0){
        var elements = document.getElementsByClassName("totalizador");
        for (let index = 0; index < elements.length; index++) {
          var element = elements[index];
          element.style.maxHeight = gridStyle+'px'
        }
      }
    }

    function definirDataCorrespondente(empresa, data) {
      const dataAtual = data ? data : new Date();
      // Formatar para "DD/MM/YYYY"
      const dataFormatada = dataAtual.toLocaleDateString('pt-BR');
      // Armazena no objeto empresa
      empresa.dataCorrespondente = dataFormatada;
      console.log(empresa.dataCorrespondente); // Exemplo: "26/02/2025"
    }

    function ConsultarEmpresasHabilitadas() {
      BaseService.get("Saldo", "GetEmpresaHabilitados")
        .then(function (response) {
          if (response.success) {
            vm.empresasHabilitados = response.data;
            vm.empresasHabilitados.forEach(function (empresa) {

              startWeekFilter(empresa);
              startConfigCompany(empresa);
              vm.cosultarDadosGridChartBbc(empresa)

              GetResumoDiaLinha(empresa).then(function (resumoLinhas) {
                empresa.carregouResumoDias = true
                empresa.resumoLinhas = resumoLinhas;
              });

            });

            vm.ConsultarDados();
            iniciarTempoAtualizacao();
          }
        });
      }
      
      function iniciarTempoAtualizacao() {
        $interval(AtualizaTela, (vm.parametros.tempoAtualizacaoTelao * 60) * 1000);
      }

      function AtualizaTela(){
        vm.fixGridHeight()
        vm.empresasHabilitados.forEach(function (empresa) {
            vm.cosultarDadosGridChartBbc(empresa)
        });
      }

    vm.ConsultarDados = function () {
      vm.empresasHabilitados.forEach(function (empresa) {
      });
    }

    vm.cosultarDadosGridChartBbc = function (empresa) {
      GetPagamentosDiaGrid(empresa).then(function (pagamentosDia) {
        empresa.pagamentosDiaGrid = pagamentosDia;
      });

      ConsultarGraficoBbc(empresa).then(function (graficoResponse) {
        empresa.chartData = {
          labels: graficoResponse.labels,
          datasets: [
            {
              label: 'Valor semana atual',
              fill: false,
              borderColor: 'rgb(256, 0, 5)',
              tension: 0.1,
              data: graficoResponse.valorSemanaAtual,
              borderWidth: 4
            },
            {
              label: 'Valor semana anterior',
              fill: false,
              borderColor: 'rgb(0, 0, 256)',
              tension: 0.1,
              data: graficoResponse.valorSemanaAnterior,
              borderWidth: 4
            },
            {
              label: 'Mesmo horário na semana anterior',
              fill: false,
              borderColor: 'rgb(238, 173, 45)',
              tension: 0.1,
              data: graficoResponse.valorMesmaHoraSemanaAnterior,
              borderWidth: 4
            }
          ]
        };;
      });
    }
    
    function ConsultarGraficoBbc(empresa) {
      return BaseService.get("Saldo", "GetPagamentosGrafico", { empresaId: empresa.id, dataCorrespondente: empresa.dataConsulta })
        .then(function (response) {
          empresa.carregouDados = true;
          if (response.success) {
            return response.data;
          } else {
            return {
              labels: [""],
              valorSemanaAtual: [0],
              valorSemanaAnterior: [0],
              valorMesmaHoraSemanaAnterior: [0],
            }
          }
        });
    }

    vm.dataGroupChanged = function(empresa){
      vm.fixGridHeight()
        vm.cosultarDadosGridChartBbc(empresa)
    }
    
    function GetResumoDiaLinha(empresa) {
      // TODO PASSAR A DATA AQUI
      return BaseService.get("Saldo", "GetResumoDia", { empresaId: empresa.id, dataCorrespondente: empresa.dataConsulta })
        .then(function (response) {
          if (response.success) {
            return response.data;
          } else {
            return [];
          }
        });
    }

    function GetPagamentosDiaGrid(empresa) {
      // TODO PASSAR A DATA AQUI
      return BaseService.get("Saldo", "GetPagamentosPorDia", { empresaId: empresa.id, dataCorrespondente: empresa.dataConsulta })
        .then(function (response) {
          empresa.carregouDados = true;
          if (response.success) {
            return response.data;
          } else {
            return [];
          }
        });
    }

    init();


    vm.selecionarSemana = function (empresa) {
        if (!empresa.dataCorrespondente) return;

        var dataSelecionada = new Date(empresa.dataCorrespondente);


        let segunda = new Date(dataSelecionada);
        segunda.setDate(segunda.getDate() - ((segunda.getDay() + 6) % 7)); // Garante que é segunda-feira
  
        empresa.semanaSelecionada = {
          segunda: new Date(segunda),
          terca: new Date(segunda.setDate(segunda.getDate() + 1)),
          quarta: new Date(segunda.setDate(segunda.getDate() + 1)),
          quinta: new Date(segunda.setDate(segunda.getDate() + 1)),
          sexta: new Date(segunda.setDate(segunda.getDate() + 1)),
          sabado: new Date(segunda.setDate(segunda.getDate() + 1)),
          domingo: new Date(segunda.setDate(segunda.getDate() + 1))
        };
  
        // Pega a data inicial e final
        empresa.dataInicialSemana = empresa.semanaSelecionada.segunda;
        empresa.dataFinalSemana = empresa.semanaSelecionada.domingo;


        empresa.dataConsulta = dataSelecionada;
        empresa.carregouResumoDias = false;
        empresa.carregouDados = false;

        vm.cosultarDadosGridChartBbc(empresa)
          
        GetResumoDiaLinha(empresa).then(function (resumoLinhas) {
          empresa.carregouResumoDias = true
          empresa.resumoLinhas = resumoLinhas;
        });

        vm.AtualizaTela()();
    };

    function startConfigCompany(empresa) {
      empresa.carregouResumoDias = false;
      empresa.carregouDados = false;
      empresa.graficoOn = true; 
      empresa.dadosBBC = false;
    }

    function startWeekFilter(empresa) {
      var dataSelecionada = new Date();
      empresa.dataCorrespondente = new Date(dataSelecionada.getFullYear(), dataSelecionada.getMonth(), dataSelecionada.getDate());
      
      let segunda = new Date(empresa.dataCorrespondente);
      segunda.setDate(segunda.getDate() - ((segunda.getDay() + 6) % 7)); // Garante que é segunda-feira

      empresa.semanaSelecionada = {
        segunda: new Date(segunda),
        terca: new Date(segunda.setDate(segunda.getDate() + 1)),
        quarta: new Date(segunda.setDate(segunda.getDate() + 1)),
        quinta: new Date(segunda.setDate(segunda.getDate() + 1)),
        sexta: new Date(segunda.setDate(segunda.getDate() + 1)),
        sabado: new Date(segunda.setDate(segunda.getDate() + 1)),
        domingo: new Date(segunda.setDate(segunda.getDate() + 1))
      };

      // Pega a data inicial e final
      empresa.dataInicialSemana = empresa.semanaSelecionada.segunda;
      empresa.dataFinalSemana = empresa.semanaSelecionada.domingo;

      empresa.dataConsulta = dataSelecionada;
    }

    vm.type = 'line'
    vm.chartOptions = {
      responsive: true,
      maintainAspectRatio: true,
      plugins: {
        legend: {
          display: true,
          position: 'top'
        },
      },
      scales: {
        y: {
          grid: {
            display: true,
            color: "rgba(255,99,132,0.2)"
          },
          ticks: {
            callback: function (value, index, values) {
              return new Intl.NumberFormat('pt-BR', {
                style: 'currency',
                currency: 'BRL'
              }).format(value);
            }
          }
        },
        x: {
          grid: {
            display: false
          }
        }
      }
    };
  }
})();