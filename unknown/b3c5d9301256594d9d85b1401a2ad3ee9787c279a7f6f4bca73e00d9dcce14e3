(function () {
    'use strict';

    angular.module('bbcWeb').controller('GrupoUsuarioCrudController', GrupoUsuarioCrudController);

    GrupoUsuarioCrudController.$inject = ['toastr', '$rootScope', 'BaseService', '$state', '$stateParams', '$window', 'PERFIL_ADMINISTRADOR', '$scope', '$timeout', 'PersistentDataService', 'DefaultsService', '$uibModal'];

    function GrupoUsuarioCrudController(
        toastr, $rootScope,
        BaseService, $state,
        $stateParams, $window,
        PERFIL_ADMINISTRADOR,
        $scope, $timeout,
        PersistentDataService,
        DefaultsService,
        $uibModal
    ) {
        var vm = this;
        vm.grupoUsuario = [];
        vm.menusPai = [];
        vm.sistema = 0;

        vm.headerItems = [{
            name: '<PERSON><PERSON><PERSON><PERSON>'
        }, {
            name: 'Grupos de usuário',
            link: 'grupo-usuario.index'
        }, {
            name: $stateParams.link == 'novo' ? 'Novo' : 'Editar'
        }];

        vm.consultaporid = false;

        var selfScope = PersistentDataService.get('GrupoUsuarioCrudController');

        if ($stateParams.link == 'novo')
            vm.grupoUsuario.idGrupoUsuario = 'Auto';

        vm.isNew = function () {
            return $stateParams.link == 'novo';
        };

        vm.combosSistema = [
            { data: 0, label: 'BBC Controle' },
            { data: 1, label: 'Rede BBC Controle' }
        ];

        // $scope.$watch('vm.sistema', function () {
        //     if(vm.sistema == 0){
        //         vm.consultaPosto.selectedValue = null;
        //         vm.consultaPosto.selectedText = null;
        //     }else
        //     vm.consultaEmpresa.selectedValue = null;
        //     vm.consultaEmpresa.selectedText = null;
        //});

        vm.clicouCard = function (modulo) {
            if (modulo.menus && modulo.menus.length > 0) {
                var modalInstance = $uibModal.open({
                    animation: true,
                    templateUrl: 'app/entities/grupo-usuario/modal/selecao-menu.html',
                    windowClass: 'width-fix-app-modal-window',
                    controller: function ($uibModalInstance, $scope, modulo, menus) {
                        var vm = this;
                        vm.itensDesmarcados = [];
                        vm.menus = angular.copy(menus);

                        vm.modulo = modulo.descricao;
                        vm.agrupadosPorPai = [];
                        
                        vm.getAgrupamentosPorPai = function () {
                            var grpPaiIds = [];
                            var agrupadoRetorno = [];
                            if (angular.isArray(vm.menus))
                                vm.menus.forEach(function (item) {
                                    if (grpPaiIds.indexOf(item.idMenuPai) == -1) {
                                        grpPaiIds.push(item.idMenuPai);
                                        agrupadoRetorno.push({
                                            IdMenuPai: item.idMenuPai,
                                            DescricaoPai: item.pai
                                        });
                                    }
                                });
                            agrupadoRetorno.forEach(function (item) {
                                var menusPai = vm.menus.filter(function (filho) {
                                    return filho.idMenuPai == item.IdMenuPai;
                                });
                                item.menus = menusPai;
                            });
                            return agrupadoRetorno;
                        };

                        vm.menusEstao = function (marcados) {
                            var canContinue = true;

                            vm.menus.forEach(function (item) {
                                if (item.selecionado !== marcados)
                                    canContinue = false;
                            });

                            return canContinue;
                        };

                        vm.menusEstaoIndefinidos = function () {
                            var canContinue = true;

                            vm.menus.forEach(function (item) {
                                if (item.selecionado != undefined)
                                    canContinue = false;
                            });

                            return canContinue;
                        };

                        vm.temAlgumMenuSelecionadoIgual = function (selStatus) {
                            var tem = false;
                            vm.menus.forEach(function (item) {
                                if (item.selecionado == selStatus)
                                    tem = true;
                            });

                            return tem;
                        };

                        vm.setarTodos = function (bool) {
                            if (bool)
                                vm.itensDesmarcados = [];

                            vm.menus.forEach(function (item) {
                                item.selecionado = bool;
                                if (bool == false)
                                    vm.itensDesmarcados.push(item.idMenu);
                            });
                        };

                        vm.close = function (noEvento) {
                            if (noEvento) {
                                $uibModalInstance.dismiss(null);
                            } else {
                                var objRet = {
                                    marcados: vm.menus.filter(function (item) {
                                        return item.selecionado == true;
                                    }).map(function (item) {
                                        return {
                                            idMenu: item.idMenu
                                        };
                                    }),
                                    desmarcados: vm.itensDesmarcados
                                };

                                $uibModalInstance.close(objRet);
                            }
                        };

                        vm.marcar = function (menu) {
                            menu.selecionado = true;
                            var idxOf = vm.itensDesmarcados.indexOf(menu.idMenu);
                            if (idxOf > -1)
                                vm.itensDesmarcados.splice(idxOf, 1);
                        };

                        vm.desmarcar = function (menu) {
                            menu.selecionado = false;
                            vm.itensDesmarcados.push(menu.idMenu);
                        };

                        vm.agrupadosPorPai = vm.getAgrupamentosPorPai();
                    },
                    resolve: {
                        modulo: function () {
                            return modulo;
                        },
                        menus: function () {
                            modulo.menus.forEach(function (item) {
                                if (angular.isArray(modulo.selectedMenus)) {
                                    item.selecionado = modulo.selectedMenus.some(function (item2) {
                                        return item2.idMenu == item.idMenu;
                                    });
                                } else
                                    item.selecionado = false;
                            });
                            return modulo.menus;
                        }
                    },
                    keyboard: true,
                    size: 'sm',
                    controllerAs: 'vm',
                    backdrop: true
                });

                modalInstance.result.then(function (ret) {
                    modulo.selectedMenus = ret.marcados;
                    vm.grupoUsuario.modulos = vm.modulos;
                    vm.desmarcarItensDeModulosById(ret.desmarcados, function () {
                        $timeout(function () {
                            vm.marcaritensDeModulosById(ret.marcados);
                        }, 100);
                    });
                });
            } else
                toastr.error('Nenhum menu disponível para o módulo ' + modulo.Descricao);
        };

        vm.desmarcarItensDeModulosById = function (listaIdsDesmarcados, callback) {
            vm.modulos.forEach(function (mod) {
                if (angular.isArray(listaIdsDesmarcados)) {
                    listaIdsDesmarcados.forEach(function (idDesmarc) {
                        var idxOfItem = mod.selectedMenus.findIndex(function (item) {
                            return item.idMenu == idDesmarc
                        });
                        if (idxOfItem > -1)
                            mod.selectedMenus.splice(idxOfItem, 1);
                    });
                }
            });
            if (angular.isFunction(callback))
                callback();
        };

        vm.marcaritensDeModulosById = function (listaIdsMarcados) {
            if (angular.isArray(listaIdsMarcados) && listaIdsMarcados.length > 0)
                vm.modulos.forEach(function (mod) {
                    if (angular.isArray(listaIdsMarcados)) {
                        listaIdsMarcados.forEach(function (idMarcado) {
                            var modulo = mod;
                            var modTemMenu = modulo.menus.findIndex(function (nn) {
                                return nn.idMenu == idMarcado.idMenu;
                            });
                            var selectedMenuModIndex = modulo.selectedMenus.findIndex(function (nn) {
                                return nn.idMenu == idMarcado.idMenu;
                            });
                            if (modTemMenu > -1 && selectedMenuModIndex == -1)
                                modulo.selectedMenus.push(idMarcado);
                        });
                    }
                });
        };

        vm.getCodeView = function () {
            var retTemp = angular.copy(vm.grupoUsuario);
            retTemp.selectedMenus = [];
            vm.modulos.forEach(function (modulo) {
                if (angular.isArray(modulo.selectedMenus))
                    modulo.selectedMenus.forEach(function (sMenu) {
                        retTemp.selectedMenus.push(sMenu.idMenu);
                    });
            });

            retTemp.selectedMenus = retTemp.selectedMenus.unique();

            return retTemp;
        };

        //primeira consulta
        vm.loadEdit = function (id) {
            BaseService.get('GrupoUsuario', 'ConsultarPorId', {
                idGrupoUsuario: id
            }).then(function (response) {
                if (!response.success) {
                    toastr.error(response.message);
                    return;
                } else {
                    // Sucesso
                    vm.consultaporid = true;
                    vm.grupoUsuario = response.data;
                    vm.modulos = vm.grupoUsuario.modulo;
                    vm.consultaEmpresa.selectedValue = response.data.idEmpresa;
                    vm.consultaEmpresa.selectedText = response.data.nomeEmpresa;
                    vm.consultaPosto.selectedValue = response.data.idPosto;
                    vm.consultaPosto.selectedText = response.data.nomePosto;
                    vm.sistema = response.data.sistema;
                    //consulta os modulos
                    vm.getModulos();
                    vm.consultaporid = false;
                }
            });
        };


        vm.save = function (form) {
            if (!form.$valid) {
                toastr.error('Todos os campos obrigatórios devem ser preenchidos corretamente com seus respectivos valores');
                return;
            }

            if (vm.isSaving === true)
                return;

            vm.isSaving = true;

            if (vm.sistema == 0) {
                vm.consultaPosto.selectedValue = null;
                vm.consultaPosto.selectedText = null;
            } else {
                vm.consultaEmpresa.selectedValue = null;
                vm.consultaEmpresa.selectedText = null;
            }

            var prm = vm.getCodeView();
            //delete prm.modulos;
            prm.idMenusSelecionados = prm.selectedMenus;

            if (vm.grupoUsuario.empresasVinculadas.length === 1) {
                prm.idEmpresa = vm.grupoUsuario.empresasVinculadas[0].id;
            } else {
                prm.idEmpresa = null;
            }
            
            prm.idPosto = vm.consultaPosto.selectedValue;
            prm.sistema = vm.sistema;
            delete prm.selectedMenus;


            BaseService.post('GrupoUsuario', 'SaveGrupoUsuario', prm).then(function (response) {
                vm.isSaving = false;
                if (response.success) {
                    toastr.success(response.message);
                    $state.go('grupo-usuario.index');
                } else
                    toastr.error(response.message);
            });
        };

        vm.getMenusDisponiveisPorModuloEmpresa = function (idModulo, callback) {
            BaseService.get('Menu', 'GetMenusDisponiveisPorModulo', {
                idGrupoUsuario: vm.grupoUsuario.idGrupoUsuario > 0 ? vm.grupoUsuario.idGrupoUsuario : null,
                idModulo: idModulo,
                idEmpresa: vm.consultaEmpresa.selectedValue,
                idPosto: vm.consultaPosto.selectedValue
            }).then(function (response) {
                if (response.success) {
                    if (angular.isFunction(callback))
                        callback(response.data);
                } else
                    toastr.error(response.message);

                vm.isSaving = false;
            });
        };

        function carregarEmpresas() {
            // BaseService.get("Empresa", "ConsultarEmpresasCombo")
            // .then(function (response) {
            //     if (response.success) {
            //         vm.empresas = response.data;
            //     }
            // });
        }

        vm.vincularEmpresa = function () {
            $uibModal.open({
                animation: true,
                ariaLabelledBy: 'modal-title',
                ariaDescribedBy: 'modal-body',
                templateUrl: 'app/entities/grupo-usuario/modal/modal-vincular-empresa-grupo-usuario.html',
                controller: 'ModalVincularEmpresaGrupoUsuarioController',
                controllerAs: 'vm',
                backdrop: 'static',
                size: 'lg',
                resolve: {
                    grupoUsuarioId: function () {
                        return vm.grupoUsuario.idGrupoUsuario;
                    },
                    listaEmpresasIds: function () {
                        return vm.grupoUsuario.empresasVinculadas ? vm.grupoUsuario.empresasVinculadas.map(e => e.id) : [];
                    }
                }
            }).result.then(function (empresasSelecionadas) {
                if (!empresasSelecionadas || empresasSelecionadas.length === 0) {
                    return toastr.warning("Nenhuma empresa foi selecionada.");
                }

                if (typeof vm.grupoUsuario !== 'object' || vm.grupoUsuario === null) {
                    vm.grupoUsuario = { empresasVinculadas: [] };
                }
        
                if (!Array.isArray(vm.grupoUsuario.empresasVinculadas)) {
                    vm.grupoUsuario.empresasVinculadas = [];
                }
        
                empresasSelecionadas.forEach(function (empresa) {
                    var existe = vm.grupoUsuario.empresasVinculadas.some(e => e.id === empresa.id);
                    if (!existe) {
                        vm.grupoUsuario.empresasVinculadas.push(empresa);
                    }
                });

            }).catch(function () {
                console.log("Modal fechada sem adicionar empresas.");
            });
        };

        vm.excluirEmpresaVinculada = function (empresa) {
            var index = vm.grupoUsuario.empresasVinculadas.findIndex(e => e.Id === empresa.Id);
            if (index !== -1) {
                vm.grupoUsuario.empresasVinculadas.splice(index, 1);
                toastr.success("Empresa removida com sucesso!");
            } else {
                toastr.error("Erro ao remover empresa. Empresa não encontrada.");
            }
        };

        vm.excluirItens = function () {
            Sistema.Msg.confirm("Você tem certeza que deseja excluir todos os itens da lista?", function () {
                vm.grupoUsuario.empresasVinculadas = [];
            });
        };

        vm.atualizaModulos = function () {
            if (vm.sistema == 0) {
                vm.consultaPosto.selectedValue = null;
            } else {
                vm.consultaEmpresa.selectedValue = null;
                vm.grupoUsuario.empresasVinculadas = [];
            }
            vm.getModulos();
        }

        vm.getModulos = function () {
            BaseService.get('GrupoUsuario', 'ConsultarModulos', {

                empresaId: vm.consultaEmpresa.selectedValue,
                postoId: vm.consultaPosto.selectedValue,
                sistema: vm.sistema
                // idGrupoUsuario: vm.grupoUsuario.GrupoUsuarioId > 0 ? vm.grupoUsuario.GrupoUsuarioId : null
            }).then(function (response) {
                if (response.success)
                    vm.modulos = response.data;
                vm.modulos.forEach(function (mod) {
                    mod.searching = true;
                    //consulta qual menu vai carregar por modulo do result
                    vm.getMenusDisponiveisPorModuloEmpresa(mod.idModulo, function (menus) {
                        mod.menus = menus;
                        mod.selectedMenus = [];
                        mod.menus.forEach(function (ss) {
                            if (ss.ativo)
                                mod.selectedMenus.push({
                                    idMenu: ss.idMenu
                                })
                        });
                        mod.searching = false;
                    });
                });
                vm.isSaving = false;
            });
        };

        vm.getModulosClear = function () {
            BaseService.get('GrupoUsuario', 'ConsultarModulos', {

                empresaId: null
                // idGrupoUsuario: vm.grupoUsuario.GrupoUsuarioId > 0 ? vm.grupoUsuario.GrupoUsuarioId : null
            }).then(function (response) {
                if (response.success)
                    vm.modulos = response.data;
                vm.modulos.forEach(function (mod) {
                    mod.searching = true;
                    vm.getMenusDisponiveisPorModuloEmpresa(mod.idModulo, function (menus) {
                        mod.menus = menus;
                        mod.selectedMenus = [];
                        mod.menus.forEach(function (ss) {
                            if (ss.ativo)
                                mod.selectedMenus.push({
                                    idMenu: ss.idMenu
                                })
                        });
                        mod.searching = false;
                    });
                });
                vm.isSaving = false;
            });
        };

        vm.empresaVisivel = function () {
            return angular.isDefined($rootScope) && $rootScope.usuarioLogado.perfil == PERFIL_ADMINISTRADOR;
        };

        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.administrador == true;
        };

        function init() {
            vm.consultaEmpresa = DefaultsService.consultaEmpresa();
            carregarEmpresas();

            if (vm.isNew()) {
                vm.grupoUsuario.idGrupoUsuario = 'Auto';

                if (!vm.isAdmin()) {
                    vm.consultaEmpresa.selectedValue = $window.localStorage.idEmpresa ? $window.localStorage.idEmpresa.toFixedType() : $window.localStorage.idEmpresa;
                    vm.consultaEmpresa.selectedText = $window.localStorage.razaoSocialEmpresa;
                }
            }

            if (vm.isNew()) {
                vm.grupoUsuario.idGrupoUsuario = 'Auto';

                if (!vm.isAdmin()) {
                    vm.consultaEmpresa.selectedValue = $window.localStorage.idEmpresa ? $window.localStorage.idEmpresa.toFixedType() : $window.localStorage.idEmpresa;
                    vm.consultaEmpresa.selectedText = $window.localStorage.razaoSocialEmpresa;
                }
            }



            vm.consultaEmpresa = {
                columnDefs: [{
                    name: 'Código',
                    field: 'id',
                    width: 80,
                    type: 'number',
                    primaryKey: true
                }, {
                    name: 'Nome Fantasia',
                    field: 'nomeFantasia',
                    width: '*',
                    minWidth: 160
                }, {
                    name: 'Razão Social',
                    field: 'razaoSocial',
                    width: '*',
                    minWidth: 160
                }, {
                    name: 'Email',
                    field: 'email',
                    width: '*',
                    minWidth: 160
                }],
                desiredValue: 'id',
                desiredText: 'nomeFantasia',
                url: 'Empresa/ConsultarGridEmpresaCombo',
                paramsMethod: function () {
                    return {}
                },
                executeAfterSelection: function () {
                    if (!vm.consultaporid) {
                        BaseService.get('GrupoUsuario', 'ConsultarModulos', {
                            empresaId: vm.consultaEmpresa.selectedValue
                        }).then(function (response) {
                            if (response.success) {
                                vm.getModulos();
                            }
                        })
                    }
                }
            };
        }

        vm.consultaPosto = {
            columnDefs: [{
                name: 'Cód.',
                field: 'id',
                width: 60,
                type: 'number',
                primaryKey: true
            }, {
                name: 'Razão Social',
                field: 'razaoSocial',
                width: '*'
            }, {
                name: 'CNPJ',
                field: 'cnpj',
                width: 200
            }],
            desiredValue: 'id',
            desiredText: 'razaoSocial',
            url: 'Posto/ConsultarGridPosto',
            paramsMethod: function () {
                return {}
            }
        }


        vm.onClickAvancar = function (wizard) {
            wizard.go(wizard.getActivePosition() + 1);
        };

        vm.onClickVoltar = function (wizard) {

            var ativoIndex = wizard.getActivePosition();
            if (ativoIndex == 1)
                $state.go('grupo-usuario.index');

            wizard.go(ativoIndex - 1);
        };

        init();
        // DO NOT TOUCH!! 666
        $scope.$on('$stateChangeStart', function (arg1, toState) {
            if (toState.name === 'grupo-usuario.index')
                PersistentDataService.remove('GrupoUsuarioCrudController');
            else
                PersistentDataService.store('GrupoUsuarioCrudController', vm, "Cadastro - Grupos de usuário", null, "grupo-usuario.grupo-usuario-crud", vm.grupoUsuario.idGrupoUsuario);
        });

        if (angular.isDefined(selfScope))
            angular.extend(vm, selfScope.data);
        else
            if (!vm.isNew())
                vm.loadEdit($stateParams.link);
            else {
                vm.grupoUsuario = 0;
                vm.modulos = 1;
                vm.getModulos();
            }

        $timeout(function () {
            PersistentDataService.remove('GrupoUsuarioController');
        }, 15);
    }
})();