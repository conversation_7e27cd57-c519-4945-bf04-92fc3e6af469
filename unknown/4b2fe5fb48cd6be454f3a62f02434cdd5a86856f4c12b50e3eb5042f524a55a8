<div class="ibox-content">
  <div class="row">
    <div class="col-xs-12 col-md-6">
      <div class="form-group">
        <label class="col-xs-12 col-md-3 control-label pull-left pt-10">
          Período:
        </label>
        <div class="col-xs-12 col-md-8 input-group">
          <input date-range-picker class="form-control date-picker" type="text" ng-model="vm.dataBBC"
            options="vm.dateOptions" style="background-color: white !important;" readonly />
        </div>
      </div>
    </div>

    <consulta-padrao-modal idname="consultaEmpresa" idmodel="Empresa" tabledefinition="vm.consultaEmpresa"
      label="'Empresa:'" placeholder="'Selecione uma empresa'"
      validate-on="blur"  directivesizes="'col-xs-12 col-sm-12 col-md-6 col-lg-6'"
      labelsize="'col-xs-12 col-sm-12 col-md-3 col-lg-3 control-label text-align-left'">
    </consulta-padrao-modal>
  </div>

  <div class="row">
    <div class="col-xs-12 col-md-6">
      <div class="form-group">
        <label class="col-xs-12 col-md-3 control-label pull-left pt-10">
          Status:
        </label>
        <div class="col-xs-12 col-md-8 input-group">
          <select class="form-control" ng-model="vm.statusBbc" id="Status" name="StatusBbc">
            <option ng-repeat="option in vm.enumStatusPagamento | orderBy:'descricao'" value="{{option.id}}">
              {{option.descricao}}</option>
          </select>
        </div>
      </div>
    </div>
  </div>


  <div class="row">
    <div class="col-6 col-sm-6 col-md-6 mt-10"></div>
    <div class="col-md-3">
    </div>
    <div class="col-md-3 mt-10">
      <button tooltip-placement="top" uib-tooltip="" type='button' ng-click="vm.atualizaTelaBbc();"
        class="btn btn-labeled btn-primary pull-right">
        <span class="btn-label text-right">
          <!-- <i class="fa fa-refresh"></i> Ícone de reload-->
        </span>
        <span class="pl-5 ">Consultar</span>
      </button>
    </div>
  </div>
</div>

<div class="row"></div>
<br>
<div ui-grid="vm.gridBbcOptions" ng-style="{height: vm.gridBbcOptions.getGridHeight()}" class="grid" style="width: 99%;" ui-grid-pinning ui-grid-save-state
  ui-grid-pagination ui-grid-auto-resize ui-grid-resize-columns ui-grid-grouping>
</div>
<div class="pull-right mt-15">
  <button type="button"
          class="btn btn-primary" ng-click="vm.abrirModalRelatorio(vm, 2)"><i
          class="fa fa-file-pdf-o"></i> Exportar relatório</button>
</div>
<div class="row"></div>
<div id="exportable-xlsBbc">
  <table style="font-family:Arial, Helvetica, sans-serif" ng-show="false" id="exportableBbc"
    class="table table-bordered" width="100%">
    <thead>
    <tr>
      <th style="text-align: left"
          ng-repeat="option in vm.modalBBCRelatorioOptions"
          ng-if="option.enabled && option.field">
        {{option.name}}
      </th>
    </tr>
    </thead>
    <tbody>
    <tr style="text-align: left"
        ng-repeat="item in vm.dadosBBCRelatorio">
      <td ng-repeat="option in vm.modalBBCRelatorioOptions"
          ng-if="option.enabled && option.field">
        {{option.pipe ? (option.pipe(item[option.field])) : (item[option.field])}}
      </td>
    </tr>
    </tbody>
  </table>
</div>