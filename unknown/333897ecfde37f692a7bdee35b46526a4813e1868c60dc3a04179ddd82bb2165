(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('SidebarController', SidebarController);

    SidebarController.$inject = ['$scope', '$rootScope', 'toastr', '$uibModal', 'BaseService', '$window', '$state', 'TITULO_SISTEMA'];

    function SidebarController($scope, $rootScope, toastr, $uibModal, BaseService, $window, $state, TITULO_SISTEMA) {
        var vm = this;
        if (angular.isDefined($rootScope.logoTitle) && $rootScope.logoTitle !== '') {
            vm.TITULO_SISTEMA = $rootScope.logoTitle;
        } else {
            vm.TITULO_SISTEMA = TITULO_SISTEMA;
        }
        vm.loading = true;
        vm.urlFotoUsuario = "assets/images/img-padrao.png";

        $rootScope.senhaAtualizada = false;

        $rootScope.GetMenuUsuarioLogado = function () {
            BaseService.get('Usuario', 'GetMenusUsuario', {
            }).then(function (response) {
                if (!response) return;
                if (!response.data) return;

                var menusIndex = [];
                var menus = [];

                //Coletas menus permitidos para o usuario
                response.data.modulo.forEach(function (modulo) {
                    modulo.menuEstruturaModel.forEach(function (submenu) {
                        submenu.menus.forEach(function (menus) {
                            menusIndex.push(menus.linkNovo);
                        });
                    });
                });

                //Removendo ".index"
                menusIndex.forEach(function (menu) {
                    menus.push(menu.split('.')[0]);
                });

                //Cria um local storage com a lista de menus permitidos
                $window.localStorage.setItem('linksPermitidos', JSON.stringify(menus));

                if (!$rootScope.menusUsuarioLogado || !$rootScope.menusUsuarioLogado.length) {
                    $rootScope.menusUsuarioLogado = response.data.modulo;
                    $rootScope.usuarioLogado = response.data;
                }

                vm.loading = false;

                $rootScope.senhaAtualizada = response.data.senhaExpirada == false;

                if (response.data.senhaExpirada && $rootScope.menucarregar) {
                    $rootScope.menucarregar = false;
                    toastr.warning(response.data.mensagem ? response.data.mensagem : 'Período de utilização de senha expirado, por gentileza crie uma nova senha!');
                    $uibModal.open({
                        animation: true,
                        backdrop: 'static',
                        ariaLabelledBy: 'modal-title',
                        ariaDescribedBy: 'modal-body',
                        templateUrl: 'app/account/atualizacao/atualizacao-dialog.html',
                        controller: function ($uibModalInstance, $uibModalStack, $scope, BaseService) {
                            var vm = this;

                            vm.senha = {
                                novaSenha: null,
                                confirmarNovaSenha: null
                            };

                            vm.validacaoSenhaMensagem = "Informe uma senha com no mínimo 8 caracteres, que possua ao menos uma letra, um número e um caracter especial. Sequências e/ou nome da empresa não são permitidos.";

                            vm.submit = function () {
                                if (vm.senha.novaSenha !== vm.senha.confirmarNovaSenha) {
                                    toastr.error("Os valores para nova senha e a confirmação devem ser os mesmos");
                                    return;
                                }


                                var objValidar = {
                                    SenhaValidacao: vm.senha.novaSenha
                                }

                                if (vm.senha.novaSenha != null) {
                                    BaseService.post('Usuario', 'ValidarSenha', objValidar).then(function (response) {
                                        if (response.success) {
                                            vm.save();
                                        } else {
                                            toastr.error(response.message);
                                            vm.senha = {
                                                novaSenha: null,
                                                confirmarNovaSenha: null
                                            };
                                        }

                                    });
                                } else {
                                    toastr.error(vm.validacaoSenhaMensagem);

                                    vm.senha = {
                                        novaSenha: null,
                                        confirmarNovaSenha: null
                                    };

                                    return;
                                }
                            }

                            vm.save = function () {
                                var objSenha = {
                                    NovaSenha: vm.senha.novaSenha
                                }

                                BaseService.post('Usuario', 'AtualizarSenha', objSenha).then(function (response) {
                                    if (response.success) {
                                        toastr.success(response.message);
                                        var modalAberta = $uibModalStack.getTop();
                                        $uibModalStack.dismiss(modalAberta.key);
                                        $rootScope.senhaAtualizada = true;
                                    } else {
                                        toastr.error(response.message)
                                    }
                                });
                            }

                            vm.fechar = function () {
                                $state.go('logout');
                            }

                        },
                        controllerAs: 'vm',
                        keyboard: false,
                        size: 'lg'
                    }).result.then(function () {
                    });
                }
            });

        };

        vm.getFotoUsuario = function () {
            BaseService.get("Usuario", "GetImageMenuJson").then(function (response) {
                if (!response || !response.success || !response.data) {
                    vm.urlFotoUsuario = "assets/images/img-padrao.png";
                    return
                }
                var img = response.data;
                var slice = 'base64,';
                var index = img.indexOf(slice)
                if (index != -1) {
                    img = img.slice(index + slice.length)
                }

                vm.urlFotoUsuario = 'data:image/png;base64,' + img;
            });
        };

        $rootScope.GetMenuUsuarioLogado();
        vm.getFotoUsuario();
    }
})();