<div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
    <hr-label dark="true" title="'Monitoramento de Servidores'"></hr-label>
    <br><br>
    <div class="row">
        <div class="col-xs-12 col-md-6">
            <div class="form-group">
                <label class="control-label col-xs-12 col-md-4">E-mails - Notificação interna contingência forçada:</label>
                <div class="input-group col-xs-12 col-md-8">
                    <textarea style="resize: none;" value="" type="text" maxlength="1500" rows="7" ng-model="vm.configuracaoMonitorametoCiot.emailsNotificacaoInternaContingenciaForcada" name="NotificacaoInternaContingenciaForçada" class="form-control ng-pristine ng-valid ng-empty ng-touched user-success" ng-keydown="vm.checkForEmailEnd($event, 'forcada')""> </textarea>
                </div>
            </div>
        </div>
        <div class="col-xs-12 col-md-6">
            <div class="form-group">
                <label class="control-label col-xs-12 col-md-4">E-mails - Notificação interna contingência automática:</label>
                <div class="input-group col-xs-12 col-md-8">
                    <textarea style="resize: none;" type="text" maxlength="1500" rows="7" ng-model="vm.configuracaoMonitorametoCiot.emailsNotificacaoInternaContingenciaAutomatica" name="NotificacaoInternaContingenciaAutomárica" class="form-control ng-pristine ng-valid ng-empty ng-touched user-success" ng-keydown="vm.checkForEmailEnd($event, 'automatica')""> </textarea>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-xs-12 col-md-6">
            <div class="form-group">
                <label class="control-label col-xs-12 col-md-4">Tempo checagem de ambiente – contingência forçada (Minutos):</label>
                <div class="input-group col-xs-12 col-md-8">
                    <input type="text" ats-numeric ng-model="vm.configuracaoMonitorametoCiot.tempoChecagemAmbiente" name="TempoChecagemAmbiente" 
                        class="form-control ng-pristine ng-valid ng-empty ng-touched user-success" />
                </div>
            </div>
        </div>
        <div class="col-xs-12 col-md-6">
            <div class="form-group">
                <label class="control-label col-xs-12 col-md-4">Quantidade de erro CIOT – virada contingência forçada:</label>
                <div class="input-group col-xs-12 col-md-8">
                    <input type="text" ats-numeric ng-model="vm.configuracaoMonitorametoCiot.quantidadeErroCiot" name="QuantidadeErroCiot"
                        class="form-control ng-pristine ng-valid ng-empty ng-touched user-success" />
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-xs-12 col-md-6">
            <div class="form-group">
                <label class="control-label col-xs-12 col-md-4">Tempo notificação externa (Minutos):</label>
                <div class="input-group col-xs-12 col-md-8">
                    <input type="text" ats-numeric ng-model="vm.configuracaoMonitorametoCiot.tempoNotificacaoExterna" name="TempoNotificacaoExterna"
                        class="form-control ng-pristine ng-valid ng-empty ng-touched user-success" />
                </div>
            </div>
        </div>

        <div class="col-xs-12 col-md-6">
            <div class="form-group">
                <label class="control-label col-xs-12 col-md-4">Serviço reenvio CIOT em contingência dias retroativos:</label>
                <div class="input-group col-xs-12 col-md-8">
                    <input type="text" ats-numeric ng-model="vm.configuracaoMonitorametoCiot.periodoReenvioCiot" name="PeriodoReenvioCiot"
                        class="form-control ng-pristine ng-valid ng-empty ng-touched user-success" 
                        maxlength="10" onkeypress="return event.charCode >= 48 && event.charCode <= 57" />
                </div>
            </div>
        </div>
    </div>
    <br>
    <hr-label dark="true" title="'Resolução de Frete ANTT'"></hr-label>
    <br><br>
    <div class="row">
        <div class="col-xs-12 col-md-6">
            <div class="form-group">
                <label class="control-label col-xs-12 col-md-4">Obrigar resolução valor frete ANTT:</label>
                <div class="input-group col-xs-12 col-md-8">
                    <toggle-switch on-label="Sim" off-label="Não" ng-model="vm.configuracaoMonitorametoCiot.obrigaValorFrete" 
                        ng-change="vm.ctrlObrigaValorFreteChange()" class="switch"></toggle-switch>
                </div>
            </div>
        </div>
        <div class="col-xs-12 col-md-6" ng-if="vm.configuracaoMonitorametoCiot.obrigaValorFrete">
            <div class="form-group">
                <label class="control-label col-xs-12 col-md-4">
                    <span class="text-danger mr-5">*</span>Data início da obrigação de valor de frete ANTT:
                </label>
                <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-8">
                    <p class="input-group">
                        <input type="text" name="DataObrigaValorFrete"
                            ng-click="vm.datePickerOpen = !vm.datePickerOpen" class="form-control" clear-text="Limpar" close-text="Fechar"
                            uib-datepicker-popup="dd/MM/yyyy" datepicker-append-to-body="true"
                            datepicker-options="vm.optionsDatePicker"
                            ng-model="vm.configuracaoMonitorametoCiot.dataInicioObrigaValorFrete" is-open="vm.datePickerOpen"
                            required-message="'Data início de obrigação do valor de frete é obrigatória!'"
                            ng-required="vm.configuracaoMonitorametoCiot.obrigaValorFrete" />
                        <span class="input-group-btn">
                            <button type="button" class="btn btn-default"
                                ng-click="vm.datePickerOpen = !vm.datePickerOpen">
                                <i class="fa fa-calendar"></i>
                            </button>
                        </span>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

