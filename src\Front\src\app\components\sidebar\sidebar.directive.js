'use strict';

angular.module('bbcWeb').directive('atsSideNavigation', bbcSlideNavigationDirective);

function bbcSlideNavigationDirective($timeout, $rootScope) {
    var template = '<div>\
                        <ul class="nav metismenu" id="side-menu" style="direction:ltr;">\
                            <li ng-if="menusUsuarioLogado.length > 0" ng-class="{ active: $state.current.name === \'index.main\' }"> \
                                <a aria-expanded="true" ui-sref="index.main">\
                                    <i class="fa fa-home"></i>\
                                    <span class="nav-label">Principal</span>\
                                </a>\
                            </li> \
                            <li ng-repeat="modulo in menusUsuarioLogado" style="direction:ltr;">\
                                <a aria-expanded="false">\
                                    <i ng-class="modulo.icone"></i>\
                                    <span class="nav-label">{{modulo.descricao}}</span>\
                                    <span class="fa arrow"></span>\
                                </a>\
                                <ul class="nav nav-second-level collapse ">\
                                    <li ui-sref-active="active" ng-repeat="menu in modulo.menuEstruturaModel">\
                                        <a href="">{{menu.menu}}<span class="fa arrow"></span></a>\
                                        <ul ng-class="{in: isIn(menu.idMenu, menu.menus)}" class="nav nav-third-level collapse">\
                                            <li ui-sref-active="active" ng-repeat="subMenu in menu.menus">\
                                                <a  ng-class="{boldWhite: $state.includes(subMenu.linkNovo)}"  ng-click="goTo(subMenu.linkNovo,$event)">{{subMenu.menu}}</a>\
                                            </li>\
                                        </ul>\
                                    </li>\
                                </ul>\
                            </li>\
                        </ul>\
                    </div>';

    return {
        restrict: 'A',
        template: template,
        controller: function ($scope, $state, $rootScope) {
            $scope.onAtsAntigoMenuClick = function (link, menuDesc) {
                if (link == "abrirGerenciadorVeiculos") {

                    $.ajax({
                        url: URL_WEB_VELHO + "Login/GetDadosAcessoGerenciadorVeiculos",
                        data: {
                            idUsuario: $rootScope.usuarioLogado.idUsuario
                        },
                        dataType: 'json',
                        crossDomain: true,
                        xhrFields: {
                            withCredentials: false
                        },
                        success: function (retorno) {
                            window.parent.parent.postMessage({
                                'func': 'abrirLinkAbaNova',
                                'link': retorno
                            }, "*");
                        }
                    });

                    return;
                }

                if (!$rootScope.usuarioLogado.usuarioEstabPrimeiroLoginSemEstab)
                    window.parent.parent.postMessage({
                        // fim dados login
                        'func': 'trocarSistema',
                        'link': link,
                        'menuDesc': menuDesc,
                        'versao': '0' // 0 = antiga, 1 = nova
                    }, "*");
            };

            $scope.isIn = function (idMenu, menusFilhos) {
                menusFilhos.forEach(function (ss) {
                    if ($state.includes(ss.LinkNovo)) {
                        return true;
                    }
                });
            };

            $scope.goTo = function (link, e) {
                e.preventDefault();
                e.stopPropagation();
                $state.go(link);
            };
        },

        link: function ($scope, element) {
            var wtch = $rootScope.$watch('menusUsuarioLogado.length', function (newValue) {
                if (newValue > 0)
                    $timeout(function () {
                        element.metisMenu();
                    });
            });
        }
    };
}