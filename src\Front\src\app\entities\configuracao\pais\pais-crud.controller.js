(function() {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('PaisCrudController', PaisCrudController);

    PaisCrudController.$inject = ['toastr', '$stateParams', '$state', 'BaseService', '$scope', 'PersistentDataService', '$timeout'];
    function PaisCrudController(toastr, $stateParams, $state, BaseService, $scope, PersistentDataService, $timeout) {
        var vm = this;
        vm.pais = {};
        if ($stateParams.link == 'novo')
            vm.pais.IdPais = 'Auto';

        vm.isNew = function() {
            return $stateParams.link == 'novo';
        }
        vm.headerItems = [{
            name: 'Configuração'
        }, {
            name: 'País',
            link: 'configuracao.pais'
        }, {
            name: $stateParams.link == 'novo' ? 'Novo' : 'Editar'
        }];

        vm.save = function() {
            var modelToSend = {
                IdPais: vm.pais.IdPais || null,
                Nome: vm.pais.Nome,
                Sigla: vm.pais.Sigla,
                Bacen: vm.pais.BACEN
            };

            if (vm.isNew()) modelToSend.IdPais = null;

            BaseService.post('PaisAts', 'CadastrarAtualizar', modelToSend).then(function(response) {
                if (response.success) {
                    toastr.success(response.message);
                    $state.go('configuracao.pais');
                } else
                    toastr.error(response.message);
            });
        }

        function consultarPorId(id, callback) {
            BaseService.get('PaisAts', 'ConsultarPorId', {
                IdPais: id
            }).then(function(response) {
                if (!response.success) {
                    toastr.error(response.message);
                    return;
                }
                if (angular.isFunction(callback)) callback(angular.fromJson(response.data));
            });
        }

        vm.loadEdit = function(id) {
            consultarPorId(id, function(ret) {
                vm.pais = ret;
            });
        }
        // DO NOT TOUCH!! 666
        $scope.$on('$stateChangeStart', function(_, toState) {
            if (toState.name === 'configuracao.pais')
                PersistentDataService.remove('PaisCrudController');
            else
                PersistentDataService.store('PaisCrudController', vm, "Cadastro de países", null, "configuracao.pais-crud", vm.pais.IdPais);
        });
        var selfScope = PersistentDataService.get('PaisCrudController');
        if (angular.isDefined(selfScope)) angular.extend(vm, selfScope.data);
        else {
            if (!vm.isNew())
                vm.loadEdit($stateParams.link);
        }
        $timeout(function() {
            PersistentDataService.remove('PaisController');
        }, 15);
    }
})();