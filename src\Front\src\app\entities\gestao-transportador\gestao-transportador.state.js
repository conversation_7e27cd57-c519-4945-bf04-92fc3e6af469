(function () {
    'use strict';

    angular.module('bbcWeb.gestao-transportador.state', []).config(routeConfig);

    function routeConfig($urlRouterProvider, $stateProvider) {
        $stateProvider.state('gestao-transportador', {
            abstract: true,
            url: "/gestao-transportador",
            templateUrl: "app/layout/content.html"
        }).state('gestao-transportador.index', {
            url: '/index',
            templateUrl: 'app/entities/gestao-transportador/gestao-transportador.html'
        }).state('gestao-transportador.gestao-transportador-crud', {
            url: '/:link',
            templateUrl: 'app/entities/gestao-transportador/gestao-transportador-crud.html'
        });
    }
})();