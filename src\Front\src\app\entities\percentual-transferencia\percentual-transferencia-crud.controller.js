(function () {
    'use strict';

    angular.module('bbcWeb').controller('PercentualTransferenciaCrudController', PercentualTransferenciaCrudController);

    PercentualTransferenciaCrudController.$inject = [
        '$rootScope',
        '$state',
        '$stateParams',
        '$window',
        '$uibModal',
        '$scope',
        '$timeout',
        'toastr',
        'BaseService',
        'PersistentDataService',
        'DefaultsService',
        'PERFIL_ADMINISTRADOR'
    ];

    function PercentualTransferenciaCrudController(
        $rootScope,
        $state,
        $stateParams,
        $window,
        $uibModal,
        $scope,
        $timeout,
        toastr,
        BaseService,
        PersistentDataService,
        PERFIL_ADMINISTRADOR
    ) {
        var vm = this;
        vm.headerItems = [
            {
                name: 'Cadastros'
            },
            {
                name: '% Transferência Automática',
                link: 'percentual-transferencia.index'
            },
            {
                name: $stateParams.link === 'novo' ? 'Novo' : 'Editar'
            }
        ];
        // cuidado ao mexer o get do PersistentDataService de lugar 
        // note que ele sobrescreve essas declaracoes iniciais ao pega-las do storage
        var selfScope = PersistentDataService.get('PercentualTransferenciaCrudController');

        function init() {
        }

        function carregarFuncoesIniciais() {
            pesquisarMotoristasCombo();
            vm.defineGridOptions(1);
        }

        function pesquisarMotoristasCombo() {
            vm.consultaPortador = {
                columnDefs: [{
                    name: 'Código',
                    field: 'id',
                    width: 80,
                    type: 'number'
                }, {
                    name: 'Nome',
                    field: 'nome',
                    width: '*',
                    minWidth: 150
                }, {
                    name: 'CPF',
                    field: 'cpfCnpj',
                    width: '*',
                    minWidth: 150
                }],
                desiredValue: 'id',
                desiredText: 'nome',
                url: 'PercentualTransferencia/ConsultarGridMotoristasCombo',
                paramsMethod: function () {
                    return {
                        proprietarioId: vm.percentualTransferencia.proprietarioId
                    }
                },
            };
        }

        //caso seja necessario usar multiplas grids em varias abas
        //a partir desse mesmo controller
        vm.defineGridOptions = function (wizardTabIndex) {
            //impede grid sendo limpada ao clicar na tab que ja esta ativa
            if (vm.gridOptionsActive === wizardTabIndex) return;
            //muda gridOptions conforme a tab selecinada
            switch (wizardTabIndex) {
                case 1: {
                    //tab 1 - aba principal - grid de motoristas 
                    vm.gridOptions = {
                        data: [],
                        onRegisterApi: BaseService.dataGrid.defaultOnRegisterApi,
                        dataSource: {
                            url: "PercentualTransferencia/ConsultarGridMotoristasProprietario",
                            params: function () {
                                return {
                                    proprietarioId: vm.percentualTransferencia.proprietarioId,
                                };
                            }
                        },
                        columnDefs: [{
                            name: 'Ações',
                            width: 80,
                            cellTemplate: '<div ng-if="!row.groupHeader" class="ui-grid-cell-contents" align="center" title="TOOLTIP">\
                                    <div class="container-btn-action">\
                                        <button tooltip-placement="right" uib-tooltip="Editar" type="button" \
                                                ng-disabled="grid.appScope.vm.isSavingMotorista || grid.appScope.vm.percentualTransferencia.paraTodosMotoristas"\
                                                ng-click="grid.appScope.vm.editarMotorista(row.entity, grid.appScope.vm.percentualTransferencia.id)" \
                                                ng-class="{ \'btn btn-xs btn-info\': true }">\
                                            <i class="fa fa-edit"></i>\
                                        </button>\
                                    </div>\
                                    <div class="container-btn-action">\
                                        <button tooltip-placement="right" uib-tooltip="Excluir" type="button" \
                                                ng-click="grid.appScope.vm.desabilitarMotorista(row.entity.id)" \
                                                ng-disabled="grid.appScope.vm.isSavingMotorista || grid.appScope.vm.percentualTransferencia.paraTodosMotoristas"\
                                                ng-class="{ \'btn btn-xs btn-danger\': true }">\
                                            <i ng-class="\'fa fa-trash-o\'"></i>\
                                        </button>\
                                    </div>\
                                </div>'
                        }, {
                            name: 'Código',
                            width: 80,
                            type: 'number',
                            primaryKey: true,
                            field: 'portadorId'
                        }, {
                            name: 'PortadorNome',
                            displayName: 'Motorista',
                            field: 'portadorNome',
                            width: '220',
                            minWidth: 180,
                            enableFiltering: true
                        }, {
                            name: 'PortadorCpf',
                            displayName: 'CPF',
                            field: 'portadorCpf',
                            width: '*',
                            minWidth: 180,
                            enableFiltering: true
                        }, {
                            name: 'Adiantamento',
                            displayName: '% Adiantamento',
                            field: 'adiantamento',
                            width: '*',
                            minWidth: 180,
                            enableFiltering: true
                        }, {
                            name: 'Saldo',
                            displayName: '% Saldo',
                            field: 'saldo',
                            width: '*',
                            minWidth: 180,
                            enableFiltering: true
                        }]
                    };
                    break;
                }
                case 2: {
                    //tab 2 - aba historico por motoristas - grid de portadorhistorico
                    vm.gridOptions = {
                        data: [],
                        onRegisterApi: BaseService.dataGrid.defaultOnRegisterApi,
                        dataSource: {
                            url: "PercentualTransferencia/ConsultarGridMotoristasComHistoricos",
                            params: function () {
                                return {
                                    proprietarioId: $stateParams.link
                                };
                            }
                        },
                        columnDefs: [{
                            name: 'Ações',
                            width: 80,
                            cellTemplate: '<div ng-if="!row.groupHeader" class="ui-grid-cell-contents" align="center" title="TOOLTIP">\
                                    <div class="container-btn-action">\
                                        <button tooltip-placement="right" uib-tooltip="Visualizar alterações" type="button" \
                                                ng-click="grid.appScope.vm.visualizarHistorico(row.entity.id, row.entity.portadorNome)" \
                                                ng-class="{ \'btn btn-xs btn-info\': true }">\
                                            <i ng-class="\'fa fa-eye\'"></i>\
                                        </button>\
                                    </div>\
                               </div>'
                        }, {
                            name: 'Código',
                            width: 80,
                            type: 'number',
                            field: 'portadorId',
                            enableFiltering: true
                        }, {
                            name: 'PortadorNome',
                            displayName: 'Motorista',
                            field: 'portadorNome',
                            width: '220',
                            minWidth: 180,
                            enableFiltering: true
                        }, {
                            name: 'PortadorCpf',
                            displayName: 'CPF/CNPJ',
                            field: 'portadorCpf',
                            width: '*',
                            minWidth: 180,
                            enableFiltering: true
                        }, {
                            name: 'DataCadastro',
                            displayName: 'Data de Cadastro',
                            field: 'dataCadastro',
                            width: '*',
                            minWidth: 180,
                            enableFiltering: false
                        }, {
                            name: 'DataAlteracao',
                            displayName: 'Última Alteração',
                            field: 'dataAlteracao',
                            width: '*',
                            minWidth: 180,
                            enableFiltering: false
                        }, {
                            name: 'UsuarioCadastroNome',
                            displayName: 'Usuario Cadastro',
                            field: 'usuarioCadastroNome',
                            width: '*',
                            minWidth: 180,
                            enableFiltering: true
                        }, {
                            name: 'UsuarioCadastroCpf',
                            displayName: 'CPF Usuario Cadastro',
                            field: 'usuarioCadastroCpf',
                            width: '*',
                            minWidth: 180,
                            enableFiltering: true
                        }]
                    };
                    break;
                }
                case 3: {
                    //tab 3 - aba historico do cadastro - grid de percentualtransferenciahistorico                    
                    vm.gridOptions = {
                        data: [],
                        onRegisterApi: BaseService.dataGrid.defaultOnRegisterApi,
                        dataSource: {
                            url: "PercentualTransferencia/ConsultarGridHistoricosDoPercentual",
                            params: function () {
                                return {
                                    proprietarioId: $stateParams.link
                                };
                            }
                        },
                        columnDefs: [
                            {
                                name: 'DataAlteracao',
                                displayName: 'Data de Alteração',
                                field: 'dataAlteracao',
                                width: '*',
                                minWidth: 180,
                                enableFiltering: false
                            }, {
                                name: 'Saldo',
                                displayName: 'Saldo',
                                field: 'saldo',
                                width: '*',
                                minWidth: 140,
                                enableFiltering: true
                            }, {
                                name: 'Adiantamento',
                                displayName: 'Adiantamento',
                                field: 'adiantamento',
                                width: '*',
                                minWidth: 140,
                                enableFiltering: true
                            }, {
                                name: 'ParaTodosMotoristas',
                                displayName: 'Para Todos os Motoristas',
                                field: 'paraTodosMotoristas',
                                width: '*',
                                minWidth: 180,
                                enableFiltering: true
                            }, {
                                name: 'UsuarioAlteracaoNome',
                                displayName: 'Usuário Alteração',
                                field: 'usuarioAlteracaoNome',
                                width: '*',
                                minWidth: 180,
                                enableFiltering: true
                            }, {
                                name: 'usuarioAlteracaoCpf',
                                displayName: 'CPF Usuário Alteração',
                                field: 'usuarioAlteracaoCpf',
                                width: '*',
                                minWidth: 180,
                                enableFiltering: true
                            }]
                    };
                    break;
                }
                default: {
                    //nao fazer nada com qualquer outra tab sem uma grid
                    return;
                }
            }
            //pra usar no ng-if das grids (precaução)
            vm.gridOptionsActive = wizardTabIndex;
        }

        vm.editarMotorista = function (motorista, percentualTransferenciaId) {
            $uibModal.open({
                animation: true,
                ariaLabelledBy: 'modal-title',
                ariaDescribedBy: 'modal-body',
                templateUrl: 'app/entities/percentual-transferencia/modal-editar-motorista/modal-editar-motorista.html',
                controller: function ($uibModalInstance, $uibModalStack) {
                    var vm = this;
                    vm.headerItems = [{name: 'Editar Motorista'}];
                    vm.isSaving = false;

                    vm.motorista = motorista;

                    vm.fechar = function () {
                        $uibModalStack.dismissAll();
                    }

                    vm.salvarPercentualTransferenciaPortador = function (form) {
                        if (!form.$valid) {
                            toastr.error('Todos os campos obrigatórios devem ser preenchidos corretamente com seus respectivos valores');
                            return;
                        }

                        if (vm.isSaving === true) return;

                        vm.isSaving = true;

                        vm.motorista.percentualTransferenciaId = percentualTransferenciaId;

                        BaseService.post('PercentualTransferencia', 'SalvarPercentualTransferenciaPortador', motorista)
                            .then(function (response) {
                                vm.isSaving = false;
                                if (response.success) {
                                    toastr.success(response.message);
                                    $uibModalStack.dismissAll();
                                } else {
                                    toastr.error(response.message);
                                }
                            });
                    }
                },
                controllerAs: 'vm',
                keyboard: true,
                size: 'lg'
            }).result.then(function () {
                vm.gridOptions.dataSource.refresh();
            });
        };

        vm.desabilitarMotorista = function (percentualTransferenciaPortadorId) {
            vm.isSavingMotorista = true;
            BaseService.post('PercentualTransferencia', 'AlterarStatusPercentualTransferenciaPortador', {
                id: percentualTransferenciaPortadorId
            }).then(function (response) {
                vm.isSavingMotorista = false;
                if (response.success) {
                    toastr.success(response.message);
                    vm.gridOptions.dataSource.refresh();
                } else
                    toastr.error(response.message);
            });
        }

        vm.visualizarHistorico = function (percentualTransferenciaPortadorId, motoristaNome) {
            $uibModal.open({
                animation: true,
                ariaLabelledBy: 'modal-title',
                ariaDescribedBy: 'modal-body',
                templateUrl: 'app/entities/percentual-transferencia/modal-visualizar-historico/modal-visualizar-historico.html',
                controller: function ($uibModalInstance, $uibModalStack) {
                    var vm = this;
                    vm.headerItems = [{name: 'Histórico de Alterações de Motorista'}];
                    vm.isSaving = false;

                    vm.fechar = function () {
                        $uibModalStack.dismissAll();
                    }

                    vm.motoristaNome = motoristaNome;

                    vm.gridOptions = {
                        data: [],
                        onRegisterApi: BaseService.dataGrid.defaultOnRegisterApi,
                        dataSource: {
                            url: "PercentualTransferencia/ConsultarGridHistoricoPorMotorista",
                            params: function () {
                                return {
                                    percentualTransferenciaPortadorId: percentualTransferenciaPortadorId,
                                };
                            }
                        },
                        columnDefs: [{
                            name: 'DataAlteracao',
                            displayName: 'Data da Alteração',
                            field: 'dataAlteracao',
                            width: '*',
                            minWidth: 180,
                            enableFiltering: true
                        }, {
                            name: 'Adiantamento',
                            displayName: 'Adiantamento',
                            field: 'adiantamento',
                            width: '*',
                            minWidth: 120,
                            enableFiltering: true
                        }, {
                            name: 'Saldo',
                            displayName: 'Saldo',
                            field: 'saldo',
                            width: '*',
                            minWidth: 120,
                            enableFiltering: true
                        }, {
                            name: 'Ativo',
                            displayName: 'Status',
                            field: 'ativo',
                            width: '*',
                            minWidth: 120,
                            enableFiltering: true
                        }, {
                            name: 'UsuarioAlteracaoNome',
                            displayName: 'Usuario Alteração',
                            field: 'usuarioAlteracaoNome',
                            width: '*',
                            minWidth: 180,
                            enableFiltering: true
                        }, {
                            name: 'UsuarioAlteracaoCpf',
                            displayName: 'CPF Usuário Alteração',
                            field: 'usuarioAlteracaoCpf',
                            width: '*',
                            minWidth: 200,
                            enableFiltering: true
                        }]
                    };

                },
                controllerAs: 'vm',
                keyboard: true,
                size: 'lg'
            }).result.then(function () {

            });
        };

        vm.adicionarMotorista = function () {
            vm.comboMotoristasDisabled = false;

            if (!vm.consultaPortador) return;

            if (!vm.consultaPortador.selectedValue) return;

            vm.comboMotoristasDisabled = true;

            BaseService.post('PercentualTransferencia', 'AdicionarMotorista', {
                proprietarioId: vm.percentualTransferencia.proprietarioId,
                motoristaId: vm.consultaPortador.selectedValue,
                adiantamento: vm.percentualTransferencia.adiantamento,
                saldo: vm.percentualTransferencia.saldo,
            }).then(function (response) {
                if (!response.success) {
                    toastr.error(response.message)
                } else {
                    toastr.success(response.message)
                    vm.gridOptions.dataSource.refresh();
                }
                vm.consultaPortador.selectedValue = '';
                vm.consultaPortador.selectedText = '';
                vm.comboMotoristasDisabled = false;
            });

        };

        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.administrador === true;
        }

        vm.save = function (form) {
            if (!form.$valid) {
                toastr.error('Todos os campos obrigatórios devem ser preenchidos corretamente com seus respectivos valores');
                return;
            }

            if (vm.isSaving === true) return;

            if (vm.percentualTransferencia.id === "Auto") vm.percentualTransferencia.id = 0;
            vm.percentualTransferencia.paraTodosMotoristas = vm.percentualTransferencia.paraTodosMotoristas ? 1 : 0;

            vm.isSaving = true;
            BaseService.post('PercentualTransferencia', 'Salvar', vm.percentualTransferencia)
                .then(function (response) {
                    vm.isSaving = false;
                    if (response.success) {
                        toastr.success(response.message);
                    } else
                        toastr.error(response.message);
                });
        }

        vm.loadEdit = function (id) {
            vm.percentualTransferenciaLoading = true;
            BaseService.get('PercentualTransferencia', 'ConsultarPorId', {
                proprietarioId: id
            }).then(function (response) {
                if (!response.success) {
                    toastr.error(response.message);
                } else {
                    vm.percentualTransferencia = response.data;
                    vm.percentualTransferencia.proprietarioNome = response.data.proprietarioNome;
                    vm.percentualTransferencia.proprietarioCpf = response.data.proprietarioCpf;
                    vm.percentualTransferenciaLoading = false;
                    carregarFuncoesIniciais();
                }
            });
        };

        vm.onClickAvancar = function (wizard) {
            const ativoIndex = wizard.getActivePosition();

            vm.defineGridOptions(ativoIndex + 1);
            wizard.go(ativoIndex + 1);
        }

        vm.onClickVoltar = function (wizard) {
            const ativoIndex = wizard.getActivePosition();

            if (ativoIndex === 1) $state.go('percentual-transferencia.index');

            vm.defineGridOptions(ativoIndex - 1);
            wizard.go(ativoIndex - 1);
        }

        init();

        $scope.$on('$stateChangeStart', function (arg1, toState) {
            if (toState.name === 'percentual-transferencia.index') {
                PersistentDataService.remove('PercentualTransferenciaCrudController');
            } else {
                //if pra nao dar erro no console qnd ele desconecta por sessao expirada
                if (vm.percentualTransferencia.proprietarioId) {
                    PersistentDataService.store('PercentualTransferenciaCrudController',
                        vm,
                        "Cadastro - Percentual de transferência automática",
                        null,
                        "percentual-transferencia.percentual-transferencia-crud",
                        vm.percentualTransferencia.proprietarioId);
                }
            }
        });

        if (angular.isDefined(selfScope)) {
            angular.extend(vm, selfScope.data);
            // chamar defineGridOptions após o get do PersistentDataService
            // pra ter certeza que ele não vai sobrescrever
            // e deixar as gridOptions como a de outra aba
            // ja que ele redireciona sempre pra aba 1
            vm.defineGridOptions(1);
        } else {
            vm.loadEdit($stateParams.link);
        }

        $timeout(function () {
            PersistentDataService.remove('PercentualTransferenciaCrudController');
        }, 15);
    }
})();