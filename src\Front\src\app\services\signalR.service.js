(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .service('SignalRService', SignalRService);

    SignalRService.$inject = ['$rootScope', 'URL_SERVER_DEV', '$window', '$log'];

    function SignalRService($rootScope, URL_SERVER_DEV, $window, $log) {
        var service = {
            registed: false,
            proxy: null,
            connection: null,
            connect: function (hubName, onConnected) {
                // var urlSignalR = URL_SERVER_DEV + "signalR";
                // service.connection = $.hubConnection(urlSignalR, { useDefaultPath: false });
                // //service.proxy = signalRConnection.createHubProxy(hubName);
                // service.connection.start({ withCredentials: false }).done(function () {

                // }).fail(function () {

                // })
            },
            disconnect: function () {
                service.proxy.connection.hub.stop();
            },
            registerHub: function (hubName, listeners, senders) {
                // var hub = service.connection.createHubProxy(hubName);
                // if (angular.isArray(listeners))
                //     listeners.forEach(function (listener) {
                //         hub.on(listener.action, function(){
                //         });
                //     });
                // if (angular.isArray(senders))
                //     senders.forEach(function (sender) {
                //         service[sender.action] = function (params, callback) {
                //             hub.invoke(sender.action, params);
                //         };
                //     })
            },
            onHubConnected: function () { },
            //onError
            onError: function () {

            },
            onStateChanged: function () {

            }
        }

        return service;
    }

})();

/*
var service = {
            //Função responsável por disponibilizar o hub de comunicação entre o cliente e o servidor.
            getHub: function (hubProperties, successCallback) {
                return new Hub(hubProperties.hubName, {
                    autoConnect: true,
                    useSharedConnection: false,
                    listeners: hubProperties.listeners,
                    methods: hubProperties.methods,
                    jsonp: true,
                    //handle connection error
                    errorHandler: function (error) {
                        $log.error(error);
                    },
                    //specify a non default root
                    rootPath: URL_SERVER_DEV + 'signalr',
                    stateChanged: function (state) {
                        if (angular.isUndefined($window.localStorage.getItem('SessionKey')) ||
                            $window.localStorage.getItem('SessionKey') === null ||
                            $window.localStorage.getItem('SessionKey') === 'invalid-key-value')
                            return;

                        if (state.newState === $.signalR.connectionState.connected) {
                            $log.log("conectou com sucesso!");
                            if (angular.isFunction(successCallback))
                                successCallback();
                        }
                    }
                });
            }
        };


*/