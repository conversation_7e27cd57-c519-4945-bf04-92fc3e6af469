(function () {
    'use strict';

    angular.module('bbcWeb.grupo-empresa.state', []).config(routeConfig);

    function routeConfig($urlRouterProvider, $stateProvider) {
        $stateProvider.state('grupo-empresa', {
            abstract: true,
            url: "/grupo-empresa",
            templateUrl: "app/layout/content.html"
        }).state('grupo-empresa.index', {
            url: '/index',
            templateUrl: 'app/entities/grupo-empresa/grupo-empresa.html'
        }).state('grupo-empresa.grupo-empresa-crud', {
            url: '/:link',
            templateUrl: 'app/entities/grupo-empresa/grupo-empresa-crud.html'
        });
    }
})();