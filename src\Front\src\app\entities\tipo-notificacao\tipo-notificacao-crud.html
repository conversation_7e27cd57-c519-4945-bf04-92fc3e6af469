<div ng-controller="TipoNotificacaoCrudController as vm">
    <form-header items="vm.headerItems" head="'Tipo de Notificação'"></form-header>

    <div class="wrapper-content animated overflow-hidden fadeIn mb-0 pb-0">
        <div class="row">
            <div class="col-lg-12">
                <div class="ibox">
                    <div class="ibox-title">
                        <h5>{{vm.isNew() ? 'Novo' : 'Editar'}} tipo de notificação</h5>
                        <div ibox-tools></div>
                    </div>
                    <div class="ibox-content wizard">
                        <form name="formTipoNotificacao" role="form" novalidate ng-submit="vm.save()" show-validation>
                            <div form-wizard steps="2">
                                <div class="form-wizard">
                                    <ol class="row">
                                        <li ng-class="{'active':wizard.active(1)}" ng-click="wizard.go(1)" class="col-sm-12 col-md-12 col-lg-12">
                                            <h4>Principal</h4>
                                        </li>
                                    </ol>
                                    <div ng-show="wizard.active(1)">
                                        <div class="form-horizontal">
                                            <hr/>
                                            <div class="col-sm-12">
                                                <div class="row">
                                                    <div class="col-sm-12 col-md-6 col-lg-6">
                                                        <div class="form-group">
                                                            <label class="col-sm-3 col-md-4 col-lg-3 control-label">Código:</label>
                                                            <div class="input-group col-sm-2 col-md-2 col-lg-2">
                                                                <input type="text" id="IdTipoNotificacao" name="IdTipoNotificacao" ng-model="vm.tipoNotificacao.IdTipoNotificacao" class="form-control" disabled value="{{vm.isNew()}}" />
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div id="blockEmpresa" class="col-sm-12 col-md-6 col-lg-6" ng-if="vm.isVisibled('blockEmpresa')">
                                                        <div class="form-group">
                                                            <label class="col-sm-3 col-md-4 col-lg-3 control-label"><span class="text-danger mr-5">*</span>Empresa:</label>
                                                            <div class="input-group col-sm-9 col-md-8 col-lg-9">
                                                                <ui-select id="empresa" name="empresa" ng-model="vm.empresaSelected" ng-change="vm.updateFiliais()" required>
                                                                    <ui-select-match placeholder="Selecione a empresa">
                                                                        <span>{{$select.selected.RazaoSocial}}</span>
                                                                        <div id="clearEmpresa" class="fa fa-close m-l-n ui-select-vas" ng-click="vm.clear($event)"></div>
                                                                    </ui-select-match>
                                                                    <ui-select-choices repeat="empresa.IdEmpresa as empresa in vm.empresas | propsFilter: {RazaoSocial: $select.search, CNPJ: $select.search}">
                                                                        <div ng-bind-html="empresa.RazaoSocial | highlight: $select.search"></div>
                                                                    </ui-select-choices>
                                                                </ui-select>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div id="blockFilial" class="col-sm-12 col-md-6 col-lg-6" ng-if="vm.isVisibled('blockFilial')">
                                                        <div class="form-group">
                                                            <label class="col-sm-3 col-md-4 col-lg-3 control-label">Filial:</label>
                                                            <div class="input-group col-sm-9 col-md-8 col-lg-9">
                                                                <ui-select id="filial" name="filial" ng-model="vm.filialSelected">
                                                                    <ui-select-match placeholder="Selecione a filial">
                                                                        <span>{{$select.selected.NomeFantasia}}</span>
                                                                        <div id="clearFilial" class="fa fa-close m-l-n ui-select-vas" ng-click="vm.clear($event)"></div>
                                                                    </ui-select-match>
                                                                    <ui-select-choices repeat="filial.IdFilial as filial in vm.filiais | propsFilter: {NomeFantasia: $select.search}">
                                                                        <div ng-bind-html="filial.NomeFantasia | highlight: $select.search"></div>
                                                                    </ui-select-choices>
                                                                </ui-select>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-sm-12 col-md-6 col-lg-6">
                                                        <div class="form-group">
                                                            <label class="col-sm-3 col-md-4 col-lg-3 control-label"><span class="text-danger mr-5">*</span>Descrição:</label>
                                                            <div class="input-group col-sm-9 col-md-8 col-lg-9">
                                                                <input type="text" id="Descricao" maxlength="100" name="Descricao" ng-model="vm.tipoNotificacao.Descricao" class="form-control" required/>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row"></div>
                                            <hr/>
                                            <div class="form-group">
                                                <div class="col-md-12 col-lg-12 text-right">
                                                    <button type="button" ui-sref="gestao-logistica-tipo-notificacao.tiponotificacao" class="btn btn-labeled btn-default">
                                                      <span class="btn-label"><i class="fa fa-arrow-circle-left"></i></span>
                                                      Voltar
                                                    </button>
                                                    <button type="submit" ng-disabled="formTipoNotificacao.$invalid" class="btn btn-labeled btn-success text-right">
                                                        <span class="btn-label"><i class="fa fa-check-circle"></i></span>
                                                        Salvar
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                        </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>