<div class="form-horizontal">
    <hr />
    <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label">Código:</label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <input type="text" ng-model="vm.mdrPrazos.id" class="form-control" disabled
                            value="{{vm.isNew()}}" />
                    </div>
                </div>
            </div>
            <consulta-padrao-modal tabledefinition="vm.consultaBanco" idname="bancoconsulta" label="'Banco:'" 
                placeholder="'Selecione um banco'" ng-required="true" 
                ng-disabled="!vm.isNew()"
                required-message="'Banco é obrigatório'"
                directivesizes="'col-xs-12 col-sm-12 col-md-6 col-lg-6'"
                labelsize="'col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label'">
            </consulta-padrao-modal>
        </div>
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-5 col-lg-3 control-label">
                        <span class="text-danger mr-5">*</span>MDR:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-7 col-lg-9">
                        <input type="number" required ng-model="vm.mdrPrazos.mdr" 
                            pattern="[0-9]+([\.,][0-9]+)?" 
                            step="0.05"
                            min= 0
                            title="Permitido números com 2 casas decimais."
                            required-message="'MDR deve ser informado'"
                            maxlength="200" name="mdr" class="form-control" />
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-5 col-lg-3 control-label">
                        <span class="text-danger mr-5">*</span>Prazo:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-7 col-lg-9">
                        <input type="number" required ng-model="vm.mdrPrazos.prazo"
                            required-message="'Prazo deve ser informado'" min= 0
                            maxlength="200" name="prazo" class="form-control" />
                    </div>
                </div>
            </div>
        </div>            
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-5 col-lg-3 control-label">
                        Descrição:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-7 col-lg-9">
                        <input type="text" ng-model="vm.mdrPrazos.descricao"
                            maxlength="200" name="descricao" class="form-control" />
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>