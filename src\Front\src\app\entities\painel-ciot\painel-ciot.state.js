(function () {
    'use strict';

    angular.module('bbcWeb.painelCiot.state', []).config(routeConfig);

    function routeConfig($urlRouterProvider, $stateProvider) {
        $stateProvider.state('painel-ciot', {
            abstract: true,
            url: "/painel-ciot",
            templateUrl: "app/layout/content.html"
        }).state('painel-ciot.index', {
            url: '/index',
            templateUrl: 'app/entities/painel-ciot/painel-ciot.html'
        }).state('painel-ciot.painel-ciot-crud', {
            url: '/:link/:status/:visualizar/:ciot',
            templateUrl: 'app/entities/painel-ciot/painel-ciot-crud.html'
        });
    }
})();