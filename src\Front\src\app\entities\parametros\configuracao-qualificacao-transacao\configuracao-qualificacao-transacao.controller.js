(function () {
    'use strict';

    angular.module('bbcWeb').controller('ConfiguracaoQualificacaoTransacaoController', ConfiguracaoQualificacaoTransacaoController);

    ConfiguracaoQualificacaoTransacaoController.$inject = ['toastr', '$rootScope', 'BaseService', '$state', '$stateParams', '$scope', '$timeout', 'PersistentDataService'];

    function ConfiguracaoQualificacaoTransacaoController(
        toastr,
        $rootScope,
        BaseService,
        $state,
        $stateParams,
        $scope,
        $timeout,
        PersistentDataService
    ) {
        var vm = this;
        vm.configuracaoQualificacaoTransacao = {};

        vm.headerItems = [{
            name: 'Cadastros'
        }, {
            name: 'Parâmetros gerais',
            link: 'parametros.index'
        }];

        vm.loadEdit = function () {
            BaseService.get('Parametros', 'ConsultaParametrosConfiguracaoQualificacaoTransacao', {})
                .then(function (response) {
                    if (!response) {
                        toastr.error('Erro de comunicação com o servidor, tente novamente em alguns minutos.');
                        return;
                    }

                    if (!response.success) {
                        toastr.error(response.message);
                        return;
                    }

                    vm.configuracaoQualificacaoTransacao = response.data;
                     
                });
        };


        vm.save = function (form) {
            if (!form.$valid) {
                toastr.error('Todos os campos obrigatórios devem ser preenchidos corretamente com seus respectivos valores.');
                return;
            }

            if (vm.saving) return;

            var request = {}
            request.linkApiQualificacaoFrete = vm.configuracaoQualificacaoTransacao.linkApiQualificacaoFrete;
            request.autorizacaoLinkApiQualificacaoFrete = vm.configuracaoQualificacaoTransacao.autorizacaoLinkApiQualificacaoFrete;

            vm.saving = true;

            BaseService.post('Parametros', 'SalvarConfiguracaoQualificacaoTransacao', request)
                .then(function (response) {
                    vm.saving = false;
                    if (!response) {
                        toastr.error('Erro de comunicação com o servidor, tente novamente em alguns minutos.');
                        return;
                    }
                    if (!response.success) {
                        toastr.error(response.message);
                        return;
                    }
                    toastr.success(response.message);
                    $state.go('parametros.index');
                })
        };


        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.administrador == true;
        };

        function init() {
            vm.loadEdit();
        };

        vm.onClickVoltar = function (wizard) {

            var ativoIndex = wizard.getActivePosition();
            if (ativoIndex == 1)
                $state.go('parametros.index');

            wizard.go(ativoIndex - 1);
        };

        init();

    
    }
})();
