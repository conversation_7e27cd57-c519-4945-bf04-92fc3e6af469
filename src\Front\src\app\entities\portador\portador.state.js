(function (){
    'use strict';

    angular.module('bbcWeb.portador.state', []).config(routeConfig);

    function routeConfig($urlRouterProvider, $stateProvider) {
        $stateProvider.state('portador', {
            abstract: true,
            url: "/portador",
            templateUrl: "app/layout/content.html"
        }).state('portador.index', {
            url: '/index',
            templateUrl: 'app/entities/portador/portador.html'
        }).state('portador.portador-crud', {
            url: '/:link',
            templateUrl: 'app/entities/portador/portador-crud.html'
        });
    }
})();