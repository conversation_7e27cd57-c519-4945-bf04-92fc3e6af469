(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('EmprestimoController', EmprestimoController);

    EmprestimoController.inject = [
        '$timeout',
        '$scope',
        'BaseService',
        'toastr',
        'PersistentDataService',
        'URL_SERVER_DEV'
    ];

    function EmprestimoController(
        $timeout,
        $scope,
        BaseService,
        toastr,
        PersistentDataService,
        URL_SERVER_DEV) {

        var vm = this;
        vm.emprestimosRelatorio = [];
        vm.desabilitarBtnRelatorio = false;

        vm.headerItems = [{
            name: 'Movimentações'
        }, {
            name: 'Empréstimo'
        }];

        vm.consultarDadosRelatorio = function (extensao) {
            if (extensao === 1)
                exportarEmExcel();

            if (extensao === 2)
                exportarEmPdf();
        };

        vm.clickGerarRelatorio = function (entity) {
            var params = { json: entity.id };
            BaseService.post('Emprestimo', 'GerarRelatorio', params).then(function(response) {
                if (response.success) {
                    var link = document.createElement("a");
                    link.href = "data:application/pdf;base64," + response.data;
                    link.download = "emprestimo.pdf";
                    document.body.appendChild(link);
                    
                    link.click();
                    
                    document.body.removeChild(link);
                } else {
                    toastr.error(response.message)
                }
            });
        }

        function exportarEmExcel() {
            vm.desabilitarBtnRelatorio = true;
            vm.gridOptions.dataSource.consultarDadosRelatorio(function (response) {
                vm.emprestimosRelatorio = response.data.data;
                $timeout(function () { BaseService.exportarTabelaEmExcel("exportable-xls", "Empréstimos") }, 500);
                vm.desabilitarBtnRelatorio = false;
            });
        }

        function exportarEmPdf() {
            vm.desabilitarBtnRelatorio = true;
            vm.gridOptions.dataSource.consultarDadosRelatorio(function (response) {
                vm.emprestimosRelatorio = response.data.data;
                $timeout(function () { BaseService.exportarTabelaEmPdf("#exportable", "Empréstimos", "Empréstimos"); }, 500);
                vm.desabilitarBtnRelatorio = false;
            });
        }

        vm.gridOptions = {
            data: [],
            onRegisterApi: BaseService.dataGrid.defaultOnRegisterApi,
            urlRelatorio: "Emprestimo/DadosRelatorioGridPagamentos",
            parametrosExtras: {
                //IdEmpresa: $rootScope.usuarioLogado.idEmpresa
            },
            dataSource: {
                url: "Emprestimo/Consultar"
            },
            columnDefs: [
                {
                    name: 'Ações', 
                    width: 80, 
                    enableColumnMenu: false, 
                    cellTemplate: '<div ng-if="!row.groupHeader" class="ui-grid-cell-contents" title="TOOLTIP">\
                        <button ng-disabled="row.entity.ativo===0" title="Editar" type="button" ui-sref="emprestimo.crud({link: row.entity.id})"\ ng-class="{ \'btn btn-xs btn-info btn-grid-size-default\': true }">\
                            <i class="fa fa-edit"></i>\
                        </button>\
                        <button title="Gerar relatório" type="button" ng-click="grid.appScope.vm.clickGerarRelatorio(row.entity)" ng-class="{ \'btn btn-xs btn-default btn-grid-size-default\': true }">\
                            <i class="fa fa-print"></i>\
                        </button>\
                    </div>'
                }, { 
                    name: 'Código.', 
                    width: 110, 
                    primaryKey: true, 
                    type: 'number', 
                    field: 'id' 
                }, { 
                    displayName: 'Data empréstimo', 
                    field: 'dataEmprestimo', 
                    width: 150, 
                    enableGrouping: false, 
                    type: 'date' ,
                    serverField: 'DataEmprestimo',
                    enableFiltering: true
                }, {
                    name: 'Portador',
                    width: '*',
                    minWidth: 150,
                    field: 'portador',
                    serverField: 'Portador.nome',
                    enableFiltering: true
                }, {
                    displayName: 'CPF/CNPJ Portador',
                    width: 150,
                    field: 'cpfCnpjPortador',
                    enableFiltering: true
                }, { 
                    displayName: 'Taxa retenção', 
                    field: 'taxaRetencao', 
                    width: 135, 
                    enableGrouping: false, 
                    type: 'float',
                    serverField: 'TaxaRetencao',
                    enableFiltering: true
                }, { 
                    displayName: 'Valor aquisição', 
                    field: 'valorAquisicao', 
                    width: 135, 
                    type: 'float', 
                    serverField: 'ValorAquisicao',
                    enableFiltering: true
                }, { 
                    displayName: 'Valor pago', 
                    field: 'valorPago', 
                    width: 135, 
                    type: 'float',
                    serverField: 'ValorPago',
                    enableFiltering: true 
                }
            ]
        }

        // Controle de aba!!
        $scope.$on('$stateChangeStart', function () {
            PersistentDataService.store('EmprestimoController', vm, "Emprestimo", "EmprestimoCrudController", "emprestimo.index");
        });
    }
})();