(function () {
    'use strict';

    angular.module('bbcWeb')
        .config(['uiMask.ConfigProvider', function (uiMaskConfigProvider) {
            uiMaskConfigProvider.maskDefinitions({
                'placa': 'AAA-9999',
                'cpf': '000-000-000/00',
                'cnpj': '00.000.000/0000-00',
                '*': /[a-zA-Z0-9]/
            });

            uiMaskConfigProvider.clearOnBlur(false);
            uiMaskConfigProvider.allowInvalidValue(true);
            uiMaskConfigProvider.eventsToHandle(['input', 'keyup', 'click']);
        }]);
})();