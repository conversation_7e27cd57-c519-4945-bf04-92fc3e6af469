(function () {
    'use strict';

    angular.module('bbcWeb.log-sms.state', []).config(routeConfig);

    function routeConfig($urlRouterProvider, $stateProvider) {
        $stateProvider
            .state('log-sms', {
                url: "/log-sms",
                abstract: true,  
                templateUrl: "app/layout/content.html"
            })
            .state('log-sms.index', {
                url: "/index",
                templateUrl: "app/entities/log-sms/log-sms.html"
            });
    }
})();