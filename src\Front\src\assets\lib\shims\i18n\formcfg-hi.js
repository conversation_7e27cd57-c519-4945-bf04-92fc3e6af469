webshims.validityMessages.hi={typeMismatch:{email:"\u0915\u0943\u092a\u092f\u093e \u0908-\u092e\u0947\u0932 \u092a\u0924\u093e \u0926\u0930\u094d\u091c \u0915\u0930\u0947\u0902.",url:"\u0915\u0943\u092a\u092f\u093e URL \u0932\u093f\u0916\u0947\u0902."},badInput:{number:"\u0905\u092e\u093e\u0928\u094d\u092f \u092e\u093e\u0928.",date:"\u0905\u092e\u093e\u0928\u094d\u092f \u092e\u093e\u0928.",time:"\u0905\u092e\u093e\u0928\u094d\u092f \u092e\u093e\u0928.",range:"\u0905\u092e\u093e\u0928\u094d\u092f \u092e\u093e\u0928.","datetime-local":"\u0905\u092e\u093e\u0928\u094d\u092f \u092e\u093e\u0928."},tooLong:"\u0905\u092e\u093e\u0928\u094d\u092f \u092e\u093e\u0928.",patternMismatch:"\u0915\u0943\u092a\u092f\u093e \u0905\u0928\u0941\u0930\u094b\u0927\u093f\u0924 \u092a\u094d\u0930\u093e\u0930\u0942\u092a \u0915\u093e \u092e\u093f\u0932\u093e\u0928 \u0915\u0930\u0947\u0902. {%title}.",valueMissing:{defaultMessage:"\u0915\u0943\u092a\u092f\u093e \u0907\u0938 \u095e\u0940\u0932\u094d\u0921 \u0915\u094b \u092d\u0930\u0947\u0902.",checkbox:"\u092f\u0926\u093f \u0906\u092a \u0906\u0917\u0947 \u092c\u0922\u093c\u0928\u093e \u091a\u093e\u0939\u0924\u0947 \u0939\u0948\u0902 \u0924\u094b \u0907\u0938 \u092c\u0949\u0915\u094d\u200d\u0938 \u0915\u094b \u091a\u0947\u0915 \u0915\u0930\u0947\u0902.",select:"\u0915\u0943\u092a\u092f\u093e \u0938\u0942\u091a\u0940 \u092e\u0947\u0902 \u0915\u093f\u0938\u0940 \u0906\u0907\u091f\u092e \u0915\u093e \u091a\u092f\u0928 \u0915\u0930\u0947\u0902.",radio:"\u0915\u0943\u092a\u092f\u093e \u0907\u0928\u092e\u0947\u0902 \u0938\u0947 \u0915\u094b\u0908 \u0935\u093f\u0915\u0932\u094d\u092a \u091a\u0941\u0928\u0947\u0902."},rangeUnderflow:{defaultMessage:"\u092e\u093e\u0928 {%min} \u0938\u0947 \u0915\u092e \u092f\u093e \u0907\u0938\u0915\u0947 \u092c\u0930\u093e\u092c\u0930 \u0939\u094b\u0928\u093e \u091a\u093e\u0939\u093f\u090f.",date:"\u092e\u093e\u0928 {%min} \u0938\u0947 \u0915\u092e \u092f\u093e \u0907\u0938\u0915\u0947 \u092c\u0930\u093e\u092c\u0930 \u0939\u094b\u0928\u093e \u091a\u093e\u0939\u093f\u090f.",time:"\u092e\u093e\u0928 {%min} \u0938\u0947 \u0915\u092e \u092f\u093e \u0907\u0938\u0915\u0947 \u092c\u0930\u093e\u092c\u0930 \u0939\u094b\u0928\u093e \u091a\u093e\u0939\u093f\u090f.","datetime-local":"\u092e\u093e\u0928 {%min} \u0938\u0947 \u0915\u092e \u092f\u093e \u0907\u0938\u0915\u0947 \u092c\u0930\u093e\u092c\u0930 \u0939\u094b\u0928\u093e \u091a\u093e\u0939\u093f\u090f."},rangeOverflow:{defaultMessage:"\u092e\u093e\u0928 {%max} \u0938\u0947 \u0915\u092e \u092f\u093e \u0907\u0938\u0915\u0947 \u092c\u0930\u093e\u092c\u0930 \u0939\u094b\u0928\u093e \u091a\u093e\u0939\u093f\u090f.",date:"\u092e\u093e\u0928 {%max} \u0938\u0947 \u0915\u092e \u092f\u093e \u0907\u0938\u0915\u0947 \u092c\u0930\u093e\u092c\u0930 \u0939\u094b\u0928\u093e \u091a\u093e\u0939\u093f\u090f.",time:"\u092e\u093e\u0928 {%max} \u0938\u0947 \u0915\u092e \u092f\u093e \u0907\u0938\u0915\u0947 \u092c\u0930\u093e\u092c\u0930 \u0939\u094b\u0928\u093e \u091a\u093e\u0939\u093f\u090f.","datetime-local":"\u092e\u093e\u0928 {%max} \u0938\u0947 \u0915\u092e \u092f\u093e \u0907\u0938\u0915\u0947 \u092c\u0930\u093e\u092c\u0930 \u0939\u094b\u0928\u093e \u091a\u093e\u0939\u093f\u090f."},stepMismatch:"\u0905\u092e\u093e\u0928\u094d\u092f \u092e\u093e\u0928."},webshims.formcfg.hi={numberFormat:{".":".",",":","},numberSigns:".",dateSigns:"/",timeSigns:":. ",dFormat:"/",patterns:{d:"dd/mm/yy"},date:{closeText:"\u092c\u0902\u0926",prevText:"\u092a\u093f\u091b\u0932\u093e",nextText:"\u0905\u0917\u0932\u093e",currentText:"\u0906\u091c",monthNames:["\u091c\u0928\u0935\u0930\u0940 ","\u092b\u0930\u0935\u0930\u0940","\u092e\u093e\u0930\u094d\u091a","\u0905\u092a\u094d\u0930\u0947\u0932","\u092e\u0908","\u091c\u0942\u0928","\u091c\u0942\u0932\u093e\u0908","\u0905\u0917\u0938\u094d\u0924 ","\u0938\u093f\u0924\u092e\u094d\u092c\u0930","\u0905\u0915\u094d\u091f\u0942\u092c\u0930","\u0928\u0935\u092e\u094d\u092c\u0930","\u0926\u093f\u0938\u092e\u094d\u092c\u0930"],monthNamesShort:["\u091c\u0928","\u092b\u0930","\u092e\u093e\u0930\u094d\u091a","\u0905\u092a\u094d\u0930\u0947\u0932","\u092e\u0908","\u091c\u0942\u0928","\u091c\u0942\u0932\u093e\u0908","\u0905\u0917","\u0938\u093f\u0924","\u0905\u0915\u094d\u091f","\u0928\u0935","\u0926\u093f"],dayNames:["\u0930\u0935\u093f\u0935\u093e\u0930","\u0938\u094b\u092e\u0935\u093e\u0930","\u092e\u0902\u0917\u0932\u0935\u093e\u0930","\u092c\u0941\u0927\u0935\u093e\u0930","\u0917\u0941\u0930\u0941\u0935\u093e\u0930","\u0936\u0941\u0915\u094d\u0930\u0935\u093e\u0930","\u0936\u0928\u093f\u0935\u093e\u0930"],dayNamesShort:["\u0930\u0935\u093f","\u0938\u094b\u092e","\u092e\u0902\u0917\u0932","\u092c\u0941\u0927","\u0917\u0941\u0930\u0941","\u0936\u0941\u0915\u094d\u0930","\u0936\u0928\u093f"],dayNamesMin:["\u0930\u0935\u093f","\u0938\u094b\u092e","\u092e\u0902\u0917\u0932","\u092c\u0941\u0927","\u0917\u0941\u0930\u0941","\u0936\u0941\u0915\u094d\u0930","\u0936\u0928\u093f"],weekHeader:"\u0939\u092b\u094d\u0924\u093e",firstDay:1,isRTL:!1,showMonthAfterYear:!1,yearSuffix:""}};