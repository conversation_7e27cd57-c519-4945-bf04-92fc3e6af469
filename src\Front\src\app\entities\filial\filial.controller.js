(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('FilialController', FilialController);

        FilialController.inject = ['BaseService', '$rootScope', 'toastr', '$scope', 'PersistentDataService', '$timeout', '$state', 'PERFIL_ADMINISTRADOR'];
  
    function  FilialController(BaseService, $rootScope, toastr, $scope, PersistentDataService, $timeout, $state, PERFIL_ADMINISTRADOR) {
        var vm = this;
        vm.headerItems = [{
            name: 'Cadastro<PERSON>'
        }, {
            name: 'Filial'
        }];

        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.perfil == PERFIL_ADMINISTRADOR;
        };
        
        vm.gridOptions = {
            data: [],  
            onRegisterApi: BaseService.dataGrid.defaultOnRegisterApi,
            dataSource: {
                url: "Filial/ConsultarGridFilial"
            },
            columnDefs: [{
                name: '<PERSON><PERSON><PERSON><PERSON>',
                width: 80,
                cellTemplate: '<div ng-if="!row.groupHeader" class="ui-grid-cell-contents" title="TOOLTIP">\
                                    <button ng-disabled="row.entity.ativo===0" tooltip-placement="right" uib-tooltip="Editar" type="button" ui-sref="filial.filial-crud({link: row.entity.id})"\
                                        ng-class="{ \'btn btn-xs btn-info\': true }">\
                                        <i class="fa fa-edit"></i>\
                                    </button>\
                                    <button type="button" tooltip-placement="right" uib-tooltip="Ativar / Inativar" ng-click="grid.appScope.vm.alterarStatus(row.entity.id, row.entity.ativo)" ng-class="row.entity.ativo===1 ? \'btn btn-xs btn-success\' : \'btn btn-xs btn-danger\'">\ <i ng-class="row.entity.ativo===1 ? \'fa fa-check\' : \'fa fa-times\'"></i>\
                                    </button>\
                                </div>'
            }, {
                name: 'Código',
                width: 80,
                type : 'number',
                primaryKey: true,
                field: 'id'
            }, {
                name: 'NomeFantasia',
                width: '*',
                minWidth: 150,
                field: 'nomeFantasia'
            }, {
                name: 'CNPJ',
                displayName: 'CNPJ',
                width: 145,
                field: 'cnpj'
            }, {
                name: 'E-mail',
                displayName: 'E-mail',
                width: '*',
                minWidth: 150,
                field: 'email'
            }, {
                name: 'Telefone',
                width: 140,
                field: 'telefone'
            }]
        };
        
        vm.alterarStatus = function (id, ativo) {
            BaseService.post('Filial', "AlterarStatus", {
                id: id
            }).then(function (response) {
                response.success ? (ativo === 1 ? toastr.success('Filial inativada com sucesso!') : toastr.success('Filial reativada com sucesso!')) : toastr.error(response.message);
                vm.gridOptions.dataSource.refresh();
            });
        };
       
        // Controle de aba!!
        $scope.$on('$stateChangeStart', function () {
            PersistentDataService.store('FilialController', vm, "Filial", "FilialCrudController", "filial.index");
        });

        var selfScope = PersistentDataService.get('FilialController');
        var filho = PersistentDataService.get('FilialCrudController');
        if (angular.isDefined(filho)) {
            $timeout(function () {
                $state.go('filial.filial-crud', {
                    link: filho.data.filial.IdFilial > 0 ? 'editar' : 'novo'
                });
            }, 50);
        } else if (angular.isDefined(selfScope)) {
            angular.extend(vm, selfScope.data);

            $timeout(function () {
                vm.gridOptions.dataSource.refresh();
            }, 450);
        }
    }
})();