<div class="form-horizontal">
    <hr/>
    <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label">Código:</label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <input type="text" ng-model="vm.clientSecret.id" class="form-control" disabled
                               value="{{vm.isNew()}}"/>
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-5 col-lg-3 control-label">
                        <span class="text-danger mr-5">*</span>Descrição:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-7 col-lg-9">
                        <input autocomplete="one-time-code" required type="text" ng-model="vm.clientSecret.descricao"
                               maxlength="200" name="Descrição" class="form-control"/>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <consulta-padrao-modal
                    tabledefinition="vm.consultaEmpresa"
                    label="'Empresa:'"
                    placeholder="'Selecione uma empresa'"
                    required-message="'Empresa é obrigatório'"
                    ng-required="!vm.consultaGrupoEmpresa.selectedValue || (vm.consultaGrupoEmpresa.selectedValue && vm.consultaEmpresa.selectedValue)"
                    ng-show="vm.isNew() || (!vm.isNew() && vm.consultaEmpresa.selectedValue && vm.consultaEmpresa.selectedText)"
                    ng-disabled="!vm.isNew()">
            </consulta-padrao-modal>

            <consulta-padrao-modal
                    tabledefinition="vm.consultaGrupoEmpresa"
                    label="'Grupo de empresa:'"
                    required-message="'Grupo de empresa é obrigatório'"
                    placeholder="'Selecione um grupo de empresa'"
                    ng-required="!vm.consultaEmpresa.selectedValue"
                    ng-show="!vm.temEmpresa || (!vm.isNew() && vm.consultaGrupoEmpresa.selectedValue && vm.consultaGrupoEmpresa.selectedText)"
                    ng-disabled="!vm.isNew() || vm.temGrupoEmpresa || vm.consultaEmpresa.selectedValue">
            </consulta-padrao-modal>
        </div>
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label">
                        Data expiração:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <p class="input-group">
                            <input type="text" readonly style="background-color: white !important"
                                   ng-click="vm.datePickerOpenemissao = !vm.datePickerOpenemissao"
                                   class="form-control" autocomplete="one-time-code"
                                   datepicker-options="vm.optionsDatePicker" current-text="Hoje" clear-text="Limpar"
                                   close-text="Fechar" uib-datepicker-popup="dd/MM/yyyy"
                                   datepicker-append-to-body="true"
                                   ng-model="vm.clientSecret.dataExpiracao" is-open="vm.datePickerOpenemissao"/>
                            <span class="input-group-btn">
                                <button type="button" class="btn btn-default"
                                        ng-click="vm.datePickerOpenemissao = !vm.datePickerOpenemissao">
                                    <i class="fa fa-calendar"></i>
                                </button>
                            </span>
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6" ng-if="vm.habilitarSenhaApi || vm.temGrupoEmpresa && !vm.temEmpresa && !vm.consultaEmpresa.selectedValue" >
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-5 col-lg-3 control-label">
                        <span ng-If="vm.isNew() && (vm.habilitarSenhaApi || vm.temGrupoEmpresa && !vm.temEmpresa && !vm.consultaEmpresa.selectedValue)" class="text-danger mr-5">*</span>Senha Api:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-7 col-lg-9">
                        <input type="password"
                               ng-required="vm.isNew() && (vm.habilitarSenhaApi || vm.temGrupoEmpresa && !vm.temEmpresa && !vm.consultaEmpresa.selectedValue)"
                               autocomplete="one-time-code" ng-model="vm.clientSecret.senhaApi" maxlength="200"
                               name="Senha da Api" class="form-control"/>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group" ng-show="!vm.isNew()">
                    <label class="col-xs-12 col-sm-3 col-md-5 col-lg-3 control-label">
                        Secret key:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-7 col-lg-9">
                        <input type="text" disabled ng-model="vm.clientSecret.secretKey"
                               maxlength="200" name="secretKey" class="form-control"/>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>