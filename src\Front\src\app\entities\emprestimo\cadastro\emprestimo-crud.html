<div ng-controller="EmprestimoCrudController as vm">
    <form-header items="vm.headerItems" head="'Empréstimo'" state="atendimento"></form-header>
    <div class="wrapper-content animated overflow-hidden fadeIn filter-position">
        <div class="row">
            <div class="col-md-12">
                <div class="ibox fadeInRight">
                    <div class="ibox-title">
                        <div class="col-md-6">
                            <h5>Empréstimo</h5>
                        </div>
                    </div>
                    <div class="ibox-content">
                        <form name="formEmprestimo" role="form" ng-submit="vm.clickSalvar(formEmprestimo)" novalidate ats-validator>
                            <div form-wizard steps="2">
                                <div class="form-wizard">
                                    <ol class="row">
                                        <li ng-class="{'active': wizard.active(1)}" class="{{vm.abasClass}}" ng-click="wizard.go(1)">
                                            <h4>Principal</h4>
                                        </li>
                                        <li ng-if="vm.showAbaRetencao" ng-class="{'active': wizard.active(2)}" class="{{vm.abasClass}}" ng-click="wizard.go(2)">
                                            <h4>Retenção</h4>
                                        </li>
                                    </ol>
                                </div>
                                
                                <hr class="mt-15" />                                                      
                                <div>
                                    <div style="margin-bottom: 10%; margin-top: 10%;" ng-show="vm.loader">
                                        <spinner-loader></spinner-loader>
                                        <div class="row">
                                            <center>
                                                <h2>Aguarde, processando ...</h2>
                                            </center>
                                        </div>
                                    </div>
                                    <div ng-show="wizard.active(1)">
                                        <div class="row" ng-show="!vm.loader">
                                            <div class="col-md-12">
                                                <div class="alert alert-success text-center"  ng-if="vm.emprestimo.idState === null || vm.emprestimo.idState === undefined">
                                                    <div ng-if="vm.emprestimo.idState === null || vm.emprestimo.idState === undefined">CRIADO PELO BBC LEASING</div>
                                                    <div ng-if="false">CRIADO PELO BBC LEASING</div>
                                                </div>
                                            </div>
                                        </div>      
                                        <div ng-show="!vm.loader" ng-include="'app/entities/emprestimo/cadastro/containers/formulario.html'"></div>
                                    </div>
                                    <div ng-show="wizard.active(2)">
                                        <div ng-show="!vm.loader" ng-include="'app/entities/emprestimo/cadastro/containers/retencao.html'"></div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="form-group">
                                        <div class="col-md-12 text-right">
                                            <hr />
                                            <button type="button" ng-disabled="vm.loader" ng-click="wizard.getActivePosition() == 1 ? $state.go('emprestimo.index')  : wizard.go(wizard.getActivePosition() - 1)" class="btn btn-labeled btn-default">
                                                <span class="btn-label">
                                                    <i class="fa fa-arrow-circle-left"></i>
                                                </span>
                                                Voltar
                                            </button>
                                            <button type="button" ng-show="!vm.isNew() && wizard.getActivePosition() == 1" ng-disabled="vm.loader" ng-click="wizard.go(wizard.getActivePosition() + 1)" class="btn btn-labeled btn-default">
                                                <span class="btn-label">
                                                    <i class="fa fa-arrow-circle-right"></i>
                                                </span>
                                                Avançar
                                            </button>
                                            <button ng-show="(vm.isNew() && wizard.getActivePosition() == 1) || !vm.isNew() && wizard.getActivePosition() == 2" ng-disabled="vm.loader" type="submit" class="btn btn-labeled btn-success text-right" data-style="expand-right">
                                                <span class="btn-label">
                                                    <i class="fa fa-check-circle"></i>
                                                </span>
                                                Salvar
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>