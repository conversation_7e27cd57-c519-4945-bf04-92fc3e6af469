<div id="PainelCiotCrudController" ng-controller="PainelCiotCrudController as vm">
    <form-header ng-submit="return" items="vm.headerItems" head="'Painel de CIOT'"></form-header>
    <div class="wrapper wrapper-content animated fadeIn mb-0 pb-0">
        <div class="row">
            <div class="col-lg-12">
                <div class="ibox animated fadeIn">
                    <div class="ibox-title">
                        <h5>{{vm.isNew() ? 'Novo' : 'Editar'}} CIOT</h5>
                        <div ibox-tools></div>
                    </div>
                    <div class="ibox-content wizard">
                        <div form-wizard steps="5">
                            <form id="formPrincipal" name="frmPainelCiotCrud" novalidate ats-validator ng-submit="vm.save(frmPainelCiotCrud)">
                                <div class="form-wizard">
                                    <ol ng-show="vm.isNew() && vm.painelCiot.tipo!==3" class="row" style="display: flex; flex-wrap: wrap;">
                                        <li ng-class="{'active':wizard.active(1)}" ng-click="wizard.go(1)"
                                            class="control-label col-xs-12 col-sm-3 col-md-3col-lg-3">
                                            <h4>Principal</h4>
                                        </li>
                                        <li ng-class="{'active':wizard.active(2)}" ng-click="wizard.go(2)"
                                            class="control-label col-xs-12 col-sm-3 col-md-3col-lg-3">
                                            <h4>Viagem</h4>
                                        </li>
                                        <li ng-class="{'active':wizard.active(3)}" ng-click="wizard.go(3)"
                                            class="control-label col-xs-12 col-sm-3 col-md-3col-lg-3">
                                            <h4>Pagamento</h4>
                                        </li>
                                        <li ng-class="{'active':wizard.active(4)}" ng-click="wizard.go(4)"
                                            class="control-label col-xs-12 col-sm-3 col-md-3col-lg-3">
                                            <h4>Resumo</h4>
                                        </li>
                                    </ol>

                                    <ol ng-show="vm.isNew() && vm.painelCiot.tipo===3" class="row" style="display: flex; flex-wrap: wrap;">
                                        <li ng-class="{'active':wizard.active(1)}" ng-click="wizard.go(1)"
                                            class="control-label col-xs-12 col-sm-4 col-md-4 col-lg-4">
                                            <h4>Principal</h4>
                                        </li>
                                        <li ng-class="{'active':wizard.active(3)}" ng-click="wizard.go(3)"
                                            class="control-label col-xs-12 col-sm-4 col-md-4 col-lg-4">
                                            <h4>Pagamento</h4>
                                        </li>
                                        <li ng-class="{'active':wizard.active(4)}" ng-click="wizard.go(4)"
                                            class="control-label col-xs-12 col-sm-4 col-md-4 col-lg-4">
                                            <h4>Resumo</h4>
                                        </li>
                                    </ol>

                                    <ol ng-show="!vm.isNew()" class="row" style="display: flex; flex-wrap: wrap;">
                                        <li ng-class="{'active':wizard.active(1)}" ng-click="wizard.go(1)"
                                            class="control-label col-xs-12 col-sm-3 col-md-3 col-lg-3">
                                            <h4>Principal</h4>
                                        </li>
                                        <li ng-class="{'active':wizard.active(2)}" ng-click="wizard.go(2)"
                                            class="control-label col-xs-12 col-sm-3 col-md-3 col-lg-3">
                                            <h4>Viagem</h4>
                                        </li>
                                        <li ng-class="{'active':wizard.active(3)}" ng-click="wizard.go(3)"
                                            class="control-label col-xs-12 col-sm-3 col-md-3 col-lg-3">
                                            <h4>Pagamento</h4>
                                        </li>
                                        <li ng-class="{'active':wizard.active(4)}" ng-click="wizard.go(4)"
                                            class="control-label col-xs-12 col-sm-3 col-md-3 col-lg-3">
                                            <h4>Resumo</h4>
                                        </li>
                                        <li ng-class="{'active':wizard.active(5)}" ng-click="wizard.go(5)"
                                            class="control-label col-xs-12 col-sm-3 col-md-3 col-lg-3">
                                            <h4>Histórico</h4>
                                        </li>
                                    </ol>

                                    <div ng-show="!vm.carregandoEdit">
                                        <div ng-show="wizard.active(1)">
                                            <div ng-include="'app/entities/painel-ciot/abas/principal.html'"
                                                class="form-horizontal"> </div>
                                        </div>


                                        <div ng-show="wizard.active(3)">
                                            <div ng-include="'app/entities/painel-ciot/abas/pagamento.html'"
                                                class="form-horizontal"></div>
                                        </div>

                                        <div ng-show="wizard.active(4)">
                                            <div ng-include="'app/entities/painel-ciot/abas/resumo.html'"
                                                class="form-horizontal"></div>
                                        </div>

                                        <div ng-show="wizard.active(5)">
                                            <div ng-include="'app/entities/painel-ciot/abas/historico.html'"
                                                class="form-horizontal"></div>
                                        </div>
                                    </div>
                                    <hr />
                                    <div ng-show="vm.carregandoEdit">
                                        <div class="spiner-example">
                                            <div class="sk-spinner sk-spinner-wave">
                                                <div class="sk-rect1"></div>
                                                <div class="sk-rect2"></div>
                                                <div class="sk-rect3"></div>
                                                <div class="sk-rect4"></div>
                                                <div class="sk-rect5"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <button id="buttonATSValidator" type="submit" style="display: none"></button>
                                </div>
                            </form>
                            <div ng-show="wizard.active(2)">
                                <div ng-include="'app/entities/painel-ciot/abas/viagem.html'" class="form-horizontal">
                                </div>
                            </div>
                        </div>
                        <div ng-show="!vm.carregandoEdit">
                            <div class="row">
                                <div class="form-group">
                                    <div class="text-right">
                                        <hr />
                                        <button type="button" ng-disabled="vm.saving"
                                            ng-click="vm.onClickVoltar(wizard)"
                                            class="btn btn-labeled btn-default">
                                            <span class="btn-label">
                                                <i class="fa fa-arrow-circle-left"></i>
                                            </span>
                                            Voltar
                                        </button>
                                        <button type="button" ng-show="!wizard.active(vm.lastIndex)" ng-disabled="vm.saving"
                                            ng-click="vm.onClickAvancar(wizard)"
                                            class="btn btn-labeled btn-default">
                                            <span class="btn-label">
                                                <i class="fa fa-arrow-circle-right"></i>
                                            </span>
                                            Avançar
                                        </button>
                                        <button type="button" ng-show="wizard.active(vm.lastIndex) && !vm.isVisualizar()"
                                            onclick="document.getElementById('buttonATSValidator').click()"
                                            ng-disabled="vm.saving"
                                            class="btn btn-labeled btn-success text-right ladda-button"
                                            data-style="expand-right">
                                            <span class="btn-label">
                                                <i class="fa fa-check-circle"></i>
                                            </span>
                                            Salvar
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div>