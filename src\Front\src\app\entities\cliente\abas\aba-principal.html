<div class="form-horizontal">
    <hr />
    <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3 col-lg-3">Código:</label>
                    <div class="input-group col-xs-12 col-sm-3 col-md-3 col-lg-3">
                        <input type="text" ng-model="vm.cliente.Id" class="form-control" disabled
                            value="{{vm.isNew()}}" />
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-3 col-lg-3 control-label">
                        <span class="text-danger mr-5">*</span>CPF/CNPJ:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-9 col-lg-9">
                        <input type="text" ui-br-cpfcnpj-mask required ng-model="vm.cliente.CpfCnpj"
                            required-message="'CPF/CNPJ é obrigatório'" maxlength="100" validate-on="blur"
                            name="CpfCnpj" class="form-control" ng-disabled="!vm.isNew()" invalid-message="'CPF/CNPJ é inválido'"/>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-3 col-lg-3 control-label">
                        <span class="text-danger mr-5">*</span>Nome fantasia:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-9 col-lg-9">
                        <input type="text" required ng-model="vm.cliente.NomeFantasia"
                            required-message="'Nome fantasia é obrigatório'" maxlength="100" validate-on="blur"
                            name="NomeFantasia" class="form-control" />
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-3 col-lg-3 control-label">
                        <span class="text-danger mr-5">*</span>Razão social:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-9 col-lg-9">
                        <input type="text" maxlength="100" required ng-model="vm.cliente.RazaoSocial"
                            required-message="'Razão social é obrigatória'" validate-on="blur" name="RazaoSocial"
                            class="form-control" />
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-3 col-lg-3 control-label">
                        <span class="text-danger mr-5">*</span>Celular:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-9 col-lg-9">
                        <input type="text" ui-br-phone-number required ng-model="vm.cliente.Celular"
                            required-message="'Celular é obrigatório'" validate-on="blur" name="Celular"
                            class="form-control" />
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-3 col-lg-3 control-label">
                        <span class="text-danger mr-5">*</span>Telefone:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-9 col-lg-9">
                        <input type="text" ui-br-phone-number required ng-model="vm.cliente.Telefone"
                            required-message="'Telefone é obrigatório'" validate-on="blur" name="Telefone"
                            class="form-control" />
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-3 col-lg-3 control-label">
                        <span class="text-danger mr-5">*</span>E-mail:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-9 col-lg-9">
                        <input type="email" maxlength="100" required ng-model="vm.cliente.Email"
                            required-message="'E-mail é obrigatório'" validate-on="blur" name="Email"
                            class="form-control" invalid-message="'E-mail é inválido'"/>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <consulta-padrao-modal ng-show="vm.isAdmin()" tabledefinition="vm.consultaEmpresa" label="'Empresa:'"
                placeholder="'Selecione uma Empresa'" required-message="'Empresa é obrigatório'"
                ng-required="vm.isAdmin()" directivesizes="'col-xs-12 col-sm-12 col-md-6 col-lg-6'"
                labelsize="'col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label'">
            </consulta-padrao-modal>
        </div>
    </div>
</div>