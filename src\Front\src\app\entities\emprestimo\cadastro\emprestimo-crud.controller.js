(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('EmprestimoCrudController', EmprestimoCrudController);

    EmprestimoCrudController.$inject = [
        '$stateParams',
        '$state',
        'BaseService',
        'EmprestimoCrudService',
        'toastr'
    ];

    function EmprestimoCrudController($stateParams, $state, BaseService, EmprestimoCrudService, toastr) {
        var vm = this;
        vm.headerItems = EmprestimoCrudService.getHeader();
        vm.enumStatus = EmprestimoCrudService.getEnumStatus();

        vm.loader = false;
        vm.showAbaRetencao = false;
        vm.desabilitarCampos = false;
        vm.disabledState = false;
        vm.disabledPortador = false;

        vm.abasClass = undefined;

        vm.emprestimo = {};
        vm.emprestimoCopy = {};

        vm.isNew = function () {
            return isNew();
        }

        vm.load = function () {
            if (isNew()) {
                vm.emprestimo.Id = 0;
                vm.abasClass = EmprestimoCrudService.getClass12();
            } else {
                consultarParaEdicao($stateParams.link);
                vm.abasClass = EmprestimoCrudService.getClass6();
                vm.showAbaRetencao = true;
                vm.desabilitarCampos = true;
            }
        }

        vm.clickSalvar = function (form) {
            //if (isNew())
            cadastrar(form);
        }

        vm.clickIntegrar = function (retencao) {
            integrarRetencao(retencao);
        }

        function cadastrar(form) {
            if (!form.$valid) {
                toastr.error('Todos os campos obrigatórios e/ou inválidos devem ser preenchidos corretamente com seus respectivos valores');
                return;
            }
            vm.emprestimoCopy = {}
            angular.copy(vm.emprestimo, vm.emprestimoCopy)
            //arrangeValoresDecimais();
            iniciarLoader();
            EmprestimoCrudService.cadastrar(vm.emprestimoCopy, function (sucesso, dados, mensagem) {
                finalizarLoader();
                if (sucesso) {
                    toastr.success("Empréstimo cadastrado com sucesso");
                    $state.go("emprestimo.index");
                } else {
                    toastr.error(mensagem);
                }
            });
        }

        function arrangeValoresDecimais() {
            if (typeof (vm.emprestimo.taxaRetencao) === "string" && typeof (vm.emprestimo.valorAquisicao) === "string" && typeof (vm.emprestimo.valorPago) === "string") {
                vm.emprestimoCopy.taxaRetencao = BaseService.parseStringToFloat(vm.emprestimoCopy.taxaRetencao);
                vm.emprestimoCopy.valorAquisicao = BaseService.parseStringToFloat(vm.emprestimoCopy.valorAquisicao);
                vm.emprestimoCopy.valorPago = BaseService.parseStringToFloat(vm.emprestimoCopy.valorPago);
            }
        }

        function consultarParaEdicao(id) {
            EmprestimoCrudService.consultarParaEdicao(id, function (sucesso, dados, mensagem) {
                if (sucesso) {
                    vm.emprestimo = dados;
                    vm.emprestimo.status = "" + vm.emprestimo.status + "";
                    vm.consultaPortador.selectedValue = vm.emprestimo.portadorId;
                    vm.consultaPortador.selectedText = vm.emprestimo.portadorNome;

                    if (vm.emprestimo.idState != "" && vm.emprestimo.idState != null) {
                        vm.disabledState = true;
                    }

                    if (vm.consultaPortador.selectedValue != null) {
                        vm.disabledPortador = true;
                    }
                }
                else
                    toastr.error(mensagem);
            })
        }

        function integrarRetencao(retencao) {
            var valorLiquido = parseFloat(vm.emprestimo.taxaRetencao) * parseFloat(vm.emprestimo.valorPago) / 100;

            var obj = [{
                CnpjConta: EmprestimoCrudService.removerMascaraCpfCnpj(vm.emprestimo.cpfCnpjPortador),
                IdOperacao: "",
                NumeroConta: vm.emprestimo.conta,
                IdLancamento: retencao.pagamentoId.toString(),
                DataLancamento: new Date(),
                DescricaoLancamento: "Retenção de empréstimo automatizado",
                ValorLiquido: valorLiquido
            }];

            iniciarLoader();

            EmprestimoCrudService.integrarRetencao(obj, function (sucesso, mensagem) {
                finalizarLoader();
                if (sucesso) {
                    toastr.success(mensagem);
                    setarRetencaoIntegrada(retencao.id);
                }
                else {
                    toastr.error(mensagem);
                }
            });
        }

        function setarRetencaoIntegrada(id) {
            var retencao = _.find(vm.emprestimo.retencoes, ['id', id]);
            var obj = { Id: retencao.id, DataIntegracao: new Date(), DataCadastro: retencao.DataCadastro, EmprestimoId: vm.emprestimo.id, PagamentoId: retencao.pagamentoId, Status: 1, Valor: BaseService.parseStringToFloat(retencao.valor), MensagemIntegracao: "Integração realizada com sucesso" };
            EmprestimoCrudService.setarRetencaoIntegrada(obj, function (sucesso, mensagem) {
                finalizarLoader();

                if (sucesso) {
                    consultarParaEdicao($stateParams.link);
                    //toastr.success(mensagem);
                }
                else
                    toastr.error(mensagem);
            })
        }

        function iniciarLoader() {
            vm.loader = true;
        }

        function finalizarLoader() {
            vm.loader = false;
        }

        vm.sinalizaClienteEmprestimo = function () {
            vm.sinalizando = true;
            EmprestimoCrudService.sinalizaClienteEmprestimo(vm.emprestimo, function (sucesso, mensagem) {
                if (sucesso)
                    toastr.success(mensagem);
                else
                    toastr.error(mensagem);

                vm.sinalizando = false;
            });
        }

        function isNew() {
            return $stateParams.link == 'novo';
        }

        vm.consultaPortador = {
            columnDefs: [
                { name: 'Código', field: 'id', width: 80, type: 'number', primaryKey: true },
                { name: 'Nome', field: 'nome', width: '*', minWidth: 120 },
                { name: 'CPF/CNPJ', field: 'cpfCnpj', enableGrouping: true, width: 150 }
            ],
            desiredValue: 'id',
            desiredText: 'nome',
            url: 'Portador/ConsultarGridPortadorCombo',
            paramsMethod: function () {
                return {}
            },
            executeAfterSelection: function () {

                if (vm.emprestimo.cpfCnpjPortador != undefined) {
                    if (!vm.isNew()) {
                        if (vm.emprestimo.cpfCnpjPortador == vm.consultaPortador.selectedEntity.cpfCnpj) {
                            vm.emprestimo.cpfCnpjPortador = vm.consultaPortador.selectedEntity.cpfCnpj;
                        }
                        else {
                            toastr.error('CPF/CNPJ portador informado não coincide com o CPF/CNPJ cadastrado.');
                            vm.consultaPortador.selectedValue = null;
                            vm.consultaPortador.selectedText = "";
                            return;
                        }
                    }
                    else {
                        vm.emprestimo.cpfCnpjPortador = vm.consultaPortador.selectedEntity.cpfCnpj;
                    }
                }
                else {
                    vm.emprestimo.cpfCnpjPortador = vm.consultaPortador.selectedEntity.cpfCnpj;
                }
                vm.emprestimo.portadorId = vm.consultaPortador.selectedValue;
            },
            clearFunction: function () {
                vm.emprestimo.portadorId = null;
                vm.emprestimo.cpfCnpjPortador = null;
            }
        };
        vm.load();
    }
})();