<div id="AutorizacaoContingenciaController" ng-controller="AutorizacaoContingenciaController as vm">
    <form-header ng-submit="return" items="vm.headerItems" head="'Autorização de contingência'"></form-header>
    <div class="wrapper-content animated overflow-hidden fadeIn mb-0 pb-0">
        <div class="row">
            <div class="ibox animated fadeIn">
                <div class="ibox-title">
                    <h5>Autorização de contingência</h5>
                    <div ibox-tools></div>
                </div>
                <div class="ibox-content wizard">
                    <form name="frmAtualizacaoPrecoCombustivel" id="frmAtualizacaoPrecoCombustivel" novalidate ats-validator ng-submit="vm.salvar(frmAtualizacaoPrecoCombustivel)" show-validation>
                        <div class="form-horizontal">
                            <div class="col-md-12 col-xs-12">
                                <div class="row">
                                    <div class="col-xs-12 col-md-6">
                                        <div class="form-group">
                                            <label class="col-xs-12 col-md-3 control-label alinhamento-labels">Período:</label>
                                            <div class="input-group col-xs-12 col-md-9">
                                                <input date-range-picker class="form-control date-picker" ui-date-mask="DD/MM/YYYY - DD/MM/YYYY" type="text" ng-model="vm.date" options="vm.dateOptions" />
                                                <span class="input-group-btn">
                                                    <button type="button" class="btn btn-primary"
                                                        ng-click="vm.buscar();" ng-disabled="vm.date.startDate == null || vm.date.endDate == null">Consultar</button>
                                                </span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-xs-12 col-md-6">
                                        <consulta-padrao-modal 
                                            idname="Posto" idmodel="Posto" 
                                            directivesizes="'col-xs-12 col-md-12'" labelsize="'col-xs-12 col-md-3 control-label alinhamento-labels'" label="'Posto:'" placeholder="'Posto'"
                                            tabledefinition="vm.consultaPostoModal"  >
                                        </consulta-padrao-modal>
                                    </div>
                                </div>
                                <hr>
                                <div form-wizard steps="12" class="row">
                                    <div class="form-wizard">
                                        <ol class="row">
                                            <li ng-class="{'active':wizard.active(1)}" ng-click="wizard.go(1)"
                                                class="control-label col-xs-12 col-md-6" style="text-align: center;">
                                                <h4>Contingência</h4>
                                            </li>
                                            <li ng-class="{'active':wizard.active(2)}" ng-click="wizard.go(2); vm.buscar()"
                                                class="control-label col-xs-12 col-md-6" style="text-align: center;">
                                                <h4>Histórico</h4>
                                            </li>
                                        </ol>
                                        <div>
                                            <div ng-show="wizard.active(1)">
                                                <div class="row">
                                                    <div class="col-xs-12 col-md-12">
                                                        <div class="col-xs-12 col-md-6">
                                                            <div class="form-group">
                                                                <label class="control-label alinhamento-labels col-xs-12 col-md-3">
                                                                    <span class="text-danger mr-5">*</span>Motivo:
                                                                </label>
                                                                <div class="input-group col-xs-12 col-md-9">
                                                                    <input type="text" ng-model="vm.MotivoInterno" maxlength="30" class="form-control" name="motivo" id="motivo" ng-required="true" required-message="'Motivo é obrigatório'"/>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="col-xs-12 col-md-6">
                                                            <div class="form-group">
                                                                <label class="control-label alinhamento-labels col-xs-12 col-md-3">
                                                                    <span class="text-danger mr-5">*</span>Status atualização:
                                                                </label>
                                                                <div class="input-group col-xs-12 col-md-9">
                                                                    <ui-select  ats-ui-select-validator ng-model="vm.status" name="status" id="status" ng-required="true" required-message="'Status atualização é obrigatório'">
                                                                        <ui-select-match>
                                                                            <span>{{$select.selected.label}}</span>
                                                                        </ui-select-match>
                                                                        <ui-select-choices repeat="ex.data as ex in vm.comboStatus | propsFilter: {label: $select.search}">
                                                                            <div ng-bind-html="ex.label | highlight: $select.search"></div>
                                                                        </ui-select-choices>
                                                                    </ui-select>
                                                                </div>   
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-xs-12 col-md-12">                                                        
                                                        <div class="col-xs-12 col-md-12" style="text-align: right;">
                                                            <div class="form-group">
                                                                <button type="button" ng-disabled="vm.saving" ng-click="vm.onClickLimparMotivoStatus()" class="btn btn-labeled btn-default">
                                                                    <span class="btn-label">
                                                                        <i class="fa fa-eraser"></i>
                                                                    </span>
                                                                    Limpar
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                
                                                <br><br>
                                                <div class="row">
                                                    <div class="col-xs-12 col-md-12">
                                                        <button type="button" class="btn btn-success" ng-click="vm.MarcarTodos();" >
                                                            <span class="btn-label">
                                                                <i class="fa fa-check-circle"></i>
                                                            </span>
                                                            Marcar todos
                                                        </button>
                                                        <button type="button" class="btn btn-danger" ng-click="vm.DesmarcarTodos();">
                                                            <span class="btn-label">
                                                                <i class="fa fa-times-circle"></i>
                                                            </span>
                                                            Desmarcar todos
                                                        </button>
                                                    </div>
                                                </div>
                                                <br>
                                                <div class="row">
                                                    <div class="ibox-content">
                                                        <div class="table-responsive tableFixHead">
                                                            <table class="table table-sm table-bordered" >
                                                                    <thead>
                                                                        <th style="min-width: 100px !important;">Selecionar</th>
                                                                        <th style="min-width: 120px !important;">Código do posto</th>
                                                                        <th style="min-width: 120px !important;">Nome do posto</th>
                                                                        <th style="min-width: 120px !important;">Placa</th>
                                                                        <th style="min-width: 150px !important;">Nome do combustível</th>
                                                                        <th style="min-width: 80px !important;">Litragem</th>
                                                                        <th style="min-width: 80px !important;">Valor</th>
                                                                        <th style="min-width: 80px !important;">Status</th>
                                                                        <th style="min-width: 150px !important;">Data solicitação</th>
                                                                        <th style="min-width: 150px !important;">Motivo solicitação</th>
                                                                    </thead>
                                                                    <tbody>
                                                                        <tr ng-repeat="item in vm.mSolicitacoesPendentes" style="height: 40px;" >
                                                                            <td>
                                                                                <input type="checkbox" id="checkbox"  name="checkbox" ng-checked="item.selecionado == 'S'" ng-click="vm.checkBoxSelecionado(item)" />
                                                                            </td>
                                                                            <td>{{item.postoId}}</td>
                                                                            <td>{{item.nomePosto}}</td>
                                                                            <td>{{item.placa}}</td>
                                                                            <td>{{item.nomeCombustivel}}</td>
                                                                            <td>
                                                                                <input type="text" ng-model="item.litragem" readonly class="no-borders" style="background: none;" ui-number-mask="3" />
                                                                            </td>
                                                                            <td>
                                                                                <input type="text" ng-model="item.valor" readonly class="no-borders" style="background: none;" ui-money-mask="3" />
                                                                            </td>
                                                                            <td>{{item.status == '0' ? 'Reprovado' : item.STATUS_APROVACAO == '2' ? 'Aguardando aprovação' : 'Aguardando aprovação'}}</td>
                                                                            <td>{{item.dataCadastro}}</td>
                                                                            <td>{{item.motivo}}</td>
                                                                        </tr>
                                                                    </tbody>
                                                            </table>
                                                        </div>
                                                    </div>
                                                   
                                                </div>
                                            </div>
                                            <div ng-show="wizard.active(2)">
                                                <div class="col-lg-12">
                                                    <div ui-grid="vm.gridOptions" ng-style="{height: vm.gridOptions.getGridHeight()}" class="grid" style="width: 100%;" 
                                                        ui-grid-pinning 
                                                        ui-grid-save-state 
                                                        ui-grid-pagination 
                                                        ui-grid-auto-resize 
                                                        ui-grid-resize-columns 
                                                        ui-grid-grouping>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <hr />
                                    </div>
                                </div>

                                

                                <div class="form-group">
                                    <div class="text-right">
                                        <hr />

                                        <button type="button" ng-disabled="vm.saving" ng-click="vm.onClickLimpar()" class="btn btn-labeled btn-danger">
                                        <span class="btn-label">
                                            <i class="fa fa-eraser"></i>
                                        </span>
                                        Limpar
                                    </button>
                                        <button type="submit" ng-disabled="vm.saving" class="btn btn-labeled btn-success text-right ladda-button" data-style="expand-right">
                                        <span class="btn-label">
                                            <i class="fa fa-check-circle"></i>
                                        </span>
                                        Salvar
                                    </button>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group"></div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
