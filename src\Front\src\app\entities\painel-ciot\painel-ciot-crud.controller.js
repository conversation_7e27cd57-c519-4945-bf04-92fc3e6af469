(function () {
    'use strict';

    angular.module('bbcWeb').controller('PainelCiotCrudController', PainelCiotCrudController);

    PainelCiotCrudController.$inject = [
        'toastr', '$rootScope', 'BaseService', '$state', '$stateParams', '$window', 'PERFIL_ADMINISTRADOR', '$scope', '$timeout', 'PersistentDataService', 
        'DefaultsService', '$uibModal'];

    function PainelCiotCrudController(
        toastr, $rootScope,
        BaseService, $state,
        $stateParams, $window,
        PERFIL_ADMINISTRADOR,
        $scope, $timeout,
        PersistentDataService,
        DefaultsService,
        $uibModal
    ) {
        var vm = this;
        vm.painelCiot = {};
        vm.painelCiot.viagensList = [];
        vm.painelCiot.dataFim = null;
        vm.painelCiot.veiculosList = [];
        vm.ciotViagem = [];
        vm.viagem = {};
        vm.valoresSomados = {};
        vm.valoresViagem = {};
        vm.painelCiot.Sucesso = false;
        vm.painelCiot.Erro = false;
        vm.painelCiot.disabled = true;
        vm.mostraListaViagens = true;
        vm.disableFieldPesoCarga = false;
        vm.disableFieldValorFrete = false;
        vm.disableFieldNaturezaCarga = false;
        vm.idViagemOrigem = 0;
        vm.idViagemDestino = 0;

        vm.comboFormaPag = {
            data: [{ id: 0, descricao: 'Depósito' }, { id: 1, descricao: 'Cartão' }, { id: 2, descricao: 'Cheque' }, { id: 3, descricao: 'Outros' }]
        };

        vm.comboTipo = {
            data: [{ id: 0, descricao: 'Adiantamento' }, { id: 1, descricao: 'Saldo' }, { id: 2, descricao: 'Complemento' }, { id: 3, descricao: 'Avulso' }, { id: 4, descricao: 'Tarifa ANTT' }]
        };

        vm.headerItems = [{
            name: 'Cadastros'
        }, {
            name: 'Painel de CIOT',
            link: 'painel-ciot.index'
        }, {
            name: $stateParams.link == 'novo' ? 'Novo' : 'Editar'
        }];


        vm.isVisualizar = function () {
            return $stateParams.visualizar == 'true';
        };

        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.administrador == true;
        };

        vm.abasNaoPertencentesTipoAgregado = [2];
        vm.lastIndex;

        vm.onClickVoltar = function (wizard) {
            const currentIndex = wizard.getActivePosition();
            if (currentIndex == 1) $state.go('painel-ciot.index');
            const nextIndex = validaAtivoIndex(currentIndex, true);
            wizard.go(nextIndex);
        }

        vm.onClickAvancar = function (wizard) {
            const currentIndex = wizard.getActivePosition();
            const nextIndex = validaAtivoIndex(currentIndex, false);
            wizard.go(nextIndex);
        }

        function getLastIndex() {
            var lastIndex;
            if (vm.isNew() && vm.painelCiot.tipo===undefined) lastIndex = 4;
            if (vm.isNew() && vm.painelCiot.tipo===1) lastIndex = 4;
            if (vm.isNew() && vm.painelCiot.tipo===3) lastIndex = 4;
            if (!vm.isNew()) lastIndex = 5;
            return lastIndex;
        }

        function validaAtivoIndex(index, voltando) {
            var nextIndex = index + (voltando ? -1 : 1);

            vm.lastIndex = getLastIndex();

            if (nextIndex > vm.lastIndex) return index;

            if (vm.isNew() && vm.painelCiot.tipo===3) {
                nextIndex = (vm.abasNaoPertencentesTipoAgregado.includes(nextIndex) ? validaAtivoIndex(nextIndex, voltando) : nextIndex)
            }

            return nextIndex;
        }

        $scope.$watch('vm.painelCiot.tipo', function () {
            vm.lastIndex = getLastIndex();
        })

        vm.comboTipoCiot = {
            data: [{ id: 1, descricao: 'Padrão' }, { id: 3, descricao: 'Agregado' }]
        };

        vm.load = function () {
        }

        vm.setMascaraCpfCnpj = function (cpfCnpj) {
            if (cpfCnpj.length == 14) {
                vm.mascaraCpfCnpjProp = "999.999.999-99"
            }
            else {
                vm.mascaraCpfCnpjProp = "99.999.999/9999-99"
            }
        };


        vm.mascaraCpfCnpj = function (valor) {
            if (valor.length > 11)
                return valor.replace(/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/g, "\$1.\$2.\$3\/\$4\-\$5");
            else
                return valor.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/g, "\$1.\$2.\$3\-\$4");
        };

        vm.consultaDescricaoTipo = function () {
            return vm.comboTipoCiot.data.filter(function (x) { return x.id === vm.painelCiot.tipo })[0].descricao;
        }

        vm.consultaDescricaoFormaPagamento = function () {
            return vm.comboFormaPag.data.filter(function (x) { return x.id === vm.painelCiot.pagamento.formaPagamento })[0].descricao;
        }

        function setDataFim(data) {
            if (angular.isDefinedNotNull(data)) {
                vm.painelCiot.dataFim = new Date(data);
            }
        }

        vm.loadEdit = function (id) {
            BaseService.get('PainelCiot', 'ConsultarOperacaoTransportePorId', {
                IdOperacaoTransporte: id
            }).then(function (response) {
                if (!response.success) {
                    toastr.error(response.message);
                    return;
                }
                vm.painelCiot = response.data;
                vm.ciotViagem = vm.painelCiot;
                vm.valoresViagem = vm.painelCiot;
                setDataFim(vm.painelCiot.dataFim);
                setValores(vm.painelCiot);

                if (vm.painelCiot.tipo === 1) {
                    vm.mostraListaViagens = false;
                    vm.consultaCidadeOrigem.selectedText = vm.painelCiot.cidadeOrigemPadrao.nome;
                    vm.consultaCidadeDestino.selectedText = vm.painelCiot.cidadeDestinoPadrao.nome;
                    vm.consultaNaturezaCarga.selectedText = vm.painelCiot.codNaturezaCarga;
                }

                vm.consultaProprietario.selectedText = vm.painelCiot.proprietario.nomeRazaoSocial == undefined ? "Não informado" : vm.painelCiot.proprietario.nomeRazaoSocial;
                vm.consultaMotorista.selectedText = vm.painelCiot.motorista.nome ? vm.painelCiot.motorista.nome : "Não informado";
                vm.consultaBanco.selectedText = vm.painelCiot.nomeBanco ? vm.painelCiot.nomeBanco : "Não informado";

                vm.consultaDestinatario.selectedText = vm.painelCiot.destinatario.nomeRazaoSocial == undefined ? "Não informado" : vm.painelCiot.destinatario.nomeRazaoSocial;
                vm.consultaRemetente.selectedText = vm.painelCiot.remetente.nomeRazaoSocial == undefined ? "Não informado" : vm.painelCiot.remetente.nomeRazaoSocial;
                vm.consultaConsignatario.selectedText = vm.painelCiot.consignatario.nomeRazaoSocial == undefined ? "Não informado" : vm.painelCiot.consignatario.nomeRazaoSocial;
            });
        };

        function setValores(ciot) {
            ciot.valorFrete = ciot.valorFrete == undefined ? "0,00" : ciot.valorFrete;
            ciot.valorImposto = ciot.valorImposto == undefined ? "0,00" : ciot.valorImposto;
            ciot.valorDespesas = ciot.valorDespesas == undefined ? "0,00" : ciot.valorDespesas;
            ciot.valorCombustivel = ciot.valorCombustivel == undefined ? "0,00" : ciot.valorCombustivel;
            ciot.valorPedagio = ciot.valorPedagio == undefined ? "0,00" : ciot.valorPedagio;
        };
        
        vm.VerRequisicao = function (jsonRequisicao, jsonResposta) {
            $uibModal.open({
                animation: true,
                ariaLabelledBy: 'modal-title',
                ariaDescribedBy: 'modal-body',
                templateUrl: 'app/entities/painel-ciot/modal/detalhes-historico/modal-detalhes-historico-ciot.html',
                controller: 'ModalDetalhesHistoricoCiotController',
                controllerAs: 'vm',
                keyboard: true,
                size: 'lg',
                windowClass: 'custom-modal-width',
                resolve: {
                    jsonRequisicao: function() {
                        return jsonRequisicao;
                    },
                    jsonResposta: function() {
                        return jsonResposta;
                    }
                }
            });
        };

        vm.gridOptions = {
            data: [],
            enableFiltering: true,
            onRegisterApi: BaseService.dataGrid.defaultOnRegisterApi,
            dataSource: {
                url: "PainelCiot/ConsultarGridOperacaoTransporteHistorico",
                params: function () {
                    return {
                        Ciot: $stateParams.ciot
                    }
                },
            },  
            columnDefs: [
                {
                    name: 'Ações',
                    width: '100',
                    cellTemplate: '<div class="ui-grid-cell-contents" align="center" title="TOOLTIP">\
                        <button tooltip-placement="right" uib-tooltip="Visualizar Json" type="button" \
                            ng-class="{ \'btn btn-xs btn-info\': true }" \
                            ng-click="grid.appScope.vm.VerRequisicao(row.entity.reqCliente, row.entity.respCliente)">\
                            <i class="fa fa-eye"></i>\
                        </button>\
                    </div>'
                },
            {
                displayName: 'Código',
                name: 'Código',
                serverField: 'ID',
                field: 'id',
                minWidth: 80,
                width: 80,
                type: 'number',
                enableGrouping: false,
                visible: false
            }, {
                displayName: 'Ciot',
                name: 'Ciot',
                serverField: 'CIOT',
                field: 'ciot',
                width: '*',
                enableGrouping: false,
                visible: false
            }, {
                displayName: 'TipoOperacao',
                name: 'TipoOperacao',
                serverField: 'TIPO_OPERACAO',
                field: 'tipoOperacao',
                width: 250,
                minWidth: 250,
                enableGrouping: false,
                enum: true,
                enumTipo: 'EStatusTipoOperacao',
                cellTemplate: '<div class="ui-grid-cell-contents" align="left">\
                                        <p ng-show="row.entity.tipoOperacao === 0"> Declarar Operação de Transporte </p>\
                                        <p ng-show="row.entity.tipoOperacao === 1"> Cancelar Operação de Transporte </p>\
                                        <p ng-show="row.entity.tipoOperacao === 2"> Encerrar Operação de Transporte </p>\
                                        <p ng-show="row.entity.tipoOperacao === 3"> Retificar Operação de Transporte </p>\
                                   </div>',
            }, {
                displayName: 'Requisição',
                name: 'ReqCliente',
                serverField: 'REQ_CLIENTE',
                field: 'reqCliente',
                width: '*',
                minWidth: 150,
                enableFiltering: false
            }, {
                displayName: 'Resposta',
                name: 'RespCliente',
                serverField: 'RESP_CLIENTE',
                field: 'respCliente',
                width: '*',
                minWidth: 150,
                enableFiltering: false
            }, {
                displayName: 'Data Cadastro',
                name: 'DataCadastro',
                serverField: 'DT_CADASTRO',
                field: 'dataCadastro',
                minWidth: 140,
                type: 'date' ,
                width: '*',
                enableGrouping: false,                
                enableFiltering: true
            }, {
                displayName: 'Sucesso',
                name: 'Sucesso',
                serverField: 'SUCESSO',
                field: 'sucesso',
                minWidth: 140,
                width: '*',             
                enableFiltering: true,
                enumTipo: 'ECiotEncerrado',
                cellTemplate: '<div class="ui-grid-cell-contents" align="left">\
                                        <p ng-show="row.entity.sucesso === \'S\'"> Sim </p>\
                                        <p ng-show="row.entity.sucesso === \'N\'"> Não </p>\
                                   </div>'
            }]
        };


        vm.consultaProprietario = {
            columnDefs: [{
                name: 'Código',
                field: 'id',
                width: 80,
                type: 'number',
                primaryKey: true
            }, {
                name: 'Nome',
                field: 'nome',
                width: '*',
                minWidth: 150
            }, {
                name: 'CPF/CNPJ',
                field: 'cpfCnpj',
                enableGrouping: true,
                width: 125,
            }, {
                name: 'RNTRC',
                field: 'rntrc',
                width: 125,
                visible: false
            }],
            desiredValue: 'id',
            desiredText: 'nome',
            url: 'Portador/ConsultarGridPortadorEmpresaCombo',
            paramsMethod: function () {
                return {
                }
            },
            executeAfterSelection: function () {
                vm.painelCiot.portadorPropId = vm.consultaProprietario.selectedValue;
                vm.painelCiot.cpfCnpjProp = vm.consultaProprietario.selectedEntity.cpfCnpj;
                vm.setMascaraCpfCnpj(vm.painelCiot.cpfCnpjProp);
                vm.painelCiot.Sucesso = false;
                vm.painelCiot.Erro = false;
                BaseService.get('PainelCiot', 'ConsultarSituacaoProprietario', {
                    proprietarioId: vm.consultaProprietario.selectedValue,
                    motoristaId: vm.consultaMotorista.selectedValue
                }).then(function (response) {
                    if (response.success) {
                        vm.painelCiot.disabled = false;
                        if (!response.data.equiparadoTAC) {
                            vm.painelCiot.disabled = true;
                        }
                        $scope.RespRNTRC = response.data.mensagem;
                        vm.painelCiot.Sucesso = true;
                    } else {
                        vm.painelCiot.disabled = true;
                        toastr.error(response.message);
                    }
                });
            }
        };

        vm.consultaViagem = {
            columnDefs: [{
                name: 'Código',
                width: 80,
                primaryKey: true,
                field: 'id',
                type: 'number',
                enableFiltering: true
            }, {
                name: 'Código Externo Id',
                width: 125,
                primaryKey: true,
                field: 'codigoExternoId',
                enableFiltering: true
            }, {
                name: 'Origem',
                width: '*',
                minWidth: 150,
                field: 'nomeOrigem',
                serverField: 'CidadeOrigem.Nome',
                enableFiltering: true
            }, {
                name: 'Destino',
                width: '*',
                minWidth: 150,
                field: 'nomeDestino',
                serverField: 'CidadeDestino.Nome',
                enableFiltering: true
            }, {
                name: 'Id Origem',
                width: '*',
                minWidth: 150,
                field: 'idMunicipioOrigem',
                enableFiltering: true,
                visible: false
            }, {
                name: 'Id Destino',
                width: '*',
                minWidth: 150,
                field: 'idMunicipioDestino',
                enableFiltering: true,
                visible: false
            },{
                name: 'Peso Carga',
                width: '*',
                minWidth: 150,
                field: 'pesoCarga',
                serverField: 'pesoCarga',
                enableFiltering: true
            }, {
                name: 'Natureza Carga',
                width: '*',
                minWidth: 150,
                field: 'naturezaCarga',
                serverField: 'naturezaCarga',
                enableFiltering: true
            }, {
                name: 'Valor Frete',
                width: '*',
                minWidth: 150,
                field: 'valorFrete',
                serverField: 'valorFrete',
                enableFiltering: true
            }],
            desiredValue: 'id',
            desiredText: 'codigoExternoId',
            url: 'Viagem/ConsultarGridViagemCombo',
            paramsMethod: function () {
                return {
                }
            },
            executeAfterSelection: function () {
                vm.consultaCidadeOrigem.selectedText = vm.consultaViagem.selectedEntity.nomeOrigem;
                vm.consultaCidadeDestino.selectedText = vm.consultaViagem.selectedEntity.nomeDestino;
                vm.idViagemOrigem = vm.consultaViagem.selectedEntity.idMunicipioOrigem;
                vm.idViagemDestino = vm.consultaViagem.selectedEntity.idMunicipioDestino;
                vm.consultaNaturezaCarga.selectedText = vm.consultaViagem.selectedEntity.naturezaCarga ? vm.consultaViagem.selectedEntity.naturezaCarga : null;
                vm.valoresViagem.peso = vm.consultaViagem.selectedEntity.pesoCarga ? vm.consultaViagem.selectedEntity.pesoCarga : null;
                vm.valoresViagem.valorFrete = vm.consultaViagem.selectedEntity.valorFrete ? vm.consultaViagem.selectedEntity.valorFrete : null;
                vm.disableFieldPesoCarga = vm.valoresViagem.peso != null;
                vm.disableFieldValorFrete = vm.valoresViagem.valorFrete != null;
                vm.disableFieldNaturezaCarga = vm.consultaNaturezaCarga.selectedText != null;
                vm.viagemId = vm.consultaViagem.selectedEntity.id;
            }
        };

        vm.limpaInfosViagem = function () {
            vm.disableFieldPesoCarga = false;
            vm.disableFieldValorFrete = false;
            vm.disableFieldNaturezaCarga = false;
            vm.consultaCidadeOrigem.selectedText = null;
            vm.consultaCidadeDestino.selectedText = null;
            vm.consultaNaturezaCarga.selectedText = null;
            vm.valoresViagem.peso  = null;
            vm.valoresViagem.valorFrete  = null
        };

        vm.consultaMotorista = {
            columnDefs: [{
                name: 'Código.',
                field: 'id',
                width: 80,
                type: 'number',
                primaryKey: true
            }, {
                name: 'Nome',
                field: 'nome',
                width: '*',
                minWidth: 150
            }, {
                name: 'CPF/CNPJ',
                field: 'cpfCnpj',
                enableGrouping: true,
                width: 125
            }],
            desiredValue: 'id',
            desiredText: 'nome',
            url: 'Portador/ConsultarGridPortadorPessoaFisica',
            paramsMethod: function () {
                return {
                }
            },
            executeAfterSelection: function () {
                vm.painelCiot.cpfCnpjMot = vm.consultaMotorista.selectedEntity.cpfCnpj;
            }
        };

        vm.consultaVeiculo = {
            columnDefs: [{
                name: 'Código',
                field: 'id',
                width: 80,
                type: 'number',
                primaryKey: true
            }, {
                name: 'Placa',
                field: 'placa',
                enableGrouping: true,
                width: 80
            }, {
                name: 'RENAVAM',
                field: 'renavam',
                enableGrouping: true,
                width: '*',
                minWidth: 150
            }],
            desiredValue: 'id',
            desiredText: 'placa',
            url: 'Veiculo/ConsultarGridVeiculoPortadorCiotCombo',
            paramsMethod: function () {
                return {
                    CpfCnpjProprietario: vm.consultaProprietario.selectedEntity.cpfCnpj
                }
            }
        };

        vm.consultaCidadeOrigem = {
            columnDefs: [{
                name: 'Código',
                width: 80,
                primaryKey: true,
                field: 'id',
                type: 'number',
                enableFiltering: true
            }, {
                name: 'Nome',
                width: '*',
                minWidth: 150,
                field: 'nome',
                serverField: 'Nome',
                enableFiltering: true
            }, {
                name: 'Estado',
                width: '*',
                minWidth: 150,
                field: 'nomeEstado',
                serverField: 'Estado.Nome',
                enableFiltering: true
            }],
            desiredValue: 'id',
            desiredText: 'nome',
            url: 'Cidade/ConsultarGridCidade',
            paramsMethod: function () {
                return {
                }
            }
        };

        vm.consultaCidadeDestino = {
            columnDefs: [{
                name: 'Código',
                width: 80,
                primaryKey: true,
                field: 'id',
                type: 'number',
                enableFiltering: true
            }, {
                name: 'Nome',
                width: '*',
                minWidth: 150,
                field: 'nome',
                serverField: 'Nome',
                enableFiltering: true
            }, {
                name: 'Estado',
                width: '*',
                minWidth: 150,
                field: 'nomeEstado',
                serverField: 'Estado.Nome',
                enableFiltering: true
            }],
            desiredValue: 'id',
            desiredText: 'nome',
            url: 'Cidade/ConsultarGridCidade',
            paramsMethod: function () {
                return {

                }
            }
        };

        vm.consultaRemetente = {
            columnDefs: [{
                name: 'Código',
                width: 80,
                primaryKey: true,
                field: 'id',
                type: 'number',
                enableFiltering: true
            }, {
                name: 'Nome',
                width: '*',
                minWidth: 150,
                field: 'nomeFantasia',
                serverField: 'NomeFantasia',
                enableFiltering: true
            }, {
                name: 'CNPJ',
                width: 125,
                field: 'cnpj',
                serverField: 'Cnpj',
                enableFiltering: true
            }, {
                name: 'Celular',
                width: 105,
                field: 'celular',
                serverField: 'Celular',
                enableFiltering: true
            }],
            desiredValue: 'id',
            desiredText: 'nomeFantasia',
            url: 'Cliente/ConsultarGridClienteCombo',
            paramsMethod: function () { return {} }
        };

        vm.consultaDestinatario = {
            columnDefs: [{
                name: 'Código',
                width: 80,
                primaryKey: true,
                field: 'id',
                type: 'number',
                enableFiltering: true
            }, {
                name: 'Nome',
                width: '*',
                minWidth: 150,
                field: 'nomeFantasia',
                serverField: 'NomeFantasia',
                enableFiltering: true
            }, {
                name: 'CNPJ',
                width: 125,
                field: 'cnpj',
                serverField: 'Cnpj',
                enableFiltering: true
            }, {
                name: 'Celular',
                width: 105,
                field: 'celular',
                serverField: 'Celular',
                enableFiltering: true
            }],
            desiredValue: 'id',
            desiredText: 'nomeFantasia',
            url: 'Cliente/ConsultarGridClienteCombo',
            paramsMethod: function () {
                return {

                }
            }
        };

        vm.consultaBanco = {
            columnDefs: [{
                name: 'Código',
                width: 80,
                primaryKey: true,
                field: 'id',
                serverField: 'Id',
                enableFiltering: true
            }, {
                name: 'Nome',
                width: '*',
                minWidth: 150,
                field: 'nome',
                serverField: 'Nome',
                enableFiltering: true
            }],
            desiredValue: 'id',
            desiredText: 'nome',
            url: 'Banco/ConsultarGridBancoCombo',
            paramsMethod: function () {
                return {

                }
            }
        };

        vm.consultaConsignatario = {
            columnDefs: [{
                name: 'Código',
                width: 80,
                primaryKey: true,
                field: 'id',
                type: 'number',
                enableFiltering: true
            }, {
                name: 'Nome',
                width: '*',
                minWidth: 150,
                field: 'nomeFantasia',
                serverField: 'NomeFantasia',
                enableFiltering: true
            }, {
                name: 'CNPJ',
                width: 125,
                field: 'cnpj',
                serverField: 'Cnpj',
                enableFiltering: true
            }, {
                name: 'Celular',
                width: 105,
                field: 'celular',
                serverField: 'Celular',
                enableFiltering: true
            }],
            desiredValue: 'id',
            desiredText: 'nomeFantasia',
            url: 'Cliente/ConsultarGridClienteCombo',
            paramsMethod: function () { return {} }
        };

        vm.limpaCpfCnpjProp = function () {
            vm.painelCiot.cpfCnpjProp = "";
            vm.painelCiot.portadorPropId = undefined;
        }

        vm.limpaCpfCnpjMot = function () {
            vm.painelCiot.cpfCnpjMot = "";
            vm.painelCiot.portadorMotId = undefined;
        }

        vm.consultaNaturezaCarga = {
            columnDefs: [{
                name: 'Código',
                width: 80,
                field: 'codigo',
                enableFiltering: true
            }, {
                name: 'Descrição',
                width: '*',
                minWidth: 150,
                field: 'descricao',
                serverField: 'Descricao',
                enableFiltering: true
            }],
            desiredValue: 'id',
            desiredText: 'codigo',
            url: 'NaturezaCarga/ConsultarGridNaturezaCarga',
            paramsMethod: function () {
                return {
                }
            }
        };

        vm.isNew = function () {
            return $stateParams.link == 'novo';
        };

        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.administrador == true;
        };

        function init() {
            if (vm.isNew()) {
                vm.painelCiot.id = 'Auto';
            }
        }

        vm.disabledFields = function () {
            return vm.isVisualizar() || vm.painelCiot.tipo == undefined || vm.painelCiot.disabled == true;
        };

        vm.disabledAgregado = function () {
            return vm.painelCiot.tipo == 3;
        };

        vm.TipoChange = function (data) {
            vm.valoresSomados = {};
            vm.ciotViagem = {};
            vm.isChangeTipe = true;
            vm.CIOT = undefined;
            vm.consultaProprietario.selectedValue = undefined;
            vm.consultaProprietario.selectedText = undefined;
            vm.consultaMotorista.selectedValue = undefined;
            vm.consultaMotorista.selectedText = undefined;
            vm.consultaVeiculo.selectedValue = undefined;
            vm.consultaVeiculo.selectedText = undefined;
            vm.painelCiot = {};
            vm.painelCiot.veiculosList = [];
            vm.painelCiot.viagensList = [];
            vm.limpar();
            if (data == 3) {
                var dateAtual = new Date();
                vm.painelCiot.dataFim = new Date(dateAtual.setDate(dateAtual.getDate() + 30));
            }
            vm.painelCiot.disabled = true;
            vm.painelCiot.tipo = data;
        };

        vm.FormaPagamento = function (data) {
            if (data != 0) {
                vm.painelCiot.agencia = null;
                vm.painelCiot.conta = null;
                vm.painelCiot.verificadorConta = null;
                vm.consultaBanco.selectedValue = undefined;
                vm.consultaBanco.selectedText = undefined;
                vm.painelCiot.tipoConta = null;
            }
        }

        vm.DataFinalChange = function (data) {
            if (data < new Date()) {
                toastr.error("A data final deve ser maior que hoje.");
                return;
            }
        }

        /*
        vm.valorFreteChange = function (valor) {
            if (valor == "" || valor == "0,00") {
                toastr.error("O valor do frete deve ser maior que 0.");
                return;
            }
        }
        */
        

        vm.removerViagem = function (row) {
            vm.painelCiot.viagensList.splice(row.$index, 1)
            if (vm.painelCiot.viagensList.length < 1) {
                vm.isChangeTipe = true;
                vm.limpar()
            }
        }

        vm.removerVeiculo = function (veiculo) {
            for (var i = 0; i < vm.painelCiot.veiculosList.length; i++) {
                if (vm.painelCiot.veiculosList[i].placa == veiculo.placa) {
                    var index = vm.painelCiot.veiculosList.indexOf((vm.painelCiot.veiculosList[i]));
                    vm.painelCiot.veiculosList.splice(index, 1)
                }
            }
            vm.consultaVeiculo.selectedText = undefined;
        }

        vm.limpar = function () {
            vm.consultaCidadeOrigem.selectedValue = undefined;
            vm.consultaCidadeOrigem.selectedText = vm.isChangeTipe ? undefined : "Selecione uma Cidade";
            vm.consultaCidadeDestino.selectedValue = undefined;
            vm.consultaCidadeDestino.selectedText = vm.isChangeTipe ? undefined : "Selecione uma Cidade";
            vm.consultaRemetente.selectedValue = undefined;
            vm.consultaRemetente.selectedText = vm.isChangeTipe ? undefined : "Selecione uma Remetente";
            vm.consultaDestinatario.selectedValue = undefined;
            vm.consultaDestinatario.selectedText = vm.isChangeTipe ? undefined : "Selecione um Destinatário";
            vm.consultaConsignatario.selectedValue = undefined;
            vm.consultaConsignatario.selectedText = vm.isChangeTipe ? undefined : "Selecione um Consignatário";
            vm.consultaNaturezaCarga.selectedValue = undefined;
            vm.consultaNaturezaCarga.selectedText = vm.isChangeTipe ? undefined : "Selecione uma Natureza Carga";
            vm.valoresViagem.valorFrete = vm.isChangeTipe ? undefined : "0,00";
            vm.valoresViagem.valorImposto = vm.isChangeTipe ? undefined : "0,00";
            vm.valoresViagem.valorDespesas = vm.isChangeTipe ? undefined : "0,00";
            vm.valoresViagem.valorCombustivel = vm.isChangeTipe ? undefined : "0,00";
            vm.valoresViagem.valorPedagio = vm.isChangeTipe ? undefined : "0,00";
            vm.valoresViagem.peso = vm.isChangeTipe ? undefined : "0,00";
            vm.consultaViagem.selectedValue = undefined;
            vm.consultaViagem.selectedText = vm.isChangeTipe ? undefined : "Selecione uma Viagem";
            vm.disableFieldPesoCarga = false;
            vm.disableFieldValorFrete = false;
            vm.disableFieldNaturezaCarga = false;
            vm.viagemId = undefined;
            //vm.ciotViagem = undefined;
        }

        vm.adicionarEnabled = function () {
            try {
                return (vm.consultaCidadeOrigem.selectedText != undefined && vm.consultaCidadeDestino.selectedText != undefined
                    && vm.consultaRemetente.selectedValue != undefined && vm.consultaConsignatario.selectedValue != undefined && vm.consultaDestinatario.selectedValue != undefined && vm.consultaNaturezaCarga.selectedValue != undefined
                    && (vm.ciotViagem.valorFrete != undefined && vm.ciotViagem.valorFrete != "0,00") && (vm.ciotViagem.peso != undefined && vm.ciotViagem.peso != "0,00"))
            } catch (e) {
                return false;
            }
        }

        vm.preencheValores = function () {
            vm.ciotViagem.valorFrete = vm.valoresViagem.valorFrete ? vm.valoresViagem.valorFrete : vm.ciotViagem.valorFrete;
            vm.ciotViagem.valorImposto = vm.valoresViagem.valorImposto ? vm.valoresViagem.valorImposto : vm.ciotViagem.valorImposto;
            vm.ciotViagem.valorDespesas = vm.valoresViagem.valorDespesas ? vm.valoresViagem.valorDespesas : vm.ciotViagem.valorDespesas;
            vm.ciotViagem.valorCombustivel = vm.valoresViagem.valorCombustivel ? vm.valoresViagem.valorCombustivel : vm.ciotViagem.valorCombustivel;
            vm.ciotViagem.valorPedagio = vm.valoresViagem.valorPedagio ? vm.valoresViagem.valorPedagio : vm.ciotViagem.valorPedagio;
            vm.ciotViagem.peso = vm.valoresViagem.peso ? vm.valoresViagem.peso : vm.ciotViagem.peso;
        }

         

        vm.adicionarViagem = function (form) {
            var adiciona = false;

            if (vm.painelCiot.tipo == 3) {
                vm.preencheValores();
                if (vm.adicionarAgregadoEnabled()) {
                    adiciona = true;
                }

            } else if (vm.painelCiot.viagensList) {
                if (vm.painelCiot.viagensList.length == 1) {
                    return toastr.error("Uma viagem já foi adicionada.");
                } else {
                    vm.preencheValores();
                    if (vm.adicionarEnabled()) {
                        adiciona = true;
                    }
                }
            }

            if (!form.$valid) {
                toastr.error('Todos os campos obrigatórios e/ou inválidos devem ser preenchidos corretamente com seus respectivos valores');
                console.log(form.$error);
                return;
            }

            if (adiciona) {
                vm.viagem.cidadeOrigemId = vm.consultaCidadeOrigem.selectedValue ? vm.consultaCidadeOrigem.selectedValue : vm.idViagemOrigem;
                vm.viagem.cidadeDestinoId = vm.consultaCidadeDestino.selectedValue ? vm.consultaCidadeDestino.selectedValue : vm.idViagemDestino;

                vm.viagem.remetenteId = vm.consultaRemetente.selectedValue;
                vm.viagem.destinatarioId = vm.consultaDestinatario.selectedValue;
                vm.viagem.consignatarioId = vm.consultaConsignatario.selectedValue;
                vm.viagem.naturezaCargaId = vm.consultaNaturezaCarga.selectedValue;
                vm.viagem.peso = vm.ciotViagem.peso;

                // Valores da viagem
                vm.viagem.valorFrete = vm.valoresViagem.valorFrete !== undefined ? vm.valoresViagem.valorFrete : "0,00";
                vm.viagem.valorImposto = vm.valoresViagem.valorImposto !== undefined ? vm.valoresViagem.valorImposto : "0,00";
                vm.viagem.valorDespesas = vm.valoresViagem.valorDespesas !== undefined ? vm.valoresViagem.valorDespesas : "0,00";
                vm.viagem.valorCombustivel = vm.valoresViagem.valorCombustivel !== undefined ? vm.valoresViagem.valorCombustivel : "0,00";
                vm.viagem.valorPedagio = vm.valoresViagem.valorPedagio !== undefined ? vm.ciotViagem.valorPedagio : "0,00";

                // Valores padrão
                vm.ciotViagem.valorFrete = vm.valoresViagem.valorFrete;
                vm.ciotViagem.valorImposto = vm.valoresViagem.valorImposto;
                vm.ciotViagem.valorDespesas =  vm.valoresViagem.valorDespesas;
                vm.ciotViagem.valorCombustivel = vm.valoresViagem.valorCombustivel;
                vm.ciotViagem.valorPedagio = vm.valoresViagem.valorPedagio;

                //MOSTRA
                vm.viagem.nomeOrigem = vm.consultaCidadeOrigem.selectedText;
                vm.viagem.nomeDestino = vm.consultaCidadeDestino.selectedText;
                vm.viagem.nomeRemetente = vm.consultaRemetente.selectedText;
                vm.viagem.nomeDestinatario = vm.consultaDestinatario.selectedText;
                vm.viagem.nomeConsignatario = vm.consultaConsignatario.selectedText;
                vm.viagem.codigoNaturezaCarga = vm.consultaNaturezaCarga.selectedText;
                vm.viagem.viagemId = vm.viagemId;

                vm.painelCiot.viagensList.push(vm.viagem);
                vm.viagem = {};
                vm.isChangeTipe = false;
                vm.limpar();
            } else {
                toastr.error("Por favor, informe os campos obrigatórios.");
            }
        };

        vm.cadastrarVeiculo = function () {
            $uibModal.open({
                animation: true,
                ariaLabelledBy: 'modal-title',
                ariaDescribedBy: 'modal-body',
                templateUrl: 'app/entities/painel-ciot/modal/modal-cadastro-veiculo.html',
                controller: 'PainelCiotModalCadVeicCrudController',
                controllerAs: 'vm',
                size: 'md',
                windowClass: 'modal-top',
                resolve: {
                    PortadorPropId: function () {
                        return vm.consultaProprietario.selectedValue;
                    },
                    PortadorPropCpfCnpj: function () {
                        return vm.consultaProprietario.selectedEntity.cpfCnpj;
                    },
                    Rntrc: function () {
                        return vm.consultaProprietario.selectedEntity.rntrc;
                    },
                }
            }).result.then(function (veiculo) {
                vm.painelCiot.veiculosList.push(veiculo);
            });
        };

        vm.cadastrarCliente = function () {
            $uibModal.open({
                animation: true,
                ariaLabelledBy: 'modal-title',
                ariaDescribedBy: 'modal-body',
                templateUrl: 'app/entities/painel-ciot/modal/modal-cadastro-cliente.html',
                controller: 'PainelCiotModalCadClienteCrudController',
                controllerAs: 'vm',
                keyboard: true,
                size: 'lg',
                resolve: {
                }
            }).result.then(function () {
            });
        }

        vm.cadastrarPortador = function () {
            $uibModal.open({
                animation: true,
                ariaLabelledBy: 'modal-title',
                ariaDescribedBy: 'modal-body',
                templateUrl: 'app/entities/painel-ciot/modal/modal-cadastro-portador.html',
                controller: 'PainelCiotModalCadPortadorCrudController',
                controllerAs: 'vm',
                keyboard: true,
                size: 'lg',
                resolve: {
                }
            }).result.then(function () {
            });
        }

        vm.adicionarVeiculo = function () {
            var permiteAdicionar = false;
            var objetosValidados = _.filter(vm.painelCiot.veiculosList, function (v) {
                return v.placa === vm.consultaVeiculo.selectedEntity.placa;
            });
            if (objetosValidados.length > 0) {
                toastr.error("Este veículo já foi adicionado.");
                return;
            }

            if (vm.consultaVeiculo.selectedEntity != undefined && vm.consultaVeiculo.selectedEntity.placa != undefined
                && vm.consultaVeiculo.desiredValue != "") {
                permiteAdicionar = true;
            }

            if (vm.painelCiot.veiculosList.length > 5) {
                permiteAdicionar = false;
                toastr.error("Não é possível informar mais de 5 veículos.");
            }

            if (permiteAdicionar) {
                vm.painelCiot.veiculosList.push(angular.copy(vm.consultaVeiculo.selectedEntity));
                vm.clearConsultaVeiculo();
            }
            else
                toastr.error("Por favor, informe o veículo.");
        }

        vm.clearConsultaVeiculo = function () {
            vm.consultaVeiculo.selectedEntity = undefined;
            vm.consultaVeiculo.selectedValue = undefined;
            vm.consultaVeiculo.selectedText = "";
        }

        vm.save = function (form) {
            vm.loading = true;
            if (!form.$valid) {
                toastr.error('Todos os campos obrigatórios e/ou inválidos devem ser preenchidos corretamente com seus respectivos valores');
                return;
            }
            vm.saving = true;
            if (vm.consultaVeiculo.selectedEntity) {
                var objetosValidados = _.filter(vm.painelCiot.veiculosList, function (v) {
                    return v.id === vm.consultaVeiculo.selectedEntity.id;
                });

                if (objetosValidados.length == 0) {
                    vm.painelCiot.veiculosList.push(angular.copy(vm.consultaVeiculo.selectedEntity));
                }
            }

            if (vm.painelCiot.veiculosList <= 0) {
                vm.saving = false;
                toastr.error('É necessário inserir um veículo.');
            }

            if (vm.isNew()) {
                vm.painelCiot.portadorPropId = vm.consultaProprietario.selectedValue;
                vm.painelCiot.portadorMotId = vm.consultaMotorista.selectedValue;
                vm.painelCiot.banco = vm.consultaBanco.selectedValue;
            }

            if (vm.painelCiot.tipo == 1) {//PADRÃO
                if (vm.painelCiot.viagensList.length < 1) {
                    vm.saving = false;
                    toastr.error('É necessário informar viagem.');
                }
            }

            if (vm.saving == true) {
                vm.painelCiot.valorFrete = vm.ciotViagem.valorFrete;
                vm.painelCiot.valorImposto = vm.ciotViagem.valorImposto;
                vm.painelCiot.valorDespesas = vm.ciotViagem.valorDespesas;
                vm.painelCiot.valorCombustivel = vm.ciotViagem.valorCombustivel;
                vm.painelCiot.valorPedagio = vm.ciotViagem.valorPedagio;

                if (vm.isNew()) {
                    vm.painelCiot.id = 'Auto';
                }

                BaseService.post('PainelCiot', 'Save', vm.painelCiot).then(function (response) {
                    if (response.success) {
                        toastr.success(response.message);
                        $state.go('painel-ciot.index');
                        vm.saving = true;
                    } else {
                        toastr.error(response.message);
                        vm.saving = false;
                    }
                });
            }
        };

        var selfScope = PersistentDataService.get('PainelCiotCrudController');

        if ($stateParams.link == 'novo')
            vm.painelCiot.id = 'Auto';

        if (angular.isDefined(selfScope))
            angular.extend(vm, selfScope.data);
        else
            if (!vm.isNew())
                vm.loadEdit($stateParams.link);
            else
                vm.load();

        init();
        // DO NOT TOUCH!! 666
        $scope.$on('$stateChangeStart', function (arg1, toState) {
            if (toState.name === 'painel-ciot.index')
                PersistentDataService.remove('PainelCiotCrudController');
            else
                PersistentDataService.store('PainelCiotCrudController', vm, "Movimentação - Painel de ciot", null, "painel-ciot.painel-ciot-crud", vm.painelCiot.id);
        });

        $timeout(function () {
            PersistentDataService.remove('PainelCiotController');
        }, 15);
    }
})();
