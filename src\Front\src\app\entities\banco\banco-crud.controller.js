(function () {
    'use strict';

    angular.module('bbcWeb').controller('BancoCrudController', BancoCrudController);

    BancoCrudController.$inject = ['toastr', '$rootScope', 'BaseService', '$state', '$stateParams', '$window', 'PERFIL_ADMINISTRADOR', '$scope', '$timeout', 'PersistentDataService', 'DefaultsService', '$uibModal'];

    function BancoCrudController(
        toastr, $rootScope,
        BaseService, $state,
        $stateParams, $window,
        PERFIL_ADMINISTRADOR,
        $scope, $timeout,
        PersistentDataService,
        DefaultsService,
        $uibModal
    ) {
        var vm = this;
        vm.banco = [];
        vm.menusPai = [];

        vm.headerItems = [{
            name: 'Cadastros'
        }, {
            name: 'Banco',
            link: 'banco.index'
        }, {
            name: $stateParams.link == 'novo' ? 'Novo' : 'Editar'
        }];

        var selfScope = PersistentDataService.get('bancoCrudController');

        if ($stateParams.link == 'novo')
            vm.banco.id = 'Auto';

        vm.isNew = function () {
            return $stateParams.link == 'novo';
        };

        vm.loadEdit = function (id) {
            BaseService.get('Banco', 'ConsultarPorId', {
                idbanco: id
            }).then(function (response) {
                if (!response.success) {
                    toastr.error(response.message);
                    return;
                } else {
                    vm.banco = response.data;
                }
            });
        };

        vm.save = function (form) {
            if (!form.$valid) {
                toastr.error('Todos os campos obrigatórios devem ser preenchidos corretamente com seus respectivos valores');
                return;
            }

            if (vm.isSaving === true)
                return;

            var banco = {}

            banco.Id = vm.banco.id == "Auto" ? "" : vm.banco.id;
            banco.Nome = vm.banco.nome;
            banco.Ativo = vm.banco.ativo;
            banco.Editar = vm.isNew() ? 0 : 1

            vm.isSaving = true;

            BaseService.post('Banco', 'Salvar', banco).then(function (response) {
                vm.isSaving = false;

                if (response && !response.sucesso)
                    return toastr.error(response.mensagem);
                else {
                    toastr.success('banco salvo com sucesso!');
                    $state.go('banco.index');
                }
            });
        };

        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.administrador == true;
        };

        function init() {
            if (vm.isNew()) {
                vm.banco.id = 'Auto';
            }
        };

        vm.onClickAvancar = function (wizard) {
            wizard.go(wizard.getActivePosition() + 1);
        };

        vm.onClickVoltar = function (wizard) {

            var ativoIndex = wizard.getActivePosition();
            if (ativoIndex == 1)
                $state.go('banco.index');

            wizard.go(ativoIndex - 1);
        };

        init();

        $scope.$on('$stateChangeStart', function (arg1, toState) {
            if (toState.name === 'banco.index')
                PersistentDataService.remove('BancoCrudController');
            else
                PersistentDataService.store('BancoCrudController', vm, "Cadastro - Bancos", null, "banco.banco-crud", vm.banco.id);
        });

        if (angular.isDefined(selfScope))
            angular.extend(vm, selfScope.data);
        else {
            if (!vm.isNew())
                vm.loadEdit($stateParams.link);
            else {
                vm.banco = 0;
                vm.modulos = 1;
            }
        }

        $timeout(function () {
            PersistentDataService.remove('BancoController');
        }, 15);

        vm.consultaEmpresa = {
            columnDefs: [{
                name: 'Cód.',
                field: 'id',
                width: 60,
                type: 'number',
                primaryKey: true
            }, {
                name: 'Nome Fantasia',
                field: 'nomeFantasia',
                width: '*'
            }, {
                name: 'Razão Social',
                field: 'razaoSocial',
                width: '*'
            }, {
                name: 'Email',
                field: 'email',
                width: 120
            }],
            desiredValue: 'id',
            desiredText: 'nomeFantasia',
            url: 'Empresa/ConsultarGridEmpresaCombo',
            paramsMethod: function () {
                return {}
            }
        }
    }
})();
