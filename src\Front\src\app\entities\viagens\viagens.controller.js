(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('ViagensController', ViagensController);

        ViagensController.inject = ['BaseService', '$rootScope', 'toastr', '$scope', 'PersistentDataService', '$timeout', '$state', 'PERFIL_ADMINISTRADOR'];

    function ViagensController(BaseService, $rootScope, toastr, $scope, PersistentDataService, $timeout, $state, PERFIL_ADMINISTRADOR) {
        var vm = this;
        vm.headerItems = [{
            name: 'Movimentações'
        }, {
            name: 'Painel de pagamento viagem' 
        }];

        vm.codViagemInterno = null;

        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.administrador == true;
        };

        vm.dateOptions = {
            timePicker: false,
            applyClass: 'btn-primary',
            locale: {
                applyLabel: "Aplicar",
                fromLabel: "De",
                format: "DD/MM/YYYY",
                toLabel: "Até",
                cancelLabel: 'Cancelar',
                customRangeLabel: 'Período',
                monthNames: ['Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho', 'Julho', 'Agosto', 'Setembro',
                    'Outubro', 'Novembro', 'Dezembro'
                ],
                daysOfWeek: ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb']
            },
            ranges: {
                'Últimos 1 dias': [moment().add(-1, 'days'), moment()],
                'Últimos 7 dias': [moment().add(-7, 'days'), moment()]
            }
        };

        vm.date = {
            startDate: moment().add(-1, 'days'),
            endDate: moment()
        };

        vm.gridOptions = {
            data: [],
            onRegisterApi: BaseService.dataGrid.defaultOnRegisterApi,
            dataSource: {
                url: "Viagem/ConsultarGridViagem",
                params: function () {
                    return {
                        dataInicial: vm.date.startDate.format('DD/MM/YYYY').toString(),
                        dataFinal: vm.date.endDate.format('DD/MM/YYYY').toString(),
                        CodViagemInterno: vm.codViagemInterno,
                        EmpresaId: vm.consultaEmpresa.selectedValue
                    }
                },
            },
            columnDefs: [{
                name: 'Ações',
                width: "90",
                cellTemplate: '<div ng-if="!row.groupHeader" class="ui-grid-cell-contents" align="center" title="TOOLTIP">\
                        <button tooltip-placement="right" uib-tooltip="Visualizar Pagamentos"\
                         type="button" ui-sref="viagens.pagamentos-viagem({link: row.entity.viagemId})"\
                         ng-class="{ \'btn btn-xs btn-info\': true }">\
                     <i class="fa fa-eye"></i>\
                    </button>\
                    </div>\
                    </div>'
            },{
                name: 'Código',
                width: '*',
                minWidth: 120,
                type: 'number',
                field: 'viagemId',
                serverField: 'id'
            }, {
                name: 'Viagem Externo Id', 
                width: '*',           
                minWidth: 150,
                type: 'number',
                field: 'viagemExternoId'
            }, {
                name: 'Status',
                displayName: 'Status',
                width: '*',                 
                minWidth: 130,
                enum: true,
                enumTipo: 'EStatusViagem',
                type: 'text',
                field: 'status'
            }, {
                name: 'CPF Proprietario',                
                displayName: 'CPF Proprietario',   
                width: '*',               
                minWidth: 150,
                type: 'text',
                field: 'cpfCnpjProprietario'
            }, {
                name: 'CPF Motorista',               
                displayName: 'CPF Motorista',  
                width: '*',               
                minWidth: 150,
                type: 'text',
                field: 'cpfCnpjMotorista'
            }, {
                name: 'RazaoSocialEmpresa',
                displayName: 'Razão Social Empresa',
                width: '*',                 
                minWidth: 250,
                type: 'text',
                field: 'razaoSocialEmpresa'
            },{
                name: 'CNPJ Empresa',               
                displayName: 'CNPJ Empresa', 
                width: '*',               
                minWidth: 150,
                type: 'text',
                field: 'cnpjEmpresa'
            }, {
                displayName: 'CIOT', 
                name: 'Ciot',
                width: '*',                
                minWidth: 200,
                type: 'text',
                field: 'ciot'
            }, {
                displayName: 'Código natureza carga', 
                name: 'naturezaCarga',
                width: '*',                
                minWidth: 100,
                type: 'text',
                field: 'naturezaCarga',
                serverField: 'CodigoNaturezaCarga'
            }, {
                displayName: 'Peso carga', 
                name: 'pesoCarga',
                width: '*',                
                minWidth: 100,
                type: 'text',
                field: 'pesoCarga',
                serverField: 'PesoCarga'
            }, {
                name: 'DataCadastro',
                displayName: 'Data Cadastro',                
                width: "*",
                minWidth: 150,
                field: 'dataCadastro',
                type: 'date',
                enableFiltering: false
            }, {
                name: 'DataAlteracao',
                displayName: 'Data Alteração',                
                width: "*",
                minWidth: 150,
                field: 'dataAlteracao',
                type: 'date',
                enableFiltering: false
            }, {
                name: 'DataBaixa',
                displayName: 'Data Baixa',                
                width: "*",
                minWidth: 150,
                field: 'dataBaixa',
                type: 'date',
                enableFiltering: false
            }, 
        
            ]
        };

        vm.consultaEmpresa = {
            columnDefs: [{
                name: 'Cód.',
                field: 'id',
                width: 60,
                type: 'number',
                primaryKey: true
            }, {
                name: 'Nome Fantasia',
                field: 'nomeFantasia',
                width: '*'
            }, {
                name: 'Razão Social',
                field: 'razaoSocial',
                width: '*'
            }, {
                name: 'Email',
                field: 'email',
                width: 120
            }],
            desiredValue: 'id',
            desiredText: 'nomeFantasia',
            url: 'Empresa/ConsultarGridEmpresaCombo',
            paramsMethod: function () {
                return {}
            }
        };

        var selfScope = PersistentDataService.get('ViagensController');
        var filho = PersistentDataService.get('PagamentosViagemController');
        if (angular.isDefined(filho)) {
            $timeout(function () {
                $state.go('viagens.pagamentos.pagamentos-viagem', {
                    link: filho.data.viagem.viagemId > 0 ? 'editar' : 'novo'
                });
            }, 50);
        } else if (angular.isDefined(selfScope)) {
            angular.extend(vm, selfScope.data);

            $timeout(function () {
                vm.gridOptions.dataSource.refresh();
            }, 450);
        }

    }
})();