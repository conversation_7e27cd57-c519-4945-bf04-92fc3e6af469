<div class="form-horizontal">
    <hr />
    <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3 col-lg-3">
                        Código:
                    </label>
                    <div class="input-group col-xs-12 col-sm-3 col-md-3 col-lg-3">
                        <input type="text" ng-model="vm.parametro.id" class="form-control" disabled value="{{vm.isNew()}}" />
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-2 col-md-4 col-lg-3">
                        <span class="text-danger mr-5">*</span>Tipo de parâmetro:
                    </label>
                    <div class="input-group col-xs-12 col-sm-10 col-md-8 col-lg-9">
                        <ui-select
                            name="tipoParametros"
                            ats-ui-select-validator
                            validate-on="blur"
                            ng-model="vm.parametro.tipoParametros"
                            required-message="'Tipo de parâmetro é obrigatório'".
                            ng-change="vm.verificarTipoParametro()"
                            ng-disabled="!vm.isNew()"
                            required>
                            <ui-select-match>
                                <span>{{$select.selected.descricao}}</span>
                            </ui-select-match>
                            <ui-select-choices repeat="ex.data as ex in vm.cmbTipoParametro | propsFilter: {descricao: $select.search}">
                                <div ng-bind-html="ex.descricao | highlight: $select.search"></div>
                            </ui-select-choices>
                        </ui-select>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6" ng-show="vm.exibirTipoValor">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-2 col-md-4 col-lg-3">
                        <span class="text-danger mr-5">*</span>Tipo de valor:
                    </label>
                    <div class="input-group col-xs-12 col-sm-10 col-md-8 col-lg-9">
                        <ui-select
                            name="tipoValor"
                            ats-ui-select-validator
                            validate-on="blur"
                            ng-model="vm.parametro.tipoValor"
                            required-message="'Tipo de valor é obrigatório'"
                            ng-change="vm.clearValor()"
                            ng-disabled="true"
                            required>
                            <ui-select-match>
                                <span>{{$select.selected.descricao}}</span>
                            </ui-select-match>
                            <ui-select-choices repeat="ex.data as ex in vm.cmbTipoValor | propsFilter: {descricao: $select.search}">
                                <div ng-bind-html="ex.descricao | highlight: $select.search"></div>
                            </ui-select-choices>
                        </ui-select>
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group" ng-show="vm.exibirTextBox">
                    <label class="col-xs-12 col-sm-3 col-md-3 col-lg-3 control-label">
                        <span class="text-danger mr-5">*</span>Valor
                        <span ng-if="vm.parametro.tipoParametros == 'IntervaloMinimoRetentativaCancelamentoPagamentoFrete'" class="text-danger mr-5">(Minutos)</span>
                        <span ng-if="vm.parametro.tipoParametros == 'LimiteMaximoRetentativaFrete'" class="text-danger mr-5">(Nº)</span>
                        <span ng-if="vm.parametro.tipoParametros == 'CodigoLinkCiot'" class="text-danger mr-5">(http)</span>
                        <span ng-if="vm.parametro.tipoParametros == 'CodigoLinkEmpresa'" class="text-danger mr-5">(http)</span>
                        <span ng-if="vm.parametro.tipoParametros == 'CodigoPeriodoMaximoProcessamento'" class="text-danger mr-5">(Minutos)</span>
                        <span ng-if="vm.parametro.tipoParametros == 'HorarioEnvioEmailGestorAbastecimentosMovida'" class="text-danger mr-5">(Horas)</span>
                        <span ng-if="vm.parametro.tipoParametros == 'CodigoTipoEmissaoCiot'" class="text-danger mr-5">(Código)</span>
                        <span ng-if="vm.parametro.tipoParametros == 'CodigoContaTransferenciaValorRetencao'" class="text-danger mr-5">(Código)</span>
                        <span ng-if="vm.parametro.tipoParametros == 'CodigoContaTransferenciaTarifaValorRetencao'" class="text-danger mr-5">(Código)</span>
                        <span ng-if="vm.parametro.tipoParametros == 'PrazoMaximaParaCancelamentoPagamentoFrete'" class="text-danger mr-5">(Dias)</span>
                        <span ng-if="vm.parametro.tipoParametros == 'LimiteMaximoRetentativaCancelamentoPagamentoFrete'" class="text-danger mr-5">(Nº)</span>
                        <span ng-if="vm.parametro.tipoParametros == 'PeriodoMaximoInatividadePortador'" class="text-danger mr-5">(Dias)</span>
                        <span ng-if="vm.parametro.tipoParametros == 'PeriodoMaximoInatividadeSenhaProvisoria'" class="text-danger mr-5">(Min)</span>
                        <span ng-if="vm.parametro.tipoParametros == 'CodigoContaCorrenteReceita'" class="text-danger mr-5">(Código)</span>
                        <span ng-if="vm.parametro.tipoParametros == 'PeriodoDuracaoSenha'" class="text-danger mr-5">(Dias)</span>
                        <span ng-if="vm.parametro.tipoParametros == 'TempoMaximoUsuarioInativo'" class="text-danger mr-5">(Minutos)</span>
                        <span ng-if="vm.parametro.tipoParametros == 'TempoRetroativoPixDuplicado'" class="text-danger mr-5">(Minutos)</span>
                        <span ng-if="vm.parametro.tipoParametros == 'DiasRetroativosGerarReceita'" class="text-danger mr-5">(Dias)</span>
                        <span ng-if="vm.parametro.tipoParametros == 'MargemErroArredondamentoXmlProtocolo'" class="text-danger mr-5">(Decimal)</span>
                        <span ng-if="vm.parametro.tipoParametros == 'TempoEsperaSegundosPix'" class="text-danger mr-5">(Segundos)</span>
                        <span ng-if="vm.parametro.tipoParametros == 'MargemArredondamentoCasasDecimaisLitragemXml'" class="text-danger mr-5">(Decimal)</span>
                        <span ng-if="vm.parametro.tipoParametros == 'MargemErroTotalAbastecimentoXmlProtocolo'" class="text-danger mr-5">(Decimal)</span>
                        :
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-9 col-lg-9" 
                        ng-if="vm.parametro.tipoValor == 'String' 
                                || vm.parametro.tipoValor == 'Criptografia' 
                                || !vm.parametro.tipoValor">
                        <input type="text" 
                            maxlength="vm.parametro.tipoValor === 'String' ? 100 : 300"
                            ng-required="vm.parametro.tipoParametros != 'CodigoReenvioPagamentoEvento' 
                                    && vm.parametro.tipoParametros != 'ConfiguracaoDeSLA' 
                                    && vm.parametro.tipoParametros != 'AprovacaoAutomaticaPrecoCombustivel'
                                    && vm.parametro.tipoParametros != 'ConfiguracaoMonitoramentoCIOT'
                                    && vm.parametro.tipoParametros != 'ConfiguracaoTelaoSaldo'
                                    && vm.parametro.tipoParametros != 'ConfiguracaoValePedagio'
                                    && vm.parametro.tipoParametros != 'ConfiguracaoQualificacaoTransacao'" 
                            ng-model="vm.parametro.valor" 
                            required-message="'Valor é obrigatório'" 
                            validate-on="blur" 
                            name="valor" 
                            class="form-control"
                        />
                    </div>
                    <div class="input-group col-xs-12 col-sm-9 col-md-9 col-lg-9" 
                        ng-if="vm.parametro.tipoValor == 'Number'">
                        <input ng-if="vm.parametro.tipoParametros !== 'HorarioEnvioEmailGestorAbastecimentosMovida'" type="text" 
                            ng-blur="vm.removeCaractereEspecial()" 
                            maxlength="100" 
                            ng-required="vm.parametro.tipoParametros != 'CodigoReenvioPagamentoEvento' 
                                    && vm.parametro.tipoParametros != 'ConfiguracaoDeSLA' 
                                    && vm.parametro.tipoParametros != 'AprovacaoAutomaticaPrecoCombustivel'
                                    && vm.parametro.tipoParametros != 'ConfiguracaoMonitoramentoCIOT'
                                    && vm.parametro.tipoParametros != 'ConfiguracaoTelaoSaldo'
                                    && vm.parametro.tipoParametros != 'ConfiguracaoValePedagio'
                                    && vm.parametro.tipoParametros != 'ConfiguracaoQualificacaoTransacao'" 
                            ng-model="vm.parametro.valor" 
                            required-message="'Valor é obrigatório'" 
                            validate-on="blur" 
                            name="valor" 
                            class="form-control" 
                            onkeypress='return event.charCode >= 48 && event.charCode <= 57' onpaste="return false" ondrop="return false"/"
                        />
                    </div>                    
                    <div class="input-group col-xs-12 col-sm-9 col-md-9 col-lg-9" 
                        ng-if="vm.parametro.tipoValor == 'Decimal'">
                        <input type="text" 
                            ng-blur="vm.removeCaractereEspecial()" 
                            maxlength="100" 
                            ng-required="vm.parametro.tipoParametros != 'CodigoReenvioPagamentoEvento' 
                                    && vm.parametro.tipoParametros != 'ConfiguracaoDeSLA' 
                                    && vm.parametro.tipoParametros != 'AprovacaoAutomaticaPrecoCombustivel'
                                    && vm.parametro.tipoParametros != 'ConfiguracaoMonitoramentoCIOT'
                                    && vm.parametro.tipoParametros != 'ConfiguracaoTelaoSaldo'
                                    && vm.parametro.tipoParametros != 'ConfiguracaoValePedagio'
                                    && vm.parametro.tipoParametros != 'ConfiguracaoQualificacaoTransacao'" 
                            ng-model="vm.parametro.valor" 
                            required-message="'Valor é obrigatório'" 
                            validate-on="blur" 
                            name="valor" 
                            class="form-control" 
                            ui-number-mask="3"
                            onkeypress='return event.charCode >= 48 && event.charCode <= 57' onpaste="return false" ondrop="return false"/"
                        />
                    </div>                                     
                    <div class="input-group col-xs-12 col-sm-9 col-md-9 col-lg-9">
                        <input ng-if="vm.parametro.tipoParametros === 'HorarioEnvioEmailGestorAbastecimentosMovida'" type="time" step="3600000"
                            ng-model="vm.parametro.valor" 
                            required-message="'Valor é obrigatório'" 
                            required
                            validate-on="blur" 
                            name="valor" 
                            class="form-control"
                        />
                    </div>                    
                </div>
                <div class="form-group" 
                    ng-show="vm.parametro.tipoParametros === 'CodigoReenvioPagamentoEvento' 
                            || vm.parametro.tipoParametros === 'AprovarPagamentosAutomaticamente' 
                            || vm.parametro.tipoParametros === 'ForcarGeracaoPagamento' 
                            || vm.parametro.tipoParametros === 'VerificaContigencia' 
                            || vm.parametro.tipoParametros === 'EmailSsl'">
                    <label class="control-label col-xs-12 col-md-3">Ativo: </label>
                    <div class="input-group col-xs-12 col-md-9">
                        <toggle-switch 
                            on-label="Sim" 
                            off-label="Não" 
                            ng-required="vm.parametro.tipoParametros === 'CodigoReenvioPagamentoEvento' 
                                    || vm.parametro.tipoParametros === 'AprovarPagamentosAutomaticamente' 
                                    || vm.parametro.tipoParametros === 'ForcarGeracaoPagamento' 
                                    || vm.parametro.tipoParametros === 'VerificaContigencia' 
                                    || vm.parametro.tipoParametros === 'EmailSsl'" 
                            ng-model="vm.parametro.valor" 
                            class="switch-mini">
                        </toggle-switch>
                    </div>
                </div>
            </div>
        </div>            
    </div>
</div>