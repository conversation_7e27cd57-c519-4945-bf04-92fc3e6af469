(function () {
    'use strict';

    angular.module('bbcWeb.autorizacao-abastecimento.state', []).config(routeConfig);

    function routeConfig($urlRouterProvider, $stateProvider) {
        $stateProvider.state('autorizacao-abastecimento', {
            abstract: true,
            url: "/autorizacao-abastecimento",
            templateUrl: "app/layout/content.html"
        }).state('autorizacao-abastecimento.index', {
            url: '/index',
            templateUrl: 'app/entities/autorizacao-abastecimento/autorizacao-abastecimento.html'
        });
    }
})();