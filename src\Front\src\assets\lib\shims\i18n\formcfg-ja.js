webshims.validityMessages.ja={typeMismatch:{email:"\u30e1\u30fc\u30eb\u30a2\u30c9\u30ec\u30b9\u304c\u6b63\u3057\u304f\u3042\u308a\u307e\u305b\u3093\u3002",url:"URL \u304c\u6b63\u3057\u304f\u3042\u308a\u307e\u305b\u3093\u3002"},badInput:{number:"\u5024\u304c\u7121\u52b9\u3067\u3059\u3002",date:"\u5024\u304c\u7121\u52b9\u3067\u3059\u3002",time:"\u5024\u304c\u7121\u52b9\u3067\u3059\u3002",range:"\u5024\u304c\u7121\u52b9\u3067\u3059\u3002","datetime-local":"\u5024\u304c\u7121\u52b9\u3067\u3059\u3002"},tooLong:"\u5024\u304c\u7121\u52b9\u3067\u3059\u3002",patternMismatch:"\u5165\u529b\u3055\u308c\u305f\u5024\u304c\u30d5\u30a3\u30fc\u30eb\u30c9\u306b\u6307\u5b9a\u3055\u308c\u305f\u66f8\u5f0f\u3068\u7570\u306a\u308a\u307e\u3059: {%title}",valueMissing:{defaultMessage:"\u3053\u306e\u30d5\u30a3\u30fc\u30eb\u30c9\u306f\u5165\u529b\u5fc5\u9808\u3067\u3059\u3002",checkbox:"\u30c1\u30a7\u30c3\u30af\u30dc\u30c3\u30af\u30b9\u306b\u30c1\u30a7\u30c3\u30af\u3092\u5165\u308c\u3066\u304f\u3060\u3055\u3044\u3002",select:"\u30ea\u30b9\u30c8\u304b\u3089\u9805\u76ee\u3092\u9078\u629e\u3057\u3066\u304f\u3060\u3055\u3044\u3002",radio:"\u3044\u305a\u308c\u304b\u306e\u30aa\u30d7\u30b7\u30e7\u30f3\u3092\u9078\u629e\u3057\u3066\u304f\u3060\u3055\u3044\u3002"},rangeUnderflow:{defaultMessage:"\u5024\u306f {%min} \u4ee5\u4e0a\u306b\u3059\u308b\u5fc5\u8981\u304c\u3042\u308a\u307e\u3059\u3002",date:"\u5024\u306f {%min} \u4ee5\u4e0a\u306b\u3059\u308b\u5fc5\u8981\u304c\u3042\u308a\u307e\u3059\u3002",time:"\u5024\u306f {%min} \u4ee5\u4e0a\u306b\u3059\u308b\u5fc5\u8981\u304c\u3042\u308a\u307e\u3059\u3002","datetime-local":"\u5024\u306f {%min} \u4ee5\u4e0a\u306b\u3059\u308b\u5fc5\u8981\u304c\u3042\u308a\u307e\u3059\u3002"},rangeOverflow:{defaultMessage:"\u5024\u306f {%max} \u4ee5\u4e0b\u306b\u3059\u308b\u5fc5\u8981\u304c\u3042\u308a\u307e\u3059\u3002",date:"\u5024\u306f {%max} \u4ee5\u4e0b\u306b\u3059\u308b\u5fc5\u8981\u304c\u3042\u308a\u307e\u3059\u3002",time:"\u5024\u306f {%max} \u4ee5\u4e0b\u306b\u3059\u308b\u5fc5\u8981\u304c\u3042\u308a\u307e\u3059\u3002","datetime-local":"\u5024\u306f {%max} \u4ee5\u4e0b\u306b\u3059\u308b\u5fc5\u8981\u304c\u3042\u308a\u307e\u3059\u3002"},stepMismatch:"\u5024\u304c\u7121\u52b9\u3067\u3059\u3002"},webshims.formcfg.ja={numberFormat:{".":".",",":","},numberSigns:".",dateSigns:"/",timeSigns:":. ",dFormat:"/",patterns:{d:"yy/mm/dd"},date:{closeText:"\u9589\u3058\u308b",prevText:"&#x3C;\u524d",nextText:"\u6b21&#x3E;",currentText:"\u4eca\u65e5",monthNames:["1\u6708","2\u6708","3\u6708","4\u6708","5\u6708","6\u6708","7\u6708","8\u6708","9\u6708","10\u6708","11\u6708","12\u6708"],monthNamesShort:["1\u6708","2\u6708","3\u6708","4\u6708","5\u6708","6\u6708","7\u6708","8\u6708","9\u6708","10\u6708","11\u6708","12\u6708"],dayNames:["\u65e5\u66dc\u65e5","\u6708\u66dc\u65e5","\u706b\u66dc\u65e5","\u6c34\u66dc\u65e5","\u6728\u66dc\u65e5","\u91d1\u66dc\u65e5","\u571f\u66dc\u65e5"],dayNamesShort:["\u65e5","\u6708","\u706b","\u6c34","\u6728","\u91d1","\u571f"],dayNamesMin:["\u65e5","\u6708","\u706b","\u6c34","\u6728","\u91d1","\u571f"],weekHeader:"\u9031",firstDay:0,isRTL:!1,showMonthAfterYear:!0,yearSuffix:"\u5e74"}};