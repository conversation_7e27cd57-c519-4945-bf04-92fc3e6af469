(function () {
    'use strict';

    angular.module('bbcWeb.percentual-transferencia.state', []).config(routeConfig);

    function routeConfig($urlRouterProvider, $stateProvider) {
        $stateProvider.state('percentual-transferencia', {
            abstract: true,
            url: "/percentual-transferencia",
            templateUrl: "app/layout/content.html"
        }).state('percentual-transferencia.index', {
            url: '/index',
            templateUrl: 'app/entities/percentual-transferencia/percentual-transferencia.html'
        }).state('percentual-transferencia.percentual-transferencia-crud', {
            url: '/:link',
            templateUrl: 'app/entities/percentual-transferencia/percentual-transferencia-crud.html'
        });
    }
})();