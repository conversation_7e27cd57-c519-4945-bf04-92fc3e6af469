(function () {
    'use strict';

    angular.module('bbcWeb.menu.state', []).config(routeConfig);

    function routeConfig( $stateProvider) {
        $stateProvider
            .state('menu', {
                url: "/menu",
                abstract: true,  
                templateUrl: "app/layout/content.html"
            })
            .state('menu.index', {
                url: "/index",
                templateUrl: "app/entities/menu/menu.html"
            }).state('menu.crud', {
                url: '/:link',  
                templateUrl: "app/entities/menu/menu-crud.html"
            });
    }
})();