webshims.validityMessages["pt-BR"]={typeMismatch:{email:"Por favor informe o e-mail.",url:"Por favor informe a URL."},badInput:{defaultMessage:"Valor inv\xe1lido.",number:"Valor inv\xe1lido.",date:"Data inv\xe1lida.",time:"Hora inv\xe1lida.",range:"Valor inv\xe1lido.","datetime-local":"Data inv\xe1lida."},tooLong:"Valor inv\xe1lido.",patternMismatch:"Por favor informe no formato: {%title}.",valueMissing:{defaultMessage:"Por favor preencha este campo.",checkbox:"Por favor selecione esta caixa se deseja continuar.",select:"Por favor selecione um item da lista.",radio:"Por favor selecione uma destas op\xe7\xf5es."},rangeUnderflow:{defaultMessage:"O valor tem de ser superior ou igual a {%min}.",date:"O valor tem de ser superior ou igual a {%min}.",time:"O valor tem de ser superior ou igual a {%min}.","datetime-local":"O valor tem de ser superior ou igual a {%min}."},rangeOverflow:{defaultMessage:"O valor tem de ser inferior ou igual a {%max}.",date:"O valor tem de ser inferior ou igual a {%max}.",time:"O valor tem de ser inferior ou igual a {%max}.","datetime-local":"O valor tem de ser inferior ou igual a {%max}."},stepMismatch:"Valor inv\xe1lido."},webshims.formcfg["pt-BR"]={numberFormat:{",":".",".":","},numberSigns:".",dateSigns:"/",timeSigns:":. ",dFormat:"/",patterns:{d:"dd/mm/yy"},month:{currentText:"Este m\xeas"},time:{currentText:"Agora"},date:{closeText:"Feito",clear:"Limpa",prevText:"Pr\xf3ximo",nextText:"Anterior",currentText:"Hoje",monthNames:["Janeiro","Fevereiro","Mar\xe7o","Abril","Maio","Junho","Julho","Agosto","Setembro","Outubro","Novembro","Dezembro"],monthNamesShort:["Jan","Fev","Mar","Abr","Mai","Jun","Jul","Ago","Set","Out","Nov","Dez"],dayNames:["Domingo","Segunda","Ter\xe7a","Quarta","Quinta","Sexta","S\xe1bado"],dayNamesShort:["Dom","Seg","Ter","Qua","Qui","Sex","Sab"],dayNamesMin:["Do","Se","Te","Qa","Qi","Se","Sa"],weekHeader:"Sem",firstDay:0,isRTL:!1,showMonthAfterYear:!1,yearSuffix:""}};