(function () {
    'use strict';

    angular.module('bbcWeb.cfop.state', []).config(routeConfig);

    function routeConfig($urlRouterProvider, $stateProvider) {
        $stateProvider.state('cfop', {
            abstract: true,
            url: "/cfop",
            templateUrl: "app/layout/content.html"
        }).state('cfop.index', {
            url: '/index',
            templateUrl: 'app/entities/cfop/cfop.html'
        });
    }
})();