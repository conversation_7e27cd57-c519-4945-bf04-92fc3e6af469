(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('IntegrarContaController', IntegrarContaController);

    IntegrarContaController.inject = ['BaseService', '$rootScope', 'toastr', '$scope', '$state', 'PERFIL_ADMINISTRADOR'];

    function IntegrarContaController(BaseService, $rootScope, toastr, $scope, $state, PERFIL_ADMINISTRADOR) {
        var vm = this;
        vm.ativarTodasConta = false;
        vm.ImportarContasExcel = "";
        vm.isSaving = true;
        vm.headerItems = [{
            name: 'Cadastros'
        }, {
            name: 'Integrar contas',
            link: 'integrar-contas.index'
        }];

        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.perfil == PERFIL_ADMINISTRADOR;
        };

        vm.gridOptions = {
            data: [],
            onRegisterApi: BaseService.dataGrid.defaultOnRegisterApi,
            dataSource: {
                url: "IntegrarConta/ConsultarGrid",
                params: function() {
                    return { 
                        AtivarTodasConta : vm.ativarTodasConta
                    }
                },
                events: {
                    onDataBound: function (data) {
                        if (angular.isArray(data))
                            data.forEach(function (item) {
                                if (angular.isArray(data) && angular.isArray(vm.contas))
                                    vm.contas.forEach(function (item2) {
                                        if (item.id == item2.Id)
                                            if  (vm.clicouNoCheckBoxConta == 1){
                                                item.ativo = item2.Ativo;
                                            }else{
                                                item.ativo == vm.ativarTodasConta;
                                            }
                                            
                                    });
                            });
                    }
                }
            },
            columnDefs: [{
                name: ' ',
                width: '2%',
                cellTemplate: '<input type=\"checkbox\" ng-show="grid.appScope.vm.isContaMarcada(row.entity)" ng-model=\"row.entity.ativo\"  ng-click="grid.appScope.vm.onClickDesmarcarConta(row.entity)" ng-false-value=\'false\' />\
                    <input type=\"checkbox\" ng-show="!grid.appScope.vm.isContaMarcada(row.entity)" ng-model=\"row.entity.ativo\"  ng-click="grid.appScope.vm.onClickMarcarConta(row.entity)" ng-true-value=\'true\'/>'
            }, {
                name: 'Código',
                width: '6%',
                type: 'number',
                primaryKey: true,
                field: 'id',
                minWidth: '80'
            }, {
                name: 'Nome',
                width: '45%',
                field: 'nome',
                minWidth: '150'
            }, {
                name: 'CPF/CNPJ',
                displayName: 'CPF/CNPJ',
                width: '15%',
                field: 'cpfCnpj',
                minWidth: '150'
            }, {
                name: 'Agência',
                width: '15%',
                field: 'agencia',
                minWidth: '120'
            }, {
                name: 'Conta',
                width: '15%',
                field: 'conta',
                minWidth: '120'
            }]
        };

        $scope.excelExport= function (event) {
            var input = event.target;
            var reader = new FileReader();
            reader.onload = function(){
                var fileData = reader.result;
                var wb = XLSX.read(fileData, {type : 'binary'});
        
                wb.SheetNames.forEach(function(sheetName){
                var rowObj =XLSX.utils.sheet_to_row_object_array(wb.Sheets[sheetName]);
                var jsonObj = JSON.stringify(rowObj);
                vm.ImportarContasExcel= jsonObj;
               // console.log(jsonObj)
                })
            };
            reader.readAsBinaryString(input.files[0]);
        };


        vm.ImportarContas = function(){
            if (!vm.ImportarContasExcel == ""){
                if  (vm.ImportarContasExcel == "[]"){
                    toastr.error('Arquivo .xml inválido, insira um arquivo válido.');
                    vm.ImportarContasExcel = "";
                    document.getElementById("readfile").value = "";
                    vm.gridApi.grid.options.dataSource.refresh();
                    vm.isSaving = true;
                }else{
                    vm.isSaving = false;
                    BaseService.post('IntegrarConta', 'ImportarContasExcel', vm.ImportarContasExcel).then(function (response) {
                        if (response && !response.success){
                            vm.ImportarContasExcel = "";
                            document.getElementById("readfile").value = "";
                            vm.gridApi.grid.options.dataSource.refresh();
                            vm.isSaving = true;
                            return toastr.error(response.message);
                        }
                        else {
                            toastr.success('Contas integradas com sucesso!');
                            document.getElementById("readfile").value = "";
                            vm.ImportarContasExcel = "";
                            vm.isSaving = true;
                            vm.gridApi.grid.options.dataSource.refresh();
                        }
                    });
                }
            }else{
                toastr.error("Escolha o arquivo a ser importado!")
            }
        }

        vm.contas = [];
        vm.clicouNoCheckBoxConta = 0;

        vm.onClickMarcarConta = function (row) {
            row.IsChecked = true;
            vm.clicouNoCheckBoxConta = 1;

            var newItem = {
                Id: row.id,
                IdBanco: row.idBanco,
                CpfCnpj: row.cpfCnpj,
                Conta: row.conta,
                Agencia: row.agencia,
                DigitoVerificadorConta: row.digitoVerificadorConta,
                DigitoVerificadorAgencia: row.digitoVerificadorAgencia,
                Ativo: true
            };
            vm.contas.push(newItem);
        };

        vm.isContaMarcada = function (row) {
            var idxOf = vm.contas.indexOf(row.Id);
            if (idxOf > -1 || row.IsChecked)
                return true;
            return row.ativo;
        };

        vm.onClickDesmarcarConta = function (row) {
            vm.clicouNoCheckBoxConta = 1;

            if  (vm.ativarTodasConta){
                var newItem = {
                    Id: row.id,
                    IdBanco: row.idBanco,
                    CpfCnpj: row.cpfCnpj,
                    Conta: row.conta,
                    Agencia: row.agencia,
                    DigitoVerificadorConta: row.digitoVerificadorConta,
                    DigitoVerificadorAgencia: row.digitoVerificadorAgencia,
                    Ativo: false
                };
                vm.contas.push(newItem);
            }else{
                vm.contas = vm.contas.filter(function(el) { 
                    return el.Id !== row.id; 
                }); 
            }

            row.IsChecked = false;
        };

        vm.inativarAtivarTodasContas = function(){
            vm.clicouNoCheckBoxConta = 0;
            if  (vm.ativarTodasConta == false){
                vm.ativarTodasConta = true;
                vm.contas = [];
                if  (angular.isDefined(vm.gridApi)){
                    vm.gridApi.grid.options.dataSource.refresh();
                }
            }else{
                vm.ativarTodasConta = false;
                vm.contas = [];
                if  (angular.isDefined(vm.gridApi)){
                    vm.gridApi.grid.options.dataSource.refresh();
                }
            }
        }

        vm.enviarContas = function(){
            vm.enviarContasJsl = {
                AtivarTodasConta : vm.ativarTodasConta,
                Contas: vm.contas != null ? vm.contas : vm.contas = []
            }
            vm.isSaving = false;
            BaseService.post('IntegrarConta', 'EnviarContas', vm.enviarContasJsl).then(function (response) {
                if (response && !response.success){
                    vm.isSaving = true;
                    return toastr.error(response.message);
                }
                else {
                    toastr.success('Contas integradas com sucesso!');
                    document.getElementById("readfile").value = "";
                    vm.ImportarContasExcel = "";
                    vm.contasParaEnvio = undefined;
                    vm.contas = [];
                    vm.ativarTodasConta = false;
                    vm.isSaving = true;
                    vm.gridApi.grid.options.dataSource.refresh();                
                }
            });
        }
    }
})();