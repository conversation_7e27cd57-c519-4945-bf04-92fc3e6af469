(function() {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('PortadorModalDadosCartaoCrudController', PortadorModalDadosCartaoCrudController);

        PortadorModalDadosCartaoCrudController.$inject = ['$uibModalInstance', 'toastr', 'BaseService', '$timeout', 'contaId'];

    function PortadorModalDadosCartaoCrudController($uibModalInstance,toastr, BaseService, $timeout, contaId) {
        var vm = this;
        vm.modal = {};

        init();

        function init() {
            BaseService.get('Portador', 'ConsultaCartao', {
                idConta: contaId
            }).then(function (response) {
                if (!response.success) {
                    toastr.error(response.message);
                    $uibModalInstance.close();
                    return;
                } else {
                    vm.modal = response.data;

                    if (response.data.idStatus == 1){
                        vm.modal.status = 'Normal';
                    }
                    else {
                        vm.modal.status = 'Bloqueado';
                    }
                    vm.modal.dataEmissao = new Date(vm.modal.dataEmissao);
                    vm.modal.dataValidade = new Date(vm.modal.dataValidade);
                }
            });
        }

        vm.DesbloquearCartao = function () {
            BaseService.post("Portador", "DesbloquearCartao", {
                idCartao: vm.modal.id
            }).then(function (response) {
                if (response.success) {
                    toastr.success('Cartao ativado com sucesso!');
                    vm.modal.status = "Normal";
                } else {
                    toastr.error(response.message);
                }
            });
        }

        vm.BloquearCartao = function () {
            BaseService.post("Portador", "BloquearCartao", {
                idCartao: vm.modal.id, motivo: vm.modal.motivoBloqueio
            }).then(function (response) {
                if (response.success) {
                    toastr.success('Cartao bloqueado com sucesso!');
                    vm.modal.status = "Bloqueado";
                } else {
                    toastr.error(response.message);
                }
            });
        }
    }
})();