(function () {
    'use strict';

    angular.module('bbcWeb').controller('EmpresaCrudController', EmpresaCrudController);

    EmpresaCrudController.$inject = [
        'toastr',
        '$rootScope',
        'BaseService',
        '$state',
        '$stateParams',
        '$scope',
        '$timeout',
        'PersistentDataService',
        '$uibModal',
        'URL_SERVER_DEV'
    ];

    function EmpresaCrudController(
        toastr,
        $rootScope,
        BaseService,
        $state,
        $stateParams,
        $scope,
        $timeout,
        PersistentDataService,
        $uibModal
    ) {
        var vm = this;
        const CONTA_CONTROLLER = "Conta";
        vm.empresa = {};
        vm.empresa.impostoIRRF = 0;
        vm.empresa.impostoCSLL = 0;
        vm.empresa.impostoCOFINS = 0;
        vm.empresa.impostoPIS = 0;
        vm.empresa.repLegaisList = [];
        vm.empresa.empresaCfop = [];
        vm.empresa.cobrancaTarifa = true;
        vm.empresa.liberaBloqueioSPD = true;
        vm.empresa.ciotClienteIps = [];
        vm.empresa.utilizaTarifaEmpresa = true;
        vm.empresa.utilizaTarifaEmpresaPagamentoPedagio = true;
        vm.empresa.grupoEmpresaId = null;
        vm.empresa.permitirPagamentoValePedagio = false;
        vm.empresa.habilitaReprocessamentoValePedagio = false;
        vm.empresa.cobrarTarifaBbcValePedagio = false;
        vm.empresa.habilitaPainelSaldo = false;


        // Tarifas
        vm.empresa.qtdMensalSemTaxaPix = 0;
        vm.empresa.valorTarifaPix = 0;
        vm.empresa.valorTarifaBbc = 0;
        vm.empresa.porcentagemTarifaServiceValePedagio = 0;

        vm.representante = {};
        vm.conta = {};
        vm.contas = [];
        vm.extratoRelatorio = {};
        vm.UfEmissao = [];
        vm.saldo = "R$ 0,00";

        vm.estados = [];
        vm.cidades = [];
        vm.estadosDisabled = true;
        vm.cidadesDisabled = true;
        vm.tipoExtrato = [{
            descricao: "Processadas",
            id: 1
        }, {
            descricao: "Não Processadas",
            id: 0
        }]
        vm.tipoExtratoSelected = 1;
        vm.desabilitarBtnRelatorio = false;
        vm.carregandoEdit = false;
        vm.saving = false;
        vm.empresa.debitoProtocolo == false;
        vm.empresa.debitoPrazo == false;
        vm.disablePrazo = false;
        vm.isFieldContaDisabled = false;

        vm.headerItems = [{
            name: 'Cadastro'
        }, {
            name: 'Empresa',
            link: 'empresa.index'
        }, {
            name: $stateParams.link === 'novo' ? 'Novo' : 'Editar'
        }];

        //----------------------Wizard tabs---------------------------

        vm.adminOnlyTabs = [4, 5, 6, 10];
        vm.notNewOnlyTabs = [3, 7, 8];
        vm.registraCiotOnlyTabs = [9];
        vm.lastIndex;

        vm.listaDocumentos = [];

        vm.onClickVoltar = function (wizard) {
            const currentIndex = wizard.getActivePosition();
            if (currentIndex == 1) $state.go('empresa.index');
            const nextIndex = validaAtivoIndex(currentIndex, true);
            wizard.go(nextIndex);
        }

        vm.onClickAvancar = function (wizard) {
            const currentIndex = wizard.getActivePosition();
            const nextIndex = validaAtivoIndex(currentIndex, false);
            wizard.go(nextIndex);
        }

        function validaAtivoIndex(index, voltando) {
            var nextIndex = index + (voltando ? -1 : 1);

            vm.lastIndex = getLastIndex();

            if (nextIndex > vm.lastIndex) return index;

            if (!vm.isAdmin()) {
                nextIndex = (vm.adminOnlyTabs.includes(nextIndex) ? validaAtivoIndex(nextIndex, voltando) : nextIndex)
            }
            if (vm.isNew()) {
                nextIndex = (vm.notNewOnlyTabs.includes(nextIndex) ? validaAtivoIndex(nextIndex, voltando) : nextIndex)
            }
            if (!vm.empresa.registraCiot) {
                nextIndex = (vm.registraCiotOnlyTabs.includes(nextIndex) ? validaAtivoIndex(nextIndex, voltando) : nextIndex)
            }
            return nextIndex;
        }


        vm.checkForEmailEnd = function (event) {
            if (event.key === ' ' || event.key === 'Enter') {
                vm.empresa.notificacaoContingenciaCiot = processEmails(vm.empresa.notificacaoContingenciaCiot);
            }
        };

        function processEmails(emailString) {
            if (!emailString) return '';

            // Remove espaços e quebras de linha no início e no fim da string
            emailString = emailString.trim();

            // Divide a string em partes utilizando espaços, quebras de linha ou ponto e vírgula como delimitadores
            var emails = emailString.split(/[\s;\n]+/).filter(Boolean);

            // Valida e formata os emails
            var formattedEmails = emails.map(function (email) {
                if (isValidEmail(email)) {
                    return email.trim() + ';';
                } else {
                    return email.trim();
                }
            });

            // Junta os emails formatados em uma única string
            return formattedEmails.join(' ');
        }

        function isValidEmail(email) {
            // Expressão regular para validar email
            var emailPattern = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
            return emailPattern.test(email);
        }


        function getLastIndex() {
            var lastIndex;
            if (!vm.isAdmin() && vm.isNew()) lastIndex = 2;
            if (vm.isAdmin() && vm.isNew()) lastIndex = 7;
            if (!vm.isNew()) lastIndex = 8;
            if (vm.empresa.registraCiot) lastIndex = 9;
            if (vm.isAdmin()) lastIndex = 11;
            return lastIndex;
        }

        function carregaGridExtrato() {
            return $stateParams.link !== 'novo';
        }

        $scope.$watch('vm.empresa.registraCiot', function () {
            vm.lastIndex = getLastIndex();
        })

        vm.consultarDocumentos = function () {
            BaseService.post("Empresa", "ConsultarGridDocumentosEmpresa", {
                EmpresaId: $stateParams.link == 'novo' ? null : $stateParams.link
            }).then(function (response) {
                if (!response.success) {
                    vm.listaDocumentos = [];
                    toastr.error(response.message);
                    return false;
                }

                vm.listaDocumentos = response.data.items;
                return;
            });
        };

        //----------------------End Wizard tabs---------------------------

        //---------------------Extrato-----------------------------------

        vm.gridOptionsExtrato = {
            data: [],
            onRegisterApi: BaseService.dataGrid.getGridApiRegistration("gridOptionsExtrato"),
            urlRelatorio: CONTA_CONTROLLER + '/ConsultarExtrato',
            dataSource: {
                autoBind: carregaGridExtrato(),
                url: CONTA_CONTROLLER + '/ConsultarExtrato',
                params: function () {
                    return {
                        idConta: vm.conta.contas,
                        tipoExtrato: vm.tipoExtratoSelected,
                        DataInicio: vm.date.startDate.format('DD/MM/YYYY').toString(), 
                        DataFim: vm.date.endDate.format('DD/MM/YYYY').toString()
                    };
                }
            },
            columnDefs: [
                {
                    displayName: 'Crédito/Débito',
                    name: 'flagCredito',
                    field: 'flagCredito',
                    width: 150,
                    enableColumnMenu: false,
                    pipe: function (input) {
                        return filterFlag(input)
                    },
                    cellTemplate: ' <div  style="text-align: center;">\
                                    <icon   ng-class="row.entity.flagCredito==1 ? \'text-center glyphicon glyphicon-arrow-up text-success\' : \'text-center glyphicon glyphicon-arrow-down text-danger\'">\
                                    </icon>\
                                    </div>',
                    cellClass: 'textAligncenter'
                },
                {
                    displayName: 'Descrição',
                    name: 'descricao',
                    field: 'descricaoAbreviada',
                    width: '*',
                    minWidth: 500
                },
                {
                    displayName: 'Valor',
                    name: 'valor',
                    field: 'valorBRL',
                    width: '*',
                    minWidth: 200
                },
                {
                    displayName: 'Data origem',
                    name: 'dataOrigem',
                    field: 'dataOrigem',
                    width: '*',
                    minWidth: 200
                }
            ],
            width: vm.viewportWidth,
            paginationPageSizes: [10, 25, 50],
        };

        function filterFlag(input) {
            var traducao;
            switch (input) {
                case 0:
                    traducao = "Débito"
                    break;
                case 1:
                    traducao = "Crédito"
                    break;
                default:
                    traducao = input;
            }
            return correcao
        }

        vm.atualizaGridExtrato = function () {
            vm.gridOptionsExtrato.dataSource.refresh();
        }  

        vm.abrirModalRelatorio = function (gridName, controllerPai) {
            $uibModal.open({
                animation: true,
                ariaLabelledBy: 'modal-title',
                ariaDescribedBy: 'modal-body',
                templateUrl: 'app/entities/empresa/consulta/modal-relatorios-extrato/modal-relatorios-extrato.html',
                controller: function ($uibModalInstance, $uibModalStack) {
                    var vm = this;

                    vm.modalRelatorioOptions = [{}];

                    vm.headerItems = [{ name: 'Extrato' }];

                    for (var x in controllerPai[gridName].columnDefs) {
                        vm.modalRelatorioOptions[x] = {
                            name: controllerPai[gridName].columnDefs[x].displayName,
                            field: controllerPai[gridName].columnDefs[x].field,
                            pipe: controllerPai[gridName].columnDefs[x].pipe,
                            enabled: true
                        }
                    }

                    vm.fechar = function () {
                        controllerPai.modalRelatorioOptions = vm.modalRelatorioOptions;
                        $uibModalStack.dismissAll();
                    }

                    vm.exportarRelatorio = function (extensao) {
                        if (vm.modalRelatorioOptions.filter(function (x) { return x.enabled }).length < 1) {
                            toastr.error("Escolha alguma coluna para gerar o relatório.");
                            return;
                        }
                        
                        controllerPai.modalRelatorioOptions = vm.modalRelatorioOptions;
                        controllerPai.exportarRelatorio(gridName, extensao);

                        $uibModalStack.dismissAll();
                    }

                },
                controllerAs: 'vm',
                keyboard: true,
                size: 'lg'
            }).result.then(function () {
            });
        };

        vm.dateOptions = {
            timePicker: false,
            applyClass: 'btn-primary',
            locale: {
                applyLabel: "Aplicar",
                fromLabel: "De",
                format: "DD/MM/YYYY",
                toLabel: "Até",
                cancelLabel: 'Cancelar',
                customRangeLabel: 'Período',
                monthNames: ['Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho', 'Julho', 'Agosto', 'Setembro',
                    'Outubro', 'Novembro', 'Dezembro'
                ],
                daysOfWeek: ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb']
            },
            ranges: {
                'Último dia': [moment().add(-1, 'days'), moment()],
                'Últimos 7 dias': [moment().add(-7, 'days'), moment()],
                'Último mês': [moment().subtract(1,'months').startOf('month'), moment().subtract(1,'months').endOf('month')]
            }
        };
        
        vm.date = {
            startDate: moment().add(-1, 'days'),
            endDate: moment()
        };


        vm.exportarRelatorio = function (gridName, extensao) {
            switch (extensao) {
                case 1: {
                    exportarEmExcel(gridName)
                    break;
                }
                case 2: {
                    exportarEmPdf(gridName)
                    break;
                }
                default: exportarEmPdf(gridName)
                    break;
            }
        };

        function exportarEmPdf(gridName) {
            vm.desabilitarBtnRelatorio = true;
            vm[gridName].dataSource.consultarDadosRelatorio(function (response) {
                vm.extratoRelatorio = response.data.data.items;
                $timeout(function () {
                    BaseService.exportarTabelaEmPdfAutoSize("#exportable", "Extrato", "Extrato");
                }, 500);
                vm.desabilitarBtnRelatorio = false;
            }, gridName);
        }

        

        function exportarEmExcel(gridName) {
            vm.desabilitarBtnRelatorio = true;
            vm[gridName].dataSource.consultarDadosRelatorio(function (response) {
                vm.extratoRelatorio = response.data.data.items;
                $timeout(function () {
                    BaseService.exportarTabelaEmExcel("exportable-xls", "Extrato", true)
                }, 500);
                vm.desabilitarBtnRelatorio = false;
            }, gridName);
        }

        //---------------------End Extrato-------------------------------

        //---------------------Cliente Ciot Ip----------------------------

        vm.gridOptionsCiot = {
            data: [],
            onRegisterApi: BaseService.dataGrid.getGridApiRegistration("gridOptionsCiot"),
            dataSource: { autoBind: false },
            width: vm.viewportWidth,
            columnDefs: [
                {
                    name: 'Ações',
                    minWidth: 80,
                    width: '10%',
                    enableColumnMenu: false,
                    enableFiltering: false,
                    enableSorting: false,
                    cellTemplate: '<div ng-if="!row.groupHeader" class="ui-grid-cell-contents" title="TOOLTIP">\
                                                   <button title="Editar" type="button" \
                                                       ng-click="grid.appScope.vm.modalClienteIp(row.entity, grid.appScope.$parent.vm)"\
                                                       ng-class="{ \'btn btn-xs btn-info\': true }">\
                                                       <i class="fa fa-edit"></i>\
                                                   </button>\
                                                   <button ng-if="grid.appScope.vm.isAdmin()" type="button" \
                                                       title="Permitir/Bloquear" ng-click="grid.appScope.vm.alterarStatusClienteIp(row.entity)" \
                                                       ng-class="row.entity.status == 1 ? \'btn btn-xs btn-success\' : \'btn btn-xs btn-danger\'">\
                                                       <i ng-class="row.entity.status == 1 ? \'fa fa-check\' : \'fa fa-ban\'"></i>\
                                                   </button>\
                                               </div>'
                },
                {
                    name: 'Código',
                    width: 80,
                    primaryKey: true,
                    enableFiltering: false,
                    enableSorting: false,
                    enableColumnMenu: false,
                    type: 'number',
                    field: 'id'
                },
                {
                    displayName: 'IP',
                    field: 'ip',
                    minWidth: 200,
                    enableFiltering: false,
                    enableSorting: false,
                    enableColumnMenu: false,
                    width: '*',
                },
                {
                    name: 'Status',
                    field: 'status',
                    enableFiltering: false,
                    enableSorting: false,
                    enableColumnMenu: false,
                    width: 200,
                    cellTemplate: '<div class="ui-grid-cell-contents" ng-if="!row.entity.status">Bloqueado</div>' +
                        '<div class="ui-grid-cell-contents" ng-if="row.entity.status">Permitido</div>'
                },
            ]
        };

        vm.modalClienteIp = function (clienteIp, controllerPai) {
            $uibModal.open({
                animation: true,
                ariaLabelledBy: 'modal-title',
                ariaDescribedBy: 'modal-body',
                templateUrl: 'app/entities/empresa/modal/modal-cliente-ip/modal-cliente-ip.html',
                controller: function ($uibModalInstance, $uibModalStack) {
                    var vm = this;
                    vm.empresaId = controllerPai.empresa.id;
                    vm.clienteIp = clienteIp != null ?
                        JSON.parse(JSON.stringify(clienteIp)) :
                        { id: undefined, ip: undefined, status: 1 };

                    vm.fechar = function () {
                        $uibModalStack.dismissAll();
                    }

                    vm.cancelar = function () {
                        vm.fechar();
                    }

                    vm.salvar = function (form) {
                        if (!form.$valid) {
                            toastr.error('Todos os campos obrigatórios devem ser preenchidos corretamente com seus respectivos valores');
                            return;
                        }

                        const ipRepetido = controllerPai.gridOptionsCiot.data.find(function (e) { return e.ip === vm.clienteIp.ip })
                        const indexIpExistente = controllerPai.gridOptionsCiot.data.indexOf(clienteIp);
                        const indexIpRepetido = controllerPai.gridOptionsCiot.data.indexOf(ipRepetido);

                        if (indexIpRepetido > -1 && indexIpExistente !== indexIpRepetido) {
                            toastr.error('Não é possível salvar valores de IPs repetidos.');
                            return;
                        }

                        vm.clienteIp.status = (vm.clienteIp.status === true || vm.clienteIp.status === 1) ? 1 : 0;

                        if (indexIpExistente !== -1) {
                            controllerPai.gridOptionsCiot.data[indexIpExistente] = vm.clienteIp;
                        } else {
                            controllerPai.adicionarClienteIp(vm.clienteIp);
                        }

                        vm.fechar();
                    }
                },
                controllerAs: 'vm',
                keyboard: true,
                size: 'md',
            }).result.then(function () {
            });
        }

        vm.adicionarClienteIp = function (clienteIp) {
            clienteIp.status = (clienteIp.status === 1 || clienteIp.status === true) ? 1 : 0;
            vm.gridOptionsCiot.data.push(clienteIp)
        }

        vm.alterarStatusClienteIp = function (clienteIp) {
            clienteIp.status = (clienteIp.status === 1) ? 0 : 1;
        }

        //---------------------End Cliente Ciot Ip------------------------

        vm.load = function () {
            carregarFuncoesIniciais();
        };

        function carregarFuncoesIniciais() {
            carregarEstados();
        }

        vm.loadEdit = function (id) {
            vm.carregandoEdit = true;
            carregarFuncoesIniciais();

            $timeout(function () {
                consultarPorId(id, function (empresaEdit) {
                    carregarCidades(empresaEdit.estadoId);
                    vm.empresa = empresaEdit;
                    vm.empresa.empresaCfop = vm.empresa.empresaCfop ? vm.empresa.empresaCfop : vm.empresa.empresaCfop = [];

                    ConsultaGrupoEmpresa(empresaEdit.grupoEmpresaId == null ? 0 : empresaEdit.grupoEmpresaId);
                    vm.consultaGrupoEmpresa.selectedValue = empresaEdit.grupoEmpresaId;

                    vm.status = empresaEdit.tipoEmpresaId;
                    vm.consultaTipoEmpresaModal.selectedValue = empresaEdit.tipoEmpresaId;
                    vm.consultaTipoEmpresaModal.selectedText = empresaEdit.tipoEmpresaNome;

                    vm.gridOptionsCiot.data = empresaEdit.ciotClienteIps || [];
                });
            }, 1000);

            $timeout(function () {
                vm.carregandoEdit = false;
            }, 6000);
        };

        function ConsultaGrupoEmpresa(grupoId) {
            BaseService.get('GrupoEmpresa', 'ConsultarPorId', {
                idGrupoEmpresa: grupoId
            }).then(function (response) {
                vm.consultaGrupoEmpresa.selectedText = response.data.razaosocial;
            });
        }

        vm.comboTipoEmissaoCiot = {
            data: [{ id: 0, descricao: 'Geral' }, { id: 1, descricao: 'Caruana' }, { id: 2, descricao: 'Leasing' }]
        };

        vm.limparCampo = function () {
            vm.empresa.tipoEmissaoCiot = null;
        };

        function consultarPorId(id, callback) {
            BaseService.get('Empresa', 'DadosEmpresaParaEditar', {
                idEmpresa: id
            }).then(function (response) {
                if (!response.success) {
                    toastr.error(response.message);
                    return;
                }
                if (angular.isFunction(callback))
                    callback(angular.fromJson(response.data));
                ConsultarClientIps(vm.empresa.cnpj)
                ConsultarContas();
                vm.empresa.impostoIRRF = vm.empresa.impostoIRRF ? vm.empresa.impostoIRRF : 0;
                vm.empresa.impostoCSLL = vm.empresa.impostoCSLL ? vm.empresa.impostoCSLL : 0;
                vm.empresa.impostoCOFINS = vm.empresa.impostoCOFINS ? vm.empresa.impostoCOFINS : 0;
                vm.empresa.impostoPIS = vm.empresa.impostoPIS ? vm.empresa.impostoPIS : 0;

            });
        }

        function ConsultarClientIps(cnpj) {
            BaseService.get("Empresa", "ConsultarClienteIps", { empresaCnpj: cnpj })
                .then(function (response) {
                    if (response.success) {
                        vm.empresa.ciotClienteIps = response.data;
                        vm.ciotClienteIps = false;
                    }
                });
        }

        function carregarEstados() {
            BaseService.get("Endereco", "ConsultarEstados")
                .then(function (response) {
                    if (response.success) {
                        vm.estados = response.data;
                        vm.estadosDisabled = false;
                    }
                });
        }

        function carregarCidades(EstadoId) {
            BaseService.get("Endereco", "ConsultarCidadesPorEstado", {
                estadoId: EstadoId
            }).then(function (response) {
                if (response.success) {
                    vm.cidades = response.data;
                    vm.cidadesDisabled = false;
                }
            });
        }

        vm.ctrlOdometroChange = function () {
            if (vm.empresa.controlaOdometro == false) {
                vm.empresa.controlaAutonomia = false;
            }
        }

        vm.buscarEnderecoRepresentante = function (cep) {
            limparEndereco();
            BaseService.getEndereco(cep).then(function (response) {
                if (response) {

                    carregarEstados();

                    $timeout(function () {
                        var estado = _.find(vm.estados, ['sigla', response.uf]);

                        if (angular.isDefined(estado))

                            vm.representante.estadoId = estado.id;
                        carregarCidades(vm.portador.estadoId);
                        $timeout(function () {
                            var cidade = _.find(vm.cidades, ['descricao', response.localidade]);
                            if (angular.isDefined(cidade))
                                vm.representante.cidadeId = cidade.id;
                        }, 1500);
                    }, 1500);

                    $timeout(function () {
                        if (angular.isDefined(response.logradouro))
                            vm.representante.endereco = response.logradouro;
                    }, 1500);

                    $timeout(function () {
                        if (angular.isDefined(response.bairro))
                            vm.representante.bairro = response.bairro;
                    }, 1500);
                } else {
                    toastr.warning("Endereço não localizado.");
                }
            });
        };

        function init() {
            if (vm.isNew()) {
                vm.empresa.id = 'Auto';
                vm.empresa.controlaContingencia = true;
            }
            vm.lastIndex = getLastIndex();
            if (!vm.isNew()) {
                vm.consultarDocumentos();
            }

        }

        vm.isNew = function () {
            return $stateParams.link === 'novo';
        }

        vm.consultaPortador = {
            columnDefs: [{
                name: 'Código',
                field: 'id',
                width: 80,
                type: 'number',
                primaryKey: true
            }, {
                name: 'Nome',
                field: 'nome',
                width: '*',
                minWidth: 150
            }, {
                name: 'CPF',
                field: 'cpfCnpj',
                enableGrouping: true,
                width: 145
            }],
            desiredValue: 'id',
            desiredText: 'nome',
            url: 'Portador/ConsultarGridPortadorPF',
            paramsMethod: function () {
                return {}
            }
        };

        vm.consultaTiposEmpresa = {
            columnDefs: [{
                name: 'Código',
                field: 'id',
                width: 80,
                type: 'number',
                primaryKey: true
            }, {
                displayName: 'Tipo de empresa',
                field: 'nome',
                width: '*',
                minWidth: 150
            }],
            desiredValue: 'id',
            desiredText: 'nome',
            url: 'TipoEmpresa/ConsultarGridTipoEmpresa',
            paramsMethod: function () {
                return {}
            }
        };

        vm.consultaGrupoEmpresa = {
            columnDefs: [{
                name: 'Código',
                field: 'id',
                width: 80,
                type: 'number',
                primaryKey: true
            }, {
                name: 'CNPJ',
                field: 'cnpj',
                width: '*',
                minWidth: 250
            }, {
                name: 'Razão Social',
                field: 'razaosocial',
                width: '*',
                minWidth: 250
            }],
            desiredValue: 'id',
            desiredText: 'razaosocial',
            url: 'GrupoEmpresa/ConsultarModalGrupoEmpresa',
            paramsMethod: function () {
                return {}
            }
        };

        vm.consultaTipoEmpresaModal = {
            columnDefs: [{
                name: 'Código',
                field: 'id',
                width: 80,
                type: 'number',
                primaryKey: true
            }, {
                displayName: 'Tipo de empresa',
                field: 'nome',
                width: '*',
                minWidth: 150
            }],
            desiredValue: 'id',
            desiredText: 'nome',
            url: 'TipoEmpresa/ConsultarGridTipoEmpresa',
        };

        vm.consultaCfop = {
            columnDefs: [{
                name: 'Código',
                field: 'id',
                width: 80,
                type: 'number',
                primaryKey: true
            }, {
                name: 'CFOP',
                displayName: 'CFOP',
                field: 'cfop',
                width: 100,
                type: 'number',
                minWidth: 150
            }, {
                name: 'Descrição',
                field: 'descricao',
                width: '*',
                minWidth: 150,
                enableFiltering: false
            }],
            desiredValue: 'id',
            desiredText: 'cfop',
            url: 'CFOP/ConsultarGridCFOP',
            paramsMethod: function () {
                return {}
            },
        };

        vm.clearConsultaPortador = function () {
            vm.consultaPortador.selectedEntity = undefined;
            vm.consultaPortador.selectedValue = undefined;
            vm.consultaPortador.selectedText = "Representante Legal";
        };

        vm.buscarEndereco = function (cep) {
            if (cep) {
                limparEndereco();
                BaseService.getEndereco(cep).then(function (response) {
                    if (response && response.erro) {
                        toastr.warning("Endereço não localizado.");
                    }

                    carregarEstados();

                    $timeout(function () {
                        var estado = _.find(vm.estados, ['sigla', response.uf]);

                        if (angular.isDefined(estado))

                            vm.empresa.estadoId = estado.id;
                        carregarCidades(vm.empresa.estadoId);
                        $timeout(function () {
                            var cidade = _.find(vm.cidades, ['descricao', response.localidade]);
                            if (angular.isDefined(cidade))
                                vm.empresa.cidadeId = cidade.id;
                        }, 1500);
                    }, 1500);

                    $timeout(function () {
                        if (angular.isDefined(response.logradouro))
                            vm.empresa.endereco = response.logradouro;
                    }, 1500);

                    $timeout(function () {
                        if (angular.isDefined(response.bairro))
                            vm.empresa.bairro = response.bairro;
                    }, 1500);

                });
            }
        };

        function limparEndereco() {
            vm.empresa.estadoId = null;
            vm.empresa.cidadeId = null;
            vm.estados = null;
            vm.cidades = null;
            vm.empresa.endereco = null;
            vm.empresa.bairro = null;
            vm.empresa.enderecoNumero = null;
            vm.empresa.complemento = null;
        }

        vm.estadoChange = function (estadoId) {
            vm.cidades = null;
            vm.cidadesDisabled = true;
            vm.empresa.cidadeId = null;
            carregarCidades(estadoId);
        };

        vm.save = function (form) {
            if (!form.$valid) {
                toastr.error('Todos os campos obrigatórios e/ou inválidos devem ser preenchidos corretamente com seus respectivos valores!');
                return;
            }

            if (vm.empresa.percentualAutonomiaInferior > 100) {
                toastr.error("Percentual de autonomia inferior não pode ser maior que 100.");
                return;
            }

            if (vm.empresa.percentualAutonomiaSuperior > 100) {
                toastr.error("Percentual de autonomia superior não pode ser maior que 100.");
                return;
            }

            if (vm.empresa.valorTarifaBbc > 100) {
                toastr.error("Percentual de tarifa BBC não pode ser maior que 100.");
                return;
            }

            if (vm.empresa.porcentagemTarifaServiceValePedagio > 100) {
                toastr.error("Percentual de tarifa Vale pedágio não pode ser maior que 100.");
                return;
            }

            if (vm.empresa.recebedorAutorizado && (!vm.listaDocumentos || vm.listaDocumentos.length === 0)) {
                toastr.error("Lista de documentos Recebedor autorizado não pode estar vazia com o parâmetro 'Realizar pagamento via Pix para o Recebedor Autorizado', na aba de parâmetros, ativo");
                return;
            }

            vm.saving = true;
            if (vm.empresa.id == "Auto")
                vm.empresa.id = 0;

            vm.empresa.ciotClienteIps = vm.gridOptionsCiot.data || [];

            if (!vm.isNew())
                realizarPost();
            else
                realizarPostCadastrarComRetorno();
        };

        $scope.$watch('vm.imagemUpload', function (nVal) {
            if (nVal) {
                if (nVal.filesize > 52428) { // 52428 = 51kb 
                    vm.usuario.foto = null;
                    vm.imagemUpload = null;
                    vm.imagemSelecionadaInput = null;
                    toastr.error('A imagem escolhida excede o tamanho de 512Kb!');
                } else {
                    vm.empresa.imagemCartao = nVal.base64;
                }
            }
        });

        function realizarPost() {
            vm.empresa.grupoEmpresaId = vm.consultaGrupoEmpresa.selectedValue;
            vm.empresa.documentosEmpresa = vm.listaDocumentos;
            BaseService.post('Empresa', 'Cadastrar', vm.empresa).then(function (response) {
                vm.saving = false;
                if (!response) {
                    toastr.error('Erro de comunicação com o servidor, tente novamente em alguns minutos.')
                    return
                }

                if (!response.success) {
                    toastr.error(response.message)
                    return
                }

                toastr.success(response.message);
                $state.go('empresa.index');
            });
        }

        vm.anexarDocumento = function (documento) {
            $uibModal.open({
                animation: true,
                ariaLabelledBy: 'modal-title',
                ariaDescribedBy: 'modal-body',
                templateUrl: 'app/empresa-cadastre-se/modal/modal-anexar-documento.html',
                controller: function ($uibModalInstance, $scope, BaseService) {
                    var vm = this;
                    vm.inputchanged = !documento;  // O input só será alterado se estiver criando novo documento
                    vm.validandoDocumento = false;
                    vm.documentoEditando = !!documento;

                    // Carrega os dados do documento se estiver editando
                    vm.idDocumento = documento ? !isNaN(documento.id) ? documento.id : 0 : null;
                    vm.arquivo = documento ? { base64: documento.arquivo, filename: documento.nome, filetype: documento.tipo } : null;
                    vm.descricaoArquivo = documento ? documento.descricao : null;
        
                    vm.fechar = function () {
                        $uibModalInstance.close();
                    };
        
                    vm.salvar = function () {
                        if (!vm.arquivo && !vm.inputchanged) {
                            toastr.error('Selecione um documento antes de salvar.');
                            return;
                        }
                    
                        vm.response = {
                            id: vm.idDocumento,
                            imagemPerfilB64: vm.arquivo.base64, // Manterá o base64 ou o novo arquivo
                            tipo: convertTipoArquivo(vm.arquivo.filetype),
                            nome: vm.arquivo.filename,
                            descricao: vm.descricaoArquivo,
                            status: 2
                        };
                    
                        $uibModalInstance.close(vm.response);
                    };
                    
        
                    // Verifica o arquivo apenas se for um novo upload
                    $scope.$watch('vm.arquivo', function (nVal) {
                        if (vm.documentoEditando && !vm.inputchanged) {
                            // Não verifica o arquivo se for edição e o input não foi alterado
                            return;
                        }
        
                        vm.validandoDocumento = true;
                        var tam = 10;
                        if (nVal) {
                            var tiposPermitidos = [
                                'image/jpeg', 
                                'image/png', 
                                'application/pdf', 
                                'application/msword', 
                                'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
                            ];
        
                            if (!tiposPermitidos.includes(nVal.filetype)) {
                                vm.arquivo = null;
                                toastr.error('Tipo de arquivo não permitido. Selecione Imagem, PDF ou Word.');
                                vm.validandoDocumento = false;
                            } else if (nVal.filesize > tam * 1048576) {
                                vm.arquivo = null;
                                toastr.error('O arquivo excede o tamanho permitido de ' + tam + 'Mb!');
                                vm.validandoDocumento = false;
                            } else {
                                vm.validandoDocumento = false;
                            }
                        }
                    });
                },
                controllerAs: 'vm',
                backdrop: 'static',
                size: 'lg',
                resolve: {
                    documento: function () {
                        return documento; // Passa o documento selecionado para edição
                    }
                }
            }).result.then(function (response) {
                if (!response) {
                    return toastr.warning("Nenhum documento foi selecionado.");
                } else {
                    if (documento) {
                        // Edição: Atualizar o documento existente
                        var index = vm.listaDocumentos.indexOf(documento);
                        if (index !== -1) {
                            vm.listaDocumentos[index] = {
                                id: response.id ? response.id : 0,
                                arquivo: response.imagemPerfilB64,
                                tipo: response.tipo,
                                nome: response.nome,
                                descricao: response.descricao,
                                status: response.status
                            };
                        }
                    } else {
                        // Novo documento: Adicionar à lista
                        if (!vm.listaDocumentos) {
                            vm.listaDocumentos = [];
                        }
        
                        vm.listaDocumentos.push({
                            id: response.id ? response.id : 0,
                            arquivo: response.imagemPerfilB64,
                            tipo: response.tipo,
                            nome: response.nome,
                            descricao: response.descricao,
                            status: response.status
                        });
                    }
                }
            });
        };
        

        vm.alterarArquivo = function () {
            vm.inputchanged = true; // Define como true para habilitar o input de arquivo
            vm.arquivo = null; // Redefine o arquivo selecionado
        };

        vm.editarDocumento = function (documento) {
            vm.anexarDocumento(documento);
        };

        function convertTipoArquivo(fileType) {
            if (fileType === 'image/jpeg' || fileType === 'image/jpg' || fileType === '0') {
                return "0";
            } else if (fileType === 'image/png') {
                return "0";
            } else if (fileType === 'application/pdf' || fileType === '1') {
                return "1";
            } else if (fileType === 'application/msword' || fileType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' || fileType === '2') {
                return "2";
            } else {
                toastr.error('Arquivo selecionado não corresponde a nenhum tipo permitido');
                return;
            }
        }

        vm.baixarDocumento = function (documento) {
            if (!documento || !documento.arquivo) {
                toastr.error('Documento inválido ou ausente.');
                return;
            }

            var base64Data = documento.arquivo;
            var extensaoArquivo = obterExtensaoArquivo(documento.nome);
            var descricao = documento.nome || 'arquivo';
            var downloadArchive = document.createElement("a");


            if (documento.tipo === '0' && extensaoArquivo === '.png') {
                downloadArchive.href = "data:image/png;base64," + base64Data;
                downloadArchive.download = descricao;
            } else if (documento.tipo === '0' && (extensaoArquivo === '.jpg' || extensaoArquivo === '.jpeg')) {
                downloadArchive.href = "data:image/jpeg;base64," + base64Data;
                downloadArchive.download = descricao;
            } else if (documento.tipo === '1') {
                downloadArchive.href = "data:application/pdf;base64," + base64Data;
                downloadArchive.download = descricao;
            } else if (documento.tipo === '2') {
                downloadArchive.href = "data:application/msword;base64," + base64Data;
                downloadArchive.download = descricao;
            } else {
                toastr.error('Tipo de arquivo não suportado.');
                return;
            }


            downloadArchive.click();
        };

        function obterExtensaoArquivo(nomeArquivo) {
            if (!nomeArquivo) {
                return null;
            }
        
            // Extrair a extensão do nome do arquivo
            var partes = nomeArquivo.split('.');
            
            // Verifica se o nome do arquivo possui uma extensão
            if (partes.length > 1) {
                return '.' + partes.pop().toLowerCase(); // Retorna a última parte como extensão, com letra minúscula
            }
            
            return null; // Caso o arquivo não tenha extensão
        };


        vm.visualizarDocumento = function (documento) {
            if (!documento || !documento.arquivo || !documento.tipo || !documento.nome) {
                toastr.error('Documento inválido ou ausente.');
                return;
            }
            
            var extensaoArquivo = obterExtensaoArquivo(documento.nome);
            var fileURL;

            if (documento.tipo === '0' && extensaoArquivo === '.png') {
                fileURL = "data:image/png;base64," + documento.arquivo;
            } else if (documento.tipo === '0' && (extensaoArquivo === '.jpg' || extensaoArquivo === '.jpeg')) {
                fileURL = "data:image/jpeg;base64," + documento.arquivo;
            } else if (documento.tipo === '1') {
                fileURL = "data:application/pdf;base64," + documento.arquivo;
            } else if (documento.tipo === '2') {
                fileURL = "data:application/msword;base64," + documento.arquivo;
            } else {
                toastr.error('Tipo de arquivo não suportado.');
                return;
            }

            var newTab = window.open();

            // Texto da aba
            newTab.document.title = documento.nome;

            var iframe = newTab.document.createElement('iframe');
            iframe.src = fileURL;
            iframe.style.border = "0";
            iframe.style.width = "100%";
            iframe.style.height = "100%";
            iframe.allowFullscreen = true;

            newTab.document.body.appendChild(iframe);
        };

        vm.excluirDocumento = function (documento) {
            if (isNaN(documento.id)) {
                vm.deletaItemLista(documento, vm.listaDocumentos);
            } else {
                vm.getExclusaoDocumento(documento);
            }
        };

        vm.getExclusaoDocumento = function (documento) {
            BaseService.get("Empresa", "ExcluirDocumentosEmpresa", {
                id: documento.id
            }).then(function (response) {
                if (!response.success) {
                    toastr.error(response.message);
                    return;
                } else {
                    vm.deletaItemLista(documento, vm.listaDocumentos);
                    return;
                }
            });
        }

        vm.deletaItemLista = function (documento, listaDocumentos) {
            var index = listaDocumentos.indexOf(documento);

            if (index !== -1) {
                listaDocumentos.splice(index, 1);
                toastr.success('Documento excluído com sucesso!');
            } else {
                toastr.error('Erro ao excluir o documento!');
            }
        }

        function realizarPostCadastrarComRetorno() {
            vm.empresa.tipoEmpresaId = vm.consultaTipoEmpresaModal.selectedValue == 0 ? null : vm.consultaTipoEmpresaModal.selectedValue;
            vm.empresa.grupoEmpresaId = vm.consultaGrupoEmpresa.selectedValue;
            vm.empresa.documentosEmpresa = vm.listaDocumentos;
            BaseService.post('Empresa', 'CadastrarComRetorno', vm.empresa).then(function (response) {
                vm.saving = false;
                if (!response) {
                    toastr.error('Erro de comunicação com o servidor, tente novamente em alguns minutos.')
                    return
                }

                if (!response.success) {
                    toastr.error(response.message)
                    return
                }

                vm.empresa.id = response.data;

                abrirModalUsuario();
            });
        }

        function abrirModalUsuario() {
            $uibModal.open({
                animation: true,
                scope: $scope,
                ariaLabelledBy: 'Usuário',
                ariaDescribedBy: 'modal-body',
                templateUrl: 'app/entities/empresa/modal/vinculo-primeiro-usuario-empresa/vinculo-primeiro-usuario-empresa-modal.html',
                controller: 'VinculoPrimeiroUsuarioEmpresaModalController',
                controllerAs: 'vm',
                size: 'lg',
                backdrop: 'static',
                resolve: {
                    empresa: function () {
                        return vm.empresa;
                    },
                    isFromEmpresa: function () {
                        return true;
                    },
                    isFromAvaliacaoEmpresa: function () {
                        return false;
                    },
                    isBloqueadoStatusAtualEmpresa: function () {
                        return false;
                    }
                }
            }).result.then(function () {

            });
        }

        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.administrador;
        };

        vm.consultaContas = function () {
            ConsultarContas();
        };

        var selfScope = PersistentDataService.get('EmpresaCrudController');

        if ($stateParams.link === 'novo')
            vm.empresa.id = 'Auto';

        if (angular.isDefined(selfScope))
            angular.extend(vm, selfScope.data);
        else if (!vm.isNew())
            vm.loadEdit($stateParams.link);
        else
            vm.load();

        init();

        // DO NOT TOUCH!! 666
        $scope.$on('$stateChangeStart', function (arg1, toState) {
            if (toState.name === 'empresa.index')
                PersistentDataService.remove('EmpresaCrudController');
            else
                PersistentDataService.store('EmpresaCrudController', vm, "Cadastro - Empresa", null, "empresa.crud", vm.empresa.id);
        });

        $timeout(function () {
            PersistentDataService.remove('EmpresaController');
        }, 15);

        vm.viewportWidth = window.innerWidth;

        function consultarSaldo(id) {
            BaseService.get('Conta', 'ConsultarSaldo', {
                idConta: id
            }).then(function (response) {
                vm.saldo = response.saldo;
            });
        }

        function ConsultarContas() {
            BaseService.get('Conta', 'ConsultarContas', {
                cpfCnpj: vm.empresa.cnpj
            }).then(function (response) {
                if (response.success == false || !response.length) {
                    return [];
                }    

                vm.contas = response;
                vm.conta.contas = response[0].id;

                if (response.length === 1) {
                    if (!vm.empresa.contaValePedagio) {
                        vm.empresa.contaValePedagio = response[0].id;
                    }

                    if (!vm.empresa.contaFrete) {
                        vm.empresa.contaFrete = response[0].id;
                    }
                    
                    vm.isFieldContaDisabled = true;
                } else {
                    vm.isFieldContaDisabled = false;
                }

                consultarSaldo(vm.conta.contas);
                vm.gridOptionsExtrato.dataSource.refresh();
            });
        }

        $scope.$watch('vm.conta.contas', function (idConta) {
            if (idConta !== undefined && idConta !== null) {
                consultarSaldo(idConta);
                vm.gridOptionsExtrato.dataSource.refresh();
            }
        });


        $scope.$watch('vm.tipoExtratoSelected', function () {
            vm.gridOptionsExtrato.dataSource.refresh();
        });

        $scope.$watch('vm.date', function () {
            vm.gridOptionsExtrato.dataSource.refresh();
        });

        //Cfop funções
        vm.adicionarCfop = function () {
            if (vm.consultaCfop.selectedValue != null) {

                if (vm.empresa.empresaCfop) {
                    for (var i = 0; i < vm.empresa.empresaCfop.length; i++) {
                        if (vm.empresa.empresaCfop[i].cfop == vm.consultaCfop.selectedText) {
                            toastr.warning("CFOP já informado!");
                            return;
                        }
                    }
                }

                var Cfop = {
                    cfopId: vm.consultaCfop.selectedValue,
                    cfop: vm.consultaCfop.selectedText,
                    descricao: vm.consultaCfop.selectedEntity.descricao
                };
                vm.empresa.empresaCfop.push(Cfop);
                vm.consultaCfop.selectedValue = null;
                vm.consultaCfop.selectedText = null;
            } else {
                toastr.warning("Selecione CFOP!");
                return;
            }
        }

        vm.removeCfop = function (cfopId) {
            for (var i = 0; i < vm.empresa.empresaCfop.length; i++) {
                if (vm.empresa.empresaCfop[i].cfopId == cfopId) {
                    var index = vm.empresa.empresaCfop.indexOf((vm.empresa.empresaCfop[i]));
                    vm.empresa.empresaCfop.splice(index, 1);
                    toastr.success("CFOP removido!");

                    $rootScope.empresa.empresaCfop = vm.empresa.empresaCfop;
                }
            }
        };
    }
})();


