(function () {
    'use strict';
    angular
        .module('bbcWeb')
        .controller('UsuarioCrudController', UsuarioCrudController);

    UsuarioCrudController.$inject = ['toastr', '$rootScope', 'BaseService', '$state', '$stateParams', '$window',
        'PERFIL_ADMINISTRADOR', '$scope', '$timeout', 'PersistentDataService', '$uibModal'];


    function UsuarioCrudController(
        toastr, $rootScope,
        BaseService, $state,
        $stateParams, $window,
        PERFIL_ADMINISTRADOR,
        $scope, $timeout,
        PersistentDataService, $uibModal) {

        var vm = this;

        vm.usuario = {
            imagemPerfilB64: null,
            usuarioFilial: [],
            usuarioCentroCusto: [],
            empresasVinculadas: []
        };
        vm.estados = [];
        vm.cidades = [];
        vm.GruposUsuarios = [];
        vm.empresas = [];
        vm.listaIdFilial = {
            listaDeFiliais: []
        };
        vm.resulUsuarioHistorico = {};
        vm.senhaSalva = "";
        vm.validacaoSenhaMensagem = "Informe uma senha com no mínimo 8 caracteres, que possua ao menos uma letra, um número e um caracter especial. Sequências e/ou nome da empresa não são permitidos.";
        vm.saving = false;
        vm.estadosDisabled = true;
        vm.cidadesDisabled = true;
        vm.carregandoEdit = false;
        vm.validacaoSenhaSucesso = true;
        vm.deveValidarSenha = false;

        vm.consultaGrupoUsuario = {
            columnDefs: [{
                name: 'Código',
                field: 'id',
                width: 80,
                type: 'number',
                primaryKey: true
            }, {
                name: 'Descrição',
                field: 'descricao',
                type: 'text',
                width: '*',
                minWidth: 250
            }],
            desiredValue: 'id',
            desiredText: 'descricao',
            url: 'GrupoUsuario/ConsultarGrupoUsuarioCombo',
            paramsMethod: function () {
                return {
                    GrupoEmpresaId: vm.consultaGrupoEmpresa.selectedValue ? vm.consultaGrupoEmpresa.selectedValue : 0,
                    ListaEmpresasIds: JSON.stringify(vm.usuario.empresasVinculadas.map(e => e.id)),
                }
            }
        };

        vm.consultaFilial = {
            columnDefs: [
                {
                    name: 'Código',
                    field: 'id',
                    width: 80,
                    type: 'number',
                    primaryKey: true
                },
                {
                    name: 'Nome',
                    field: 'nomeFantasia',
                    width: '*',
                    minWidth: 150
                },
                {
                    name: 'Razão social',
                    field: 'razaoSocial',
                    width: '*',
                    minWidth: 150
                },
                {
                    name: 'CNPJ',
                    field: 'cnpj',
                    width: '*',
                    minWidth: 150
                }
            ],
            desiredValue: 'id',
            desiredText: 'nomeFantasia',
            url: 'Filial/ConsultarGridFilial',
            paramsMethod: function () {
                return {
                    EmpresaId: vm.usuario.empresaId
                }
            },
        };

        vm.consultaCentroCusto = {
            columnDefs: [
                {
                    name: 'Código',
                    field: 'id',
                    serverField: 'id',
                    width: 80,
                    type: 'number',
                    primaryKey: true
                },
                {
                    name: 'Descrição',
                    field: 'descricao',
                    width: '*',
                    minWidth: 150
                }
            ],
            desiredValue: 'id',
            desiredText: 'descricao',
            url: 'CentroCusto/ConsultarGridCentroCusto',
            paramsMethod: function () {
                return {
                    ListaFiliaid: JSON.stringify(vm.listaIdFilial.listaDeFiliais),
                    EmpresaId: vm.usuario.empresaId,
                    FiltraFilial: true
                }
            }
        };

        vm.consultaGrupoEmpresa = {
            columnDefs: [
                {
                    name: 'Código',
                    field: 'id',
                    width: 80,
                    type: 'number',
                    primaryKey: true
                },
                {
                    name: 'CNPJ',
                    field: 'cnpj',
                    width: '*',
                    minWidth: 250
                },
                {
                    name: 'Razão Social',
                    field: 'razaosocial',
                    width: '*',
                    minWidth: 250
                }
            ],
            desiredValue: 'id',
            desiredText: 'razaosocial',
            url: 'GrupoEmpresa/ConsultarModalGrupoEmpresa',
            paramsMethod: function () {
                return {}
            },
            executeAfterSelection: function () {
                vm.consultaGrupoUsuario.selectedValue = undefined;
                vm.consultaGrupoUsuario.selectedText = '';
            },
            clearFunction: function () {
                vm.consultaGrupoUsuario.selectedValue = undefined;
                vm.consultaGrupoUsuario.selectedText = '';
                vm.consultaGrupoEmpresa.selectedValue = undefined;
            }
        };

        /*
            vm.consultaEmpresa = {
                columnDefs: [
                    {
                        name: 'Código',
                        field: 'id',
                        width: 80,
                        type: 'number',
                        primaryKey: true
                    },
                    {
                        name: 'Nome Fantasia',
                        field: 'nomeFantasia',
                        width: '*',
                        minWidth: 250
                    },
                    {
                        name: 'Razão Social',
                        field: 'razaoSocial',
                        width: '*',
                        minWidth: 250
                    },
                    {
                        name: 'Email',
                        field: 'email',
                        width: '*',
                        minWidth: 250
                    }
                ],
                desiredValue: 'id',
                desiredText: 'nomeFantasia',
                url: 'Empresa/ConsultarGridEmpresaCombo',
                paramsMethod: function () {
                    return {}
                },
                executeAfterSelection: function () {
                    vm.consultaGrupoUsuario.selectedValue = undefined;
                    vm.consultaGrupoUsuario.selectedText = '';

                    var existe = vm.usuario.empresasVinculadas.some(e => e.id === vm.consultaEmpresa.selectedValue);
                    if (existe) {
                        toastr.error("Esta empresa já está vinculada ao usuário.");
                        vm.consultaEmpresa.selectedValue = undefined;
                        vm.consultaEmpresa.selectedText = '';
                    }
                },
                clearFunction: function () {
                    vm.consultaGrupoUsuario.selectedValue = undefined;
                    vm.consultaGrupoUsuario.selectedText = '';
                }
            };
        */


        $scope.$watch('vm.imagemUpload', function (nVal) {
            if (nVal) {
                if (nVal.filesize > 524288) { // 524288 = 512kb 
                    vm.usuario.foto = null;
                    vm.imagemUpload = null;
                    vm.imagemSelecionadaInput = null;
                    toastr.error('A imagem escolhida excede o tamanho de 512Kb!');
                } else {
                    vm.usuario.foto = 'data:' + nVal.filetype + ';base64,' + nVal.base64;
                    vm.usuario.imagemPerfilB64 = nVal.base64;
                }
            }
        });

        $scope.$watch('vm.consultaGrupoEmpresa.selectedValue', function (value) {
            if (!value) return

            //vm.consultaEmpresa.selectedValue = undefined
            //vm.consultaEmpresa.selectedText = ''

            vm.usuario.empresasVinculadas = [];
        });

        /*
        $scope.$watch('vm.consultaEmpresa.selectedValue', function (value) {
            if (!value) return

            vm.consultaGrupoEmpresa.selectedValue = undefined
            vm.consultaGrupoEmpresa.selectedText = ''
        });
        */

        function buscarHistorico(id) {
            BaseService.get("Usuario", "GetUsuarioHistorico", {
                usuarioId: id
            }).then(function (response) {
                if (response.success) {
                    vm.resulUsuarioHistorico = response.data;
                }
            });
        }

        function consultarPorId(id) {
            BaseService.get('Usuario', 'ConsultarPorId', {
                UsuarioId: id
            }).then(function (response) {
                vm.carregandoEdit = false;

                if (!response) {
                    toastr.error('Erro de comunicação com o servidor, tente novamente em alguns minutos.');
                    return;
                }
                if (!response.success) {
                    toastr.error(response.message);
                    return;
                }

                vm.usuario = response.data;

                vm.senhaSalva = vm.usuario.senha

                //vm.consultaEmpresa.selectedValue = vm.usuario.empresaId;
                //vm.consultaEmpresa.selectedText = vm.usuario.nomeEmpresa;

                vm.consultaGrupoEmpresa.selectedValue = vm.usuario.grupoEmpresaId;
                vm.consultaGrupoEmpresa.selectedText = vm.usuario.nomeGrupoEmpresa;

                vm.consultaGrupoUsuario.selectedValue = vm.usuario.grupoUsuarioId;
                vm.consultaGrupoUsuario.selectedText = vm.usuario.grupoUsuarioDescricao;

                vm.validacaoSenhaSucesso = true;

                for (var x = 0; x < vm.usuario.usuarioFilial.length; x++) {
                    vm.listaIdFilial.listaDeFiliais.push(vm.usuario.usuarioFilial[x].filialId)
                }

                //vm.usuario.empresasVinculadas = vm.usuario.empresasVinculadas.filter(e => e.id !== vm.usuario.empresaId);

                buscarHistorico(id);
            });
        }

        function getImagemPerfilUsuario(id) {
            BaseService.get('Usuario', 'GetImageJson', {
                UsuarioId: id
            }).then(function (response) {
                if (!response) {
                    toastr.error('Erro de comunicação com o servidor, tente novamente em alguns minutos.');
                    return;
                }
                if (!response.success) {
                    toastr.error(response.message);
                    return;
                }

                if (!response.data)
                    return

                var sPlit = response.data.split(',');
                vm.usuario.imagemPerfilB64 = sPlit[1];
                vm.imagemUpload = {
                    filetype: sPlit[0].replace('data:', '').replace(';base64', ''),
                    base64: sPlit[1],
                    filename: 'foto'
                };
            });
        }

        vm.load = function () {
            vm.validacaoSenhaSucesso = false;
            //vm.consultaEmpresa.selectedValue = $rootScope.usuarioLogado.empresaId;
            vm.usuario.empresaId = $rootScope.usuarioLogado.empresaId;
            if (!vm.usuario.Id === 'Auto') {
                buscarHistorico(vm.usuario.Id);
            }
        }

        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.administrador === true;
        };

        vm.isGrupoEmpresa = function () {
            return $rootScope.usuarioLogado.administrador === false && $rootScope.usuarioLogado.empresaId === null;
        };

        vm.isEmpresa = function () {
            return $rootScope.usuarioLogado.empresaId !== null;
        };

        vm.isNew = function () {
            return $stateParams.link === 'novo';
        }

        vm.loadEdit = function (id) {
            vm.carregandoEdit = true;
            consultarPorId(id)
            getImagemPerfilUsuario(id)
        };


        vm.vincularEmpresa = function () {
            $uibModal.open({
                animation: true,
                ariaLabelledBy: 'modal-title',
                ariaDescribedBy: 'modal-body',
                templateUrl: 'app/entities/usuario/modal/modal-vincular-empresa.html',
                controller: 'ModalVincularEmpresaController',
                controllerAs: 'vm',
                backdrop: 'static',
                size: 'lg',
                resolve: {
                    usuarioId: function () {
                        return vm.usuario.id;
                    },
                    listaEmpresasIds: function () {
                        return vm.usuario.empresasVinculadas ? vm.usuario.empresasVinculadas.map(e => e.id) : [];
                    },
                }
            }).result.then(function (empresasSelecionadas) {
                if (!empresasSelecionadas || empresasSelecionadas.length === 0) {
                    return toastr.warning("Nenhuma empresa foi selecionada.");
                }

                empresasSelecionadas.forEach(function (empresa) {
                    var existe = vm.usuario.empresasVinculadas.some(e => e.Id === empresa.id);
                    if (!existe) {
                        vm.consultaGrupoUsuario.selectedText = undefined;
                        vm.consultaGrupoUsuario.selectedValue = null;
                        vm.usuario.empresasVinculadas.push(empresa);
                    }
                });

            }).catch(function () {
                console.log("Modal fechada sem adicionar empresas.");
            });
        };


        vm.excluirEmpresaVinculada = function (empresa) {
            var index = vm.usuario.empresasVinculadas.findIndex(e => e.id === empresa.id);
            if (index !== -1) {
                vm.usuario.empresasVinculadas.splice(index, 1);

                vm.consultaGrupoUsuario.selectedText = undefined;
                vm.consultaGrupoUsuario.selectedValue = null;

                toastr.success("Empresa removida com sucesso!");
            } else {
                toastr.error("Erro ao remover empresa. Empresa não encontrada.");
            }
        };

        vm.excluirItens = function () {
            Sistema.Msg.confirm("Você tem certeza que deseja excluir todos os itens da lista?", function () {
                vm.usuario.empresasVinculadas = [];
            });
        };


        vm.save = function (form) {
            vm.saving = true;
            vm.usuario.empresaId = vm.usuario.empresasVinculadas && vm.usuario.empresasVinculadas.length > 0
                ? vm.usuario.empresasVinculadas[0].id
                : ($rootScope.usuarioLogado && $rootScope.usuarioLogado.empresaId
                    ? $rootScope.usuarioLogado.empresaId
                    : null);
            vm.usuario.grupoEmpresaId = vm.consultaGrupoEmpresa.selectedValue;
            vm.usuario.grupoUsuarioId = vm.consultaGrupoUsuario.selectedValue;

            if (!vm.validacaoSenhaSucesso && !vm.isNew()) {
                vm.saving = false;
                toastr.error(vm.validacaoSenhaMensagem);
                return;
            }

            if (vm.usuario.senha !== vm.usuario.confirmarSenha && !vm.isNew()) {
                vm.saving = false;
                toastr.error('Os valores para senha e a confirmação devem ser os mesmos!');
                return;
            }

            if (!form.$valid) {
                vm.saving = false;
                toastr.error('Todos os campos obrigatórios e/ou inválidos devem ser preenchidos corretamente com seus respectivos valores!');
                return;
            }

            BaseService.post('Usuario', 'Salvar', vm.usuario).then(function (response) {
                vm.saving = false;
                if (response.success && vm.isNew()) {
                    toastr.success(response.message + " <br>Senha de acesso enviada para o e-mail cadastrado!");
                    $state.go('usuario.index');
                } else if (response.success) {
                    toastr.success(response.message);
                    $state.go('usuario.index');
                } else {
                    toastr.error(response.message);
                }
            });
        }

        vm.lengthHigherThan = function (variable, len) {
            if (angular.isUndefined(variable) || variable == null)
                return false;

            return variable.length > len;
        };

        vm.validarSenha = function () {
            if (vm.senhaSalva !== vm.usuario.senha) {
                if (vm.usuario.senha != null) {
                    BaseService.post('Usuario', 'ValidarSenha', {
                        SenhaValidacao: vm.usuario.senha
                    }).then(function (response) {
                        vm.validacaoSenhaSucesso = response.success;
                        if (!response.success) {
                            toastr.error(response.message);
                        } else {
                            vm.validacaoSenhaSucesso = true
                        }
                    });
                } else {
                    vm.validacaoSenhaSucesso = false;
                }
            }
        }

        vm.iconeErroSenha = function () {
            return (!(!vm.lengthHigherThan(vm.usuario.senha, 6) || !vm.lengthHigherThan(vm.usuario.confirmarSenha, 6) || vm.usuario.senha !== vm.usuario.confirmarSenha));
        };

        vm.onLoadImageUploader = function (a, b, c, d, e, f) {
            if (f.base64 !== null) {
                var vl = (f.filesize / 1024).toFixed(0);
                vm.imagemSelecionadaInput = f.filename + " (" + vl + "Kb)";
            } else {
                vm.imagemSelecionadaInput = null;
            }
        };

        vm.adicionarFilial = function () {
            var permiteAdicionar = false;

            if (!vm.consultaFilial.selectedText)
                return toastr.error("Nenhuma Filial foi selecionada.");

            var objetosValidos = _.filter(vm.usuario.usuarioFilial, function (v) {
                return v.filialId === vm.consultaFilial.selectedValue;
            });

            if (objetosValidos.length > 0) {
                toastr.error("Esta Filial já foi adicionada.");
                return;
            }

            if (vm.consultaFilial.selectedValue) {
                permiteAdicionar = true;
            }

            if (permiteAdicionar) {
                var filial = {
                    nome: vm.consultaFilial.selectedText,
                    filialId: vm.consultaFilial.selectedValue
                }
                if (!vm.usuario.usuarioFilial) {
                    vm.usuario.usuarioFilial = [];
                }
                vm.usuario.usuarioFilial.push(filial);
                vm.listaIdFilial.listaDeFiliais.push(filial.filialId)
                vm.clearconsultaFilial();
            }
        };

        vm.clearconsultaFilial = function () {
            vm.consultaFilial.selectedEntity = undefined;
            vm.consultaFilial.selectedValue = undefined;
            vm.consultaFilial.selectedText = "";
        };

        vm.removerFilial = function (filialId) {
            for (var i = 0; i < vm.usuario.usuarioFilial.length; i++) {
                if (vm.usuario.usuarioFilial[i].filialId === filialId) {
                    var index = vm.usuario.usuarioFilial.indexOf((vm.usuario.usuarioFilial[i]));
                    vm.usuario.usuarioFilial.splice(index, 1)
                    vm.listaIdFilial.listaDeFiliais.splice(index, 1)
                } else {
                    if (vm.usuario.usuarioFilial[i] === filialId) {
                        var jndex = vm.usuario.usuarioFilial.indexOf((vm.usuario.usuarioFilial[i]));
                        vm.usuario.usuarioFilial.splice(jndex, 1)
                        vm.listaIdFilial.listaDeFiliais.splice(jndex, 1)
                    }
                }
            }

            if (vm.usuario.usuarioFilial.length < 1) {
                vm.consultaFilial.selectedEntity = undefined;
                vm.consultaFilial.selectedValue = undefined;
                vm.consultaFilial.selectedText = undefined;
            }
        };

        vm.adicionarCentroCusto = function () {
            var permiteAdicionar = false;

            if (!vm.consultaCentroCusto.selectedText)
                return toastr.error("Nenhum Centro de custo foi selecionado.");

            var objetosValidos = _.filter(vm.usuario.usuarioCentroCusto, function (v) {
                return v.centroCustoId === vm.consultaCentroCusto.selectedValue;
            });

            if (objetosValidos.length > 0) {
                toastr.error("Este Centro de custo já foi adicionado.");
                return;
            }

            if (vm.consultaCentroCusto.selectedValue) {
                permiteAdicionar = true;
            }

            if (permiteAdicionar) {
                var centroCusto = {
                    nome: vm.consultaCentroCusto.selectedText,
                    centroCustoId: vm.consultaCentroCusto.selectedValue,
                    filialId: vm.consultaCentroCusto.selectedEntity.filialId
                }

                if (!vm.usuario.usuarioCentroCusto) {
                    vm.usuario.usuarioCentroCusto = [];
                }
                if (!centroCusto.filialId) {
                    toastr.info("Centro custo sem filial");
                }
                vm.usuario.usuarioCentroCusto.push(centroCusto);
                vm.clearconsultaCentroCusto();
            }
        };

        vm.clearconsultaCentroCusto = function () {
            vm.consultaCentroCusto.selectedEntity = undefined;
            vm.consultaCentroCusto.selectedValue = undefined;
            vm.consultaCentroCusto.selectedText = "";
        };

        vm.removerCentroCusto = function (centroCusto) {
            for (var i = 0; i < vm.usuario.usuarioCentroCusto.length; i++) {
                if (vm.usuario.usuarioCentroCusto[i].id === centroCusto.id) {
                    var index = vm.usuario.usuarioCentroCusto.indexOf((vm.usuario.usuarioCentroCusto[i]));
                    vm.usuario.usuarioCentroCusto.splice(index, 1)
                } else {
                    if (vm.usuario.usuarioCentroCusto[i] === centroCusto) {
                        var jndex = vm.usuario.usuarioCentroCusto.indexOf((vm.usuario.usuarioCentroCusto[i]));
                        vm.usuario.usuarioCentroCusto.splice(jndex, 1)
                    }
                }
            }

            if (vm.usuario.usuarioCentroCusto.length < 1) {
                vm.consultaCentroCusto.selectedEntity = undefined;
                vm.consultaCentroCusto.selectedValue = undefined;
                vm.consultaCentroCusto.selectedText = undefined;
            }
        };

        vm.preencherLogin = function () {
            var valueEmail = document.getElementById("email").value;
            valueEmail = valueEmail.substring(0, valueEmail.indexOf('@'));

            if (valueEmail) {
                vm.usuario.login = valueEmail;
            }
        }

        // -------------------------- Inicialização da Tela -------------------------- 

        var selfScope = PersistentDataService.get('UsuarioCrudController');

        if ($stateParams.link === 'novo')
            vm.usuario.Id = 'Auto';

        if (angular.isDefined(selfScope))
            angular.extend(vm, selfScope.data);
        else if (!vm.isNew())
            vm.loadEdit($stateParams.link);
        else
            vm.load();

        // -------------------------- Inicialização da Tela -------------------------- 
    }
})();