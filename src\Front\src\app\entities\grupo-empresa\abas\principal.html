<style>
    .fixLabelFile {
        float: right;
        right: -14px;
    }

    .borderSettings {
        border: 1px solid #dcdada;
        padding-top: 3px;
        margin-right: -2px;
        text-align: right;
        border-radius: 7px;
        margin-top: 8px;
        min-height: 69px;
        max-height: 69px;
        height: 69px;
    }

    .borderSettings:hover {
        background-color: whitesmoke;
    }

    #GrupoEmpresaCrudController .ats-switch.switch-small {
        min-width: 30px;
        height: 10px;
    }

    @media (min-width: 600px) {
        #GrupoEmpresaCrudController .xsFix {
            margin-right: 0;
            left: 0;
        }
    }

    @media (min-width: 1200px) {
        .col-lg-4:focus {
            outline: none;
        }

        #GrupoEmpresaCrudController .xsFix {
            margin-right: -49px;
            left: 49px;
        }

    }
</style>
<div class="row">
    <div class="col-sm-12 col-md-6 col-lg-6">
        <div class="row">
            <div class="form-group">
                <label class="col-xs-12 col-sm-3 col-md-3 col-lg-3 control-label">Código:</label>
                <div class="input-group col-xs-12 col-sm-3 col-md-3 col-lg-3">
                    <input type="text" ng-model="vm.grupoEmpresa.id" class="form-control" disabled
                        value="{{vm.isNew()}}" />
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-12 col-md-12 col-lg-12 pull-left">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-3 col-lg-3 control-label">
                        <span class="text-danger mr-5">*</span>Cnpj:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-9 col-lg-9">
                        <input ttype="text" name="CNPJ" class="form-control" ui-br-cnpj-mask validate-on="blur"
                            ng-disabled="!vm.isNew()" ng-model="vm.grupoEmpresa.cnpj"
                            required-message="'CNPJ é obrigatório'" required />
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-12 col-md-12 col-lg-12">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-3 col-lg-3 control-label">
                        <span class="text-danger mr-5">*</span>Razao social:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-9 col-lg-9">
                        <input type="text" required ng-model="vm.grupoEmpresa.razaosocial"
                            required-message="'Razao social é obrigatória'" maxlength="100" validate-on="blur"
                            name="Nome" class="form-control" />
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<br />