﻿(function () {
    'use strict';

    angular.module('bbcWeb').controller('CentralMensagensCrudController', CentralMensagensCrudController);

    CentralMensagensCrudController.$inject = [
        'toastr',
        '$rootScope',
        'BaseService',
        '$state',
        '$stateParams',
        '$window',
        'PERFIL_ADMINISTRADOR',
        '$scope',
        '$timeout',
        'PersistentDataService',
        'DefaultsService',
        '$uibModal'
    ];

    function CentralMensagensCrudController(
        toastr, 
        $rootScope,
        BaseService, 
        $state,
        $stateParams, 
        $window,
        PERFIL_ADMINISTRADOR,
        $scope, 
        $timeout,
        PersistentDataService,
        DefaultsService,
        $uibModal
    ) {
        var vm = this;
        vm.mensagem = {};
        vm.comboTipoMensagem = [];
        vm.desabilitaCampoTipoMensagem = false;
        vm.edicao = false;
        vm.loaded = false;

        vm.periodoMensagem = {
            startDate: moment(),
            endDate: moment().add(365, 'days')
        };

        vm.headerItems = [{
            name: '<PERSON>ada<PERSON><PERSON>'
        }, {
            name: 'Central de mensagens',
            link: 'central-mensagens.index'
        }, {
            name: $stateParams.link == 'novo' ? 'Novo' : 'Editar'
        }];

        var selfScope = PersistentDataService.get('CentralMensagensCrudController');

        if ($stateParams.link == 'novo') {
            vm.mensagem.id = 'Auto';
            vm.mensagem.ativo = true;
            vm.edicao = false;
        } else {
            vm.edicao = true;
        }

        vm.isNew = function () {
            return $stateParams.link == 'novo';
        };

        vm.changeTipoMensagem = function (nVal) {
            if (nVal == 1) {
                $("#exibirImagemTitle").attr("style", "display: block");
                $("#exibirImagem").attr("style", "display: block");
            } else {
                $("#exibirImagemTitle").attr("style", "display: none");
                $("#exibirImagem").attr("style", "display: none");
            }
        };

        vm.changeCodigoAplicacao = function (nVal) {
            if (nVal == 2) {
                $("#exibirConfigMensagemDock").attr("style", "display: block");
                $("#exibirConfigMensagemNaoDock").attr("style", "display: none");
                $("#exibirConfigMensagemImagemNaoDock").attr("style", "display: none");
                $("#exibirImagemTitle").attr("style", "display: none");
                $("#exibirImagem").attr("style", "display: none");
                $("#textMensagem").html('<span class="text-danger mr-5">*</span>Mensagem de retorno do cliente:');
                vm.mensagem.tipoMensagem = 0;
                vm.desabilitaCampoTipoMensagem = true;
            } else {
                $("#exibirConfigMensagemDock").attr("style", "display: none");
                $("#exibirConfigMensagemNaoDock").attr("style", "display: block");
                $("#exibirConfigMensagemImagemNaoDock").attr("style", "display: block");
                $("#textMensagem").html('<span class="text-danger mr-5">*</span>Mensagem:');
                vm.mensagem.tipoMensagem = null;
                vm.desabilitaCampoTipoMensagem = false;
            }
        };

        $scope.$watch('vm.imagemUpload', function (nVal) {
            if (nVal) {
                if (nVal.filesize > 524288) { // 524288 = 512kb 
                    vm.mensagem.imagemMensagem = null;
                    vm.imagemUpload = null;
                    vm.imagemSelecionadaInput = null;
                    toastr.error('A imagem escolhida excede o tamanho de 512Kb!');
                } else {
                    vm.mensagem.imagemMensagem = 'data:' + nVal.filetype + ';base64,' + nVal.base64;
                    vm.mensagem.imagemPerfilB64 = nVal.base64;
                }
            }
        });

        function carregarCodigosMensagem() {
            BaseService.get("Mensagem", "ComboCodigoMensagem")
                .then(function (response) {
                    if (response.success) {
                        vm.comboCodigoMensagem = response.data;
                    }
                });
        };

        vm.comboCodigoAplicacao = {
            data: [{ id: 0, descricao: 'Posto' }, { id: 1, descricao: 'CIOT' }, { id: 2, descricao: 'Dock' }]
        };

        vm.comboTipoMensagem = {
            data: [{ id: 0, descricao: 'Texto' }, { id: 1, descricao: 'Imagem' }]
        };

        vm.dateOptions = {
            timePicker: false,
            timePicker24Hour: false,
            applyClass: 'btn-primary',
            locale: {
                applyLabel: "Aplicar",
                fromLabel: "De",
                format: "DD/MM/YYYY",
                toLabel: "Até",
                cancelLabel: 'Cancelar',
                customRangeLabel: 'Período',
                monthNames: ['Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho', 'Julho', 'Agosto', 'Setembro',
                    'Outubro', 'Novembro', 'Dezembro'],
                daysOfWeek: ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb']
            },
            ranges: {}
        };

        vm.loadEdit = function (id) {
            carregarCodigosMensagem();

            BaseService.get('Mensagem', 'ConsultarPorId', {
                idMensagem: id
            }).then(function (response) {
                if (!response.success) {
                    toastr.error(response.message);
                    return;
                } else {
                    vm.mensagem = response.data;
                    
                    vm.mensagem.mensagemTratada = vm.mensagem.mensagemTratada == 1 ? true : false
                    vm.changeTipoMensagem(vm.mensagem.tipoMensagem);
                    setDataInicioMensagem(vm.mensagem.dataInicioMensagem)
                    setDataFimMensagem(vm.mensagem.dataFimMensagem);
                    vm.loaded = true;
                    if (vm.mensagem.imagemMensagem) {
                        var sPlit = vm.mensagem.imagemMensagem.split(',');
                        vm.mensagem.imagemPerfilB64 = sPlit[1];

                        vm.imagemUpload = {
                            filetype: sPlit[0].replace('data:', '').replace(';base64', ''),
                            base64: sPlit[1],
                            filename: 'foto'
                        };
                    }
                }
            });
        };
        

        function setDataInicioMensagem(data) {
            if (angular.isDefinedNotNull(data)) {
                vm.periodoMensagem.startDate = moment(data);
            }
        }
        
        function setDataFimMensagem(data) {
            if (angular.isDefinedNotNull(data)) {
                vm.periodoMensagem.endDate = moment(data);
            }
        }
        

        vm.save = function (form) {
            if (!form.$valid) {
                toastr.error('Todos os campos obrigatórios devem ser preenchidos corretamente com seus respectivos valores');
                return;
            }

            if (vm.isSaving === true)
                return;

            vm.mensagem.id = vm.mensagem.id == "Auto" ? 0 : vm.mensagem.id;
            vm.mensagem.ativo = vm.mensagem.ativo ? 1 : 0;
            if (vm.mensagem.codigoAplicacao == 2) {
                vm.mensagem.mensagemTratada = vm.mensagem.mensagemTratada ? 1 : 0;
                vm.mensagem.dataInicioMensagem = null;
                vm.mensagem.dataFimMensagem = null;
                vm.mensagem.codigoMensagem = null;
                vm.mensagem.descricaoMensagem = null;
                vm.mensagem.imagemMensagem = null;
            } else {
                vm.mensagem.dataInicioMensagem = vm.periodoMensagem.startDate.format('MM/DD/YYYY').toString();
                vm.mensagem.dataFimMensagem = vm.periodoMensagem.endDate.format('MM/DD/YYYY').toString();
                vm.mensagem.textoMensagemPadrao = null;
                vm.mensagem.textoMensagemOriginal = null;
                vm.mensagem.mensagemTratada = null;
            }
            
            
            vm.isSaving = true;

            BaseService.post('Mensagem', 'Salvar', vm.mensagem).then(function (response) {
                vm.isSaving = false;

                if (response && !response.sucesso)
                    return toastr.error(response.mensagem);
                else {
                    toastr.success('Mensagem salva com sucesso!');
                    $state.go('central-mensagens.index');
                }
            });
        };

        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.administrador == true;
        };

        function init() {
            if (vm.isNew()) {
                vm.mensagem.id = 'Auto';
                vm.mensagem.ativo = 1;
                vm.loaded = true;
                carregarCodigosMensagem();
            }
        };

        vm.onClickAvancar = function (wizard) {
            wizard.go(wizard.getActivePosition() + 1);
        };

        vm.onClickVoltar = function (wizard) {
            var ativoIndex = wizard.getActivePosition();
            if (ativoIndex == 1)
                $state.go('central-mensagens.index');

            wizard.go(ativoIndex - 1);
        };

        init();

        $scope.$on('$stateChangeStart', function (arg1, toState) {
            if (toState.name === 'central-mensagens.index')
                PersistentDataService.remove('CentralMensagensCrudController');
            else
                PersistentDataService.store('CentralMensagensCrudController', vm, "Cadastro - Central de mensagens", null, "central-mensagens.central-mensagens-crud", vm.mensagem.id);
        });

        if (angular.isDefined(selfScope))
            angular.extend(vm, selfScope.data);
        else {
            if (!vm.isNew())
                vm.loadEdit($stateParams.link);
            else {
                init();
            }
        }

        $timeout(function () {
            PersistentDataService.remove('CentralMensagensCrudController');
        }, 15);
    }
})();
