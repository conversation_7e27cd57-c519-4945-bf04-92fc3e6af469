(function () {
    'use strict';

    angular.module('bbcWeb.painel-abastecimento.state', []).config(routeConfig);

    function routeConfig($stateProvider) {
        $stateProvider
            .state('painel-abastecimento', {
                url: "/painel-abastecimento",
                abstract: true,
                templateUrl: "app/layout/content.html"})
            .state('painel-abastecimento.index', {
                url: '/index',
                templateUrl: 'app/entities/painel-abastecimento/painel-abastecimento.html'});
    }
})();