(function () {
    'use strict';

    angular.module('bbcWeb.painel-pagamento.state', []).config(routeConfig);

    function routeConfig($urlRouterProvider, $stateProvider) {
        $stateProvider.state('painel-pagamento', {
            abstract: true,
            url: "/painel-pagamento",
            templateUrl: "app/layout/content.html"
        }).state('painel-pagamento.index', {
            url: '/index',
            templateUrl: 'app/entities/painel-pagamento/painel-pagamento.html'
        }).state('painel-pagamento.painel-pagamento-crud', {
            url: '/:link',
            templateUrl: 'app/entities/painel-pagamento/painel-pagamento-crud.html'
        }).state('painel-pagamento.painel-pagamentoevento-crud', {
            url: '/evento/:link',
            templateUrl: 'app/entities/painel-pagamento/painel-pagamentoevento-crud.html'
        });
    }
})();