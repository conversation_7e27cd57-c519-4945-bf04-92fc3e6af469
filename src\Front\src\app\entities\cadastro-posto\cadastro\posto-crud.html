<style>
    #PostoCrudController .fixLRpg {
        padding-left: 4px !important;
        padding-right: 4px !important;
        text-align: -webkit-center;
    }

    .widthHF {
        width: 25px;
    }

    .colorGreen {
        color: green;
    }

    .form-wizard>ol>li {
        min-height: 50px;
        padding-top: 10px;
        padding-bottom: 7px;
        background-color: #eeeeee;
        border-color: #eaeaea;
        border-style: solid;
        cursor: pointer;
        border-width: 2px;
    }

    #PostoCrudController .imgPassCombination {
        position: relative;
        top: 5px;
        right: -37px;
    }

    #PostoCrudController .ui-select-bootstrap .ui-select-toggle>a.btn {
        position: absolute !important;
        height: 10px !important;
        right: 10px !important;
        margin-top: 0px !important;
    }

    #PostoCrudController .imgFilial,
    .imgFilialFile {
        position: absolute;
        margin: -20px 105px;
        margin-left: 50%;
        height: 128px;
        width: 128px;
        border-radius: 65%;
        object-fit: cover;
        object-position: center;
    }

    #PostoCrudController .inputUploadImg {
        position: absolute;
        right: 0;
        top: 117px;
    }

    .form-wizard>ol>li {
        min-height: 33px;
        padding-top: 2px;
        padding-bottom: 0px;
        background-color: #eeeeee;
        border-color: #eaeaea;
        border-style: solid;
        cursor: pointer;
        border-width: 2px;
    }
</style>
<div id="Controller" ng-controller="PostoCrudController as vm">
    <form-header ng-submit="return" items="vm.headerItems" head="'Posto'"></form-header>
    <div class="wrapper-content animated overflow-hidden fadeIn mb-0 pb-0">
        <div class="row">
            <div class="col-lg-12">
                <div class="ibox">
                    <div class="ibox-title">
                        <h5>{{vm.isNew() ? 'Novo' : 'Editar'}} posto</h5>
                        <div ibox-tools></div>
                    </div>
                    <div class="ibox-content wizard">
                        <form name="frmPostoCrud" role="form" novalidate ats-validator
                            ng-submit="vm.save(frmPostoCrud)" show-validation>
                            <div form-wizard steps="8">
                                <div class="form-wizard">
                                    <ol class="row" style="margin-bottom: 0;">
                                        <li ng-click="wizard.go(1)" class=" fixLRpg control-label col-xs-12 col-sm-6 col-md-4 col-lg-4"
                                            ng-class="{'active': wizard.active(1)}">
                                            <h4>Principal</h4>
                                        </li>
                                        <li ng-click="wizard.go(2)" class="fixLRpg control-label col-xs-12 col-sm-6 col-md-4 col-lg-4"
                                            ng-class="{'active': wizard.active(2)}">
                                            <h4>Endereço</h4>
                                        </li>                                        
                                        <li ng-click="wizard.go(3)" class="fixLRpg control-label col-xs-12 col-sm-6 col-md-4 col-lg-4"
                                            ng-class="{'active': wizard.active(3)}">
                                            <h4>Contato</h4>
                                        </li>
                                    </ol>
                                    <ol class="row mt-n-10" style="margin-bottom: 0;">
                                        <li ng-click="wizard.go(4)" class="fixLRpg control-label col-xs-12 col-sm-6 col-md-4 col-lg-4"
                                            ng-class="{'active': wizard.active(4)}">
                                            <h4>Adicionais</h4>
                                        </li>
                                        <li ng-click="wizard.go(5)" class="fixLRpg control-label col-xs-12 col-sm-6 col-md-4 col-lg-4"
                                            ng-class="{'active': wizard.active(5)}">
                                            <h4>Banco</h4>
                                        </li>
                                        <li ng-click="wizard.go(6)" class="fixLRpg control-label col-xs-12 col-sm-6 col-md-4 col-lg-4"
                                            ng-class="{'active': wizard.active(6)}">
                                            <h4>Vínculo combustíveis</h4>
                                        </li>
                                    </ol>
                                    <ol class="row mt-n-10">
                                        <li ng-click="wizard.go(7)" class="fixLRpg control-label col-xs-12 col-sm-6 col-md-4 col-lg-4"
                                            ng-class="{'active': wizard.active(7)}">
                                            <h4>Documentos</h4>
                                        </li>
                                        <li ng-click="wizard.go(8)" class="control-label col-xs-12 col-sm-6 col-md-4 col-lg-4"
                                            ng-class="{'active': wizard.active(8)}">
                                            <h4>Abastecimento</h4>
                                        </li>
                                    </ol>
                                    <div id="activateTab1" ng-show="wizard.active(1)">
                                        <div ng-include="'app/entities/cadastro-posto/cadastro/abas/aba-principal.html'"
                                            class="form-horizontal"> </div>
                                    </div>
                                    <div id="activateTab2" ng-show="wizard.active(2)">
                                        <div ng-include="'app/entities/cadastro-posto/cadastro/abas/aba-endereco.html'"
                                            class="form-horizontal"> </div>
                                    </div>
                                    <div id="activateTab3" ng-show="wizard.active(3)">
                                        <div ng-include="'app/entities/cadastro-posto/cadastro/abas/aba-contato.html'"
                                            class="form-horizontal"> </div>
                                    </div>
                                    <div id="activateTab4" ng-show="wizard.active(4)">
                                        <div ng-include="'app/entities/cadastro-posto/cadastro/abas/aba-adicionais.html'"
                                            class="form-horizontal"> </div>
                                    </div>
                                    <div id="activateTab5" ng-show="wizard.active(5)">
                                        <div ng-include="'app/entities/cadastro-posto/cadastro/abas/aba-banco.html'"
                                            class="form-horizontal"> </div>
                                    </div>
                                    <div id="activateTab6" ng-show="wizard.active(6)">
                                        <div ng-include="'app/entities/cadastro-posto/cadastro/abas/aba-vinculo.html'"
                                            class="form-horizontal"> </div>
                                    </div>
                                    <div id="activateTab7" ng-show="wizard.active(7)">
                                        <div ng-include="'app/entities/cadastro-posto/cadastro/abas/aba-documentos.html'"
                                            class="form-horizontal"> </div>
                                    </div>
                                    <div id="activateTab8" ng-show="wizard.active(8)">
                                        <div ng-include="'app/entities/cadastro-posto/cadastro/abas/aba-abastecimento.html'"
                                            class="form-horizontal"> </div>
                                    </div>
                                </div>
                                <div ng-show="vm.carregandoEdit">
                                    <div class="spiner-example">
                                        <div class="sk-spinner sk-spinner-wave">
                                            <div class="sk-rect1"></div>
                                            <div class="sk-rect2"></div>
                                            <div class="sk-rect3"></div>
                                            <div class="sk-rect4"></div>
                                            <div class="sk-rect5"></div>
                                            <div class="sk-rect6"></div>
                                            <div class="sk-rect7"></div>
                                            <div class="sk-rect8"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row clearfix"> </div>
                            <hr-label dark="true"></hr-label>
                            <br />
                            <div class="row">
                                <div class="form-group">
                                    <div class="col-md-12 col-lg-12 text-right">
                                        <button type="button" ng-disabled="vm.isSaving"
                                            ng-click="vm.onClickVoltar(wizard)" class="btn btn-labeled
                                                btn-default">
                                            <span class="btn-label">
                                                <i class="fa fa-arrow-circle-left"></i>
                                            </span>
                                            Voltar
                                        </button>
                                        <button type="button" ng-show="!wizard.active(8)" ng-disabled="vm.saving"
                                            ng-click="wizard.go(wizard.getActivePosition() + 1);"
                                            class="btn btn-labeled btn-default">
                                            <span class="btn-label">
                                                <i class="fa fa-arrow-circle-right"></i>
                                            </span>
                                            Avançar
                                        </button>
                                        <button type="submit" ng-show="wizard.active(8)" ng-disabled="vm.saving"
                                            class="btn btn-labeled btn-success text-right"
                                            data-style="expand-right">
                                            <span class="btn-label">
                                                <i class="fa fa-check-circle"></i>
                                            </span>
                                            Salvar
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>