<div ng-controller="CentralPendenciaController as vm">
    <form-header items="vm.headerItems" head="'Central de pendências'" state="central-pendencias">
    </form-header>
    <div class="wrapper-content animated fadeIn filter-position overflow-hidden">
        <div class="row">
            <div class="col-lg-12">
                <div class="ibox float-e-margins">
                    <div style="height: 38px;" class="ibox-title">
                        <h5>Central de pendências</h5>
                        <div ibox-tools></div>
                    </div>
                    <div class="ibox-content">
                        <div form-wizard steps="3">
                            <div class="form-wizard">
                                <ol class="row">
                                    <li ng-click="wizard.go(1); vm.atualizaTelaFrete()" class="fixLRpg col-sm-6" ng-class="{'active': wizard.active(1)}">
                                        <h4>Frete</h4>
                                    </li>
                                    <li ng-click="wizard.go(2); vm.atualizaTelaValePedagio()" class="fixLRpg col-sm-6" ng-class="{'active': wizard.active(2)}">
                                        <h4>Vale Pedágio</h4>
                                    </li>
                                </ol>
                                <br/>
                            </div>
                            <div id="activateTab1" ng-show="wizard.active(1)">
                                <div ng-include="'app/entities/central-pendencias/abas/frete/frete.html'" class="form-horizontal"> </div>
                            </div>
                            <div id="activateTab2" ng-show="wizard.active(2)">
                                <div ng-include="'app/entities/central-pendencias/abas/vale-pedagio/vale-pedagio.html'" class="form-horizontal"> </div>
                            </div>
                        </div>                    
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>