(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('ClienteController', ClienteController);

          ClienteController.inject = ['BaseService', '$rootScope', 'toastr', '$scope', 'PersistentDataService', '$timeout', '$state', 'PERFIL_ADMINISTRADOR'];
  
    function  ClienteController(BaseService, $rootScope, toastr, $scope, PersistentDataService, $timeout, $state, PERFIL_ADMINISTRADOR) {
        var vm = this;
        vm.headerItems = [{
            name: '<PERSON>adastro<PERSON>'
        }, {
            name: '<PERSON>lient<PERSON>'
        }];

        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.perfil == PERFIL_ADMINISTRADOR;
        };
        
        vm.gridOptions = {
            data: [],  
            onRegisterApi: BaseService.dataGrid.defaultOnRegisterApi,
            dataSource: {
                url: "Cliente/ConsultarGridCliente"
            },
            columnDefs: [{
                name: 'Ações',
                width: 80,
                cellTemplate: '<div ng-if="!row.groupHeader" class="ui-grid-cell-contents" title="TOOLTIP">\
                                    <button ng-disabled="row.entity.status===0" tooltip-placement="right" uib-tooltip="Editar" type="button" ui-sref="cliente.cliente-crud({link: row.entity.id})"\
                                        ng-class="{ \'btn btn-xs btn-info\': true }">\
                                        <i class="fa fa-edit"></i>\
                                    </button>\
                                    <button type="button" tooltip-placement="right" uib-tooltip="Ativar / Inativar" ng-click="grid.appScope.vm.alterarStatus(row.entity.id, row.entity.status)" ng-class="row.entity.status===1 ? \'btn btn-xs btn-success\' : \'btn btn-xs btn-danger\'">\ <i ng-class="row.entity.status===1 ? \'fa fa-check\' : \'fa fa-times\'"></i>\
                                    </button>\
                                </div>'
            }, {
                name: 'Código',
                width: 80,
                type : 'number',
                primaryKey: true,
                field: 'id'
            }, {
                name: 'NomeFantasia',
                width: '*',
                minWidth: 150,
                field: 'nomeFantasia'
            }, {
                name: 'CPF/CNPJ',
                displayName: 'CPF/CNPJ',
                width: 145,
                field: 'cnpj',
                serverField: 'cpfCnpj',
                enableFiltering: true
            }, {
                name: 'Celular',
                width: 120,
                field: 'celular'
            }]
        };
        
        vm.alterarStatus = function (id, ativo) {
            BaseService.post('Cliente', "AlterarStatus", {
                id: id
            }).then(function (response) {
                response.success ? (ativo === 1 ? toastr.success('Cliente inativado com sucesso!') : toastr.success('Cliente reativado com sucesso!')) : toastr.error(response.message);
                vm.gridOptions.dataSource.refresh();
            });
        };
       
        // Controle de aba!!
        $scope.$on('$stateChangeStart', function () {
            PersistentDataService.store('ClienteController', vm, "Cliente", "ClienteCrudController", "cliente.index");
        });

        var selfScope = PersistentDataService.get('ClienteController');
        var filho = PersistentDataService.get('ClienteCrudController');
        if (angular.isDefined(filho)) {
            $timeout(function () {
                $state.go('cliente.cliente-crud', {
                    link: filho.data.cliente.IdCliente > 0 ? 'editar' : 'novo'
                });
            }, 50);
        } else if (angular.isDefined(selfScope)) {
            angular.extend(vm, selfScope.data);

            $timeout(function () {
                vm.gridOptions.dataSource.refresh();
            }, 450);
        }
    }
})();