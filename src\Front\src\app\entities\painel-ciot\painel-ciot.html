<div ng-controller="PainelCiotController as vm">
    <form-header items="vm.headerItems" head="'Painel de CIOT'" state="painelciot"></form-header>
    <div class="animated fadeIn filter-position">
        <div class="row">
        </div>
    </div>
    <div class="wrapper wrapper-content animated fadeIn mt-5">
        <div class="row">
            <div class="col-lg-12">
                <div class="ibox float-e-margins">
                    <div style="height: 38px;" class="ibox-title">
                        <h5>Painel de CIOT</h5>
                        <div ibox-tools></div>
                    </div>
                    <div class="ibox-content">
                        <div class="row">
                            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                                <div class="form-group">
                                    <label class="col-xs-12 col-md-3 control-label" style="text-align: right; padding-top: 10px;">Período:</label>
                                    <div class="input-group col-xs-12 col-md-9">
                                        <input date-range-picker class="form-control date-picker"
                                            ui-date-mask="DD/MM/YYYY - DD/MM/YYYY" type="text" ng-model="vm.date"
                                            options="vm.dateOptions" id="periodoDatePicker" />
                                    </div>
                                </div>
                            </div>
                    
                            <consulta-padrao-modal
                                    idname="consultaEmpresa"
                                    idmodel="Empresa"
                                    tabledefinition="vm.consultaEmpresa"
                                    label="'Empresa:'"
                                    placeholder="vm.isEmpresa() ? vm.getNomeEmpresaUsuarioLogado() : 'Selecione uma empresa'"
                                    validate-on="blur"
                                    ng-disabled="vm.isEmpresa()"
                                    directivesizes="'col-xs-12 col-sm-12 col-md-6 col-lg-6'"
                                    labelsize="'col-xs-12 col-sm-12 col-md-3 col-lg-3 control-label text-align-left'">
                            </consulta-padrao-modal>
                        </div>    
                        

                        <div class="row">
                            <div class="col-6 col-sm-6 col-md-6 mt-10"></div>
                            <div class="col-md-3">
                            </div>
                            <div class="col-md-3 mt-10">
                                <button tooltip-placement="top"
                                        uib-tooltip=""
                                        type='button' ng-click="vm.atualizaTela();"
                                        class="btn btn-labeled btn-primary pull-right">
                                    <span class="btn-label text-right">
                                        <!-- <i class="fa fa-refresh"></i> Ícone de reload-->
                                    </span>
                                    <span class="pl-5 ">Consultar</span>
                                </button>
                            </div>
                        </div>

                        <hr></hr>

                        <div class="pull-right">
                            <button tooltip-placement="top" ng-click="vm.gridOptions.dataSource.refresh();"
                                uib-tooltip="Atualizar" type='button' class="btn btn-labeled btn-default">
                                <i class="fa fa-refresh"></i>
                                <span class="pl-5 ">Atualizar</span>
                            </button>
                            <button tooltip-placement="top" ui-sref="painel-ciot.painel-ciot-crud({link: 'novo'})"
                                uib-tooltip="Cadastrar " type='button' class="btn btn-labeled btn-primary "
                                ng-show="!vm.isAdmin()">
                                <span class="btn-label text-right">
                                    <i class="fa fa-plus"></i>
                                </span>
                                <span class="pl-5 ">Novo</span>
                            </button>
                        </div>

                        <div class="row"></div>
                        <hr>
                        <div ui-grid="vm.gridOptions" ng-style="{height: vm.gridOptions.getGridHeight()}" class="grid" style="width: 100%;"
                            ui-grid-pinning ui-grid-save-state ui-grid-pagination ui-grid-auto-resize
                            ui-grid-resize-columns ui-grid-grouping>
                        </div>


                        <div class="pull-right mt-15">
                            <button type="button" ng-disabled="vm.desabilitarBtnRelatorio"
                                class="btn btn-labeled btn-primary" ng-click="vm.abrirModalRelatorio('gridOptions', vm)">
                                <i class="fa fa-file-pdf-o"></i> Exportar Relatório
                            </button>
                        </div>

                        <div class="row">
                            <div class="row">
                                <div class="col-xs-12 col-md-12">
                                    <div id="exportable-xls">
                                        <table style="font-family:Arial, Helvetica, sans-serif" ng-show="false" id="exportable"
                                               class="table table-bordered" width="100%">
                                            <thead>
                                            <tr>
                                                <th style="text-align: left"
                                                    ng-repeat="option in vm.modalRelatorioOptions"
                                                    ng-if="option.enabled && option.field && option.visible != false">
                                                    {{option.name}}
                                                </th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            <tr style="text-align: left"
                                                ng-repeat="item in vm.dadosRelatorio">
                                                <td ng-repeat="option in vm.modalRelatorioOptions"
                                                    ng-if="option.enabled && option.field && option.visible != false">
                                                    {{option.pipe ? (option.pipe(item[option.field])) : (item[option.field])}}
                                                </td>
                                            </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>