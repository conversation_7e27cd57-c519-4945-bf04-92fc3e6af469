(function () {
    'use strict';

    angular.module('bbcWeb').controller('CentralNotificacaoValePedagioCrudController', CentralNotificacaoValePedagioCrudController);

    CentralNotificacaoValePedagioCrudController.$inject = ['toastr', '$rootScope', 'BaseService', '$state', '$stateParams', '$window', 'PERFIL_ADMINISTRADOR', '$scope', '$timeout', 'PersistentDataService', 'DefaultsService', '$uibModal'];

    function CentralNotificacaoValePedagioCrudController(
        toastr, $rootScope,
        BaseService, $state,
        $stateParams, $window,
        PERFIL_ADMINISTRADOR,
        $scope, $timeout,
        PersistentDataService,
        DefaultsService,
        $uibModal
    ) {
        var vm = this;
        vm.notificacao = [];

        vm.headerItems = [{
            name: 'Movimentações'
        }, {
            name: 'Central de notificações',
            link: 'central-notificacoes.index'
        }];
        
        vm.loadEdit = function (id) {
            BaseService.get('Pedagio', 'ConsultarNotificacaoPorId', {
                idCentralNotificacoes: id
            }).then(function (response) {
                if (!response.success) {
                    toastr.error(response.message);
                    return;
                } else {
                    vm.notificacao = response.data;
                }
            });
        };
        
        vm.loadEdit($stateParams.link);

        vm.onClickVoltar = function () {
            $state.go('central-notificacoes.index');
        };
    }
})();
