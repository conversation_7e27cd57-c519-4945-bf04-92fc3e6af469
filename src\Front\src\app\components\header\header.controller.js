(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('HeaderController', HeaderController);

    HeaderController.$inject = ['$scope', '$rootScope', 'TITULO_SISTEMA', 'BaseService'];

    function HeaderController($scope, $rootScope, TITULO_SISTEMA, BaseService) {
        var vm = this;

        var wtch = $rootScope.$watch('currentAtsDomain', function (newV) {
            if (angular.isDefined(newV))
                carregarConfiguracoes();
        });

        function carregarConfiguracoes() {
            vm.logoTitle = "BBC";
            vm.htmlTitle = "BBC";
            $rootScope.usuarioLogado.logoTitle = "BBC";
            $rootScope.usuarioLogado.htmlTitle = "BBC";
            vm.favicon = 'assets/images/favicon2.png';

        }
    }
})();