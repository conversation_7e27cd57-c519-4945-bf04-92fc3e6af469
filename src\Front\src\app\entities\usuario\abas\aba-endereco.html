<!-- <div class="form-horizontal">
    <hr />
    <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="control-label col-xs-3 col-sm-2 col-md-4 col-lg-3">
                        <span class="text-danger mr-5">*</span>CEP:
                    </label>
                    <div class="input-group col-xs-12 col-sm-12 col-md-8 col-lg-9">
                        <input
                            type="text"
                            name="CEP"
                            ui-br-cep-mask
                            class="form-control"
                            validate-on="blur"
                            ng-blur="vm.buscarEndereco(vm.usuario.cep)"
                            ng-model="vm.usuario.cep"
                            />
                    </div>                        
                </div>
            </div> 
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="control-label col-xs-3 col-sm-2 col-md-4 col-lg-3">
                        <span class="text-danger mr-5">*</span>Estado:
                    </label>
                    <div class="input-group col-xs-12 col-sm-12 col-md-8 col-lg-9">
                        <ui-select
                            name="Estado"
                            ats-ui-select-validator
                            validate-on="blur"
                            ng-model="vm.usuario.estadoId"
                            ng-change="vm.estadoChange(vm.usuario.estadoId)"
                            >
                            <ui-select-match>
                                <span>{{$select.selected.descricao}}</span>
                            </ui-select-match>
                            <ui-select-choices repeat="ex.id as ex in vm.estados | propsFilter: {descricao: $select.search}">
                                <div ng-bind-html="ex.descricao | highlight: $select.search"></div>
                            </ui-select-choices>
                        </ui-select>
                    </div>
                </div>
            </div>  
        </div>
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="control-label col-xs-3 col-sm-2 col-md-4 col-lg-3">
                        <span class="text-danger mr-5">*</span>Cidade:
                    </label>
                    <div class="input-group col-xs-12 col-sm-12 col-md-8 col-lg-9">
                        <ui-select 
                            name="Cidade"
                            ats-ui-select-validator
                            validate-on="blur"
                            ng-disabled="vm.cidadesDisabled"
                            ng-model="vm.usuario.cidadeId"
                            >
                            <ui-select-match>
                                <span>{{$select.selected.descricao}}</span>
                            </ui-select-match>
                            <ui-select-choices repeat="ex.id as ex in vm.cidades | propsFilter: {descricao: $select.search}">
                                <div ng-bind-html="ex.descricao | highlight: $select.search"></div>
                            </ui-select-choices>
                        </ui-select>
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="control-label col-xs-3 col-sm-2 col-md-4 col-lg-3">
                        <span class="text-danger mr-5">*</span>Endereço:
                    </label>
                    <div class="input-group col-xs-12 col-sm-12 col-md-8 col-lg-9">
                        <input
                            type="text"
                            name="Endereço"
                            maxlength="200"
                            class="form-control"
                            validate-on="blur"
                            ng-model="vm.usuario.endereco"
                            />
                    </div>                        
                </div>
            </div> 
        </div>
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="control-label col-xs-3 col-sm-2 col-md-4 col-lg-3">
                        <span class="text-danger mr-5">*</span>Bairro:
                    </label>
                    <div class="input-group col-xs-12 col-sm-12 col-md-8 col-lg-9">
                        <input
                            type="text"
                            name="Bairro"
                            maxlength="100"
                            class="form-control"
                            validate-on="blur"
                            ng-model="vm.usuario.bairro"
                            />
                    </div>                        
                </div>
            </div> 
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="control-label col-xs-3 col-sm-2 col-md-4 col-lg-3">Número:</label>
                    <div class="input-group col-xs-12 col-sm-12 col-md-8 col-lg-9">
                        <input
                            type="text"
                            ats-numeric
                            maxlength="10"
                            ng-model="vm.usuario.enderecoNumero"
                            name="Número"
                            class="form-control"/>
                    </div>                        
                </div>
            </div> 
        </div>
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="control-label col-xs-3 col-sm-2 col-md-4 col-lg-3">Complemento:</label>
                    <div class="input-group col-xs-12 col-sm-12 col-md-8 col-lg-9">
                        <textarea
                            style="resize: none;"
                            type="text"
                            maxlength="200"
                            ng-model="vm.usuario.complemento"
                            name="Complemento"
                            class="form-control">
                        </textarea>
                    </div>                        
                </div>
            </div>
        </div>
    </div>     
</div> -->