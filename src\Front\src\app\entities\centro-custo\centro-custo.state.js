(function () {
    'use strict';

    angular.module('bbcWeb.centro-custo.state', []).config(routeConfig);

    function routeConfig($urlRouterProvider, $stateProvider) {
        $stateProvider.state('centro-custo', {
            abstract: true,
            url: "/centro-custo",
            templateUrl: "app/layout/content.html"
        }).state('centro-custo.index', {
            url: '/index',
            templateUrl: 'app/entities/centro-custo/centro-custo.html'
        }).state('centro-custo.centro-custo-crud', {
            url: '/:link',
            templateUrl: 'app/entities/centro-custo/centro-custo-crud.html'
        });
    }
})();