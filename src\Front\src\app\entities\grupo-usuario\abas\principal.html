<style>
    .fixLabelFile {
        float: right;
        right: -14px;
    }

    .borderSettings {
        border: 1px solid #dcdada;
        padding-top: 3px;
        margin-right: -2px;
        text-align: right;
        border-radius: 7px;
        margin-top: 8px;
        min-height: 69px;
        max-height: 69px;
        height: 69px;
    }

    .borderSettings:hover {
        background-color: whitesmoke;
        /* color: #D74655; */
    }

    #GrupoUsuarioCrudController .ats-switch.switch-small {
        min-width: 30px;
        height: 10px;
    }

    @media (min-width: 600px) {
        #GrupoUsuarioCrudController .xsFix {
            margin-right: 0;
            left: 0;
        }
    }

    @media (min-width: 1200px) {
        .col-lg-4:focus {
            outline: none;
        }

        #GrupoUsuarioCrudController .xsFix {
            margin-right: -49px;
            left: 49px;
        }

    }
</style>
<div class="row">
    <div class="col-sm-12 col-md-6 col-lg-6">
        <div class="form-group">
            <label class="col-xs-12 col-sm-3 col-md-3 col-lg-3 control-label">Código:</label>
            <div class="input-group col-xs-12 col-sm-3 col-md-3 col-lg-3">
                <input type="text" ng-model="vm.grupoUsuario.idGrupoUsuario" class="form-control" disabled
                    value="{{vm.isNew()}}" />
            </div>
        </div>
    </div>
</div>
<div class="row" ng-show="vm.isAdmin()">
    <div class="col-sm-12 col-md-6 col-lg-6">
        <div class="form-group">
            <label class="col-sm-3 col-md-4 col-lg-3 control-label">Sistema:</label>
            <div class="input-group col-sm-9 col-md-8 col-lg-9">
                <ui-select ats-ui-select-validator ng-model="vm.sistema" ng-disabled="!vm.isNew()"
                    ng-change="vm.atualizaModulos()" name="status" id="sistema">
                    <ui-select-match>
                        <span>{{$select.selected.label}}</span>
                    </ui-select-match>
                    <ui-select-choices
                        repeat="ex.data as ex in vm.combosSistema | propsFilter: {label: $select.search}">
                        <div ng-bind-html="ex.label | highlight: $select.search"></div>
                    </ui-select-choices>
                </ui-select>
            </div>
        </div>
    </div>
</div>
<div class="row" ng-if="vm.sistema == 0">
    <div class="col-xs-12 col-md-6" ng-show="vm.isAdmin() && vm.sistema == 0">
        <div class="form-group form-horizontal">
            <label class="control-label col-xs-3 col-sm-2 col-md-4 col-lg-3">
                Empresa(s)</label>
            <p class="input-group">
                <input type="text" ng-disabled="true" placeholder="Empresas" class="form-control" />
                <span class="input-group-btn">
                    <button type="button" ng-disabled="vm.disabledFields()" ng-required="false" class="btn btn-default"
                        ng-click="vm.vincularEmpresa()">
                        <i class="fa fa-search"></i>
                    </button>
                </span>
            </p>
        </div>
    </div>

    <consulta-padrao-modal ng-show="false" ng-disabled="vm.disabledFields()" tabledefinition="vm.consultaEmpresa"
        label="'Empresa:'" placeholder="'Selecione uma Empresa'" functionclear="vm.getModulosClear"
        required-message="'Empresa é obrigatório'" ng-required="false">
    </consulta-padrao-modal>
</div>
<div class="row" ng-if="vm.sistema == 1">
    <consulta-padrao-modal ng-show="vm.isAdmin()" ng-disabled="vm.disabledFields()" tabledefinition="vm.consultaPosto"
        label="'Posto:'" placeholder="'Selecione um Posto'" ng-required="false">
    </consulta-padrao-modal>
</div>
<div class="row">
    <div class="col-sm-12 col-md-6 col-lg-6">
        <div class="form-group">
            <label class="col-xs-12 col-sm-3 col-md-3 col-lg-3 control-label">
                <span class="text-danger mr-5">*</span>Descrição:
            </label>
            <div class="input-group col-xs-12 col-sm-9 col-md-9 col-lg-9">
                <input type="text" required ng-model="vm.grupoUsuario.descricao"
                    required-message="'Descrição é obrigatória'" maxlength="100" validate-on="blur" name="Descrição"
                    class="form-control" />
            </div>
        </div>
    </div>
</div>
<br />
<div class="row">
    <button style="margin-left: 21px;" type="button" class="mr-5 btn-labeled btn btn-danger" tooltip-placement="top"
        uib-tooltip="Excluir itens" ng-disabled="vm.grupoUsuario.empresasVinculadas.length == 0 || !vm.isAdmin()"
        ng-click="vm.excluirItens()">
        <span class="pl-3">Limpar todos</span>
    </button>
    <div class="ibox-content" style="border-style: none !important;">
        <div class="table-responsive tableFixHead">
            <table class="table table-sm table-bordered">
                <thead>
                    <tr style="width: 100% !important">
                        <th style="width: 10% !important;">Código</th>
                        <th style="width: 35% !important;">Nome Fantasia</th>
                        <th style="width: 35% !important;">Razão social</th>
                        <th style="width: 10% !important;">CNPJ</th>
                        <th style="width: 10% !important;">Ação</th>
                    </tr>
                </thead>
                <tbody>
                    <tr ng-repeat="empresaVinculada in vm.grupoUsuario.empresasVinculadas">
                        <td>{{empresaVinculada.id}}</td>
                        <td>{{empresaVinculada.nomeFantasia}}</td>
                        <td>{{empresaVinculada.razaoSocial}}</td>
                        <td>{{empresaVinculada.cnpj}}</td>
                        <td>
                            <button type="button" mwl-confirm class="btn btn-danger btn-xs" placement="top"
                                message="Você tem certeza que deseja excluir esta empresa?" tooltip-placement="top"
                                uib-tooltip="Excluir empresa" confirm-text="Sim" cancel-text="Não"
                                confirm-button-type="danger" cancel-button-type="default" ng-disabled="!vm.isAdmin()"
                                on-confirm="vm.excluirEmpresaVinculada(empresaVinculada)">
                                <i class="fa fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>