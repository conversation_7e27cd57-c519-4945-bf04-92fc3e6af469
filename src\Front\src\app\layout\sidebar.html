<nav class="navbar-default navbar-static-side" role="navigation" ng-controller="SidebarController as vm">
    <div class="sidebar-collapse" metismenu>
        <ul class="nav" id="side-menu_">
            <li class="nav-header animated fadeIn" style="z-index: 1;">
                <center>
                    <div class="profile-element" uib-dropdown>
                        <img alt="Imagem de perfil do Usuário" class="img-circle" src="{{vm.urlFotoUsuario}}" />
                        <a uib-dropdown-toggle="" href>
                            <span class="clear">
                                <span class="block m-t-xs">
                                    <strong class="font-bold">Bem vindo (a), </strong>
                                </span>
                                <span class="text-muted text-xs block">{{usuarioLogado.usuarioNome ?
                                    usuarioLogado.usuarioNome : usuarioLogado.nome}}<b class="caret"></b></span>
                            </span>
                        </a>
                        <ul uib-dropdown-menu class="animated flipInX m-t-xs">
                            <li><a href="" ng-click="main.redefinirSenha()"><PERSON><PERSON> senha</a></li>
                            <li><a ui-sref="logout">Sair</a></li>
                        </ul>
                    </div>

                    <div class="logo-element">
                        BBC CIOT
                        <!-- <img src="../../assets/images/favicon2.png" style="width: 30px;" alt=""> -->
                    </div>

                </center>

            </li>
        </ul>
        <span ats-side-navigation></span>
        <div>
            <div ng-if="vm.loading">
                <div class="sk-spinner sk-spinner-wave">
                    <div class="sk-rect1" style="background-color: white;"></div>
                    <div class="sk-rect2" style="background-color: white;"></div>
                    <div class="sk-rect3" style="background-color: white;"></div>
                    <div class="sk-rect4" style="background-color: white;"></div>
                    <div class="sk-rect5" style="background-color: white;"></div>
                </div>
            </div>
        </div>
    </div>
</nav>