<div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
    <div class="row">
        <div class="col-xs-12 col-md-6">
            <div class="form-group">
                <div class="col-xs-12 col-md-2">
                    <i
                        aria-hidden="true"
                        class="fa fa-check-circle"
                        style="color: #056233; border-color: #056233; font-size: 3em; margin-top: -5%; margin-left: 90%;"
                        title="Farol indicativo de que o E.C. está dentro do período de validação cadastral"
                    ></i>
                </div>
                <label class="control-label col-xs-12 col-md-4">Validação em horas SLA - Dentro do prazo:</label>
                <div class="input-group col-xs-12 col-md-2">
                    <input type="text" class="form-control" ts-numeric onpaste="return event.charCode >= 48 && event.charCode <= 57"
                    onkeypress="return event.charCode >= 48 && event.charCode <= 57" maxlength="4" ng-model="vm.configuracaoSla.farolVerdeHoras" style="" aria-invalid="false" />
                </div>
            </div>
        </div>
        <div class="col-xs-12 col-md-6">
            <div class="form-group">
                <label class="control-label col-xs-12 col-md-4">E-mails - Farol verde:</label>
                <div class="input-group col-xs-12 col-md-8" style="">
                    <textarea style="resize: none;" value="" type="text" multiple maxlength="500" ng-model="vm.configuracaoSla.emailsFarolVerde" name="FarolVerde" class="form-control ng-pristine ng-valid ng-empty ng-touched user-success"> </textarea>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-xs-12 col-md-6">
            <div class="form-group">
                <div class="col-xs-12 col-md-2">
                    <i
                        aria-hidden="true"
                        class="fa fa-info-circle"
                        style="color: #fec726; border-color: #fec726; font-size: 3em; margin-top: -5%; margin-left: 90%;"
                        title="Farol indicativo de que o E.C. está em alerta do período de validação cadastral"
                    ></i>
                </div>
                <label class="control-label col-xs-12 col-md-4">Validação em horas SLA - Em alerta do prazo:</label>
                <div class="input-group col-xs-12 col-md-2">
                    <input type="text" class="form-control" ts-numeric onpaste="return event.charCode >= 48 && event.charCode <= 57"
                    onkeypress="return event.charCode >= 48 && event.charCode <= 57" maxlength="4" ng-model="vm.configuracaoSla.farolAmareloHoras" style="" aria-invalid="false" />
                </div>
            </div>
        </div>
        <div class="col-xs-12 col-md-6">
            <div class="form-group">
                <label class="control-label col-xs-12 col-md-4">E-mails - Farol amarelo:</label>
                <div class="input-group col-xs-12 col-md-8">
                    <textarea style="resize: none;" type="text" maxlength="100" ng-model="vm.configuracaoSla.emailsFarolAmarelo" name="FarolAmarelo" class="form-control ng-pristine ng-valid ng-empty ng-touched user-success"> </textarea>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-xs-12 col-md-6">
            <div class="form-group">
                <div class="col-xs-12 col-md-2">
                    <i
                        aria-hidden="true"
                        class="fa fa-times-circle"
                        style="color: #ed5565; border-color: #056233; font-size: 3em; margin-top: -5%; margin-left: 90%;"
                        title="Farol indicativo de que o E.C. está fora do período de validação cadastral"
                    ></i>
                </div>
                <label class="control-label col-xs-12 col-md-4">E-mails farol Vermelho - Fora do prazo SLA:</label>
                <div class="input-group col-xs-12 col-md-6">
                    <textarea style="resize: none;" type="text" maxlength="500" ng-model="vm.configuracaoSla.emailsFarolVermelho" name="FarolVermelho" class="form-control ng-pristine ng-valid ng-empty ng-touched user-success"> </textarea>
                </div>
            </div>
        </div>
    </div>
</div>