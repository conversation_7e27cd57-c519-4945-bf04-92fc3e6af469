<div id="PainelPedidosPendentesController" ng-controller="PainelPedidosPendentesController as vm">
    <form-header items="vm.headerItems" head="'Painel pedidos pendentes'"></form-header>
    <div class="wrapper wrapper-content animated fadeIn mt-5 overflow-hidden" >
        <div class="row">
            <div class="col-lg-12">
                <div class="ibox float-e-margins">
                    <div style="height: 38px;" class="ibox-title">
                        <h5>Painel pedidos pendentes</h5>
                        <div ibox-tools></div>
                    </div>
                    <div class="ibox-content">
                        <div class="row">
                            <div class="col-xs-12 col-md-6">
                                <label class="col-xs-12 col-md-3 control-label" style="text-align: right; padding-top: 10px;">
                                    Período:</label>
                                <div class="input-group col-xs-12 col-md-9">
                                    <input date-range-picker class="form-control date-picker" type="text"
                                        ng-model="vm.date" options="vm.dateOptions"
                                        style="background-color: white !important;" readonly />
                                </div>
                            </div>
                            <consulta-padrao-modal 
                                tabledefinition="vm.consultaEmpresa" 
                                label="'Empresa:'" idname="consultaEmpresa" 
                                placeholder="'Selecione uma empresa'" 
                                directivesizes="'col-xs-12 col-sm-12 col-md-6 col-lg-6'"
                                labelsize="'col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label'">
                            </consulta-padrao-modal> 
                        </div>
                        <div class="row">
                            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                                <div class="form-group">
                                    <label class="col-xs-12 col-md-3 control-label" style="text-align: right; padding-top: 10px;">
                                        Status:
                                    </label>
                                    <div class="input-group col-xs-12 col-md-9">
                                        <ui-select name="status" ats-ui-select-validator validate-on="blur"
                                            ng-model="vm.status">
                                            <ui-select-match>
                                                <span>{{$select.selected.descricao}}</span>
                                            </ui-select-match>
                                            <ui-select-choices
                                                repeat="ex.id as ex in vm.enumStatus | propsFilter: {descricao: $select.search}">
                                                <div ng-bind-html="ex.descricao | highlight: $select.search"></div>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3 pull-right">
                                <button tooltip-placement="top"
                                    type='button' on-blur="vm.atualizaTela()" ng-click="vm.gridOptions.dataSource.refresh()"
                                    class="btn btn-labeled btn-primary pull-right">
                                    <span class="btn-label text-right">
                                    </span>
                                    <span class="pl-5 ">Consultar</span>
                                </button>
                            </div>
                        </div>
                        <div class="row"></div>
                        <hr>
                        <div ui-grid="vm.gridOptions" ng-style="{height: vm.gridOptions.getGridHeight()}" class="grid" style="width: 100%;" ui-grid-pinning ui-grid-save-state
                            ui-grid-pagination ui-grid-auto-resize ui-grid-resize-columns ui-grid-grouping>
                        </div>
                        <br>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>