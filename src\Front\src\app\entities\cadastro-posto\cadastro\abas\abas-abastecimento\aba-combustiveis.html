<div class="form-horizontal">
    <hr />
    <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
        <div class="row">
            <div class="table-responsive" style="margin-top: 5px;">
                <table class="table table-bordered table-hover col-xs-12">
                    <thead>
                        <tr>
                            <th width="10%">Código</th>
                            <th width="20%">Combustível</th>
                            <th width="20%">Unidade medida</th>
                            <th width="20%">Valor bomba</th>
                            <th width="20%">Valor BBC</th>     
                            <th width="20%">Desconto</th>                       
                        </tr>
                    </thead>
                    <tbody>
                        <tr ng-repeat="combustiveis in vm.posto.postoCombustiveis">
                            <td>{{combustiveis.id}}</td>
                            <td>{{combustiveis.nomeCombustivel}}</td>
                            <td>
                                <p ng-show="combustiveis.unidadeMedida === 'LT'"> Litros (LT) </p>
                                <p ng-show="combustiveis.unidadeMedida === 'MC'"> Metro cubico (MC) </p>
                                <p ng-show="combustiveis.unidadeMedida === 'UN'"> Unidade (UN) </p>
                            </td>
                            <td>
                                <input type="text" ng-model="combustiveis.valorCombustivelBomba" readonly
                                class="no-borders" style="background: none;" ui-money-mask="3" />
                            </td>
                            <td>
                                <input type="text" ng-model="combustiveis.valorCombustivelBBC" readonly
                                class="no-borders" style="background: none;" ui-money-mask="3" />
                            </td>
                            <td>
                                <input type="text" ng-model="combustiveis.valorDesconto" readonly
                                class="no-borders" style="background: none;" ui-money-mask="3" />
                            </td>                                
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>