(function () {
    'use strict';

    angular.module('bbcWeb').controller('PainelAbastecimentoController', PainelAbastecimentoController);

    PainelAbastecimentoController.inject = [
        'BaseService',
        '$scope',
        'PersistentDataService',
        '$timeout'
    ];

    function PainelAbastecimentoController
    (BaseService,
     $scope,
     PersistentDataService,
     $timeout) {
        var vm = this;
        vm.status = 4;
        vm.enumStatusLista = [
            {id: 0, descricao: 'Cancelado'},
            {id: 3, descricao: 'Pendente'},
            {id: 1, descricao: 'Realizado'},
            {id: 4, descricao: 'Todos'}
        ]
        vm.enumStatus = vm.enumStatusLista;
        vm.headerItems = [{name: 'Movimentações'}, {name: 'Painel abastecimento'}];

        vm.dateOptions = {
            timePicker: false,
            applyClass: 'btn-primary',
            locale: {
                applyLabel: "Aplicar",
                fromLabel: "De",
                format: "DD/MM/YYYY",
                toLabel: "Até",
                cancelLabel: 'Cancelar',
                customRangeLabel: 'Período',
                monthNames: ['Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho', 'Julho', 'Agosto', 'Setembro',
                    'Outubro', 'Novembro', 'Dezembro'
                ],
                daysOfWeek: ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb']
            },
            ranges: {
                'Último dia': [moment().add(-1, 'days'), moment()],
                'Últimos 7 dias': [moment().add(-7, 'days'), moment()],
                'Último mês': [moment().subtract(1,'months').startOf('month'), moment().subtract(1,'months').endOf('month')]
            }
        };
        vm.date = {
            startDate: moment().add(-1, 'days'),
            endDate: moment().add(3, 'hours')
        };

        $scope.$watch('vm.date.endDate', function () {
            vm.date.endDate = vm.date.endDate.add(-3, 'hours')
        })

        vm.gridOptions = {
            data: [],
            onRegisterApi: BaseService.dataGrid.defaultOnRegisterApi,
            urlRelatorio: "Abastecimento/ConsultarGridPainelAbastecimento",
            // params: function () {
            //     return {
            //         dtInicial: vm.date.startDate.toDate(),
            //         dtFinal: vm.date.endDate.toDate(),
            //         status: vm.status
            //     }
            // },
            dataSource: {
                autoBind: false,
                url: "Abastecimento/ConsultarGridPainelAbastecimento",
                params: function () {
                    return {
                        DataInicial: vm.date.startDate.toDate(),
                        DataFinal: vm.date.endDate,
                        status: vm.status,
                        EmpresaId: vm.consultaEmpresa.selectedValue ? vm.consultaEmpresa.selectedValue : 0
                    }
                }
            },
            columnDefs: [
                {
                    name: 'Codigo',
                    displayName: 'Código',
                    width: 100,
                    field: 'id',
                    serverField: 'id',
                    type: 'number',
                    enableFiltering: true
                },
                {
                    name: 'Razao Social',
                    displayName: 'Razão Social',
                    width: 135,
                    field: 'razaoSocial',
                    serverField: 'razaoSocial',
                    enableFiltering: false,
                    enableSorting: false
                },
                {
                    name: 'Posto CNPJ',
                    displayName: 'Posto CNPJ',
                    width: 135,
                    field: 'cnpj',
                    serverField: 'cnpj',
                    enableFiltering: false,
                    enableSorting: false
                },
                {
                    name: 'Placa',
                    displayName: 'Placa',
                    width: 135,
                    field: 'placa',
                    serverField: 'placa',
                    enableFiltering: false,
                    enableSorting: false
                },
                {
                    name: 'Data de cadastro',
                    displayName: 'Data de cadastro',
                    width: 140,
                    type: 'date',
                    field: 'dataCadastro',
                    serverField: 'dataCadastro',
                    enableFiltering: false,
                    enableSorting: false
                },
                {
                    name: 'Combustivel',
                    displayName: 'Combustível',
                    width: 100,
                    field: 'combustivelNome',
                    serverField: 'combustivelNome',
                    enableFiltering: false,
                    enableSorting: false
                },
                {
                    name: 'Valor',
                    displayName: 'Valor',
                    width: 135,
                    field: 'valorAbastecimento',
                    serverField: 'valorAbastecimento',
                    cellTemplate: '<div class="ui-grid-cell-contents">\
                                    <input type="text" ng-model="row.entity.valorAbastecimento" readonly\
                                            class="no-borders" style="background: none;" ui-money-mask="3" />\
                               </div>',
                    enableFiltering: false,
                    enableSorting: false
                },
                {
                    name: 'Litragem',
                    displayName: 'Litragem',
                    width: 120,
                    field: 'litragem',
                    serverField: 'litragem',
                    cellTemplate: '<div class="ui-grid-cell-contents">\
                                    <input type="text" ng-model="row.entity.litragem" readonly\
                                            class="no-borders" style="background: none;" ui-number-mask="3" />\
                               </div>',
                    enableFiltering: false,
                    enableSorting: false
                },
                {
                    name: 'Data de Pagamento',
                    displayName: 'Data de Pagamento',
                    width: 140,
                    type: 'date',
                    field: 'dataBaixa',
                    serverField: 'dataBaixa',
                    enableFiltering: false,
                    enableSorting: false
                },
                {
                    name: 'Status',
                    displayName: 'Status',
                    width: 170,
                    field: 'status',
                    serverField: 'status',
                    enableFiltering: false,
                    enableSorting: false
                },
                {
                    name: 'Status Financeiro',
                    displayName: 'Status Financeiro',
                    width: 170,
                    field: 'statusFinanceiro',
                    serverField: 'statusFinanceiro',
                    enableFiltering: false,
                    enableSorting: false
                },
                {
                    name: 'Media realizada',
                    displayName: 'Média realizada',
                    width: 120,
                    field: 'mediaRealizada',
                    serverField: 'mediaRealizada',
                    cellTemplate: '<div class="ui-grid-cell-contents">\
                                    <input type="text" ng-model="row.entity.mediaRealizada" readonly\
                                            class="no-borders" style="background: none;" ui-number-mask="3" />\
                               </div>',
                    enableFiltering: false,
                    enableSorting: false
                },
                {
                    name: 'Media sugerida',
                    displayName: 'Média sugerida',
                    width: 120,
                    field: 'mediaSugerida',
                    serverField: 'mediaSugerida',
                    cellTemplate: '<div class="ui-grid-cell-contents">\
                                    <input type="text" ng-model="row.entity.mediaSugerida" readonly\
                                            class="no-borders" style="background: none;" ui-number-mask="3" />\
                               </div>',
                    enableFiltering: false,
                    enableSorting: false
                },
                {
                    name: 'Media mínima',
                    displayName: 'Média mínima',
                    width: 120,
                    field: 'mediaMinima',
                    serverField: 'mediaMinima',
                    cellTemplate: '<div class="ui-grid-cell-contents">\
                                    <input type="text" ng-model="row.entity.mediaMinima" readonly\
                                            class="no-borders" style="background: none;" ui-number-mask="3" />\
                               </div>',
                    enableFiltering: false,
                    enableSorting: false
                }]
        };

        vm.consultaEmpresa = {
            columnDefs: [{
                name: 'Cód.',
                field: 'id',
                width: 60,
                type: 'number',
                primaryKey: true
            }, {
                name: 'Nome Fantasia',
                field: 'nomeFantasia',
                width: '*'
            }, {
                name: 'Razão Social',
                field: 'razaoSocial',
                width: '*'
            }, {
                name: 'Email',
                field: 'email',
                width: 120
            }],
            desiredValue: 'id',
            desiredText: 'nomeFantasia',
            url: 'Empresa/ConsultarGridEmpresaCombo',
            paramsMethod: function () {
                return {}
            }
        };

        // controle de aba
        $scope.$on('$stateChangeStart', function () {
            PersistentDataService.store('PainelAbastecimentoController', vm, "Painel abastecimento", "PainelAbastecimentoController", "painel-abastecimento.index");
        });


        $timeout(function () {
            PersistentDataService.remove('PainelAbastecimentoController');
        }, 15);

        vm.load = function () {
        }

        vm.atualizaTela = function () {
            vm.gridOptions.dataSource.refresh();
        }  
    }
})();