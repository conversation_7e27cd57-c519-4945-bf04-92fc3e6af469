(function () {
    'use strict';

    angular.module('bbcWeb').controller('VeiculoCrudController', VeiculoCrudController);

    VeiculoCrudController.$inject = ['toastr', '$rootScope', 'BaseService', '$state', '$stateParams', '$window', 'PERFIL_ADMINISTRADOR', '$scope', '$timeout', 'PersistentDataService', 'DefaultsService', '$uibModal'];

    function VeiculoCrudController(
        toastr, $rootScope,
        BaseService, $state,
        $stateParams, $window,
        PERFIL_ADMINISTRADOR,
        $scope, $timeout,
        PersistentDataService,
        DefaultsService,
        $uibModal
    ) {
        var vm = this;
        vm.veiculo = {};
        vm.veiculo.tipoAbastecimento = 1;        
        vm.veiculo.tipoVeiculo = 1;
        vm.veiculo.quantidadeEixos = 0;        
        vm.veiculo.veiculoCombustiveis = [];
        vm.veiculo.controlaAutonomia = false;
        vm.menusPai = [];

        vm.estados = [];
        vm.cidades = [];

        vm.headerItems = [{
            name: 'Cadastros'
        }, {
            name: 'Veículos',
            link: 'veiculo.index'
        }, {
            name: $stateParams.link == 'novo' ? 'Novo' : 'Editar'
        }];

        var selfScope = PersistentDataService.get('VeiculoCrudController');

        function carregarFuncoesIniciais() {
            carregarEstados();
        };

        if ($stateParams.link == 'novo')
            vm.veiculo.id = 'Auto';

        vm.isNew = function () {
            return $stateParams.link == 'novo';
        };

        vm.load = function () {
            carregarFuncoesIniciais(); 
        };

        vm.comboTipoAbatecimento = {
            data: [{ id: 1, descricao: 'Orçamento' }, { id: 2, descricao: 'Autorização' }]
        };

        vm.comboTipoVeiculo = {
            data: [{ id: 1, descricao: 'Automóvel' }, { id: 2, descricao: 'Ônibus' }, { id: 3, descricao: 'Caminhão' }, { id: 4, descricao: 'Caminhão-Trator' }, { id: 5, descricao: 'Inloader' }, { id: 6, descricao: 'Truck speed' }, { id: 7, descricao: 'Carreta' }]
        };

        vm.estadoChange = function (estadoId) {
            vm.cidades = null;
            vm.cidadesDisabled = true;
            vm.veiculo.CidadeId = null;
            carregarCidades(estadoId);
        }

        function carregarEstados() {
            BaseService.get("Endereco", "ConsultarEstados")
                .then(function (response) {
                    if (response.success) {
                        vm.estados = response.data;
                        vm.estadosDisabled = false;
                    }
                });
        };

        function carregarCidades(EstadoId) {
            BaseService.get("Endereco", "ConsultarCidadesPorEstado", {
                estadoId: EstadoId
            }).then(function (response) {
                if (response.success) {
                    vm.cidades = response.data;
                    vm.cidadesDisabled = false;
                }
            });
        };

        vm.loadEdit = function (id) {
            BaseService.get('veiculo', 'ConsultarPorId', {
                idveiculo: id
            }).then(function (response) {
                if (!response.success) {
                    toastr.error(response.message);
                    return;
                } else {
                    vm.veiculo = response.data;

                    if(response.data.estadoId != null){
                        carregarCidades(response.data.estadoId);

                        vm.veiculo.CidadeId = response.data.cidadeId;
                        vm.veiculo.EstadoId = response.data.estadoId;
                    }
                                        
                    vm.consultaProprietario.selectedValue = response.data.portador ? response.data.portador.id : "";
                    vm.consultaProprietario.selectedText = response.data.portador ? response.data.portador.nome : "";
                    
                    vm.consultaFabricante.selectedValue = vm.veiculo.fabricante ? vm.veiculo.fabricante.id : "";
                    vm.consultaFabricante.selectedText = vm.veiculo.fabricante ? vm.veiculo.fabricante.nome : "";

                    vm.consultaFilial.selectedValue = vm.veiculo.filial ? vm.veiculo.filial.id : "";
                    vm.consultaFilial.selectedText = vm.veiculo.filial ? vm.veiculo.filial.nomeFantasia : "";

                    vm.consultaCentroCusto.selectedValue = vm.veiculo.centroCusto ? vm.veiculo.centroCusto.id : "";
                    vm.consultaCentroCusto.selectedText = vm.veiculo.centroCusto ? vm.veiculo.centroCusto.descricao : "";

                    vm.consultaModelo.selectedValue = vm.veiculo.modelo ? vm.veiculo.modelo.id : "";
                    vm.consultaModelo.selectedText = vm.veiculo.modelo ? vm.veiculo.modelo.nome : "";

                    vm.consultaCombustivelPreferencial.selectedValue = vm.veiculo.combustivelPreferencial ? vm.veiculo.combustivelPreferencial.id : "";
                    vm.consultaCombustivelPreferencial.selectedText = vm.veiculo.combustivelPreferencial ? vm.veiculo.combustivelPreferencial.nome : "";

                    vm.consultaEmpresa.selectedValue = vm.veiculo.empresa ? vm.veiculo.empresa.id : "";
                    vm.consultaEmpresa.selectedText = vm.veiculo.empresa ? vm.veiculo.empresa.nomeFantasia : ""; 
                }
            });
        };

        vm.consultaVeiculo = function (placa) {
            var veiculo = {}

            veiculo.Placa = vm.veiculo.placa;
            veiculo.Id = vm.veiculo.id == "Auto" ? 0 : vm.veiculo.id;

            if (vm.consultaProprietario.selectedValue) {
                veiculo.PortadorProprietarioId = vm.consultaProprietario.selectedValue;
            }
            if (vm.veiculo.renavam && vm.veiculo.renavam != "") {
                veiculo.Renavam = vm.veiculo.renavam;
            }

            if (vm.isAdmin()) {
                if (vm.consultaEmpresa) {
                    veiculo.EmpresaId = vm.consultaEmpresa.selectedValue;
                } else {
                    veiculo.EmpresaId = 0
                }
            }

            if (!vm.isAdmin() && vm.consultaEmpresa) {
                veiculo.EmpresaId = vm.consultaEmpresa.selectedValue;
            } else {
                if (!vm.isAdmin()) {
                    veiculo.EmpresaId = $window.localStorage.empresaId;
                }
            }

            if (vm.veiculo.placa && vm.veiculo.placa.length == 7) {
                BaseService.get('Veiculo', 'ConsultarPorPlaca', veiculo, {
                }).then(function (response) {
                    if (!response.success) {
                        toastr.error(response.message);
                        return;
                    } else {
                        if (response.data.data) {
                            if (response && response.data && response.data.data.cadastradoEmOutraEmpresa) {
                                return Sistema.Msg.confirm('Veículo já cadastrado em outra empresa, deseja salvar o mesmo e carregar as informações?', function () {
                                    if (veiculo.EmpresaId == null) {
                                        vm.veiculo.placa = ""
                                        toastr.error("Por favor selecione uma empresa para salvar as informações");
                                    } else {
                                        BaseService.post('Veiculo', 'SalvarVeiculoEmpresa', {
                                            veiculoId: response.data.data.veiculo.id,
                                            empresaId: veiculo.EmpresaId
                                        }).then(function (responseSave) {
                                            if (responseSave && !responseSave.sucesso) {
                                                return toastr.error(responseSave.mensagem);
                                            }
                                            toastr.success('Registro salvo com sucesso');
                                        });

                                        vm.veiculo = response.data.data.veiculo;
                                        vm.consultaProprietario.selectedValue = response.data.data.veiculo.portador.id;
                                        vm.consultaProprietario.selectedText = response.data.data.veiculo.portador.nome;
                                    }
                                }, angular.noop());
                            }
                        } else {
                            if (response.success) {
                                toastr.success(response.data.mensagem);
                            } else {
                                toastr.error(response.data.mensagem);
                            }
                        }
                    }
                });
            } else {
                toastr.error("Preencha corretamente o campo PLACA");
            }
        };

        vm.consultaCombustivel = {
            columnDefs: [{
                name: 'Código',
                field: 'id',
                width: 80,
                type: 'number',
                primaryKey: true
            }, {
                name: 'Nome',
                field: 'nome',
                width: '*',
                minWidth: 250
            }, {
                name: 'Unidade de medida',
                field: 'unidadeMedida',
                enableGrouping: true,
                width: 145
            }],
            desiredValue: 'id',
            desiredText: 'nome',
            url: 'Combustivel/ConsultarGridCombustivel',
            paramsMethod: function () {
                return {}
            }
        };

        vm.consultaCombustivelPreferencial = {
            columnDefs: [{
                name: 'Código',
                field: 'id',
                width: 80,
                type: 'number',
                primaryKey: true
            }, {
                name: 'Nome',
                field: 'nome',
                width: '*',
                minWidth: 250
            }, {
                name: 'Unidade de medida',
                field: 'unidadeMedida',
                enableGrouping: true,
                width: 145
            }],
            desiredValue: 'id',
            desiredText: 'nome',
            url: 'Combustivel/ConsultarGridCombustivel',
            paramsMethod: function () {
                return {}
            }
        };

        //informado a filial para centro de custo para não aver bugs, não implementada nesta versão
        vm.consultaCentroCusto = {
            columnDefs: [{
                name: 'Código',
                width: 80,
                type : 'number',
                primaryKey: true,
                field: 'id'
            }, {
                name: 'Descrição',
                width: '*',
                minWidth: 150,
                field: 'descricao'
            }],
            desiredValue: 'id',
            desiredText: 'descricao',
            url: 'CentroCusto/ConsultarGridCentroCusto',
            paramsMethod: function () {
                return {}
            }
        };

        vm.consultaFilial = {
            columnDefs: [{
                name: 'Código',
                width: 80,
                type : 'number',
                primaryKey: true,
                field: 'id'
            }, {
                name: 'NomeFantasia',
                width: '*',
                minWidth: 150,
                field: 'nomeFantasia'
            }, {
                name: 'CNPJ',
                displayName: 'CNPJ',
                width: 145,
                field: 'cnpj'
            }],
            desiredValue: 'id',
            desiredText: 'nomeFantasia',
            url: 'Filial/ConsultarGridFilial',
            paramsMethod: function () {
                return {}
            }
        };

        vm.consultaFabricante = {
            columnDefs: [{
                name: 'Código',
                width: 80,
                type : 'number',
                primaryKey: true,
                field: 'id'
            }, {
                name: 'Fabricante',
                width: '*',
                minWidth: 150,
                field: 'nome'
            }],
            desiredValue: 'id',
            desiredText: 'nome',
            url: 'Fabricante/ConsultarGridFabricante',
            paramsMethod: function () {
                return {}
            }
        };

        vm.consultaModelo = {
            columnDefs: [{
                name: 'Código',
                width: 80,
                type : 'number',
                primaryKey: true,
                field: 'id'
            }, {
                name: 'Modelo',
                width: '*',
                minWidth: 150,
                field: 'nome'
            }],
            desiredValue: 'id',
            desiredText: 'nome',
            url: 'Modelo/ConsultarGridModelo',
            paramsMethod: function () {
                return {}
            }
        };

        vm.consultaProprietario = {
            columnDefs: [{
                name: 'Código',
                field: 'id',
                width: 80,
                type: 'number',
                primaryKey: true
            }, {
                name: 'Nome',
                field: 'nome',
                width: '*',
                minWidth: 250
            }, {
                name: 'CPF/CNPJ',
                field: 'cpfCnpj',
                enableGrouping: true,
                width: 145
            }],
            desiredValue: 'id',
            desiredText: 'nome',
            url: 'Portador/ConsultarGridPortador',
            paramsMethod: function () {
                return {
                }
            }
        };  

        vm.consultaCarreta = {
            columnDefs: [{
                name: 'Código',
                width: 80,
                type: 'number',
                primaryKey: true,
                field: 'id'
            }, {
                name: 'Placa',
                width: 110,
                field: 'placa'
            }, {
                name: 'Renavam',
                width: '*',
                minWidth: 150,
                field: 'renavam'
            }, {
                name: 'Proprietário',
                width: '*',
                minWidth: 250,
                field: 'portador.nome',
                serverField: 'portador.Nome'
            }],
            desiredValue: 'id',
            desiredText: 'placa',
            url: 'Veiculo/ConsultarGridVeiculo',
            paramsMethod: function () {
                return {
                    TipoVeiculo: '5,7'
                }
            }
        };          

        vm.save = function (form) {
            if (!form.$valid) {
                toastr.error('Todos os campos obrigatórios devem ser preenchidos corretamente com seus respectivos valores');
                return;
            }

            if (vm.isSaving === true)
                return;

            vm.veiculo.id = vm.veiculo.id == "Auto" ? "0" : vm.veiculo.id;

            vm.veiculo.filialId = vm.consultaFilial.selectedValue;
            vm.veiculo.modeloId = vm.consultaModelo.selectedValue;
            vm.veiculo.fabricanteId = vm.consultaFabricante.selectedValue;

            vm.veiculo.combustivelPreferencialId = vm.consultaCombustivelPreferencial.selectedValue == 0 ? null : vm.consultaCombustivelPreferencial.selectedValue;
            vm.veiculo.empresaId = vm.consultaEmpresa.selectedValue ? vm.consultaEmpresa.selectedValue : vm.veiculo.empresaId;
            vm.veiculo.centroCustoId = vm.consultaCentroCusto.selectedValue ? vm.consultaCentroCusto.selectedValue : null;
            vm.veiculo.portadorProprietarioId = vm.consultaProprietario.selectedValue ? vm.consultaProprietario.selectedValue : vm.veiculo.portadorProprietarioId;

            vm.isSaving = true;

            BaseService.post('Veiculo', 'Salvar', vm.veiculo).then(function (response) {
                vm.isSaving = false;

                if (response && response.data && response.data.cadastradoEmOutraEmpresa) {
                    return Sistema.Msg.confirm('Veículo já cadastrado em outra empresa, deseja continuar?', function () {
                        BaseService.post('Veiculo', 'SalvarVeiculoEmpresa', {
                            veiculoId: response.data.veiculoId,
                            empresaId: response.data.empresaId
                        }).then(function (response) {
                            if (response && !response.sucesso) {
                                return toastr.error(response.mensagem);
                            }
                            toastr.success('Registro salvo com sucesso!');
                            $state.go('veiculo.index');
                        });
                    }, angular.noop());
                }

                if (response && !response.sucesso)
                    return toastr.error(response.mensagem);
                else {
                    toastr.success('Veículo salvo com sucesso!');
                    $state.go('veiculo.index');
                }
            });
        };

        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.administrador == true;
        };

        function init() {
            if (vm.isNew()) {
                vm.veiculo.id = 'Auto';
            }

            carregarModais();
            carregarFuncoesIniciais();
        };

        vm.onClickAvancar = function (wizard) {
            wizard.go(wizard.getActivePosition() + 1);
        };

        vm.onClickVoltar = function (wizard) {

            var ativoIndex = wizard.getActivePosition();
            if (ativoIndex == 1)
                $state.go('veiculo.index');

            wizard.go(ativoIndex - 1);
        };

        init();

        $scope.$on('$stateChangeStart', function (arg1, toState) {
            if (toState.name === 'veiculo.index')
                PersistentDataService.remove('VeiculoCrudController');
            else
                PersistentDataService.store('VeiculoCrudController', vm, "Cadastro - Veículo", null, "veiculo.veiculo-crud", vm.veiculo.id);
        });

        if (angular.isDefined(selfScope))
            angular.extend(vm, selfScope.data);
        else {
            if (!vm.isNew())
                vm.loadEdit($stateParams.link);
            else {
                vm.modulos = 1;
                vm.load();
            }
        }

        $timeout(function () {
            PersistentDataService.remove('VeiculoController');
        }, 15);

        function carregarModais() {
            vm.consultaEmpresa = {
                columnDefs: [{
                    name: 'Cód.',
                    field: 'id',
                    width: 60,
                    type: 'number',
                    primaryKey: true
                }, {
                    name: 'Nome Fantasia',
                    field: 'nomeFantasia',
                    width: '*'
                }, {
                    name: 'Razão Social',
                    field: 'razaoSocial',
                    width: '*'
                }, {
                    name: 'Email',
                    field: 'email',
                    width: 120
                }],
                desiredValue: 'id',
                desiredText: 'nomeFantasia',
                url: 'Empresa/ConsultarGridEmpresaCombo',
                paramsMethod: function () {
                    return {}
                }
            };

            //Add/Remover vinculos de combustivel
            vm.adicionarCombustivel = function () {
                var permiteAdicionar = false;

                if (!vm.consultaCombustivel)
                    return toastr.error("Nenhum combustível foi selecionado.");

                if (!vm.autonomia)
                    return toastr.error("Autonomia não informada.");
                
                if (!vm.capacidadeTanque)
                    return toastr.error("Autonomia não informada.");

                var objetosValidos = _.filter(vm.veiculo.veiculoCombustiveis, function (v) {
                    return v.combustivelId === vm.consultaCombustivel.selectedValue;
                });

                if (objetosValidos.length > 0 && (objetosValidos[0].autonomia != vm.autonomia || objetosValidos[0].capacidade != vm.capacidadeTanque)){
                    
                    var edicao = {
                        combustivelNome:vm.consultaCombustivel.selectedText,
                        combustivelId: vm.consultaCombustivel.selectedValue,
                        veiculoId: vm.veiculo.id == "Auto" ? 0: vm.veiculo.id,
                        autonomia: vm.autonomia,
                        capacidade: vm.capacidadeTanque.replace(",", ".")
                    }    
                    
                    for (var i = 0; i < vm.veiculo.veiculoCombustiveis.length; i++) {
                        if (vm.veiculo.veiculoCombustiveis[i].combustivelId == edicao.combustivelId) {
                            var index = vm.veiculo.veiculoCombustiveis.indexOf((vm.veiculo.veiculoCombustiveis[i]));
                            vm.veiculo.veiculoCombustiveis.splice(index, 1)
                        }  
                    }  
                    
                    vm.veiculo.veiculoCombustiveis.push(edicao);
                    vm.clearConsultaCombustivel();
                    toastr.info("Dados alterados com sucesso! Salve o veículo para efetivar alterações!");
                    return;
                    
                }else if (objetosValidos.length > 0) {
                    toastr.error("Este combustível já foi adicionado.");                    
                    vm.clearConsultaCombustivel();
                    return;
                }

                if (vm.consultaCombustivel.selectedEntity != undefined && vm.consultaCombustivel.desiredValue != "" && vm.autonomia) {
                    permiteAdicionar = true;
                }

                if (permiteAdicionar) {
                    var combustivel = {
                        combustivelNome:vm.consultaCombustivel.selectedEntity.nome,
                        combustivelId: vm.consultaCombustivel.selectedValue,
                        veiculoId: vm.veiculo.id == "Auto" ? 0: vm.veiculo.id,
                        autonomia: vm.autonomia,
                        capacidade: vm.capacidadeTanque.replace(",", ".")
                    }            
                    
                    vm.veiculo.veiculoCombustiveis.push(combustivel);
                    vm.clearConsultaCombustivel();
                }
                else
                    toastr.error("Por favor, informe a Combustível/Autonomia.");
            };

            vm.clearConsultaCombustivel = function () {
                vm.consultaCombustivel.selectedEntity = undefined;
                vm.consultaCombustivel.selectedValue = undefined;
                vm.consultaCombustivel.selectedText = "Combustível";
                vm.autonomia = "";
                vm.capacidadeTanque = "";
            };

            vm.removerCombustivel = function (combustiveis) {
                for (var i = 0; i < vm.veiculo.veiculoCombustiveis.length; i++) {
                    if (vm.veiculo.veiculoCombustiveis[i].id == combustiveis.id) {
                        var index = vm.veiculo.veiculoCombustiveis.indexOf((vm.veiculo.veiculoCombustiveis[i]));
                        vm.veiculo.veiculoCombustiveis.splice(index, 1)
                    }   
                }

                if (vm.posto.postoCombustiveis.length < 1) {
                    vm.consultaCombustivel.selectedEntity = undefined;
                    vm.consultaCombustivel.selectedValue = undefined;
                    vm.consultaCombustivel.selectedText = undefined;
                }
            };

            vm.editarCombustivel = function (combustiveis) {
                for (var i = 0; i < vm.veiculo.veiculoCombustiveis.length; i++) {
                    if (vm.veiculo.veiculoCombustiveis[i].id == combustiveis.id) {
                        vm.consultaCombustivel.selectedText = combustiveis.combustivelNome;
                        vm.consultaCombustivel.selectedValue = combustiveis.combustivelId;
                        vm.autonomia = combustiveis.autonomia.toString();
                        vm.capacidadeTanque = combustiveis.capacidade.toString();
                    } 
                }

                if (vm.posto.postoCombustiveis.length < 1) {
                    vm.consultaCombustivel.selectedEntity = undefined;
                    vm.consultaCombustivel.selectedValue = undefined;
                    vm.consultaCombustivel.selectedText = undefined;
                }
            };
            ///

           
        }
    }
})();
