<div ng-controller="RelatorioPortasAbertasController as vm">
    <form-header items="vm.headerItems" head="'Portas Abertas'"></form-header>
    <div class="wrapper-content animated overflow-hidden fadeIn filter-position">
        <div class="row">
            <div class="col-lg-12">
                <div class="ibox">
                    <div class="ibox-title">
                        <h5>Portas Abertas</h5>
                        <div ibox-tools></div>
                    </div>
                    <div class="ibox-content">
                        <form class="row" name="tipo-form">
                            <div class="col-sm-12">
                                <h4 class="text-navy m-t-none mb-30">Consulta</h4>
                                <hr class="mb-30" />
                                <div class="form-horizontal">
                                    <div class="col-sm-12">
                                        <div class="row">
                                            <div id="blockEmpresa" class="col-sm-12 col-md-6 col-lg-6" ng-if="vm.componenteVisivel('blockEmpresa')">
                                                <div class="form-group">
                                                    <label class="col-sm-3 col-md-4 col-lg-3 control-label"><span class="text-danger mr-5">*</span>Empresa:</label>
                                                    <div class="input-group col-sm-9 col-md-8 col-lg-9">
                                                        <ui-select ng-model="vm.portaAberta.empresas.selected" ng-change="vm.getImeis()" required>
                                                            <ui-select-match placeholder="Selecione a empresa">
                                                                <span>{{$select.selected.RazaoSocial}}</span>
                                                                <div id="clearEmpresa" class="fa fa-close m-l-n ui-select-vas" ng-click="vm.clear($event)"></div>
                                                            </ui-select-match>
                                                            <ui-select-choices repeat="empresa.IdEmpresa as empresa in vm.portaAberta.empresas | propsFilter: {RazaoSocial: $select.search, CNPJ: $select.search}">
                                                                <div ng-bind-html="empresa.RazaoSocial | highlight: $select.search"></div>
                                                            </ui-select-choices>
                                                        </ui-select>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-sm-12 col-md-6 col-lg-6">
                                                <div class="form-group">
                                                    <label class="col-sm-3 col-md-4 col-lg-3 control-label">IMEI:</label>
                                                    <div class="input-group col-sm-9 col-md-8 col-lg-9">
                                                        <ui-select multiple id="imei" name="IMEI" ng-model="vm.portaAberta.ImeiSelected">
                                                            <ui-select-match placeholder="IMEI">
                                                                <span>{{$item.Identificacao}}</span>
                                                            </ui-select-match>
                                                            <ui-select-choices repeat="imei.Identificacao as imei in vm.portaAberta.Imeis | propsFilter: {Identificacao: $select.search}">
                                                                <div ng-bind-html="imei.Identificacao | highlight: $select.search"></div>
                                                            </ui-select-choices>
                                                        </ui-select>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12 col-md-6 col-lg-6">
                                                <div class="form-group">
                                                    <label class="col-sm-3 col-md-4 col-lg-3 control-label"><span class="text-danger mr-5">*</span>Data Inicial:</label>
                                                    <p class="input-group">
                                                        <input type="text" class="form-control" uib-datepicker-popup="dd/MM/yyyy" ng-model="vm.portaAberta.dataInicial" is-open="vm.dataInicial.opened" ng-change="vm.changeDataInicial()" show-button-bar="false" ng-required="true" close-text="Close" />
                                                        <span class="input-group-btn">
                                                            <button type="button" class="btn btn-default" ng-click="vm.dataInicial()">
                                                                <i class="glyphicon glyphicon-calendar"></i>
                                                            </button>
                                                        </span>
                                                    </p>
                                                </div>
                                            </div>
                                            <div class="col-sm-12 col-md-6 col-lg-6">
                                                <div class="form-group">
                                                    <label class="col-sm-3 col-md-4 col-lg-3 control-label"><span class="text-danger mr-5">*</span>Data Final:</label>
                                                    <p class="input-group">
                                                        <input type="text" class="form-control" uib-datepicker-popup="dd/MM/yyyy" min-date="vm.minDate()" ng-model="vm.portaAberta.dataFinal" is-open="vm.dataFinal.opened" show-button-bar="false" ng-required="true" close-text="Close" />
                                                        <span class="input-group-btn">
                                                            <button type="button" class="btn btn-default" ng-click="vm.dataFinal()">
                                                                <i class="glyphicon glyphicon-calendar"></i>
                                                            </button>
                                                        </span>
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row"></div>
                                    <hr/>
                                    <div class="col-sm-12 col-md-12 col-lg-12">
                                        <div class="form-group pull-right">
                                            <button tooltip-placement="left" ng-click="vm.gerarPDF()" uib-tooltip="Consultar" type='button' class="mr-5 btn btn-labeled btn-primary">
                        <span class="btn-label text-right"><i class="fa fa-file-pdf-o"></i></span>
                        <span class="pl-5">Gerar PDF</span>
                      </button>
                                            <button tooltip-placement="left" ng-click="vm.gerarExcel()" uib-tooltip="Consultar" type='button' class="mr-5 btn btn-labeled btn-primary">
                        <span class="<btn-la></btn-la>bel text-right"><i class="fa fa-file-excel-o"></i></span>
                        <span class="pl-5">Gerar Excel</span>
                      </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>