<div class="form-horizontal">
    <hr />
    <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label">Código:</label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <input type="text" ng-model="vm.veiculo.id" class="form-control" disabled
                            value="{{vm.isNew()}}" />
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-5 col-lg-3 control-label">
                        <span class="text-danger mr-5">*</span>Placa:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-7 col-lg-9">
                        <input type="text" required ng-disabled="!vm.isNew()" ng-model="vm.veiculo.placa"
                            style="text-transform: uppercase" ui-mask="*******" required-message="'Placa é obrigatória'"
                            maxlength="100" ng-blur="vm.consultaVeiculo(vm.veiculo.placa)" validate-on="blur"
                            name="Placa" class="form-control" />
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-2 col-md-4 col-lg-3">
                        Tipo de veículo:
                    </label>
                    <div class="input-group col-xs-12 col-sm-10 col-md-8 col-lg-9">
                        <ui-select
                            name="tipoVeiculo"
                            id="tipovei"
                            ats-ui-select-validator
                            required required-message="'Tipo de veículo é obrigatória'"
                            validate-on="blur"
                            ng-model="vm.veiculo.tipoVeiculo">
                            <ui-select-match>
                                <span>{{$select.selected.descricao}}</span>
                            </ui-select-match>
                            <ui-select-choices repeat="ex.id as ex in vm.comboTipoVeiculo.data | propsFilter: {descricao: $select.search}">
                                <div ng-bind-html="ex.descricao | highlight: $select.search"></div>
                            </ui-select-choices>
                        </ui-select>
                    </div>
                </div>
            </div>
            <consulta-padrao-modal tabledefinition="vm.consultaFabricante" idname="fabricante" label="'Fabricante:'"
                placeholder="'Selecione um fabricante'" required-message="'Fabricante é obrigatório'"
                ng-required="'true'" directivesizes="'col-xs-12 col-sm-12 col-md-6 col-lg-6'"
                labelsize="'col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label'">
            </consulta-padrao-modal>
        </div>
        <div class="row">
            <consulta-padrao-modal tabledefinition="vm.consultaModelo "
                label="'Modelo:'" placeholder="'Selecione um modelo'"
                required-message="'Modelo é obrigatório'" ng-required="true"
                directivesizes="'col-xs-12 col-sm-12 col-md-6 col-lg-6'"
                labelsize="'col-xs-12 col-sm-3 col-md-5 col-lg-3 control-label'" idname="modeloconsulta">
            </consulta-padrao-modal>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label">
                        <span class="text-danger mr-5">*</span>Ano:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <input type="text" ats-numeric onpaste="return event.charCode >= 48 && event.charCode <= 57"
                            onkeypress="return event.charCode >= 48 && event.charCode <= 57" maxlength="11" required
                            ng-model="vm.veiculo.ano" required-message="'Ano é obrigatório'" maxlength="100"
                            validate-on="blur" name="ano" class="form-control" />
                    </div>
                </div>
            </div>
            
        </div>
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label">
                        <span class="text-danger mr-5">*</span>Renavam:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <input type="text" ats-numeric onpaste="return event.charCode >= 48 && event.charCode <= 57"
                            onkeypress="return event.charCode >= 48 && event.charCode <= 57" maxlength="11" required
                            ng-model="vm.veiculo.renavam" required-message="'Renavam é obrigatório'"
                            validate-on="blur" name="Renavam" class="form-control" />
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label">
                        <span class="text-danger mr-5">*</span>Chassi:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <input type="text" required
                            ng-model="vm.veiculo.chassi" required-message="'Chassi é obrigatório'" maxlength="17"
                            validate-on="blur" name="chassi" class="form-control" />
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label">
                        Quantidade de eixos:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <input type="number" maxlength="2" ng-model="vm.veiculo.quantidadeEixos" 
                            min=0 max="10" validate-on="blur" class="form-control" />
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label">Cor:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <input type="text" ng-model="vm.veiculo.cor" maxlength="20" validate-on="blur" name="cor" class="form-control" />
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <consulta-padrao-modal tabledefinition="vm.consultaCarreta" label="'Carreta:'"
                placeholder="'Selecione um carreta'" idname="consultaCarreta"
                directivesizes="'col-xs-12 col-sm-12 col-md-6 col-lg-6'" 
                labelsize="'col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label'">
            </consulta-padrao-modal>
            <div class="col-xs-12 col-md-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-md-3">Controla autonomia:</label>
                    <div class="input-group col-xs-12 col-md-9">
                        <toggle-switch on-label="Sim" off-label="Não" ng-model="vm.veiculo.controlaAutonomia"class="switch-mini"></toggle-switch>
                    </div>
                </div>
            </div>  
            
        </div>
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-2 col-md-4 col-lg-3">
                        <span class="text-danger mr-5">*</span>Estado:
                    </label>
                    <div class="input-group col-xs-12 col-sm-10 col-md-8 col-lg-9">
                        <ui-select
                            name="Estado"
                            ats-ui-select-validator
                            validate-on="blur"
                            ng-model="vm.veiculo.EstadoId"
                            ng-change="vm.estadoChange(vm.veiculo.EstadoId)"
                            required-message="'Estado é obrigatório'"
                            required>
                            <ui-select-match>
                                <span>{{$select.selected.descricao}}</span>
                            </ui-select-match>
                            <ui-select-choices repeat="ex.id as ex in vm.estados | propsFilter: {descricao: $select.search}">
                                <div ng-bind-html="ex.descricao | highlight: $select.search"></div>
                            </ui-select-choices>
                        </ui-select>
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-2 col-md-4 col-lg-3">
                        <span class="text-danger mr-5">*</span>Cidade:
                    </label>
                    <div class="input-group col-xs-12 col-sm-10 col-md-8 col-lg-9">
                        <ui-select 
                            name="Cidade"
                            ats-ui-select-validator
                            validate-on="blur"
                            ng-model="vm.veiculo.CidadeId"
                            ng-disabled="vm.cidadesDisabled || vm.veiculo.EstadoId == null"
                            required-message="'Cidade é obrigatória'"
                            required>
                            <ui-select-match>
                                <span>{{$select.selected.descricao}}</span>
                            </ui-select-match>
                            <ui-select-choices repeat="ex.id as ex in vm.cidades | propsFilter: {descricao: $select.search}">
                                <div ng-bind-html="ex.descricao | highlight: $select.search"></div>
                            </ui-select-choices>
                        </ui-select>
                    </div>
                </div>
            </div>            
            
        </div>
        <div class="row">
            <consulta-padrao-modal tabledefinition="vm.consultaCentroCusto "
                label="'Centro de custo:'" placeholder="'Selecione um Centro de custo'" idname="consultaCentroCusto"
                required-message="'Centro de custo é obrigatório'" ng-required="false"
                directivesizes="'col-xs-12 col-sm-12 col-md-6 col-lg-6'"
                labelsize="'col-xs-12 col-sm-3 col-md-5 col-lg-3 control-label'">
            </consulta-padrao-modal>
            <consulta-padrao-modal tabledefinition="vm.consultaFilial" label="'Filial:'"
                placeholder="'Selecione uma Filial'" idname="consultaFilial" required-message="'Filial é obrigatória'"
                ng-required="vm.isAdmin()" directivesizes="'col-xs-12 col-sm-12 col-md-6 col-lg-6'"
                labelsize="'col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label'">
            </consulta-padrao-modal>
        </div>
        <div class="row" ng-show="vm.isAdmin()">
            <consulta-padrao-modal ng-disabled="vm.disabledFields()" tabledefinition="vm.consultaProprietario"
                label="'Proprietário:'" placeholder="'Selecione um proprietário'"
                required-message="'Proprietário é obrigatório'" ng-required="true"
                directivesizes="'col-xs-12 col-sm-12 col-md-6 col-lg-6'" idname="consultaProprietario"
                labelsize="'col-xs-12 col-sm-3 col-md-5 col-lg-3 control-label'">
            </consulta-padrao-modal>
            <consulta-padrao-modal tabledefinition="vm.consultaEmpresa" idname="consultaEmpresa" label="'Empresa:'"
                placeholder="'Selecione uma Empresa'" required-message="'Empresa é obrigatória'"
                ng-required="true" directivesizes="'col-xs-12 col-sm-12 col-md-6 col-lg-6'"
                labelsize="'col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label'">
            </consulta-padrao-modal>
        </div>
    </div>
</div>