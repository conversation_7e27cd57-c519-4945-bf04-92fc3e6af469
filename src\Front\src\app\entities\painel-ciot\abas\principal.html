<div class="form-horizontal">
    <hr />
    <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
        <div class="row">
            <center>
                <span ng-show="vm.painelCiot.Sucesso && vm.painelCiot.cpfCnpjProp != null" style="color: green"
                    class="fa fa-exclamation-triangle fa-1x"><span
                        style="font-family: open sans, Helvetica Neue, Helvetica, Arial, sans-serif; font-weight: bold">
                        {{RespRNTRC}}</span></span>
                <span ng-show="vm.painelCiot.Erro && vm.painelCiot.cpfCnpjProp != null" style="color: red" class="fa fa-exclamation-triangle fa-1x"><span
                        style="font-family: open sans, Helvetica Neue, Helvetica, Arial, sans-serif; font-weight: bold">
                        {{RespRNTRC}}</span></span>
                <p></p>
            </center>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-3 col-md-4 col-lg-3">
                        <span ng-show="!vm.isVisualizar()" class="text-danger mr-5">*</span>Tipo viagem:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <ui-select form="formPrincipal" name="TipoViagem" ng-model="vm.painelCiot.tipo" ats-ui-select-validator
                            validate-on="blur" ng-change="vm.TipoChange(vm.painelCiot.tipo)" ng-disabled="!vm.isNew()"
                            required-message="'Tipo de viagem é obrigatório'" required> 
                            <ui-select-match>
                                <span>{{$select.selected.descricao}}</span>
                            </ui-select-match>
                            <ui-select-choices
                                repeat="ex.id as ex in vm.comboTipoCiot.data | propsFilter: {descricao: $select.search}">
                                <div ng-bind-html="ex.descricao | highlight: $select.search"></div>
                            </ui-select-choices>
                        </ui-select>
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-3 col-md-4 col-lg-3">
                        CIOT:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-6">
                        <input name="CIOT" type="text" ng-model="vm.painelCiot.ciot" class="form-control" ng-disabled="true">
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <consulta-padrao-modal labelsize="'control-label col-xs-12 col-sm-3 col-md-4 col-lg-3'"
                function="vm.cadastrarPortador" idname="Proprietario" idmodel="Propietario" ngshowadd="true"
                validate-on="blur" ng-disabled="vm.painelCiot.tipo === undefined || !vm.isNew()"
                functionclear="vm.limpaCpfCnpjProp" tabledefinition="vm.consultaProprietario" label="'Proprietário:'"
                placeholder="'Selecione um Proprietário'" required-message="'Proprietário é obrigatório'"
                ng-required="!vm.isVisualizar()"></consulta-padrao-modal>

            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-3 col-md-4 col-lg-3">CPF/CNPJ:</label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <input type="text" class="form-control" name="CPFCNPJ" ng-model="vm.painelCiot.cpfCnpjProp"
                            ng-disabled="true">
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <consulta-padrao-modal labelsize="'control-label col-xs-12 col-sm-3 col-md-4 col-lg-3'"
                function="vm.cadastrarPortador" idname="Motorista" idmodel="Motorista" ngshowadd="true"
                ng-disabled="vm.disabledFields()" tabledefinition="vm.consultaMotorista"
                functionclear="vm.limpaCpfCnpjMot" label="'Motorista:'" placeholder="'Selecione um Motorista'"
                required-message="'Motorista é obrigatório'" ng-model="vm.painelCiot.cpfCnpjMot" ng-required="!vm.isVisualizar()">
            </consulta-padrao-modal>

            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-3 col-md-4 col-lg-3">CPF:</label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <input type="text" class="form-control" name="CPFCNPJ" ng-model="vm.painelCiot.cpfCnpjMot"
                            ui-mask="999.999.999-99" ng-disabled="true">
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6"> </div>
                
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-3 col-md-4 col-lg-3">
                        <span ng-show="vm.painelCiot.tipo !== 3 && !vm.isVisualizar()" class="text-danger mr-5">*</span>Data final do contrato:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <p class="input-group">
                            <input type="text" ng-change="vm.DataFinalChange(vm.painelCiot.dataFim)"
                                ng-click="vm.datePickerOpen = !vm.datePickerOpen" name="DataFim"
                                invalid-message="'Data Final do Contrato é inválida'" class="form-control"
                                max-date="new Date()"
                                ng-disabled="vm.disabledAgregado() || vm.disabledFields()"
                                current-text="Hoje" clear-text="Limpar" close-text="Fechar"
                                uib-datepicker-popup="dd/MM/yyyy" datepicker-append-to-body="true"
                                ng-model="vm.painelCiot.dataFim" is-open="vm.datePickerOpen" id="data"
                                validate-on="blur" name="data" ng-required="vm.painelCiot.tipo !== 3"
                                required-message="'Data Final do Contrato é obrigatório'" />
                            <span class="input-group-btn">
                                <button type="button" class="btn btn-default"
                                    ng-click="vm.datePickerOpen = !vm.datePickerOpen">
                                    <i class="fa fa-calendar"></i>
                                </button>
                            </span>
                        </p>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-3 col-md-4 col-lg-3"><span class="text-danger mr-5"
                            ng-show="vm.isNew()">*</span>Quantidade das tarifas:</label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <input type="text" maxlength="6" ats-numeric
                            onkeypress="return event.charCode >= 48 && event.charCode <= 57" 
                            placeholder="Informe a quantidade das tarifas"
                            ng-disabled="vm.disabledFields()"
                            ng-model="vm.painelCiot.quantidadeTarifas" name="Quantidade das Tarifas"
                            class="form-control" id="Quantidade das Tarifas" validate-on="blur" ng-required="true"
                            required-message="'Quantidade das tarifas é obrigatório'" />
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group" style="height: 34px">
                    <label class="control-label col-xs-12 col-sm-3 col-md-4 col-lg-3"><span class="text-danger mr-5"
                            ng-show="vm.isNew()">*</span>Valor das tarifas:</label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <span class="input-group-addon" style="height: 34px">R$ </span>
                        <div class="message-required-size">
                            <input type="text" maxlength="16" placeholder="Informe o valor das tarifas"
                                ng-disabled="vm.disabledFields()"
                                ng-model="vm.painelCiot.valorTarifas" ats-price name="Valor das Tarifas"
                                class="form-control" validate-on="blur" id="Valor das Tarifas"
                                ng-required="true" required-message="'Valor das tarifas é obrigatório'" />
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div ng-show="vm.painelCiot.tipo ===3 || vm.painelCiot.tipo ===1">
            <div class="row">
                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                    <div class="form-group">
                        <label class="control-label col-xs-12 col-sm-3 col-md-4 col-lg-3">Valor do frete:</label>
                        <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                            <span class="input-group-addon">R$</span>
                            <div class="message-required-size ">
                                <input type="text" maxlength="16" ng-model="vm.ciotViagem.valorFrete" ats-price
                                    placeholder="Informe o valor do frete"
                                    ng-disabled="!vm.disabledAgregado() || vm.disabledFields() || vm.painelCiot.tipo ===1"
                                    name="Valor do Frete" class="form-control" validate-on="blur" id="Valor do Frete" />
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                    <div class="form-group">
                        <label class="control-label col-xs-12 col-sm-3 col-md-4 col-lg-3">Valor dos impostos:</label>
                        <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                            <span class="input-group-addon">R$</span>
                            <div class="message-required-size ">
                                <input type="text" ng-model="vm.ciotViagem.valorImposto" ats-price
                                    placeholder="Informe o valor dos impostos"
                                    ng-disabled="!vm.disabledAgregado() || vm.disabledFields() || vm.painelCiot.tipo ===1"
                                    id="ValorImposto" name="ValorImposto" class="form-control" validate-on="blur"/>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                    <div class="form-group">
                        <label class="control-label col-xs-12 col-sm-3 col-md-4 col-lg-3">Valor das despesas:</label>
                        <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                            <span class="input-group-addon">R$</span>
                            <input type="text" ng-model="vm.ciotViagem.valorDespesas" ats-price
                                placeholder="Informe o Preço Unitário"
                                ng-disabled="!vm.disabledAgregado() || vm.disabledFields() || vm.painelCiot.tipo ===1"
                                class="form-control" />
                        </div>
                    </div>
                </div>
                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                    <div class="form-group">
                        <label class="control-label col-xs-12 col-sm-3 col-md-4 col-lg-3">Valor do combustível:</label>
                        <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                            <span class="input-group-addon">R$</span>
                            <input type="text" ng-model="vm.ciotViagem.valorCombustivel" ats-price
                                placeholder="Informe o valor do combustível"
                                ng-disabled="!vm.disabledAgregado() || vm.disabledFields() || vm.painelCiot.tipo ===1"
                                class="form-control" />
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                    <div class="form-group">
                        <label class="control-label col-xs-12 col-sm-3 col-md-4 col-lg-3">Valor dos pedágios:</label>
                        <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                            <span class="input-group-addon">R$</span>
                            <input type="text" ng-model="vm.ciotViagem.valorPedagio" ats-price
                                ng-disabled="!vm.disabledAgregado() || vm.disabledFields() || vm.painelCiot.tipo ===1"
                                placeholder="Informe o valor do pedágio" class="form-control" />
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row align-div-veiculo">
            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5>Veículos</h5>
                    <div ibox-tools></div>
                </div>
                <div class="ibox-content">
                    <div class="row">
                        <div class="col-sm-12 col-md-12 col-lg-12">
                            <div class="row">
                                <div class="row">
                                    <div class="text-left">
                                        <div class="form-header">
                                            <consulta-padrao-modal directivesizes="'col-sm-6 col-md-6 col-lg-6'"
                                                labelsize="'col-sm-3 col-md-3 col-lg-3 control-label fixLeftLabel'"
                                                tabledefinition="vm.consultaVeiculo" idname="Veiculo" idmodel="Veiculo"
                                                label="'Veículo:'" function="vm.cadastrarVeiculo" ngshowadd="true"
                                                ng-disabled="!vm.painelCiot.portadorPropId || vm.disabledFields()"
                                                placeholder="'Selecione um veículo'" 
                                                ng-required="vm.painelCiot.veiculo.length === 0" 
                                                required-message="'Veículo é obrigatório'">
                                            </consulta-padrao-modal>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <div class="form-header" style="width: 98%;">
                                            <button type="button" ng-disabled="vm.disabledFields()"
                                                class="mr-5 btn-labeled btn btn-info" ng-click="vm.adicionarVeiculo()">
                                                <i class="fas fa-arrow-down"></i> 
                                                <span class="pl-5">Inserir Veículo</span> 
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <br />
                                <div class="table-responsive">
                                    <table class="table table-bordered table-hover">
                                        <thead>
                                            <tr>
                                                <th width="25%">Placa</th>
                                                <th width="25%">RNTRC</th>
                                                <th width="5%">Ações</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr ng-repeat="veiculo in vm.painelCiot.veiculosList">
                                                <td>{{veiculo.placa}}</td>
                                                <td>{{vm.consultaProprietario.selectedEntity.rntrc ? vm.consultaProprietario.selectedEntity.rntrc : veiculo.rntrc}}</td>
                                                <td class="text-center" style="vertical-align: middle">
                                                    <button type="button" ng-disabled="vm.disabledFields()"
                                                        uib-tooltip="Remover" class="btn btn-xs btn-danger"
                                                        ng-click="vm.removerVeiculo(veiculo)">
                                                        <i class="fa fa-trash-o"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>