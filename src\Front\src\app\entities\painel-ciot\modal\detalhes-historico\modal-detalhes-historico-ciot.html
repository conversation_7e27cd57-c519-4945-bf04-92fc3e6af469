<div>
    <style>
        .custom-modal-width .modal-dialog {
            width: 60%; /* Largura desejada da modal */
            max-width: none; /* Para garantir que a largura personalizada funcione corretamente */
        }

        textarea:hover {
            cursor: pointer;
        }
    </style>
    <form name="formPortador" novalidate show-validation>
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" ng-click="$dismiss()" aria-label="Close">
                    <em class="fa fa-times"></em>
                </button>
                <h4 class="modal-title"><PERSON><PERSON>'s histórico</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-xs-12 col-md-6">
                        <div class="form-group">
                            <label class="control-label col-xs-12 col-md-3">Json Requisição:</label>
                            <div class="input-group col-xs-12 col-md-9">
                                <a ng-click="vm.jsonRequisicao ? vm.copiar(1) : null">
                                    <textarea style="resize: none;" type="text" ng-disabled="!vm.jsonRequisicao"
                                        tooltip-placement="left" uib-tooltip="{{vm.jsonRequisicao ? 'Copiar json envio' : ''}}"
                                        validate-on="blur" multiple maxlength="10000" readonly name="jsonRequisicao" 
                                        rows="25"
                                        class="form-control ng-pristine ng-valid ng-empty ng-valid-maxlength ng-touched user-success">{{ vm.jsonRequisicao ? (vm.prettyPrint(vm.jsonRequisicao) || '') : '' }}
                                    </textarea>
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12 col-md-6">
                        <div class="form-group">
                            <label class="control-label col-xs-12 col-md-3">Json Resposta:</label>
                            <div class="input-group col-xs-12 col-md-9">
                                <a ng-click="vm.jsonResposta ? vm.copiar(2) : null">
                                    <textarea style="resize: none;" type="text" ng-disabled="!vm.jsonResposta"
                                        tooltip-placement="left" uib-tooltip="{{vm.jsonResposta ? 'Copiar json resposta' : ''}}"
                                        validate-on="blur" multiple maxlength="10000" readonly name="jsonResposta"
                                        rows="25"
                                        class="form-control ng-pristine ng-valid ng-empty ng-valid-maxlength ng-touched user-success">{{ vm.jsonResposta ? (vm.prettyPrint(vm.jsonResposta) || '') : '' }}
                                    </textarea>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>