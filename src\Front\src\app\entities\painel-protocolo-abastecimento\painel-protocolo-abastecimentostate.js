(function () {
    'use strict';

    angular.module('bbcWeb.painel-protocolo-abastecimento.state', []).config(routeConfig);

    function routeConfig($stateProvider) {
        $stateProvider
            .state('painel-protocolo-abastecimento', {
                url: "/painel-protocolo-abastecimento",
                abstract: true,
                templateUrl: "app/layout/content.html"})
            .state('painel-protocolo-abastecimento.index', {
                url: '/index',
                templateUrl: 'app/entities/painel-protocolo-abastecimento/painel-protocolo-abastecimento.html'});
    }
})();