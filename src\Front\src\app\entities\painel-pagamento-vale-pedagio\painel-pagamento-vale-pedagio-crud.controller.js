(function () {
    'use strict';

    angular.module('bbcWeb').controller('PainelPagamentoValePedagioCrudController', PainelPagamentoValePedagioCrudController);

    PainelPagamentoValePedagioCrudController.$inject = ['toastr', '$rootScope', 'BaseService', '$state', '$stateParams', '$window', 'PERFIL_ADMINISTRADOR', '$scope', '$timeout', 'PersistentDataService', 'DefaultsService', '$uibModal'];

    function PainelPagamentoValePedagioCrudController(
        toastr, $rootScope,
        BaseService, $state,
        $stateParams, $window,
        PERFIL_ADMINISTRADOR,
        $scope, $timeout,
        PersistentDataService,
        DefaultsService,
        $uibModal
    ) {
        var vm = this;
        vm.pagamentoValePedagio = {};
        vm.status = 100;

        vm.headerItems = [{
            name: 'Movimentações'
        }, {
            name: 'Painel pagamento vale pedágio',
            link: 'painel-pagamento-vale-pedagio.index'
        }, {
            name: $stateParams.link === 'novo' ? 'Visualizar' : 'Visualizar'
        }];

        vm.enumStatusLista = [
            { id: 0, descricao: 'Baixado' },
            { id: 1, descricao: 'Cancelado' },
        ]
        vm.enumStatus = vm.enumStatusLista;

        vm.load = function () {
        }

        vm.loadEdit = function (id) {
            id = parseInt(id);
            BaseService.get('Pedagio', 'ConsultarPorId', {
                idPagamentoValePedagio: id
            }).then(function (response) {
                if (!response.success) {
                    toastr.error(response.message);
                    return;
                }
                vm.pagamentoValePedagio = response.data;

                vm.pagamentoValePedagio.jsonEnvio = prettyPrint(vm.pagamentoValePedagio.jsonEnvio)  
                vm.pagamentoValePedagio.jsonRetorno = prettyPrint(vm.pagamentoValePedagio.jsonRetorno);    
            });
        };

        function prettyPrint(objJson) {
            var obj = JSON.parse(objJson);
            var pretty = JSON.stringify(obj, undefined, 4);
            return pretty;
        }

        vm.VerTransacao = function (
            idTransacao, 
            jsonEnvioDock, 
            jsonRespostaDock,
            responseCodeDock,
            dataCadastroReq,
            dataRetornoDock) {
            $uibModal.open({
                animation: true,
                ariaLabelledBy: 'modal-title',
                ariaDescribedBy: 'modal-body',
                templateUrl: 'app/entities/painel-pagamento-vale-pedagio/detalhe-transacao/modal-detalhe-transacao.html',
                controller: 'ModalDetalhesTransacaoController',
                controllerAs: 'vm',
                keyboard: true,
                size: 'lg',
                windowClass: 'custom-modal-width',
                resolve: {
                    idTransacao: function() {
                        return idTransacao;
                    },
                    jsonEnvioDock: function() {
                        return jsonEnvioDock;
                    },
                    jsonRespostaDock: function() {
                        return jsonRespostaDock;
                    },
                    responseCodeDock: function() {
                        return responseCodeDock;
                    },
                    dataCadastroReq: function() {
                        return dataCadastroReq;
                    },
                    dataRetornoDock: function() {
                        return dataRetornoDock;
                    }
                }
                
            });
        };

        vm.gridOptions = {
            data: [],
            onRegisterApi: BaseService.dataGrid.defaultOnRegisterApi,
            urlRelatorio: "",
            dataSource: {
                url: "Pedagio/ConsultarGridTransacaoPagamentoValePedagio",
                params: function () {
                    return {
                        PagamentoValePedagioId: $stateParams.link
                    }
                },
            },
            columnDefs: [
                { 
                name: 'Ações',
                width: '5%',
                cellTemplate: '<div class="ui-grid-cell-contents" align="center" title="TOOLTIP">\
                        <button tooltip-placement="right" uib-tooltip="Visualizar detalhes" type="button" \
                            ng-class="{ \'btn btn-xs btn-info\': true }" \
                            ng-click="grid.appScope.vm.VerTransacao(row.entity.id, row.entity.jsonEnvioDock, row.entity.jsonRespostaDock, row.entity.responseCodeDock, row.entity.dataCadastro, row.entity.dataRetornoDock)">\
                            <i class="fa fa-eye"></i>\
                        </button>\
                        </div>\
                    </div>'
                },
                {
                    name: 'id',
                    displayName: 'Código',
                    width: 150,
                    field: 'id',
                    serverField: 'id',
                    type: 'number',
                    enableFiltering: true
                },
                {
                    name: 'TipoTransacao',
                    displayName: 'Tipo de Transação',
                    width: 175,
                    field: 'tipoTransacao',
                    serverField: 'tipoTransacao',
                    enableFiltering: false,
                },
                {
                    name: 'DataCadastro',
                    displayName: 'Data de Cadastro',
                    width: '*',
                    minWidth: 150,
                    field: 'dataCadastro',
                    serverField: 'dataCadastro',
                    enableFiltering: true
                },
                {
                    name: 'DataBaixa',
                    displayName: 'Data Baixa',
                    width: '*',
                    minWidth: 150,
                    field: 'dataBaixa',
                    serverField: 'dataBaixa',
                    enableFiltering: true
                },
                {
                    name: 'Valor',
                    displayName: 'Valor',
                    width: 175,
                    field: 'valor',
                    serverField: 'valor',
                    enableFiltering: false
                },
                {
                    name: 'Status',
                    displayName: 'Status',
                    width: 175,
                    field: 'status',
                    serverField: 'status',
                    enableFiltering: false,
                },
                {
                    name: 'ContaOrigem',
                    displayName: 'Conta Origem',
                    width: 175,
                    field: 'contaOrigem',
                    serverField: 'contaOrigem',
                    enableFiltering: false
                },
                {
                    name: 'ContaDestino',
                    displayName: 'Conta Destino',
                    width: 170,
                    field: 'contaDestino',
                    serverField: 'contaDestino',
                    enableFiltering: false,
                },
                {
                    name: 'UUID',
                    displayName: 'UUID',
                    width: 170,
                    field: 'uuid',
                    serverField: 'uuid',
                    enableFiltering: true
                },
                {
                    name: 'Mensagem',
                    displayName: 'Mensagem',
                    width: 400,
                    field: 'mensagem',
                    serverField: 'mensagem',
                    enableFiltering: false,
                },
                {
                    name: 'JsonEnvioDock',
                    displayName: 'Json envio Dock',
                    width: 250,
                    field: 'jsonEnvioDock',
                    serverField: 'JsonEnvioDock',
                    enableFiltering: false,
                },
                {
                    name: 'JsonRespostaDock',
                    displayName: 'Json resposta Dock',
                    width: 250,
                    field: 'jsonRespostaDock',
                    serverField: 'jsonRespostaDock',
                    enableFiltering: false,
                },
            ]
        };

        vm.atualizaAbaHistorico = function () {
            vm.gridPagamentoValePedagioHistoricoOptions.dataSource.refresh();
        }
        
        vm.gridPagamentoValePedagioHistoricoOptions = {
            data: [],
            enableFiltering: true,
            dataSource: {
                autoBind: false,
                url: "Pedagio/ConsultarGridPagamentoValePedagioHistorico",
                params: function () {
                    return {
                        PagamentoValePedagioId: $stateParams.link
                    }
                },
            },
            onRegisterApi: BaseService.dataGrid.getGridApiRegistration("gridPagamentoValePedagioHistoricoOptions"),
            columnDefs: [{
                name: 'id',
                displayName: 'Código',
                width: 150,
                field: 'id',
                serverField: 'id',
                type: 'number',
                enableFiltering: true
            },
            {
                name: 'DataCadastro',
                displayName: 'Data de Cadastro',
                width: '*',
                minWidth: 150,
                field: 'dataCadastro',
                serverField: 'dataCadastro',
                enableFiltering: true
            },
            {
                name: 'DataBaixa',
                displayName: 'Data de Baixa',
                width: '*',
                minWidth: 150,
                field: 'dataBaixa',
                serverField: 'dataBaixa',
                enableFiltering: true
            },
            {
                name: 'Valor',
                displayName: 'Valor',
                width: 175,
                field: 'valor',
                serverField: 'valor',
                enableFiltering: false
            },
            {
                name: 'ValorComplemento',
                displayName: 'Valor Complemento',
                width: 175,
                field: 'valorComplemento',
                serverField: 'valorComplemento',
                enableFiltering: false
            },
            {
                name: 'ValorTotal',
                displayName: 'Valor Total',
                width: 175,
                field: 'valorTotal',
                serverField: 'valorTotal',
                enableFiltering: false
            },
            {
                name: 'ValorTarifa',
                displayName: 'Valor Tarifa',
                width: 175,
                field: 'valorTarifa',
                serverField: 'valorTarifa',
                enableFiltering: false
            },
            {
                name: 'PagamentoTarifa',
                displayName: 'Pagamento Tarifa',
                width: 170,
                field: 'pagamentoTarifa',
                serverField: 'pagamentoTarifa',
                enableFiltering: false,
                cellTemplate: '<div class="ui-grid-cell-contents" align="left">\
                                        <p ng-show="row.entity.pagamentoTarifa === 0"> Não </p>\
                                        <p ng-show="row.entity.pagamentoTarifa === 1"> Sim </p>\
                                   </div>'
            },
            {
                name: 'CNPJContratante',
                displayName: 'CNPJ Contratante',
                width: 175,
                field: 'empresaCnpj',
                serverField: 'empresaCnpj',
                pipe: function (input) {
                    return filterFormatCNPJ(input)
                },
                enableFiltering: true
            },
            {
                name: 'CodigoExterno',
                displayName: 'Código Externo',
                width: 170,
                field: 'codigoExterno',
                serverField: 'codigoExterno',
                type: 'number',
                enableFiltering: true
            },
            {
                name: 'CodigoValePedagio',
                displayName: 'Código Vale Pedágio',
                width: 170,
                field: 'codigoValePedagio',
                serverField: 'codigoValePedagio',
                type: 'number',
                enableFiltering: true
            },
            {
                name: 'FormaPagamento',
                displayName: 'Forma de Pagamento',
                width: 170,
                field: 'formaPagamento',
                serverField: 'formaPagamento',
                enableFiltering: true,
                enum: true,
                enumTipo: 'EFormaPagamentoPedagio',
                cellTemplate: '<div class="ui-grid-cell-contents" align="left">\
                                        <p ng-show="row.entity.formaPagamento == 0"> Vale Pedágio BBC </p>\
                                   </div>'
            },
            {
                name: 'Status',
                displayName: 'Status',
                width: 170,
                field: 'status',
                serverField: 'status',
                enableFiltering: false,
                enum: true,
                enumTipo: 'EStatusPagamentoPedagio',
                cellTemplate: '<div class="ui-grid-cell-contents" align="left">\
                                        <p ng-show="row.entity.status === 0"> Processando </p>\
                                        <p ng-show="row.entity.status === 1"> Baixado </p>\
                                        <p ng-show="row.entity.status === 2"> Pendente pagamento </p>\
                                        <p ng-show="row.entity.status === 3"> Pendente cancelamento </p>\
                                        <p ng-show="row.entity.status === 4"> Cancelado </p>\
                                   </div>'
            },
            {
                name: 'mensagem',
                displayName: 'Mensagem',
                width: 400,
                field: 'mensagem',
                serverField: 'mensagem',
                enableFiltering: false
            },
            {
                name: 'DataAlteracao',
                displayName: 'Data Alteração',
                width: '*',
                minWidth: 150,
                field: 'dataAlteracao',
                serverField: 'dataAlteracao',
                enableFiltering: true
            },
            {
                name: 'DataCancelamento',
                displayName: 'Data Cancelamento',
                width: '*',
                minWidth: 150,
                field: 'dataCancelamento',
                serverField: 'dataCancelamento',
                enableFiltering: true
            },
            {
                name: 'ContadorReenvio',
                displayName: 'Contador Reenvio',
                width: 170,
                field: 'contadorReenvio',
                serverField: 'contadorReenvio',
                type: 'number',
                enableFiltering: false
            }]
        };

        

        vm.isNew = function () {
            return $stateParams.link == 'novo';
        };

        if (angular.isDefined(selfScope))
            angular.extend(vm, selfScope.data);
        else
            if (!vm.isNew())
                vm.loadEdit($stateParams.link);
            else
                vm.load();

        vm.onClickVoltar = function (wizard) {

            var ativoIndex = wizard.getActivePosition();
            if (ativoIndex == 1)
                $state.go('painel-pagamento-vale-pedagio.index');

            wizard.go(ativoIndex - 1);
        };

        var selfScope = PersistentDataService.get('PainelPagamentoValePedagioCrudController');



        // DO NOT TOUCH!! 666
        $scope.$on('$stateChangeStart', function (arg1, toState) {
            if (toState.name === 'painel-pagamento-vale-pedagio.index')
                PersistentDataService.remove('PainelPagamentoValePedagioCrudController');
            else
                PersistentDataService.store('PainelPagamentoValePedagioCrudController', vm, "Movimentação - Painel de pagamento vale pedágio", null, "painel-pagamento-vale-pedagio.painel-pagamento-vale-pedagio-crud", vm.painelPagamento.id);
        });

        $timeout(function () {
            PersistentDataService.remove('PainelPagamentoValePedagioController');
        }, 15);
    }
})();
