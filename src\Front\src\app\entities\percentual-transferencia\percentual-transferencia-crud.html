<style>
	#PercentualTransferenciaCrudController .fixLRpg {
		padding-left: 4px !important;
		padding-right: 4px !important;
		text-align: -webkit-center;
	}

	.widthHF {
		width: 25px;
	}

	.colorGreen {
		color: green;
	}

	.form-wizard > ol > li {
		min-height: 50px;
		padding-top: 10px;
		padding-bottom: 7px;
		background-color: #eeeeee;
		border-color: #eaeaea;
		border-style: solid;
		cursor: pointer;
		border-width: 2px;
	}

	#PercentualTransferenciaCrudController .imgPassCombination {
		position: relative;
		top: 5px;
		right: -37px;
	}

	#PercentualTransferenciaCrudController .ui-select-bootstrap .ui-select-toggle > a.btn {
		position: absolute !important;
		height: 10px !important;
		right: 10px !important;
		margin-top: 0px !important;
	}

	#PercentualTransferenciaCrudController .imgPercentualTransferencia,
	.imgPercentualTransferenciaFile {
		position: absolute;
		margin: -20px 105px;
		margin-left: 50%;
		height: 128px;
		width: 128px;
		border-radius: 65%;
		object-fit: cover;
		object-position: center;
	}

	#PercentualTransferenciaCrudController .inputUploadImg {
		position: absolute;
		right: 0;
		top: 117px;
	}

	.form-wizard > ol > li {
		min-height: 33px;
		padding-top: 2px;
		padding-bottom: 0px;
		background-color: #eeeeee;
		border-color: #eaeaea;
		border-style: solid;
		cursor: pointer;
		border-width: 2px;
	}
</style>
<div id="PercentualTransferenciaController" ng-controller="PercentualTransferenciaCrudController as vm">
    <form-header ng-submit="return" items="vm.headerItems" head="'% Transferência Automática'"></form-header>
    <div class="wrapper-content animated overflow-hidden fadeIn mb-0 pb-0">
        <div class="row">
            <div class="col-lg-12">
                <div class="ibox">
                    <div class="ibox-title">
                        <h5>{{vm.isNew() ? 'Nova configuração de' : 'Editar'}} percentuais de transferência
                            automática</h5>
                        <div ibox-tools></div>
                    </div>
                    <div class="ibox-content wizard">
                        <form name="frmPercentualTransferenciaCrud" role="form" novalidate ats-validator
                              ng-submit="vm.save(frmPercentualTransferenciaCrud)" show-validation>
                            <div form-wizard steps="3">
                                <div class="form-wizard">
                                    <ol class="row">
                                        <li ng-click="vm.defineGridOptions(1); wizard.go(1)" class="fixLRpg col-sm-4"
                                            ng-class="{'active': wizard.active(1)}">
                                            <h4>Principal</h4>
                                        </li>
                                        <li ng-click="vm.defineGridOptions(2); wizard.go(2)" class="fixLRpg col-sm-4"
                                            ng-class="{'active': wizard.active(2)}">
                                            <h4>Histórico por Motorista</h4>
                                        </li>
                                        <li ng-click="vm.defineGridOptions(3); wizard.go(3)" class="fixLRpg col-sm-4"
                                            ng-class="{'active': wizard.active(3)}">
                                            <h4>Histórico do Cadastro</h4>
                                        </li>
                                    </ol>
                                    <br/>
                                </div>
                                <div id="activateTab1" ng-show="wizard.active(1)">
                                    <div ng-include="'app/entities/percentual-transferencia/abas/aba-principal.html'"
                                         class="form-horizontal"></div>
                                </div>
                                <div id="activateTab2" ng-show="wizard.active(2)">
                                    <div ng-include="'app/entities/percentual-transferencia/abas/aba-historico-motoristas.html'"
                                         class="form-horizontal"></div>
                                </div>
                                <div id="activateTab3" ng-show="wizard.active(3)">
                                    <div ng-include="'app/entities/percentual-transferencia/abas/aba-historico-cadastro.html'"
                                         class="form-horizontal"></div>
                                </div>
                            </div>
                            <div class="row clearfix"></div>
                            <hr-label dark="true"></hr-label>
                            <br/>
                            <div class="row">
                                <div class="form-group">
                                    <div class="col-md-12 col-lg-12 text-right">
                                        <button type="button" ng-disabled="vm.isSaving"
                                                ng-click="vm.onClickVoltar(wizard)"
                                                class="btn btn-labeled btn-default">
                                            <span class="btn-label">
                                                <i class="fa fa-arrow-circle-left"></i>
                                            </span>
                                            Voltar
                                        </button>
                                        <button type="button" ng-disabled="vm.isSaving" ng-show="!wizard.active(3)"
                                                ng-click="vm.onClickAvancar(wizard)"
                                                class="btn btn-labeled btn-default">
                                            <span class="btn-label">
                                                <i class="fa fa-arrow-circle-right"></i>
                                            </span>
                                            Avançar
                                        </button>
                                        <button type="submit" ng-disabled="vm.isSaving" ng-show="false"
                                                class="btn btn-labeled btn-success text-right"
                                                data-style="expand-right">
                                            <span class="btn-label">
                                                <i class="fa fa-check-circle"></i>
                                            </span>
                                            Salvar
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>