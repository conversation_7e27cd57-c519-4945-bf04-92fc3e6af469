<div class="form-horizontal">
    <hr/>
    <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
        <hr-label dark="true" title="'Dados do Proprietário'"></hr-label>
        <br/>
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3 col-lg-3">
                        Código:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-9 col-lg-9">
                        <input type="text" class="form-control" disabled
                               value="{{vm.percentualTransferencia.proprietarioId}}"/>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3 col-lg-3">
                        Recebedor do Frete (Proprietário Contratado):
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-9 col-lg-9">
                        <input type="text" class="form-control" disabled
                               value="{{vm.percentualTransferencia.proprietarioNome}}"/>
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3 col-lg-3">
                        CPF/CNPJ:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-9 col-lg-9">
                        <input type="text" class="form-control" disabled
                               value="{{vm.percentualTransferencia.proprietarioCpf}}"/>
                    </div>
                </div>
            </div>
        </div>
        <hr-label dark="true" title="'% Transferência'"></hr-label>
        <br/>
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-3 col-lg-3">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-6 col-lg-6 control-label">
                        <span ng-if="vm.percentualTransferencia.paraTodosMotoristas"
                              class="text-danger mr-5">*</span>Adiantamento:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-6 col-lg-6">
                        <input type="number"
                               class="form-control"
                               validate-on="blur"
                               name="Adiantamento"
                               ng-model="vm.percentualTransferencia.adiantamento"
                               max="100"
                               ng-disabled="vm.percentualTransferenciaLoading"
                               ng-required="vm.percentualTransferencia.paraTodosMotoristas"
                               required-message="'Adiantamento é obrigatório'"/>
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-3 col-lg-3">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-6 col-lg-6 control-label">
                        <span ng-if="vm.percentualTransferencia.paraTodosMotoristas"
                              class="text-danger mr-5">*</span>Saldo:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-6 col-lg-6">
                        <input type="number"
                               class="form-control"
                               validate-on="blur"
                               name="Saldo"
                               ng-model="vm.percentualTransferencia.saldo"
                               max="100"
                               ng-disabled="vm.percentualTransferenciaLoading"
                               ng-required="vm.percentualTransferencia.paraTodosMotoristas"
                               required-message="'Saldo é obrigatório'"/>
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-3 col-lg-3">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-6 col-lg-6 control-label">Para todos os motoristas:</label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-6 col-lg-6">
                        <toggle-switch on-label="Sim" off-label="Não"
                                       ng-model="vm.percentualTransferencia.paraTodosMotoristas"
                                       class="switch"></toggle-switch>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <div class="col-xs-12 col-sm-12 col-md-3 col-lg-3 text-right">
                    <button type="submit" ng-disabled="vm.isSaving || vm.percentualTransferenciaLoading"
                            class="btn btn-labeled btn-success text-right"
                            data-style="expand-right">
                        <span class="btn-label">
                            <i class="fa fa-check-circle"></i>
                        </span>
                        Salvar
                    </button>
                </div>
            </div>
        </div>
        <hr-label dark="true" title="'Adicionar motoristas'"></hr-label>
        <br>
        <div class="row">
            <consulta-padrao-modal tabledefinition="vm.consultaPortador" label="'Consultar Motoristas:'"
                                   placeholder="'Procure motoristas'"
                                   directivesizes="'col-xs-12 col-sm-12 col-md-6 col-lg-6'"
                                   labelsize="'col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label'"
                                   ng-disabled="vm.comboMotoristasDisabled || vm.percentualTransferenciaLoading || vm.percentualTransferencia.paraTodosMotoristas"
                                   ng-if="vm.consultaPortador">
            </consulta-padrao-modal>
            <div class="form-group">
                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6 text-right">
                    <button type="button" ng-disabled="vm.comboMotoristasDisabled || vm.percentualTransferenciaLoading || vm.percentualTransferencia.paraTodosMotoristas"
                            ng-click="vm.adicionarMotorista()"
                            class="btn btn-labeled btn-success text-right"
                            data-style="expand-right">
                        <span class="btn-label">
                            <i class="fa fa-plus"></i>
                        </span>
                        Adicionar
                    </button>
                </div>
            </div>           
        </div>
        <hr-label dark="true" title="'Motoristas Configurados'"></hr-label>
        <br/>
        <div class="row">
            <div class="col-lg-12">
                <div class="ibox float-e-margins">
                    <div>
                        <div class="pull-right">
                            <button tooltip-placement="top" ng-click="vm.gridOptions.dataSource.refresh();" 
                                    ng-disabled="vm.percentualTransferenciaLoading"
                                    uib-tooltip="Atualizar" type='button' class="btn btn-labeled btn-default">
                                <i class="fa fa-refresh"></i>
                                <span class="pl-5 ">Atualizar</span>
                            </button>
                        </div>
                        <div class="row"></div>
                        <hr>
                        <div ui-grid="vm.gridOptions"
                             ng-if="vm.gridOptionsActive === 1"
                             ng-style="{height: vm.gridOptions.getGridHeight()}"
                             class="grid" style="width: 100%;"
                             ui-grid-pinning ui-grid-save-state
                             ui-grid-pagination ui-grid-auto-resize ui-grid-resize-columns ui-grid-grouping>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>