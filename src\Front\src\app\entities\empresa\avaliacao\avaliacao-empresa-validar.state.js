(function () {
    'use strict';

    angular.module('bbcWeb.empresa-avaliacao.state', []).config(routeConfig);

    function routeConfig($urlRouterProvider, $stateProvider) {
        $stateProvider.state('empresa-avaliacao', {
            abstract: true,
            url: "/empresa-avaliacao",
            templateUrl: "app/layout/content.html"
        })
        .state('empresa-avaliacao.avaliacao', {
            url: '/avaliacao',
            templateUrl: 'app/entities/empresa/avaliacao/avaliacao-empresa.html'
        })
        .state('empresa-avaliacao.validar', {
            url: '/:link',
            templateUrl: 'app/entities/empresa/avaliacao/avaliacao-empresa-validar.html'
        });
    }
})();