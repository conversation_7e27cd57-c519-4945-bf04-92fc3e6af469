/* Only demo */
@media (max-width: 1000px) {
    .welcome-message {
        display: none;
    }
}

.ibox-title {
    -moz-border-bottom-colors: none;
    -moz-border-left-colors  : none;
    -moz-border-right-colors : none;
    -moz-border-top-colors   : none;
    background-color         : #ffffff;
    border-color             : #e7eaec;
    -o-border-image          : none;
    border-image             : none;
    border-style             : solid solid none;
    border-width             : 2px 0 0 !important;
    color                    : inherit !important;
    margin-bottom            : 0 !important;
    padding                  : 7px 15px 0px !important;
    min-height               : 31px !important;
}

.boldWhite {
    font-weight     : bold !important;
    color           : #056233 !important;
    background-color: rgba(0, 0, 0, 0.13);
}

.scrollable-menu {
    height    : auto;
    max-height: 200px;
    overflow-x: hidden;
}

.filter-position {
    .wrapper;
    .wrapper-content;
    .mb-0;
    .pb-0;
    position  : relative;
    // z-index: 2;
}

.flex-group {
    display            : -webkit-flex;
    display            : flex;
    -webkit-align-items: center;
    align-items        : center;
}

.flex-group .control-label {
    padding-top: 0;
}

.control-label-modal {
    text-align: left !important;
}



.imgRecarregar {
    margin-top        : 0px;
    cursor            : pointer;
    -webkit-transition: -webkit-transform .8s ease-in-out;
    transition        : transform .8s ease-in-out;
}

.imgRecarregar:active {
    outline: none;
    border : none;
}

.imgRecarregar:focus {
    outline: 0;
}

.imgRecarregar:hover {
    -moz-transform   : rotate(360deg);
    -webkit-transform: rotate(360deg);
    -o-transform     : rotate(360deg);
    -ms-transform    : rotate(360deg);
    transform        : rotate(360deg);
}

.centerText {
    text-align    : center;
    vertical-align: middle;

}

.titleCenter {
    margin-left  : 50%;
    float        : left;
    font-size    : 20px;
    text-overflow: ellipsis;
}

.titleCenter span {
    font-weight: 100;
    font-size  : 20px;
    margin-left: -50%;
}

.gridHeight {
    height: 50vh !important;
}

.img-cadastro {
    position     : absolute;
    margin-left  : auto;
    margin-right : auto;
    left         : 0;
    right        : 0;
    height       : 128px;
    width        : 128px;
    border-radius: 65%;
}

.input-upload {
    position: absolute;
    right   : 50%;
    top     : 50%;
}

.ats-switch span.switch-left {
    color     : #fff;
    background: #1ac95b;
}

.ats-switch span.switch-right {
    background: #ed5565;
    color     : #fff;
}

.fixLabelbtn {
    position: absolute;
    top     : 113px;
}

.sllipse {
    max-width    : 270px;
    width        : 30%;
    overflow     : hidden;
    text-overflow: ellipsis;
}

@media only screen and (max-width: 769px) {
    .ats-validator-label {
        position  : absolute;
        left      : 0;
        top       : 100%;
        margin-top: 1px;
        font-size : 9px; 
    }
}

@media only screen and (min-width: 769px) {
    .ats-validator-label {
        position  : absolute;
        left      : 0;
        top       : 100%;
        margin-top: -7px;
        font-size : 9px;
    }
}

.imgEmpresa,
.imgEmpresaFile {
    position       : absolute;
    margin         : 10px 105px;
    margin-left    : 50%;
    height         : 128px;
    width          : 128px;
    border-radius  : 65%;
    object-fit     : cover;
    object-position: center;
}

.inputUploadImgEmpresa {
    position: absolute;
    right   : 0;
    top     : 117px;
}

.fixLabelbtnEmp {
    position: absolute;
    top     : 145px;
}

.gridCheck {
    width: 25px;
}

.img-tooltip {
    margin-top: -20px;
    width     : 10px;
    height    : 10px;
}


.btn-disabled {
    background-color: gray;
    border-color    : gray;
    color           : #FFFFFF;
}


.btn-disabled:hover {
    color: #FFFFFF;
}

.btn-disabled:focus {
    color: #FFFFFF
}


.field-conta-pagamento {
    display              : grid;
    grid-template-columns: 60% 10% 30%;
}

.field-conta-pagamento .input-group-addon {
    width: auto;
}

.container-btn-action {
    min-width  : 24px;
    display: inline-block;
}

.btn-grid-size-default {
    width: 28px;
    height: 25px;
}

@media only screen and (max-width: 475px) {

    .field-import-integrar{
        padding-right: 0 !important;
        margin-bottom: 5px;
    }
}

@media only screen and (max-width: 475px) {

    .buttons-celular {
        padding-left: 13px !important;
    }

    .buttons-ativar-desativar-celular {
        float: left !important;
    }

    .buttons-ativar-desativar-celular-div {
        padding-left: 0px !important;
    }
}