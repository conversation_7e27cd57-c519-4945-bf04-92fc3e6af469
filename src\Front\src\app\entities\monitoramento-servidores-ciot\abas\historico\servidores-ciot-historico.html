<div id="ServidoresCiotHistoricoController" ng-controller="ServidoresCiotHistoricoController as vm">
  <div class="ibox-content">
      <div class="table-responsive tableFixHead" id="table-scroll">
          <table class="table table-sm table-bordered" >
                  <thead class="table table-hover">
                      <th style="min-width: 120px !important;">Código do servidor</th>
                      <th style="min-width: 120px !important;">Usuário alteração</th>
                      <th style="min-width: 150px !important;">Usuário cadastro</th>
                      <th style="min-width: 80px !important;">Data alteração</th>
                      <th style="min-width: 80px !important;">Data cadastro</th>
                      <th style="min-width: 80px !important;">Status</th>
                      <th style="min-width: 150px !important;">Comunicação com a ANTT</th>
                      <th style="min-width: 180px !important;">Servidor</th>
                      <th style="min-width: 80px !important;">Tipo servidor</th>
                  </thead>
                  <tbody>
                      <tr ng-repeat="item in vm.servidorCiotHistorico" style="height: 40px;" >
                         
                          <td>{{item.id}}</td>
                          <td>{{item.usuarioAlteracaoId}}</td>
                          <td>{{item.usuarioCadastroId}}</td>
                          <td>{{item.dataAlteracao}}</td>
                          <td>{{item.dataCadastro}}</td>
                          <td>{{item.status == '0' ? 'Ok' : 
                            item.status == '1' ? 'Contingência manual' : 
                            item.status == '2' ? 'Contingência forçada' : 
                            item.status == '3' ? 'Offline' : 
                            item.status == '4' ? 'Verificando' : 'Verificando'}}
                          </td>
                          <td>{{item.statusComunicacaoAntt == '0' ? 'Ok' : item.statusComunicacaoAntt == '1' ? 'Sem Comunicação' : 'Sem Comunicação'}}</td>
                          <td>{{item.link}}</td>
                          <td>{{item.tipoServidor == '0' ? 'Servidor velho XML' : item.tipoServidor == '1' ? 'Servidor novo JSON' : 'Servidor novo JSON'}}</td>
                      </tr>
                  </tbody>
          </table>
      </div>
  </div>
</div>
<style>
  #table-scroll {
        height: 400px;
        overflow: auto;
        margin-top: 40px;
    }
</style>