(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('RequestLogController', RequestLogController);

    RequestLogController.inject = [
        '$scope',
        'toastr',
        'BaseService',
        'PERFIL_ADMINISTRADOR',
        'PERFIL_EMPRESA',
        'URL_SERVER_DEV',
        '$window',
        'PersistentDataService',
        '$rootScope',
        '$timeout'
    ];

    function RequestLogController(
        $scope,
        toastr,
        BaseService,
        PERFIL_ADMINISTRADOR,
        PERFIL_EMPRESA,
        URL_SERVER_DEV,
        $window,
        PersistentDataService,
        $rootScope,
        $timeout) {

        var vm = this;
        vm.filtro = {
            Campo: "",
            Valor: ""
        };

        vm.inicializarTraducaoKendo = function () {
            kendo.culture("pt-BR");
            eval("kendoPtBr.init()");
        }

        function formatJSON(json) {
            try {
                return JSON.stringify(JSON.parse(json), null, 6)
            } catch (ex) {
                return "O JSON está em um formato inválido.";
            }
        }

        vm.abrirDialogo = function (abrirDialogoModel) {
            if (!abrirDialogoModel.json) {
                toastr.error("O json está nulo.");
                return;
            }

            var tipoRequisicao = abrirDialogoModel.tipoRequisicao === 0 ? "Request" : "Response";
            var dialogo = $("#dialogo").kendoDialog({
                width: "600px",
                title: tipoRequisicao,
                closable: true,
                modal: true,
                content: "<p>" +
                    "<textarea id='areaTexto' rows='20' cols='90'>" +
                    formatJSON(abrirDialogoModel.json) +
                    "</textarea>" +
                    "</p>",
                actions: [
                    {
                        text: 'Copiar conteúdo',
                        primary: true,
                        action: function (e) {
                            copiarConteudoTextoDialog();
                            return false; // Previne o encerramento do diálogo
                        }
                    }
                ]
            }).data("kendoDialog").open();
        }

        function copiarConteudoTextoDialog() {
            areaTexto.select();
            document.execCommand("copy");
            toastr.success("Conteúdo copiado com sucesso.");
        }

        vm.inicializarGrid = function () {
            var grid = $("#grid").kendoGrid({
                autoBind: false,
                excel: {
                    fileName: "RequestsLog.xlsx",
                    allPages: true,
                    filterable: true
                },
                pdf: {
                    fileName: "RequestsLog.pdf",
                    allPages: true,
                    filterable: true
                },
                dataSource: {
                    type: "aspnetmvc-ajax",
                    transport: {
                        read: {
                            url: URL_SERVER_DEV + "RequestLogAts/GetAllRequestLogsToKendoDataSource",
                            data: function () {
                                return {
                                    filtros: vm.filtro
                                }
                            },
                            type: "get",
                            beforeSend: function (req) {
                                req.setRequestHeader("SessionKey", vm.gerarTokenCabecalho());
                            },
                            // Se a sessão expirou redireciona para o login.
                            complete: function(res) {
                                if (!res.responseJSON.success && res.responseJSON.error === "[ERR_SIS_TOKEN_EXPIRED]") {
                                    $rootScope.goToState("login.sessao-expirada-login");
                                }
                            }
                        }
                    },
                    pageSize: 10,
                    page: 1,
                    total: 0,
                    serverPaging: true,
                    serverSorting: true,
                    serverFiltering: true,
                    serverGrouping: true,
                    serverAggregates: true,
                    schema: {
                        data: "data.Data",
                        total: "data.Total",
                        errors: "data.Errors",
                        aggregates: "data.AggregateResults",
                        model: {
                            id: "IdRequestLog",
                            fields: {
                                IdRequestLog: { type: "number" },
                                Controller: { type: "string" },
                                Method: { type: "string" },
                                DateTimeRequest: { type: "date" },
                                ExecutionTime: { type: "date" },
                                MethodType: { type: "string" },
                                StatusCodeResponse: { type: "string" }
                            }
                        },
                    }
                },
                height: 420,
                filterable: {
                    extra: true
                },
                reorderable: true,
                groupable: true,
                sortable: true,
                pageable: {
                    refresh: true,
                    pageSizes: true,
                    buttonCount: 10
                },
                columns: [
                    { field: "IdRequestLog", title: "Código", hidden: false },
                    { field: "Controller", title: "Controller" },
                    { field: "Method", title: "Método" },
                    { field: "DateTimeRequest", title: "Data", type: "date", format: "{0:dd/MM/yyyy HH:mm}" },
                    { field: "ExcutionTime", title: "Tempo resposta", filterable: false, template: function (params) { return params.ExcutionTime ? params.ExcutionTime.TotalSeconds.toString().substring(0, 4) + " segundos" : "Indisponível" } },
                    { field: "MethodType", title: "Protocolo" },
                    { field: "StatusCodeResponse", title: "Código retorno", template: function (params) { return params.StatusCodeResponse ? params.StatusCodeResponse : "Indisponível" } },
                    {
                        field: "terminal",
                        title: "Request/Response",
                        filterable: false,
                        groupable: false,
                        sortable: false,
                        template: "<div align=center><button class='k-icon k-i-upload'></button><button class='k-icon k-i-download'></button></div>"
                    }
                ]
            });

            // Escuta os clicks nos botões de request e response e 
            // captura o id da coluna e a classe do botão para fazer a consulta do JSON no Webservice.
            $("#grid tbody").on("click", "tr", function (e) {
                var idLinha = $(this).find('td').html();
                // Quando a coluna está agrupada é necessário mudar a forma de selecionar o id da linha
                // para cada nível de agrupamento indo do 1 até 5 que é o máximo de agrupamentos possíveis.
                if (!$.isNumeric(idLinha)) {
                    idLinha = $(this).find("td:eq(1)").html();
                    if (!$.isNumeric(idLinha)) {
                        idLinha = $(this).find("td:eq(2)").html();
                        if (!$.isNumeric(idLinha)) {
                            idLinha = $(this).find("td:eq(3)").html();
                            if (!$.isNumeric(idLinha)) {
                                idLinha = $(this).find("td:eq(4)").html();
                                if (!$.isNumeric(idLinha)) {
                                    idLinha = $(this).find("td:eq(5)").html();
                                    if (!$.isNumeric(idLinha)) {
                                        idLinha = $(this).find("td:eq(5)").html();
                                    }
                                }
                            }
                        }
                    }
                }
                var classeBotao = $(e).attr('target').className;
                vm.consultarJsonRequestResponse(idLinha, classeBotao, vm.abrirDialogo);
            });
        }

        vm.consultarJsonRequestResponse = function (idLinha, classeBotao, callback) {
            var abrirDialogoModel = {
                json: "",
                tipoRequisicao: 0
            }

            if (!classeBotao || !idLinha)
                return;
            if (classeBotao == 'k-icon k-i-upload') {
                BaseService.get('RequestLogAts', 'GetJsonRequestResponseById', {
                    idRequestLog: idLinha,
                    tipoJSON: 0
                }).then(function (response) {
                    if (response.success) {
                        abrirDialogoModel = {
                            json: response.message,
                            tipoRequisicao: 0
                        }
                        if (angular.isFunction(callback)) {
                            callback(abrirDialogoModel);
                        }
                    } else {
                        toastr.error("Não foi possível realizar a consulta.");
                    }
                });
            } else if (classeBotao == 'k-icon k-i-download') {
                BaseService.get('RequestLogAts', 'GetJsonRequestResponseById', {
                    idRequestLog: idLinha,
                    tipoJSON: 1
                }).then(function (response) {
                    if (response.success) {
                        abrirDialogoModel = {
                            json: response.message,
                            tipoRequisicao: 1
                        }
                        if (angular.isFunction(callback)) {
                            callback(abrirDialogoModel);
                        }
                    } else {
                        toastr.error("Não foi possível realizar a consulta.");
                    }
                });
            } else {
                toastr.error("Botão não configurado.");
            }
        }

        vm.gerarTokenCabecalho = function () {
            if ($window.localStorage.getItem('SessionKey'))
                return $window.localStorage.getItem('SessionKey');
            else{
                $rootScope.goToState("login.sessao-expirada-login");
            }
        }
        
        vm.consultarEvent = function(){
            if(!vm.filtro.Campo && vm.filtro.Valor){
                toastr.error("Informe um campo para continuar!");
                return;
            }

            if(!vm.filtro.Valor && vm.filtro.Campo){
                toastr.error("Informe um valor para continuar!");
                return;
            } 

            $("#grid").data().kendoGrid.dataSource.read();

        }

        vm.consultar = function () {
            $("#grid").data().kendoGrid.dataSource.read();
        }

        vm.limparFiltros = function() {
            vm.filtro.Campo = "";
            vm.filtro.Valor = "";
        }

        vm.inicializarTraducaoKendo();
        vm.inicializarGrid();
        vm.consultar();
    }
})();