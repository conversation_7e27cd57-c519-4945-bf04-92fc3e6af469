<div class="form-horizontal">
    <hr />
    <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-3 col-md-4 col-lg-3">Código:</label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-3 col-lg-3">
                        <input type="text" ng-model="vm.portador.id" class="form-control" disabled
                            value="{{vm.isNew()}}" />
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-3 col-md-4 col-lg-3">
                        <span class="text-danger mr-5">*</span>Tipo de pessoa:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <ui-select name="Tipo" ng-model="vm.portador.tipoPessoa" ats-ui-select-validator
                            ng-disabled="!vm.isNew()" validate-on="blur"
                            ng-change="vm.tipoPessoaChange(vm.portador.tipoPessoa)"
                            required-message="'Tipo de pessoa é obrigatório'" required>
                            <ui-select-match>
                                <span>{{$select.selected.descricao}}</span>
                            </ui-select-match>
                            <ui-select-choices
                                repeat="ex.id as ex in vm.comboTipoPessoa.data | propsFilter: {descricao: $select.search}">
                                <div ng-bind-html="ex.descricao | highlight: $select.search"></div>
                            </ui-select-choices>
                        </ui-select>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-3 col-md-4 col-lg-3">
                        <span class="text-danger mr-5">*</span>Nome:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <input type="text" maxlength="200" name="Nome" ng-model="vm.portador.nome" class="form-control"
                            validate-on="blur" required-message="'Nome é obrigatório'" required />
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-3 col-md-4 col-lg-3">
                        <span class="text-danger mr-5">*</span>{{vm.labelCpfCnpj}}:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <input type="text" ui-mask="{{vm.mascaraCpfCnpj}}" ng-disabled="!vm.isNew()" required
                            ng-model="vm.portador.cpfCnpj" name="{{vm.labelCpfCnpj}}"
                            class="form-control" />
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-3 col-md-4 col-lg-3">
                        <span class="text-danger mr-5"></span>RNTRC:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <input type="text" maxlength="8" minlength="8" name="Rntrc" ng-model="vm.portador.rntrc"
                            class="form-control" validate-on="blur" required-message="'RNTRC é obrigatório'" />
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-3 col-md-4 col-lg-3">
                        <span class="text-danger mr-5">*</span>Atividade:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <ui-select name="Atividade" ng-model="vm.portador.atividade" ats-ui-select-validator
                            validate-on="blur" required-message="'Atividade é obrigatório'" required>
                            <ui-select-match>
                                <span>{{$select.selected.descricao}}</span>
                            </ui-select-match>
                            <ui-select-choices
                                repeat="ex.id as ex in vm.comboAtividade.data | propsFilter: {descricao: $select.search}">
                                <div ng-bind-html="ex.descricao | highlight: $select.search"></div>
                            </ui-select-choices>
                        </ui-select>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-3 col-md-4 col-lg-3">
                        <span class="text-danger mr-5">*</span>Celular:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <input type="text" name="Celular" class="form-control" ng-model="vm.portador.celular"
                            ui-br-phone-number validate-on="blur" required-message="'Celular é obrigatório'" required />
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-3 col-md-4 col-lg-3">
                        <span class="text-danger mr-5">*</span>Telefone:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <input type="text" name="Telefone" class="form-control" ng-model="vm.portador.telefone"
                            ui-br-phone-number validate-on="blur" required-message="'Telefone é obrigatório'"
                            required />
                    </div>
                </div>
            </div>
        </div>
        <div class="row" ng-show="vm.portador.tipoPessoa === 1">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-3 col-md-4 col-lg-3"><span
                            class="text-danger mr-5">*</span>Nome da mãe:</label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <input type="text" maxlength="200" name="Nome Mae" ng-model="vm.portador.nomeMae"
                            class="form-control" validate-on="blur" required-message="'Nome da mãe é obrigatório'"
                            ng-required="vm.portador.tipoPessoa === 1" />
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-3 col-md-4 col-lg-3"><span
                            class="text-danger mr-5">*</span>Nome do pai:</label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <input type="text" maxlength="200" name="Nome Pai" ng-model="vm.portador.nomePai"
                            class="form-control" validate-on="blur" required-message="'Nome do pai é obrigatório'"
                            ng-required="vm.portador.tipoPessoa === 1" />
                    </div>
                </div>
            </div>
        </div>
        <div class="row" ng-show="vm.portador.tipoPessoa === 1">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-3 col-md-4 col-lg-3"><span
                            class="text-danger mr-5">*</span>Sexo:</label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <ui-select name="sexo" ng-model="vm.portador.sexo" ats-ui-select-validator validate-on="blur"
                            required-message="'Sexo é obrigatório'" ng-required="vm.portador.tipoPessoa === 1">
                            <ui-select-match>
                                <span>{{$select.selected.descricao}}</span>
                            </ui-select-match>
                            <ui-select-choices
                                repeat="ex.id as ex in vm.comboSexo.data | propsFilter: {descricao: $select.search}">
                                <div ng-bind-html="ex.descricao | highlight: $select.search"></div>
                            </ui-select-choices>
                        </ui-select>
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-3 col-md-4 col-lg-3"><span
                            class="text-danger mr-5">*</span>Número identidade:</label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <input type="text" name="Número Identidade" ng-model="vm.portador.numeroIdentidade" ats-numeric
                            onkeypress="return event.charCode >= 48 && event.charCode <= 57" class="form-control"
                            validate-on="blur" required-message="'Número identidade é obrigatório'"
                            ng-required="vm.portador.tipoPessoa === 1" />
                    </div>
                </div>
            </div>
        </div>
        <div class="row" ng-show="vm.portador.tipoPessoa === 1">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-3 col-md-4 col-lg-3"><span
                            class="text-danger mr-5">*</span>Orgão emissão:</label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <input type="text" name="Orgão Emissão" ng-model="vm.portador.orgaoEmissor" class="form-control"
                            validate-on="blur" required-message="'Orgão emissor é obrigatório'"
                            ng-required="vm.portador.tipoPessoa === 1" />
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-3 col-md-4 col-lg-3"><span
                            class="text-danger mr-5">*</span>UF emissão:</label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <ui-select name="UF Emissão" ats-ui-select-validator validate-on="blur"
                            ng-model="vm.portador.ufEmissao" required-message="'UF emissão é obrigatório'"
                            ng-required="vm.portador.tipoPessoa === 1">
                            <ui-select-match>
                                <span>{{$select.selected.descricao}}</span>
                            </ui-select-match>
                            <ui-select-choices
                                repeat="ex.id as ex in vm.estados | propsFilter: {descricao: $select.search}">
                                <div ng-bind-html="ex.descricao | highlight: $select.search"></div>
                            </ui-select-choices>
                        </ui-select>
                    </div>
                </div>
            </div>
        </div>
        <div class="row" ng-show="vm.portador.tipoPessoa === 1">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label"><span
                            class="text-danger mr-5">*</span>Emissão identidade:</label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <p class="input-group">
                            <input type="text" ng-click="vm.datePickerOpenemissao = !vm.datePickerOpenemissao"
                                class="form-control" datepicker-options="vm.optionsDatePicker" name="emisaoid" current-text="Hoje" clear-text="Limpar"
                                close-text="Fechar" uib-datepicker-popup="dd/MM/yyyy" datepicker-append-to-body="true"
                                ng-model="vm.portador.emissaoIdentidade" is-open="vm.datePickerOpenemissao"
                                required-message="'Emissão identidade é obrigatório'"
                                ng-required="vm.portador.tipoPessoa === 1" />
                            <span class="input-group-btn">
                                <button type="button" class="btn btn-default"
                                    ng-click="vm.datePickerOpenemissao = !vm.datePickerOpenemissao">
                                    <i class="fa fa-calendar"></i>
                                </button>
                            </span>
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label"><span
                            class="text-danger mr-5">*</span>Data nascimento:</label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <p class="input-group">
                            <input type="text" ng-click="vm.datePickerOpennasc = !vm.datePickerOpennasc"
                                class="form-control" datepicker-options="vm.optionsDatePicker" name="nascimento" current-text="Hoje" clear-text="Limpar"
                                close-text="Fechar" uib-datepicker-popup="dd/MM/yyyy" datepicker-append-to-body="true"
                                ng-model="vm.portador.dataNascimento" is-open="vm.datePickerOpennasc"
                                required-message="'Data nascimento é obrigatório'"
                                ng-required="vm.portador.tipoPessoa === 1" />
                            <span class="input-group-btn">
                                <button type="button" class="btn btn-default"
                                    ng-click="vm.datePickerOpennasc = !vm.datePickerOpennasc">
                                    <i class="fa fa-calendar"></i>
                                </button>
                            </span>
                        </p>
                    </div>
                </div>
            </div>
        </div>
        <div class="row" ng-show="vm.portador.tipoPessoa === 2">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label"><span
                            class="text-danger mr-5">*</span>Razão social:</label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <input type="text" maxlength="200" name="Razão social" ng-model="vm.portador.razaoSocial"
                            class="form-control" validate-on="blur" required-message="'Razão social é obrigatório'"
                            ng-required="vm.portador.tipoPessoa === 2" />
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label">Inscrição estadual:</label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <input type="text" maxlength="15" name="Inscrição estadual" name="RG"
                            ng-model="vm.portador.inscricaoEstadual" class="form-control" />
                    </div>
                </div>
            </div>
        </div>
        <div class="row" ng-show="vm.portador.tipoPessoa === 2">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label"><span
                            class="text-danger mr-5">*</span>Data abertura empresa:</label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <p class="input-group">
                            <input type="text" name="DataAberturaEmpresa" ng-change="vm.DataNascimentoChange(vm.portador.dataAberturaEmpresa)"
                                ng-click="vm.datePickerOpen = !vm.datePickerOpen" class="form-control"
                                max-date="new Date()" current-text="Hoje" clear-text="Limpar" close-text="Fechar"
                                uib-datepicker-popup="dd/MM/yyyy" datepicker-append-to-body="true"
                                ng-model="vm.portador.dataAberturaEmpresa" is-open="vm.datePickerOpen"
                                required-message="'Data abertura da empresa é obrigatório'"
                                ng-required="vm.portador.tipoPessoa === 2" />
                            <span class="input-group-btn">
                                <button type="button" class="btn btn-default"
                                    ng-click="vm.datePickerOpen = !vm.datePickerOpen">
                                    <i class="fa fa-calendar"></i>
                                </button>
                            </span>
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label"><span
                            class="text-danger mr-5">*</span>Forma constituição:</label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <input type="text" maxlength="200" name="Forma constituição"
                            ng-model="vm.portador.formaConstituicao" class="form-control" validate-on="blur"
                            required-message="'Forma constituição é obrigatório'"
                            ng-required="vm.portador.tipoPessoa === 2" />
                    </div>
                </div>
            </div>
        </div>
        <div class="row" ng-show="vm.portador.tipoPessoa === 2">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label"><span
                            class="text-danger mr-5">*</span>Cnae:</label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <input type="text" maxlength="10" ats-numeric name="Cnae" ng-model="vm.portador.cnae"
                            class="form-control" validate-on="blur" required-message="'Cnae é obrigatório'"
                            ng-required="vm.portador.tipoPessoa === 2" />
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label"><span
                            class="text-danger mr-5">*</span>Natureza jurídica:</label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <input type="text" maxlength="4" ats-numeric name="Cnae" ng-model="vm.portador.naturezaJuridica"
                            class="form-control" validate-on="blur" required-message="'Natureza jurídica é obrigatória'"
                            ng-required="vm.portador.tipoPessoa === 2" />
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12 col-md-6">
                <div class="form-group">
                    <label class="col-xs-12 col-md-3 control-label">
                        <span class="text-danger mr-5">*</span>E-mail:
                    </label>
                    <div class="input-group col-xs-12 col-md-9">
                        <input type="email" name="Email" maxlength="200" 
                            class="form-control" validate-on="blur" ng-model="vm.portador.email"
                            required-message="'E-mail é obrigatório'" required
                            invalid-message="'E-mail é inválido'"/>
                        <!--<small>Caso informado mais de um e-mail, separar por ponto e vírgula.</small>-->
                    </div>
                </div>
            </div>
            <consulta-padrao-modal ng-disabled="!vm.isAdmin()"
                tabledefinition="vm.consultaEmpresa" label="'Empresa:'" idname="Empresa"
                placeholder="'Selecione uma Empresa'" required-message="'Empresa é obrigatório'"
                ng-required="vm.isAdmin()" directivesizes="'col-xs-12 col-sm-12 col-md-6 col-lg-6'"
                labelsize="'col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label'">
            </consulta-padrao-modal>
        </div>
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label"></label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <button type="button" class="btn btn-success" ng-click="vm.sinalizaClienteEmprestimo()"
                            ng-show="!vm.isNew()" ng-disabled="vm.sinalizando">Sinalizar cliente empréstimo</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>