(function () {
    'use strict';

    angular.module('bbcWeb.posto.state', []).config(routeConfig);

    function routeConfig($urlRouterProvider, $stateProvider) {
        $stateProvider.state('posto', {
            abstract: true,
            url: "/posto",
            templateUrl: "app/layout/content.html"
        })
        .state('posto.index', {
            url: '/index',
            templateUrl: 'app/entities/cadastro-posto/consulta/posto.html'
        })
        .state('posto.crud', {
            url: '/:link',
            templateUrl: 'app/entities/cadastro-posto/cadastro/posto-crud.html'
        });
    }
})();