(function () {
    'use strict';

    angular.module('bbcWeb').controller('GestaoTransportadoresCrudController', GestaoTransportadoresCrudController);

    GestaoTransportadoresCrudController.$inject = ['toastr', '$rootScope', 'BaseService', '$state', '$stateParams', '$window', 'PERFIL_ADMINISTRADOR', '$scope', '$timeout', 'PersistentDataService', 'DefaultsService', '$uibModal'];

    function GestaoTransportadoresCrudController(
        toastr, $rootScope,
        BaseService, $state,
        $stateParams, $window,
        PERFIL_ADMINISTRADOR,
        $scope, $timeout,
        PersistentDataService,
        DefaultsService,
        $uibModal
    ) {
        var vm = this;
        vm.transportador = [];
        vm.transportador.tipoPessoa = 1;    
        vm.transportador.ciotTacAgregado = 1;        
        vm.labelCpfCnpj = "CPF";
        vm.mascaraCpfCnpj = "999.999.999-99"  
        vm.menusPai = [];

        vm.headerItems = [{
            name: 'Cadastro<PERSON>'
        }, {
            name: 'Transportador',
            link: 'gestao-transportador.index'
        }, {
            name: $stateParams.link == 'novo' ? 'Novo' : 'Editar'
        }];

        var selfScope = PersistentDataService.get('GestaoTransportadoresCrudController');

        if ($stateParams.link == 'novo')
            vm.transportador.id = 'Auto';

        vm.isNew = function () {
            return $stateParams.link == 'novo';
        };

        vm.comboTipoPessoa = {
            data: [
                {id: 1, descricao: 'Física'},
                {id: 2, descricao: 'Jurídica'}
            ]
        };

        vm.tipoPessoaChange = function (tipoPessoa) {
            if (tipoPessoa == 1) {
                vm.labelCpfCnpj = "CPF";
                vm.mascaraCpfCnpj = "999.999.999-99"  
            }

            if (tipoPessoa == 2) {
                vm.labelCpfCnpj = "CNPJ";
                vm.mascaraCpfCnpj = "99.999.999/9999-99"
            }
        };

        vm.loadEdit = function (id) {
            BaseService.get('Transportador', 'ConsultarPorId', {
                idTransportador: id
            }).then(function (response) {
                if (!response.success) {
                    toastr.error(response.message);
                    return;
                } else {
                    vm.transportador = response.data;
                    vm.tipoPessoaChange(vm.transportador.tipoPessoa)
                }
            });
        };

        vm.onChangeCpfCnpj = function () {
            if(vm.isNew && vm.transportador.cpfCnpj){
                BaseService.get('Transportador', 'ConsultarPorCpfCnpj', {
                    cpfCnpj: vm.transportador.cpfCnpj
                }).then(function (response) {
                    if (response.data != null) {
                        if(response.data.empresaidFrota != $rootScope.usuarioLogado.empresaId){
                            return Sistema.Msg.confirm('Transportador já cadastrado em outra empresa, deseja salvar o mesmo e carregar as informações?', function () {
                                BaseService.get('Transportador', 'ConsultarPorCpfCnpj', {
                                    cpfCnpj: vm.transportador.cpfCnpj
                                }).then(function (response) {                               
                                vm.transportador = response.data;
                                }); 
                            }, function () {
                                $state.go('gestao-transportador.index');
                            });
                        }else{
                            vm.transportador = response.data;
                        }                                                 
                    }
                });                
            }
        }

        vm.save = function (form) {
            if (!form.$valid) {
                toastr.error('Todos os campos obrigatórios devem ser preenchidos corretamente com seus respectivos valores');
                return;
            }

            if (vm.isSaving === true)
                return;

            var transportador = {}

            transportador.Id = vm.transportador.id == "Auto" ? 0 : vm.transportador.id;
            transportador.Nome = vm.transportador.nome;
            transportador.CpfCnpj = vm.transportador.cpfCnpj;
            transportador.CiotTacAgregado = vm.transportador.ciotTacAgregado;
            transportador.Ativo = 1;

            vm.isSaving = true;

            BaseService.post('transportador', 'Salvar', transportador).then(function (response) {
                vm.isSaving = false;

                if (response && !response.sucesso)
                    return toastr.error(response.mensagem);
                else {
                    toastr.success('Transportador salvo com sucesso!');
                    $state.go('gestao-transportador.index');
                }
            });
        };

        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.administrador == true;
        };

        function init() {
            if (vm.isNew()) {
                vm.transportador.id = 'Auto';
            }
        };

        vm.onClickAvancar = function (wizard) {
            wizard.go(wizard.getActivePosition() + 1);
        };

        vm.onClickVoltar = function (wizard) {

            var ativoIndex = wizard.getActivePosition();
            if (ativoIndex == 1)
                $state.go('gestao-transportador.index');

            wizard.go(ativoIndex - 1);
        };

        init();

        $scope.$on('$stateChangeStart', function (arg1, toState) {
            if (toState.name === 'gestao-transportador.index')
                PersistentDataService.remove('GestaoTransportadoresCrudController');
            else
                PersistentDataService.store('GestaoTransportadoresCrudController', vm, "Cadastro - Transportador", null, "gestao-transportador.gestao-transportador-crud", vm.transportador.id);
        });

        if (angular.isDefined(selfScope))
            angular.extend(vm, selfScope.data);
        else {
            if (!vm.isNew())
                vm.loadEdit($stateParams.link);
            else {
                vm.Transportador = 0;
                vm.modulos = 1;
            }
        }

        $timeout(function () {
            PersistentDataService.remove('GestaoTransportadoresController');
        }, 15);
        
    }
})();
