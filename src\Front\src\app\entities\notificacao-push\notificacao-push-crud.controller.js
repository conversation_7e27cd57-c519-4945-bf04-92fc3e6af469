(function () {
    'use strict';

    angular.module('bbcWeb').controller('NotificacaoPushCrudController', NotificacaoPushCrudController);

    NotificacaoPushCrudController.$inject = [
        'toastr',
        '$rootScope',
        'BaseService',
        '$state',
        '$stateParams',
        '$window',
        'PERFIL_ADMINISTRADOR',
        'PERFIL_EMPRESA',
        '$scope',
        '$timeout',
        '$uibModal',
        'PersistentDataService'
    ];

    function NotificacaoPushCrudController(
        toastr,
        $rootScope,
        BaseService,
        $state,
        $stateParams,
        $window,
        PERFIL_ADMINISTRADOR,
        PERFIL_EMPRESA,
        $scope,
        $timeout,
        $uibModal,
        PersistentDataService) {

        var vm = this;
        vm.notificacaoPush = {};
        // Empresa
        vm.empresas = [];
        // Filial
        vm.filiais = [];
        vm.filialSelected = undefined;
        // Grupo de usuário
        vm.gruposUsuario = [];
        vm.grupoUsuarioSelected = undefined;
        // Tipo de notificação
        vm.tiposNotificacao = [];
        vm.tipoNotificacaoSelected = undefined;
        // Hardwares
        vm.hardwaresIMEI = [];
        vm.hardwareIMEISelected = undefined;
        // IMEIS
        vm.gridImeisApelidos = [];
        vm.hardwareIMEISelectedApelido = "";
        //
        vm.momentosExecucao = [{ IdExecucao: 0, Descricao: 'Integração' }, { IdExecucao: 1, Descricao: 'Serviço' }];
        vm.headerItems = [
            { name: 'Cadastros', link: '' },
            { name: 'Notificação push', link: 'gestao-logistica-notificacao-push.notificacao' },
            { name: $stateParams.link == 'novo' ? 'Novo' : 'Editar', link: '' }
        ];

        vm.deletarImeiTabela = function ($index) {
            vm.gridImeisApelidos.splice($index, 1);
        };

        vm.gridIMEISJaExisteImei = function (imei) {
            return vm.gridImeisApelidos.map(function (item) { return item.IMEI; }).indexOf(imei) > -1;
        };

        vm.gridIMEISTemItem = function () {
            return vm.gridImeisApelidos.length > 0;
        };

        vm.getIMEIByHardwareId = function (id) {
            var retorno = "";
            vm.hardwaresIMEI.forEach(function (hh) {
                if (hh.IdHardware === id) {
                    retorno = hh.Identificacao;
                    return;
                }
            });

            return retorno;
        };

        vm.gridIMEISAdd = function () {
            var imeiHardwareSelecionado = vm.getIMEIByHardwareId(vm.hardwareIMEISelected);
            var itemJaAdicionado = vm.gridIMEISJaExisteImei(imeiHardwareSelecionado);

            if (!itemJaAdicionado)
                vm.gridImeisApelidos.push({
                    IMEI: imeiHardwareSelecionado,
                    Apelido: vm.hardwareIMEISelectedApelido,
                    IdHardware: vm.hardwareIMEISelected
                });

            vm.hardwareIMEISelected = undefined;
            vm.hardwareIMEISelectedApelido = "";
        };

        vm.loadEdit = function (id) {
            setTimeout(function () {
                if (vm.isProfileAdmin())
                    consultaNotificacaoPorId(id, function () {
                        carregaSelectEmpresa(function () {
                            carregaSelectFilial(false);
                            carregaSelectGrupoUsuario(false);
                            carregaSelectTipoNotificacao(false)
                        });
                    });
                else if (vm.isProfileEmpresa())
                    consultaNotificacaoPorId(id, function () {
                        carregaSelectFilial(false);
                        carregaSelectGrupoUsuario(false);
                        carregaSelectTipoNotificacao(false)
                    });
                else
                    consultaNotificacaoPorId(id, function () {
                        carregaSelectGrupoUsuario(false);
                        carregaSelectTipoNotificacao(false);
                    });
            }, 50);
        };

        vm.load = function () {
            vm.notificacaoPush.MomentoExecucao = 1;

            if (vm.isProfileAdmin())
                carregaSelectEmpresa();
            else if (vm.isProfileEmpresa()) {
                carregaSelectFilial();
                carregaSelectGrupoUsuario();
                carregaSelectTipoNotificacao()
            } else {
                carregaSelectGrupoUsuario();
                carregaSelectTipoNotificacao()
            }
        };

        //Métodos privados
        function consultaNotificacaoPorId(id, callback) {
            BaseService.get('NotificacaoPushAts', 'ConsultarPorId', { idNotificacaoPush: id }).then(function (response) {
                vm.notificacaoPush = angular.fromJson(response.data.notPush);
                vm.empresaSelected = vm.notificacaoPush.IdEmpresa;
                vm.filialSelected = vm.notificacaoPush.IdFilial;
                vm.grupoUsuarioSelected = angular.fromJson(response.data.grupos);
                // Primeiro carregamos a select de imeis para depois carregar os filhos na tela...
                carregaSelectHardwaresIMEI(function () {
                    vm.gridImeisApelidos = angular.fromJson(response.data.items);
                });
                if (typeof callback === 'function') callback();
            });
        };

        function carregaSelectEmpresa(callback) {
            BaseService.get('EmpresaAts', 'Consultar').then(function (response) {
                vm.empresas = angular.fromJson(response.data);
                vm.empresaSelected = vm.notificacaoPush.IdEmpresa;
                if (typeof callback === 'function') callback();
            });
        };

        function carregaSelectFilial(force) {
            BaseService.get('FilialAts', 'ConsultarPorEmpresa', { idEmpresa: vm.empresaSelected || vm.notificacaoPush.IdEmpresa }).then(function (response) {
                vm.filiais = angular.fromJson(response.data);
                vm.filialSelected = force ? undefined : vm.notificacaoPush.IdFilial;
            });
        };

        function carregaSelectGrupoUsuario(force) {
            BaseService.get('GrupoUsuarioAts', 'ConsultarPorEmpresa', { idEmpresa: vm.empresaSelected || vm.notificacaoPush.IdEmpresa }).then(function (response) {
                vm.gruposUsuario = angular.fromJson(response.data);
                vm.grupoUsuarioSelected = force ? undefined : vm.grupoUsuarioSelected;
            });
        };

        function carregaSelectTipoNotificacao(force) {
            BaseService.get('TipoNotificacaoAts', 'Consultar', {
                idEmpresa: vm.empresaSelected || vm.notificacaoPush.IdEmpresa,
                idFilial: vm.filialSelected || vm.notificacaoPush.IdFilial
            }).then(function (response) {
                vm.tiposNotificacao = angular.fromJson(response.data);
                vm.tipoNotificacaoSelected = force ? undefined : vm.notificacaoPush.IdTipoNotificacao;
            });
        };

        function carregaSelectHardwaresIMEI(callback) {
            BaseService.get('HardwareAts', 'Consultar', {
                idEmpresa: vm.empresaSelected,
                idFilial: vm.filialSelected
            }).then(function (response) {
                vm.hardwaresIMEI = angular.fromJson(response.data);
                vm.hardwareIMEISelected = undefined;
                if (typeof callback === 'function') callback();
            });
        };
        //#end

        vm.updateFiliais = function (force) {
            if (angular.isDefined(vm.empresaSelected)) carregaSelectFilial(force);
            else {
                vm.filiais = [];
                vm.filialSelected = undefined;
            }
        };

        vm.onEmpresaChange = function () {
            vm.filialSelected = undefined;
            vm.filiais = [];
            // Grupo de usuário
            vm.grupoUsuarioSelected = undefined;
            vm.gruposUsuario = [];
            // Tipo de notificação
            vm.tipoNotificacaoSelected = undefined;
            vm.tiposNotificacao = [];
            // Hardwares
            vm.hardwareIMEISelected = undefined;
            vm.hardwaresIMEI = [];
        };

        vm.onFilialChange = function () {
            // Tipo de notificação
            vm.tipoNotificacaoSelected = undefined;
            vm.tiposNotificacao = [];
            // Hardwares
            vm.hardwareIMEISelected = undefined;
            vm.hardwaresIMEI = [];
            //
            vm.gridImeisApelidos = [];
        };

        vm.updateGruposUsuario = function (force) {
            if (angular.isDefined(vm.empresaSelected)) carregaSelectGrupoUsuario(force);
            else {
                vm.grupoUsuarioSelected = undefined;
                vm.gruposUsuario = [];
            }
        };

        vm.updateHardwaresIMEI = function (force) {
            if (angular.isDefined(vm.empresaSelected) ||
                angular.isDefined(vm.filialSelected))
                carregaSelectHardwaresIMEI(force);
            else {
                vm.hardwareIMEISelected = undefined;
                vm.hardwaresIMEI = [];
            }
        };

        vm.resetarGridIMEIS = function () {
            vm.gridImeisApelidos = [];
        };

        vm.updateTiposNotificacao = function (force) {
            if (angular.isDefined(vm.empresaSelected) ||
                angular.isDefined(vm.filialSelected))
                carregaSelectTipoNotificacao(force);
            else {
                vm.tipoNotificacaoSelected = undefined;
                vm.tiposNotificacao = [];
            }
        };

        if ($stateParams.link == 'novo')
            vm.notificacaoPush.IdNotificacaoPush = 'Auto';
        vm.isNew = function () {
            return $stateParams.link == 'novo';
        };

        vm.isProfileAdmin = function () {
            return $rootScope.usuarioLogado.perfil == PERFIL_ADMINISTRADOR;
        };

        vm.isProfileEmpresa = function () {
            return ($rootScope.usuarioLogado.perfil == PERFIL_EMPRESA);
        };

        vm.componenteVisivel = function (block) {
            if (block == 'blockEmpresa')
                return $rootScope.usuarioLogado.perfil == PERFIL_ADMINISTRADOR;
            else if (block == 'blockFilial')
                return $rootScope.usuarioLogado.perfil == PERFIL_ADMINISTRADOR || $rootScope.usuarioLogado.perfil == PERFIL_EMPRESA;
            else if (block == 'blockGrupoUsuario')
                return true;
            else if (block == 'blockTipoNotificacao')
                return true;
            else return false;
        };

        vm.getGruposSelecionados = function () {
            return (vm.grupoUsuarioSelected != null && angular.isDefined(vm.grupoUsuarioSelected)) ? vm.grupoUsuarioSelected.map(function (x) { return x.IdGrupoUsuario; }) : [];
        }

        vm.grupoUsuarioJaSelecionado = function (id) {
            if (angular.isUndefined(vm.grupoUsuarioSelected) || vm.grupoUsuarioSelected === null)
                return;
            return vm.grupoUsuarioSelected.map(function (x) { return x.IdGrupoUsuario; }).indexOf(id) > -1;
        };

        vm.clear = function ($event) {
            if ($event.currentTarget.id == 'clearEmpresa') {
                vm.empresaSelected = undefined;
                //
                vm.grupoUsuarioSelected = undefined;
                vm.gruposUsuario = [];
                //
                vm.tipoNotificacaoSelected = undefined;
                vm.tiposNotificacao = [];
                //  
                vm.filialSelected = undefined;
                vm.filiais = [];
                //
                vm.hardwareIMEISelected = undefined;
                vm.hardwaresIMEI = [];
                // Fim
            } else if ($event.currentTarget.id == 'clearFilial') {
                vm.filialSelected = undefined;
                vm.updateTiposNotificacao(true);
                vm.updateHardwaresIMEI();
            } else if ($event.currentTarget.id == 'clearGrupoUsuario')
                vm.grupoUsuarioSelected = undefined;
            else if ($event.currentTarget.id == 'clearTipoNotificacao') {
                vm.tipoNotificacaoSelected = undefined;
            } else if ($event.currentTarget.id == 'clearHardwaresIMEI') {
                vm.hardwareIMEISelected = undefined;
                vm.hardwareIMEISelectedApelido = "";
            }

            $event.stopPropagation();
        };

        //Salvar e atualizar
        vm.save = function () {
            var model = {
                IdNotificacaoPush: angular.isDefined(vm.notificacaoPush.IdNotificacaoPush) ? vm.notificacaoPush.IdNotificacaoPush : null,
                IdEmpresa: vm.empresaSelected,
                IdFilial: vm.filialSelected,
                IdsGruposUsuario: vm.getGruposSelecionados(),
                IdTipoNotificacao: vm.tipoNotificacaoSelected || vm.notificacaoPush.IdTipoNotificacao,
                Descricao: vm.notificacaoPush.Descricao,
                DescricaoMensagem: vm.notificacaoPush.DescricaoMensagem,
                Sql: vm.notificacaoPush.Sql,
                MomentoExecucao: vm.notificacaoPush.MomentoExecucao,
                Items: vm.gridImeisApelidos.map(function (x) { return [x.IdHardware, x.Apelido, x.IdNotificacaoPush, x.IdNotificacaoPushItem]; })
            };

            BaseService.post('NotificacaoPushAts', $stateParams.link === 'novo' ? 'Cadastrar' : 'Editar', model).then(function (response) {
                if (response.success) {
                    toastr.success(response.message);
                    $state.go('gestao-logistica-notificacao-push.notificacao');
                } else
                    toastr.error(response.message);
                if ($stateParams.link === 'novo')
                    model.IdNotificacaoPush = null;
            });
        };

        vm.validaQtdMinimaGridIMEIS = function () {
            if (vm.notificacaoPush.MomentoExecucao === 1)
                return true;

            var continuar = vm.gridIMEISTemItem();
            if (!continuar)
                toastr.warning('É necessário no mínimo um IMEI selecionado para esta regra');
            return continuar;
        };

        vm.campoSqlTemValor = function () {
            if (vm.notificacaoPush.Sql == null ||
                vm.notificacaoPush.Sql == null ||
                vm.notificacaoPush.Sql.length < 1) {
                toastr.warning('É necessário informar um SQL para esta regra');
                return false;
            }

            return true;
        };

        vm.campoDescricaoMensagemTemValor = function () {
            if (vm.notificacaoPush.DescricaoMensagem == null ||
                vm.notificacaoPush.DescricaoMensagem == null ||
                vm.notificacaoPush.DescricaoMensagem.length < 1) {
                toastr.warning('É necessário informar mensagem de descrição para esta regra');
                return false;
            }

            return true;
        };

        vm.campoDescricaoMensagemTemValor = function () {
            if (vm.notificacaoPush.DescricaoMensagem == null ||
                vm.notificacaoPush.DescricaoMensagem.length < 1) {
                toastr.warning('É necessário informar mensagem de descrição para esta regra');
                return false;
            }

            return true;
        };

        vm.validaCamposNecessariosTesteRegra = function () {
            if (!vm.validaQtdMinimaGridIMEIS()) return;
            if (!vm.campoSqlTemValor()) return;
            if (!vm.campoDescricaoMensagemTemValor()) return;
            if (vm.grupoUsuarioSelected === undefined) { toastr.warning('É necessário informar um grupo de usuário para esta regra'); return; };
            if (vm.tipoNotificacaoSelected === undefined) { toastr.warning('É necessário informar um tipo de notificação para esta regra'); return; };

            return true;
        };

        vm.btnAcoesTestar = {
            mensagem: function () {
                if (!vm.validaCamposNecessariosTesteRegra()) return;
                var params = {
                    query: vm.notificacaoPush.Sql,
                    modeloMensagem: vm.notificacaoPush.DescricaoMensagem,
                    idEmpresa: vm.empresaSelected || vm.notificacaoPush.IdEmpresa,
                    apelido: vm.gridImeisApelidos.map(function (x) { return x.Apelido; })[0],
                    imeis: vm.gridImeisApelidos.map(function (x) { return x.IMEI; }),
                    idFilial: vm.filialSelected || vm.notificacaoPush.IdFilial
                };
                BaseService.post('NotificacaoPushAts', 'ValidarRegras', params).then(function (response) {
                    if (!response.success)
                        toastr.error(response.message);
                    else
                        toastr.success(response.message);
                });
            },
            sql: function () {
                if (!vm.validaCamposNecessariosTesteRegra()) return;
                var params = {
                    query: vm.notificacaoPush.Sql,
                    imeis: vm.gridImeisApelidos.map(function (item) { return item.IMEI }),
                    imei: vm.gridImeisApelidos.map(function (x) { return x.IMEI; })[0],
                    idEmpresa: vm.empresaSelected || vm.notificacaoPush.IdEmpresa,
                    idFilial: vm.filialSelected || vm.notificacaoPush.IdFilial
                };
                BaseService.post('NotificacaoPushAts', 'ExecutaRegra', params).then(function (response) {
                    var template = "<div style='font-size: 15px;' class='modal-body'>\
                                        <div class='row'>\
                                            <p>Sucesso: <span style='color: {{getColor(sucesso)}}'>{{sucesso}}</span></p>\
                                            <p>Retorno:</p>\
                                            <div style='color: {{getColor(sucesso)}}' id='retornoExecutarSql'>{{response}}</div>\
                                    </div>";

                    if (!response.success) {
                        toastr.error(response.message);
                        return;
                    }

                    // Sempre que o método for chamado no servidor e nao cair no ex
                    if (response.data === null || response.data === undefined)
                        response.success = false;

                    $uibModal.open({
                        animation: false,
                        template: template,
                        cache: true,
                        controller: function ($scope) {
                            $scope.sucesso = response.success ? "Sim" : "Não";
                            $scope.response = JSON.stringify(response.success ? response.data : "", null, 4).escapeSpecialChars();
                            $scope.getColor = function (success) {
                                return response.success ? 'darkgreen' : 'red';
                            }
                        },
                        size: 'md'
                    });
                });
            },
            push: function () {
                if (!vm.validaCamposNecessariosTesteRegra()) return;
                var params = {
                    query: vm.notificacaoPush.Sql,
                    imeis: vm.gridImeisApelidos.map(function (x) { return x.IMEI; }),
                    imei: vm.gridImeisApelidos.map(function (x) { return x.IMEI; })[0],
                    idEmpresa: vm.empresaSelected,
                    idFilial: vm.filialSelected,
                    IdsGruposUsuario: vm.grupoUsuarioSelected.map(function (x) { return x.IdGrupoUsuario; }),
                    modeloMensagem: vm.notificacaoPush.DescricaoMensagem,
                    apelido: vm.gridImeisApelidos.map(function (x) { return x.Apelido; })[0],
                    idTipoNotificacao: vm.tipoNotificacaoSelected,
                    isTeste: true
                };

                BaseService.post('NotificacaoPushAts', 'EnviarPush', params).then(function (response) {
                    toastr[response.success ? 'success' : 'error'](response.message);
                });
            }
        };

        vm.onChangeMomentoExecucao = function () {
            if (!vm.gridIMEISTemItem())
                vm.notificacaoPush.MomentoExecucao = 1;
        };

        $scope.$watch("vm.gridImeisApelidos.length > 0", function (val) {
            if (!val)
                if (vm.notificacaoPush.MomentoExecucao === 0)
                    vm.notificacaoPush.MomentoExecucao = 1;
        });

        vm.forEachFunc = function (methods) {
            methods.forEach(function (fn) {
                vm[fn](true);
            });
        };

        vm.empresaSelected = $window.localStorage.getItem("idEmpresa").toFixedType();;

        // DO NOT TOUCH!!
        $scope.$on('$stateChangeStart', function (_, toState) {
            if (toState.name === 'gestao-logistica-notificacao-push.notificacao')
                PersistentDataService.remove('NotificacaoPushCrudController');
            else PersistentDataService.store('NotificacaoPushCrudController', vm, "Cadastro - Notificação push", null, "gestao-logistica-notificacao-push.notificacao-crud", vm.notificacaoPush.IdNotificacaoPush);
        });
        var selfScope = PersistentDataService.get('NotificacaoPushCrudController');
        if (angular.isDefined(selfScope)) {
            angular.extend(vm, selfScope.data);
        } else
            if (!vm.isNew()) vm.loadEdit($stateParams.link);
            else vm.load();

        $timeout(function () { PersistentDataService.remove('NotificacaoPushController'); }, 15);
        // End
    }
})();