<div id="GrupoUsuarioController" ng-controller="GrupoUsuarioController as vm">
    <form-header items="vm.headerItems" head="'Grupos de usuário'"></form-header>
    <div class="wrapper wrapper-content animated fadeIn mt-5 overflow-hidden" >
        <div class="row">
            <div class="col-lg-12">
                <div class="ibox float-e-margins">
                    <div style="height: 38px;" class="ibox-title">
                        <h5>Grupos de usuário</h5>
                        <div ibox-tools></div>
                    </div>
                    <div class="ibox-content">
                        <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6" >
                            <label class="col-xs-12 col-md-3 control-label" style="text-align: right; padding-top: 10px;">
                                Sistema:
                            </label>
                            <div class="input-group col-xs-12 col-sm-4 col-md-4 col-lg-4">
                                <ui-select ats-ui-select-validator 
                                    ng-model="vm.sistema" name="sistema" id="sistema"
                                    ng-change="vm.gridOptions.dataSource.refresh()">
                                    <ui-select-match>
                                        <span>{{$select.selected.label}}</span>
                                    </ui-select-match>
                                    <ui-select-choices repeat="ex.data as ex in vm.combosSistema | propsFilter: {label: $select.search}">
                                        <div ng-bind-html="ex.label | highlight: $select.search"></div>
                                    </ui-select-choices>
                                </ui-select>
                            </div>
                        </div>
                        <div class="pull-right">
                            <button tooltip-placement="top" ng-click="vm.gridOptions.dataSource.refresh();" uib-tooltip="Atualizar" type='button' class="btn btn-labeled btn-default">
                                <i class="fa fa-refresh"></i>
                                <span class="pl-5 ">Atualizar</span>
                            </button>
                            <button tooltip-placement="top" ui-sref="grupo-usuario.grupo-usuario-crud({link: 'novo'})" uib-tooltip="Cadastrar " type='button'
                                class="btn btn-labeled btn-primary ">
                                <span class="btn-label text-right">
                                    <i class="fa fa-plus"></i>
                                </span>
                                <span class="pl-5 ">Novo</span>
                            </button>
                        </div>
                        <div class="row"></div>
                        <hr>
                        <div ui-grid="vm.gridOptions" ng-style="{height: vm.gridOptions.getGridHeight()}" class="grid" style="width: 100%;" ui-grid-pinning ui-grid-save-state
                            ui-grid-pagination ui-grid-auto-resize ui-grid-resize-columns ui-grid-grouping>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>