(function () {
    'use strict';

    angular.module('bbcWeb.sem-acesso.state', []).config(routeConfig);

    function routeConfig($urlRouterProvider, $stateProvider) {
        $stateProvider.state('sem-acesso', {
            abstract: true,
            url: "/sem-acesso",
            templateUrl: "app/layout/content.html"
        }).state('sem-acesso.index', {
            url: '/index',
            templateUrl: 'app/entities/sem-acesso/sem-acesso.html'
        });
    }
})();