<div class="form-horizontal">
    <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
        <hr-label class="mb-15" dark="true" title="'Tarifas de pagamento de frete'"></hr-label>
        <br/><br/>
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-4 control-label">
                        Quantidade transações PIX sem taxa:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-7 col-lg-8">
                        <input type="text" ng-model="vm.grupoEmpresa.qtdMensalSemTaxaPix"
                        ats-numeric
                        onkeypress="return event.charCode >= 48 && event.charCode <= 57" 
                        onpaste="return event.charCode >= 48 && event.charCode <= 57" maxlength="10" class="form-control"/>
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-5 col-lg-4 control-label">
                        Valor tarifa PIX:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-7 col-lg-8">
                        <input type="text" ui-money-mask="3" ng-model="vm.grupoEmpresa.valorTarifaPix"
                        onpaste="return event.charCode >= 48 && event.charCode <= 57" maxlength="10"
                         class="form-control" />
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-4 control-label">
                        % Tarifa BBC:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-7 col-lg-8">
                        <input type="number" min="0" max="100" ng-model="vm.grupoEmpresa.percentualTarifaBbc"
                        class="form-control"/>
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-md-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-md-4">Cobrança de Tarifa BBC e PIX</label>
                    <div class="input-group col-xs-12 col-md-8">
                        <toggle-switch on-label="Sim" off-label="Não" ng-model="vm.grupoEmpresa.cobrancaTarifa" class="switch"></toggle-switch>
                    </div>
                </div>
            </div>
        </div>
        <hr-label class="mb-15" dark="true" title="'Tarifas de pagamento de pedágio'"></hr-label>
        <br/><br/>
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-4 control-label">
                        % Tarifa Vale Pedágio:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-7 col-lg-8">
                        <input type="number" min="0" max="100" ng-model="vm.grupoEmpresa.percentualTarifaValePedagio"
                            class="form-control" />
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-md-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-md-4">Cobrança tarifa Vale pedágio</label>
                    <div class="input-group col-xs-12 col-md-8">
                        <toggle-switch on-label="Sim" off-label="Não" ng-model="vm.grupoEmpresa.cobrarTarifaBbcValePedagio"
                            class="switch"></toggle-switch>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>