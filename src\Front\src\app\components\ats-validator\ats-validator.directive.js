(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .directive('atsValidator', atsValidator);

    atsValidator.$inject = ['$log', '$timeout'];

    function atsValidator($log, $timeout) {

        var directive = {
            bindToController: false,
            require: ['form'],
            link: link,
            restrict: 'A',
            scope: {}
        };
        
        return directive;

        function link(scope, element, attrs, controllers) {
            var formCtrl = controllers[0];
            formCtrl.submitted = false;
            var watches = [];

            function setupWatches() {
                $timeout(function () {
                    var DOMForm = angular.element(element)[0];
                    var formInvalidMessage = hasFormInvalidMessage(DOMForm);
                    for (var i = 0; i < DOMForm.length; i++) {
                        if (i in DOMForm) {
                            formInvalidMessage = hasFormInvalidMessage(DOMForm[i]);
                            setupWatch(DOMForm[i], formInvalidMessage);
                        }
                    }
                }, 3000);
            }

            setupWatches(formCtrl);

            function hasFormInvalidMessage(formElement) {
                if (formElement && 'invalid-message' in formElement.attributes) {
                    return formElement.attributes['invalid-message'].value;
                } else {
                    return false;
                }
            }

            function setupWatch(elementToWatch, formInvalidMessage) {
                if ("validate-on" in elementToWatch.attributes && elementToWatch.attributes["validate-on"].value === "blur") {
                    angular.element(elementToWatch).on('blur', function () {
                        updateValidationMessage(elementToWatch, formInvalidMessage);
                        updateValidationClass(elementToWatch);
                    });
                }

                var watch = scope.$watch(function () {
                        return elementToWatch.value + elementToWatch.required + formCtrl.submitted + checkElementValidity(elementToWatch) + getDirtyValue(formCtrl[elementToWatch.name]) + getValidValue(formCtrl[elementToWatch.name]);
                    },
                    function () {
                        if (formCtrl.submitted) {
                            updateValidationMessage(elementToWatch, formInvalidMessage);
                            updateValidationClass(elementToWatch);
                        } else {

                            var isDirtyElement = "validate-on" in elementToWatch.attributes && elementToWatch.attributes["validate-on"].value === "dirty";

                            if (isDirtyElement) {
                                updateValidationMessage(elementToWatch, formInvalidMessage);
                                updateValidationClass(elementToWatch);
                            } else if (formCtrl[elementToWatch.name] && formCtrl[elementToWatch.name].$pristine) {
                                updateValidationMessage(elementToWatch, formInvalidMessage);
                                updateValidationClass(elementToWatch);
                            }
                        }

                    });

                watches.push(watch);
            }

            function getDirtyValue(element) {
                if (element && "$dirty" in element) {
                    return element.$dirty;
                }
            }

            function getValidValue(element) {
                if (element && "$valid" in element) {
                    return element.$valid;
                }
            }

            function checkElementValidity(element) {
                if ("validator" in element.attributes) {
                    var isElementValid = scope.$eval(element.attributes.validator.value);
                    formCtrl[element.name].$setValidity("angularValidator", isElementValid);
                    return isElementValid;
                }
            }

            function updateValidationMessage(element, formInvalidMessage) {
                if (!(element.name in formCtrl)) {
                    return;
                }

                var defaultRequiredMessage = function () {
                    return element.name + " é obrigatório";
                };
                var defaultInvalidMessage = function () {
                    return element.name + " é inválido";
                };

                var scopeElementModel = formCtrl[element.name];
                var validationMessageElement = isValidationMessagePresent(element);
                if (validationMessageElement) {
                    validationMessageElement.remove();
                }

                var metodoAlternativo = possuiMetodoAlternativo(element);
                if (metodoAlternativo) {
                    metodoAlternativo.remove();
                }

                if (scopeElementModel.$dirty || formCtrl.submitted) {
                    var needFloating = angular.isDefined(angular.element(element).next()[0]) && angular.element(element).next()[0].tagName === 'SPAN';
                    if (scopeElementModel.$error.required) {
                        if ("required-message" in element.attributes) {
                            angular.element(element).after(generateErrorMessage(element.attributes['required-message'].value, needFloating));
                        } else {
                            angular.element(element).after(generateErrorMessage(defaultRequiredMessage, needFloating));
                        }
                    } else if (!scopeElementModel.$valid || !caracteresValidos(element, scopeElementModel)) {
                        if ("invalid-message" in element.attributes) {
                            angular.element(element).after(generateErrorMessage(element.attributes['invalid-message'].value, needFloating));
                        } else if (formInvalidMessage) {
                            angular.element(element).after(generateErrorMessage(formInvalidMessage.message(scopeElementModel, element, needFloating)));
                        } else {
                            angular.element(element).after(generateErrorMessage(defaultInvalidMessage, needFloating));
                        }
                    }
                }
            }

            function generateErrorMessage(messageText, needFloating) {
                if (needFloating === true)
                    return "<div class='divValidatorMargin' style='margin-bottom: 35px;'></div> <label class='control-label has-error validationMessage ats-validator-label'> <i class='fa fa-times'></i> " + scope.$eval(messageText) + "</label>";

                return "<label class='control-label has-error validationMessage' style='margin-top: -7px;font-size: 9px;'> <i class='fa fa-times'></i> " + scope.$eval(messageText) + "</label>";
            }

            function isValidationMessagePresent(element) {
                var elementSiblings = angular.element(element).parent().children();
                for (var i = 0; i < elementSiblings.length; i++) {
                    if (angular.element(elementSiblings[i]).hasClass("validationMessage")) {
                        return angular.element(elementSiblings[i]);
                    }
                    if (angular.element(elementSiblings[i]).hasClass("divValidatorMargin"))
                        angular.element(elementSiblings[i]).remove();
                }
                return false;
            }

            function possuiMetodoAlternativo(element) {
                var elementSiblings = angular.element(element).parent().children();
                for (var i = 0; i < elementSiblings.length; i++) {
                    if (angular.element(elementSiblings[i]).hasClass("divValidatorMargin")) {
                        angular.element(elementSiblings[i]).remove();
                    }
                }
                return false;
            }

            function updateValidationClass(element) {
                if (!(element.name in formCtrl)) {
                    return;
                }
                var formField = formCtrl[element.name];

                angular.element(element).removeClass('has-error');
                angular.element(element.parentNode).removeClass('has-error');

                if (formField.$dirty || formCtrl.submitted) {
                    if (formField.$invalid) {
                        angular.element(element.parentNode).addClass('has-error');
                        angular.element(element).addClass('has-error');
                    }
                }
            }

            function caracteresValidos(element, scopeElementModel) {
                if ("cpf" in element.attributes) {
                    if (!validacpf(scopeElementModel.$modelValue.toString())) {
                        scopeElementModel.$invalid = true;
                        return false;
                    }
                }

                if ("cnpj" in element.attributes) {
                    if (!validarCNPJ(scopeElementModel.$modelValue.toString())) {
                        scopeElementModel.$invalid = true;
                        return false;
                    }
                }

                return true;
            }

            function validarCNPJ(cnpj) {
                cnpj = cnpj.replace(/[^\d]+/g, '');

                if (cnpj == '') return false;

                if (cnpj.length != 14)
                    return false;

                // Elimina CNPJs invalidos conhecidos
                if (cnpj == "00000000000000" ||
                    cnpj == "11111111111111" ||
                    cnpj == "22222222222222" ||
                    cnpj == "33333333333333" ||
                    cnpj == "44444444444444" ||
                    cnpj == "55555555555555" ||
                    cnpj == "66666666666666" ||
                    cnpj == "77777777777777" ||
                    cnpj == "88888888888888" ||
                    cnpj == "99999999999999")
                    return false;

                // Valida DVs
                tamanho = cnpj.length - 2
                numeros = cnpj.substring(0, tamanho);
                digitos = cnpj.substring(tamanho);
                soma = 0;
                pos = tamanho - 7;
                for (i = tamanho; i >= 1; i--) {
                    soma += numeros.charAt(tamanho - i) * pos--;
                    if (pos < 2)
                        pos = 9;
                }
                resultado = soma % 11 < 2 ? 0 : 11 - soma % 11;
                if (resultado != digitos.charAt(0))
                    return false;

                tamanho = tamanho + 1;
                numeros = cnpj.substring(0, tamanho);
                soma = 0;
                pos = tamanho - 7;
                for (i = tamanho; i >= 1; i--) {
                    soma += numeros.charAt(tamanho - i) * pos--;
                    if (pos < 2)
                        pos = 9;
                }
                resultado = soma % 11 < 2 ? 0 : 11 - soma % 11;
                if (resultado != digitos.charAt(1))
                    return false;

                return true;

            }


            function validacpf(cpf) {
                var numeros, digitos, soma, i, resultado, digitos_iguais;
                digitos_iguais = 1;
                if (cpf.length < 11)
                    return false;
                for (i = 0; i < cpf.length - 1; i++)
                    if (cpf.charAt(i) != cpf.charAt(i + 1)) {
                        digitos_iguais = 0;
                        break;
                    }
                if (!digitos_iguais) {
                    numeros = cpf.substring(0, 9);
                    digitos = cpf.substring(9);
                    soma = 0;
                    for (i = 10; i > 1; i--)
                        soma += numeros.charAt(10 - i) * i;
                    resultado = soma % 11 < 2 ? 0 : 11 - soma % 11;
                    if (resultado != digitos.charAt(0))
                        return false;
                    numeros = cpf.substring(0, 10);
                    soma = 0;
                    for (i = 11; i > 1; i--)
                        soma += numeros.charAt(11 - i) * i;
                    resultado = soma % 11 < 2 ? 0 : 11 - soma % 11;
                    if (resultado != digitos.charAt(1))
                        return false;
                    return true;
                } else
                    return false;
            }

            element.on('submit', function (event) {

                event.preventDefault();
                scope.$apply(function () {
                    formCtrl.submitted = true;
                });
                if (!formCtrl.$valid) {}
            });
        }
    }
})();