<div id="AutorizacaoAbastecimentoController" ng-controller="AutorizacaoAbastecimentoController as vm">
    <form-header items="vm.headerItems" head="'Autorização abastecimento'"></form-header>
    <div class="wrapper wrapper-content animated fadeIn mt-5 overflow-hidden">
        <div class="row">
            <div class="col-lg-12">
                <div class="ibox float-e-margins">
                    <div style="height: 38px;" class="ibox-title">
                        <h5>Autorização abastecimento</h5>
                        <div ibox-tools></div>
                    </div>
                    <div class="ibox-content">
                        <div class="row">
                            <consulta-padrao-modal tabledefinition="vm.consultaVeiculo" label="'Veículo:'"
                                                   idname="consultaVeiculo"
                                                   ng-disabled="vm.consultaFilial.selectedValue"
                                                   placeholder="'Selecione um Veículo'"
                                                   ng-required="vm.consultaFilial.selectedValue == null || vm.consultaFilial.selectedValue == 0"
                                                   required-message="'Veículo é obrigatório'"
                                                   directivesizes="'col-xs-12 col-sm-12 col-md-6 col-lg-6'"
                                                   labelsize="'col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label'">
                            </consulta-padrao-modal>

                            <consulta-padrao-modal tabledefinition="vm.consultaModelo" label="'Modelo:'"
                                                   idname="consultaModelo"
                                                   ng-disabled="vm.consultaVeiculo.selectedValue || !vm.consultaFilial.selectedValue"
                                                   placeholder="'Selecione um modelo'" ng-required="true"
                                                   required-message="'Modelo é obrigatório'"
                                                   directivesizes="'col-xs-12 col-sm-12 col-md-6 col-lg-6'"
                                                   labelsize="'col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label'">
                            </consulta-padrao-modal>
                        </div>

                        <div class="row">
                            <consulta-padrao-modal tabledefinition="vm.consultaFilial" label="'Filial:'"
                                                   idname="consultaFilial"
                                                   ng-disabled="vm.consultaVeiculo.selectedValue"
                                                   placeholder="'Selecione um filial'"
                                                   ng-required="vm.consultaVeiculo.selectedValue == null || vm.consultaVeiculo.selectedValue == 0"
                                                   required-message="'Filial é obrigatório'"
                                                   directivesizes="'col-xs-12 col-sm-12 col-md-6 col-lg-6'"
                                                   labelsize="'col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label'">
                            </consulta-padrao-modal>
                            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                                <div class="form-group">
                                    <label class="col-xs-12 col-md-3 control-label"
                                           style="text-align: right; padding-top: 10px;">
                                        <span class="text-danger mr-5">*</span>Método de abastecimento:
                                    </label>
                                    <div class="input-group col-xs-12 col-md-9">
                                        <ui-select
                                                name="Metodo"
                                                ats-ui-select-validator
                                                validate-on="blur"
                                                ng-model="vm.metodo"
                                                required-message="'Método é obrigatório'"
                                                ng-disabled="vm.consultaFilial.selectedValue"
                                                ng-change="vm.obrigatorioConbustivel()"
                                                required>
                                            <ui-select-match>
                                                <span>{{$select.selected.descricao}}</span>
                                            </ui-select-match>
                                            <ui-select-choices
                                                    repeat="ex.id as ex in vm.cmbMetodo.data | propsFilter: {descricao: $select.search}">
                                                <div ng-bind-html="ex.descricao | highlight: $select.search"></div>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                                <div class="form-group">
                                    <label class="col-xs-12 col-md-3 control-label"
                                           style="text-align: right; padding-top: 10px;">
                                        <span class="text-danger mr-5">*</span>Combustivel:
                                    </label>
                                    <div class="input-group col-xs-12 col-md-9">
                                        <ui-select
                                                name="combustiveis"
                                                ats-ui-select-validator
                                                validate-on="blur"
                                                ng-model="vm.combustivel"
                                                required-message="'Combustivel é obrigatório'"
                                                ng-disabled="vm.cmbConbustivelDesativa"
                                                ng-required="vm.combustivelObrigatorio">
                                            <ui-select-match>
                                                <span>{{$select.selected.combustivelNome}}</span>
                                            </ui-select-match>
                                            <ui-select-choices
                                                    repeat="ex as ex in vm.combustiveisVeiculo | propsFilter: {combustivelNome: $select.search}">
                                                <div ng-bind-html="ex.combustivelNome | highlight: $select.search"></div>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                                <div class="form-group">
                                    <label class="col-xs-12 col-md-3 control-label"
                                           style="text-align: right; padding-top: 10px;">
                                        <span class="text-danger mr-5">*</span>Litragem:
                                    </label>
                                    <div class="input-group col-xs-12 col-md-9">
                                        <input type="text" ui-number-mask="3" maxlength="13" placeholder="0,000"
                                               ng-model="vm.litragem"
                                               class="form-control"/>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-3 pull-right">
                                <button tooltip-placement="top" ng-disabled="vm.permiteAdicionar"
                                        uib-tooltip="Adicionar "
                                        type='button' ng-click="vm.adicionarAutorizacao()"
                                        class="btn btn-labeled btn-primary pull-right">
                                    <span class="btn-label text-right">
                                        <i class="fa fa-plus"></i>
                                    </span>
                                    <span class="pl-5 ">Adicionar</span>
                                </button>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
                                <div class="table-responsive" style="margin-top: 5px;">
                                    <table class="table table-bordered table-hover col-xs-12">
                                        <thead>
                                        <tr>
                                            <th width="20%">Placa</th>
                                            <th width="20%">Autorização</th>
                                            <th width="25%">Combustível</th>
                                            <th width="25%">Litragem</th>
                                            <th width="10%">Ações</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <tr ng-repeat="combustiveis in vm.autorizacaoCombustiveis">
                                            <td>{{combustiveis.placa}}</td>
                                            <td>
                                                <p ng-show="combustiveis.filial !== null"> Por
                                                    filial: {{combustiveis.filial}} </p>
                                                <p ng-show="combustiveis.filial === null"> Por veículo. </p>
                                            </td>
                                            <td>{{combustiveis.combustivelNome}}</td>
                                            <td>
                                                <input type="text" ng-model="combustiveis.litragem" readonly
                                                       class="no-borders" style="background: none;" ui-number-mask="3"/>
                                            </td>
                                            <td class="text-center" style="vertical-align: middle">
                                                <button type="button" uib-tooltip="Remover"
                                                        class="btn btn-xs btn-danger"
                                                        ng-click="vm.removerCombustivel(combustiveis)">
                                                    <i class="fa fa-trash-o"></i>
                                                </button>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-3 pull-right">
                                <button tooltip-placement="top"
                                        ng-disabled="vm.autorizacaoCombustiveis.length == 0 || vm.salvandoAutorizacao"
                                        uib-tooltip="Adicionar "
                                        type='button' ng-click="vm.save()"
                                        class="btn btn-labeled btn-primary pull-right">
                                    <span class="btn-label text-right">
                                    </span>
                                    <span class="pl-5 ">Criar</span>
                                </button>
                            </div>
                        </div>

                        <div class="row"></div>
                        <hr>
                        <div ui-grid="vm.gridOptions" ng-style="{height: vm.gridOptions.getGridHeight()}" class="grid" style="width: 100%;"
                             ui-grid-pinning ui-grid-save-state
                             ui-grid-pagination ui-grid-auto-resize ui-grid-resize-columns ui-grid-grouping>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>