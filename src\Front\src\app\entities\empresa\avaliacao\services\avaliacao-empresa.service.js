(function() {
    'use strict';

    angular.module('bbcWeb')
        .factory('AvaliacaoEmpresaService', AvaliacaoEmpresaService);

    AvaliacaoEmpresaService.inject = [
        '$rootScope',
        'BaseService'
    ];

    function AvaliacaoEmpresaService($rootScope, BaseService) {
        const USUARIO_CONTROLLER = "Usuario";

        var functions = {
            getHeader: function() {
                return [{ name: 'Empresa' }, { name: 'Avaliação' }];
            },
            getStatusToDropdown: function() {
                return [{ id: 0, descricao: "Bloqueado"}, { id: 1, descricao: "Ativo"}/*, { id: 2, descricao: "Pendente de validação"}*/];
            },
            getGridTemplate: function() {
                return '<div class="ui-grid-cell-contents">\
                            <button title="Realizar validação" type="button" ui-sref="empresa-avaliacao.validar({link: row.entity.id})" ng-class="{ \'btn btn-xs btn-info\': true }">\
                                <i class="fa fa-check"></i>\
                            </button>\
                        </div>'
            }
        }

        return functions;
    }
})();