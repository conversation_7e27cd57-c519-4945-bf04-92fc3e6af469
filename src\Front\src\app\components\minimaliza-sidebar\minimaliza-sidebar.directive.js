(function() {
    'use strict';

    angular.module('bbcWeb').directive('minimalizaSidebar', function($timeout, $interval, $window) {
        return {
            restrict: 'A',
            template: '<a class="navbar-minimalize minimalize-styl-2 btn btn-primary " ng-if="!usuarioLogado.usuarioEstabPrimeiroLoginSemEstab" href="" ng-click="minimalize()"><i class="fa fa-bars"></i></a>',
            controller: function($scope, $interval) {
                $scope.minimalize = function() {
                    angular.element('body').toggleClass('mini-navbar');
                    if (!angular.element('body').hasClass('mini-navbar') ||
                        angular.element('body').hasClass('body-small')) {
                        angular.element('#side-menu').hide(0);
                        angular.element('#side-menu').fadeIn(0);
                    } else
                        angular.element('#side-menu').removeAttr('style');

                    $timeout(function() {
                        angular.element($window).trigger('resize');
                    }, 400)
                };
            }
        };
    });
})();