(function () {
    'use strict';

    angular.module('bbcWeb').controller('CentroCustoCrudController', CentroCustoCrudController);

    CentroCustoCrudController.$inject = ['toastr', '$rootScope', 'BaseService', '$state', '$stateParams', '$window', 'PERFIL_ADMINISTRADOR', '$scope', '$timeout', 'PersistentDataService', 'DefaultsService', '$uibModal'];

    function CentroCustoCrudController(
        toastr, $rootScope,
        BaseService, $state,
        $stateParams, $window,
        PERFIL_ADMINISTRADOR,
        $scope, $timeout,
        PersistentDataService,
        DefaultsService,
        $uibModal
    ) {
        var vm = this;
        vm.centroCusto = [];
        vm.menusPai = [];

        vm.headerItems = [{
            name: 'Cadastros'
        }, {
            name: 'Centro de custo',
            link: 'centro-custo.index'
        }, {
            name: $stateParams.link == 'novo' ? 'Novo' : 'Editar'
        }];

        var selfScope = PersistentDataService.get('CentroCustoCrudController');

        if ($stateParams.link == 'novo')
            vm.centroCusto.id = 'Auto';

        vm.isNew = function () {
            return $stateParams.link == 'novo';
        };

        vm.loadEdit = function (id) {
            BaseService.get('CentroCusto', 'ConsultarPorId', {
                idCentroCusto: id
            }).then(function (response) {
                if (!response.success) {
                    toastr.error(response.message);
                    return;
                } else {
                    vm.centroCusto = response.data;
                    vm.consultaEmpresa.selectedValue = vm.centroCusto.empresaId;
                    vm.consultaEmpresa.selectedText = vm.centroCusto.empresa;
                    vm.consultaFilial.selectedValue = vm.centroCusto.filialId;
                    vm.consultaFilial.selectedText = vm.centroCusto.filial;
                }
            });
        };

        vm.consultaEmpresa = {
            columnDefs: [{
                name: 'Código',
                field: 'id',
                width: 80,
                type: 'number',
                primaryKey: true
            }, {
                name: 'Nome Fantasia',
                field: 'nomeFantasia',
                width: '*',
                minWidth: 250
            }, {
                name: 'Razão Social',
                field: 'razaoSocial',
                width: '*',
                minWidth: 250
            }, {
                name: 'Email',
                field: 'email',
                width: '*',
                minWidth: 250
            }],
            desiredValue: 'id',
            desiredText: 'nomeFantasia',
            url: 'Empresa/ConsultarGridEmpresaCombo',
            paramsMethod: function () {
                return {}
            },
        };

        vm.consultaFilial = {
            columnDefs: [{
                name: 'Código',
                field: 'id',
                width: 80,
                type: 'number',
                primaryKey: true
            }, {
                name: 'Nome',
                field: 'nomeFantasia',
                width: '*',
                minWidth: 150
            }, {
                name: 'Razão social',
                field: 'razaoSocial',
                width: '*',
                minWidth: 150
            }, {
                name: 'CNPJ',
                field: 'cnpj',
                width: '*',
                minWidth: 150
            }],
            desiredValue: 'id',
            desiredText: 'nomeFantasia',
            url: 'Filial/ConsultarGridFilial',
            paramsMethod: function () {
                return {}
            },
        };

        vm.bloquearCaracteresIndesejados = function(event) {
            var teclaDigitada = String.fromCharCode(event.charCode);
            var caracteresBloqueados = ['<', '>', '$', '\''];

            if (caracteresBloqueados.includes(teclaDigitada)) {
                event.preventDefault();
            }
        };
        
        vm.save = function (form) {
            if (!form.$valid) {
                toastr.error('Todos os campos obrigatórios devem ser preenchidos corretamente com seus respectivos valores');
                return;
            }

            if (!vm.consultaEmpresa.selectedValue) {
                toastr.error('');
                return;
            }

            if (vm.isSaving === true)
                return;

            var centroCusto = {}

            centroCusto.Id = vm.centroCusto.id == "Auto" ? 0 : vm.centroCusto.id;
            centroCusto.Descricao = vm.centroCusto.descricao;
            centroCusto.CodigoExterno = vm.centroCusto.codigoExterno;
            centroCusto.CodigoCentroCusto = vm.centroCusto.codigoCentroCusto;
            centroCusto.Ativo = vm.centroCusto.ativo;
            centroCusto.EmpresaId = vm.consultaEmpresa.selectedValue;
            centroCusto.empresa = vm.consultaEmpresa.selectedText;
            centroCusto.filialId = vm.consultaFilial.selectedValue;
            centroCusto.filial = vm.consultaFilial.selectedText;
            
            vm.isSaving = true;

            BaseService.post('CentroCusto', 'Salvar', centroCusto).then(function (response) {
                vm.isSaving = false;

                if (response && !response.sucesso)
                    return toastr.error(response.mensagem);
                else {
                    toastr.success('Centro de custo salvo com sucesso!');
                    $state.go('centro-custo.index');
                }
            });
        };

        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.administrador == true;
        };

        function init() {
            if (vm.isNew()) {
                vm.centroCusto.id = 'Auto';
            }
        };

        vm.onClickAvancar = function (wizard) {
            wizard.go(wizard.getActivePosition() + 1);
        };

        vm.onClickVoltar = function (wizard) {

            var ativoIndex = wizard.getActivePosition();
            if (ativoIndex == 1)
                $state.go('centro-custo.index');

            wizard.go(ativoIndex - 1);
        };

        init();

        $scope.$on('$stateChangeStart', function (arg1, toState) {
            if (toState.name === 'centro-custo.index')
                PersistentDataService.remove('CentroCustoCrudController');
            else
                PersistentDataService.store('CentroCustoCrudController', vm, "Cadastro - Centros de custos", null, "centro-custo.centro-custo-crud", vm.centroCusto.id);
        });

        if (angular.isDefined(selfScope))
            angular.extend(vm, selfScope.data);
        else {
            if (!vm.isNew())
                vm.loadEdit($stateParams.link);
            else {
                vm.modulos = 1;
            }
        }

        $timeout(function () {
            PersistentDataService.remove('CentroCustoController');
        }, 15);
    }
})();
