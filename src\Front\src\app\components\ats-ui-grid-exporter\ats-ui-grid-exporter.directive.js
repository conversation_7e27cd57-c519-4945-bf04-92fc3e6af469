(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .directive('atsUiGridExporter', atsUiGridExporter);

    atsUiGridExporter.$inject = ['$http', 'URL_SERVER_DEV', 'toastr'];

    function atsUiGridExporter($http, URL_SERVER_DEV, toastr) {

        var directive = {
            replace: true,
            priority: 0,
            require: '^uiGrid',
            link: link,
            scope: false
        };
        return directive;

        function link(scope, element, attrs, uiGridController) {

            var access = {
                headers: {
                    "Content-Type": "application/json; charset=utf-8",
                    "Accept": "application/json"
                }
            };

            var columnsToExport = [];
            var exportOptions = {
                headers: true,
                columns: []
            };

            //Salvamos as opções dos nomes das colunas que se desejam ser exportadas e também a largura da coluna
            for (var i = 0; i < uiGridController.grid.options.columnDefs.length; i++) {
                var columnGrid = uiGridController.grid.options.columnDefs[i];
                if (angular.isDefined(columnGrid) && angular.isDefined(columnGrid.export) && columnGrid.export === true) {
                    columnsToExport.push(columnGrid.serverField);
                    exportOptions.columns.push({
                        columnid: columnGrid.serverField,
                        width: columnGrid.exportWidth
                    });
                }
            }

            //Carregamos a data do servidor com os filtros padrões da grid e enviamos para o alasql exportar
            uiGridController.grid.options.exportar = function () {
                uiGridController.grid.options.loading = true;

                var dtSrc = uiGridController.grid.options.dataSource;

                var params = angular.isFunction(dtSrc.params) ? dtSrc.params() : {};
                var queryFiltersOptionalFnRet = angular.isFunction(dtSrc.queryFiltersOptionalFn) ? dtSrc.queryFiltersOptionalFn() : [];


                params.Page = 1;
                //Como estamos exportando a grid, jogamos o máximo de valor possível para o take, para trazermos o total de data a partir do filtro;
                params.Take = 2147483647;

                if (uiGridController.grid.options.lastSortUsed)
                    params.Order = {
                        Campo: uiGridController.grid.options.lastSortUsed.Campo,
                        Operador: uiGridController.grid.options.lastSortUsed.Operador
                    };

                if (angular.isUndefined(params.Filters) || params.Filters == null)
                    params.Filters = [];

                // Caso haja 
                if (angular.isObject(uiGridController.grid.options.filtrosPorColuna) && uiGridController.grid.options.filtrosPorColuna.length > 0)
                    if (angular.isArray(uiGridController.grid.options.filtrosPorColuna))
                        uiGridController.grid.options.filtrosPorColuna.forEach(function (xxyy) {
                            params.Filters.push(xxyy);
                        });

                // Roda o queryFiltersOptionalFnRet..
                if (queryFiltersOptionalFnRet.length > 0)
                    queryFiltersOptionalFnRet.forEach(function (ref) {
                        params.Filters.push(ref);
                    });

                // Busca os dados no servidor. sim... é POST e não GET, GET não aceito objetos na requisição
                $http.post(URL_SERVER_DEV + uiGridController.grid.options.dataSource.url, params, access).then(function (response) {
                    if (response.data && response.data.data && response.data.data.items &&
                        response.data.data.items.length > 0) {
                        var exportData = response.data.data.items.map(function (item) {
                            var returnObject = {};
                            for (var i = 0; i < columnsToExport.length; i++) {
                                returnObject[columnsToExport[i]] = item[columnsToExport[i]];
                                if (returnObject[columnsToExport[i]] === null)
                                    returnObject[columnsToExport[i]] = "";
                            }

                            return returnObject;
                        });
                        alasql('SELECT * INTO XLSXML(' + uiGridController.grid.options.exportName + ', ?) FROM ?', [exportOptions, exportData]);
                    }
                    if (!response.data.success) toastr.error(response.data.message);
                }).finally(function () {
                    uiGridController.grid.options.loading = false;
                });
            };
        }
    }
})();