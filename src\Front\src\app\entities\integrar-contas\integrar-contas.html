<div id="IntegrarContaController" ng-controller="IntegrarContaController as vm">
    <form-header items="vm.headerItems" head="'Integrar contas'"></form-header>
    <div class="wrapper wrapper-content animated fadeIn mt-5 overflow-hidden">
        <div class="row">
            <div class="">
                <div class="ibox float-e-margins">
                    <div style="height: 38px;" class="ibox-title">
                        <h5>Integrar contas</h5>
                        <div ibox-tools></div>
                    </div>
                    <div class="ibox-content">
                        <div>
                            <div class="form-group">
                                <div class="col-xs-12 col-sm-7 col-md-4 col-lg-5 pl-0 field-import-integrar">
                                    <input class="form-control" id="readfile" accept=".xlsx, .xls" type="file"
                                        onchange="angular.element(this).scope().excelExport(event)" />
                                </div>
                                <div class="col-xs-12 col-sm-3 col-md-2 col-lg-2 pl-0 pr-0">
                                    <button tooltip-placement="top" uib-tooltip="Importar " type='button' ng-disabled="!vm.isSaving"
                                        ng-click="vm.ImportarContas()" class="btn btn-labeled btn-primary">
                                        <span class="btn-label text-right">
                                            <i class="fa fa-arrow-down"></i>
                                        </span>
                                        <span class="pl-5 ">Importar</span>
                                    </button>
                                </div>
                                <div class="col-xs-12 col-sm-2 col-md-6 col-lg-5 pr-0 buttons-ativar-desativar-celular-div">
                                    <button tooltip-placement="top" ng-click="vm.gridOptions.dataSource.refresh();"
                                        uib-tooltip="Atualizar" type='button'
                                        class="btn btn-labeled btn-default pull-right buttons-ativar-desativar-celular">
                                        <i class="fa fa-refresh"></i>
                                        <span class="pl-5">Atualizar</span>
                                    </button>
                                    <button tooltip-placement="top" ng-click="vm.inativarAtivarTodasContas()" type='button' style="margin-right: 5px" class="btn btn-labeled btn-primary pull-right buttons-ativar-desativar-celular">
                                        <span class="pl-5">Ativar/Inativar todos</span>
                                    </button> 
                                </div>
                            </div>
                        </div>
                        <div class="row"></div>
                        <hr>
                        <div class="col-sm-12">
                            <div class="row">
                                <div class="form-group">
                                    <div class="">
                                        <div ui-grid="vm.gridOptions" ng-style="{height: vm.gridOptions.getGridHeight()}" class="grid" style="width: 100%;"
                                        ui-grid-pinning ui-grid-save-state ui-grid-pagination ui-grid-auto-resize
                                        ui-grid-resize-columns ui-grid-grouping></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="pull-right mt-15">
                            <button type="button" class="btn btn-primary pull-right" ng-disabled="vm.gridApi.grid.options.data.length == 0 || (vm.contas.length == 0 && !vm.ativarTodasConta) || !vm.isSaving"
                                ng-click="vm.enviarContas()"><i class="fa fa-chevron-circle-right"></i>
                                Enviar</button>
                        </div>
                        <div class="row"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>