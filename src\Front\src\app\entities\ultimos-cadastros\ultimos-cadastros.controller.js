(function() {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('UltomosCadastrosController', UltomosCadastrosController);

    UltomosCadastrosController.inject = [
        '$scope',
        'toastr',
        'BaseService',
        'PERFIL_ADMINISTRADOR',
        'PERFIL_EMPRESA',
        '$window',
        'PersistentDataService',
        '$rootScope',
        '$timeout',
        '$uibModal'
    ];

    function UltomosCadastrosController(
        $scope,
        toastr,
        BaseService,
        PERFIL_ADMINISTRADOR,
        PERFIL_EMPRESA,
        $window,
        PersistentDataService,
        $rootScope,
        $timeout,
        $uibModal) {

        var vm = this;
        vm.fila = [];

        vm.headerItems = [{
            name: 'Cadastros'
        }, {
            name: 'Últimos cadastros'
        }];

        vm.gridOptions = {
            data: [],
            onRegisterApi: BaseService.dataGrid.defaultOnRegisterApi,
            dataSource: {
                autoBind: true,
                url: "MotoristaBaseAts/ConsultarGrid",
                params: function() {
                    return {

                    };
                }
            },
            columnDefs: [{
                    name: '<PERSON><PERSON><PERSON>',
                    primaryKey: true,
                    field: 'IdMotoristaBase'
                },
                {
                    name: 'Nome',
                    field: 'Nome',
                    width: '*',
                    minWidth: 250
                },
                {
                    name: 'Placa',
                    field: 'Placa',
                    serverField: 'Conjuntos.PlacaCavalo',
                    width: 130,
                },
                {
                    name: 'Veículo',
                    field: 'NomeCavalo',
                    serverField: 'Conjuntos.NomeCavalo',
                    width: 130,
                },
                {
                    name: 'Carretas',
                    field: 'NomeCarreta',
                    serverField: 'Conjuntos.NomeCarreta',
                    width: 130,
                },
                {
                    name: 'Localização',
                    field: 'UltimoEndereco',
                    width: '*',
                    minWidth: 220
                },
                {
                    name: 'Status',
                    field: 'Status',
                    serverField: 'Conjuntos.Status',
                    enum: true,
                    width: 120,
                    enumTipo: 'EStatusGR'
                }
                //,
                //{
                //    name: 'Ações',
                //    width: '6%',
                //    enableColumnMenu: false,
                //    cellTemplate: '<div  ng-if="!row.groupHeader" class="ui-grid-cell-contents" ng-attr-title="{{row.entity.Ativo ? \'Inativar\' : \'Ativar\'}}">\
                //                        \<button type="button" title="Detalhes" ui-sref="ultimos-cadastros.crud({link: row.entity.IdMotoristaBase})" \ ng-class="{ \'btn btn-xs btn-warning\': true }">\
                //                            <i class="fa fa-file-text-o"></i>\
                //                        </button>\
                //                   </div>'
                //}                      
            ]
        };

        // DO NOT TOUCH!!
        $scope.$on('$stateChangeStart', function(_, toParams) {
            PersistentDataService.store('UltomosCadastrosController', vm, "Últimos cadastros", "UltomosCadastrosController", "ultimos-cadastros.index");
        });
    }
})();