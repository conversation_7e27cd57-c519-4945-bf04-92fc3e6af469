﻿<div>
    <form name="formRelacionamentoProtocoloXml" role="form" novalidate show-validation>
        <div class="modal-header">
            <button type="button" class="close" ng-click="vm.fechar()" aria-label="Close">
                <em class="fa fa-times"></em>
            </button>
            <h3 class="modal-title" id="modal-title">Anexar documento</h3>
        </div>
        <div class="modal-body bd-example-modal-xl">
            <div class="row">
                <div class="ibox-content" style="border: none !important;">
                    <div class="form-horizontal">
                        <div class="row">
                            <div class="col-md-6">
                                <!-- Se houver um arquivo, mostrar o nome -->
                                <div ng-if="!vm.inputchanged && vm.arquivo">
                                    <p>Arquivo atual: {{ vm.arquivo.filename }}</p>
                                    <button type="button" class="btn btn-warning" ng-click="vm.inputchanged = true; vm.arquivo = null">
                                        Alterar arquivo
                                    </button>
                                </div>
                                
                                <!-- Se for um novo arquivo ou o usuário quiser alterar -->
                                <input ng-if="vm.inputchanged" class="form-control" id="readfile" type="file" 
                                    ng-model="vm.arquivo" do-not-parse-if-oversize base-sixty-four-input 
                                    accept=".jpg, .jpeg, .png, .pdf, .doc, .docx" />
                            </div>

                            <div class="col-md-6">
                                <input class="form-control" type="text" placeholder="Descrição do arquivo" 
                                    ng-model="vm.descricaoArquivo" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" ng-click="vm.salvar()" class="btn btn-success">
                <i class="fa fa-check-circle"></i> Salvar
            </button>
            <button style="float: right;" type="button" ng-click="vm.fechar()" class="btn btn-primary">
                <i class="fa fa-close"></i> Fechar
            </button>
        </div>
    </form>
</div>