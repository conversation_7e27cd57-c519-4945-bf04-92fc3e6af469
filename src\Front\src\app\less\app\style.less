/*
 *
 *   INSPINIA - Responsive Admin Theme
 *   version 2.6
 *
*/

// Google Fonts
@import url("https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700");

// Variables, Mixins
@import "variables.less";
@import "mixins.less";
@import "variables_margins_padding";

// ATS Theme Elements
@import "typography.less";
@import "navigation.less";
@import "top_navigation.less";
@import "buttons.less";
@import "badgets_labels.less";
@import "elements.less";
@import "sidebar.less";
@import "base.less";
@import "pages.less";
@import "chat.less";
@import "metismenu.less";
@import "spinners.less";
@import "form-wizard.less";
@import "ui-select.less";
@import "login.less";

// Landing page styles
@import "landing.less";

// RTL Support
@import "rtl.less";

// For demo only - config box style
@import "theme-config.less";

// INSPINIA Skins
@import "skins.less";
@import "md-skin.less";

// Media query style
@import "media.less";
@import "media-queries.less";

// Custom style
// Your custom style to override base style
@import "custom.less";

// Clear layout on print mode
@media print {
  nav.navbar-static-side {
    display: none;
  }

  body { overflow: visible !important; }

  #page-wrapper {
    margin: 0;
  }
}













