<div class="form-horizontal" ng-show="vm.isAdmin">
    <hr />
    <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
        <div>
            <div class="form-group">
                <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
                    <div class="row">
                        <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                            <div class="form-group">
                                <label class="control-label col-xs-12 col-sm-2 col-md-4 col-lg-3">
                                    Tipo:
                                </label>
                                <div class="input-group col-xs-12 col-sm-10 col-md-8 col-lg-9">
                                    <ui-select 
                                        name="Tipo"
                                        ats-ui-select-validator
                                        ng-model="vm.tipo">
                                        <ui-select-match>
                                            <span>{{$select.selected.descricao}}</span>
                                        </ui-select-match>
                                        <ui-select-choices repeat="ex.id as ex in vm.tipoCombo.data | propsFilter: {descricao: $select.search}">
                                            <div ng-bind-html="ex.descricao | highlight: $select.search"></div>
                                        </ui-select-choices>
                                    </ui-select>
                                </div>
                            </div>
                        </div>
                        <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                            <div class="form-group">
                                <label class="col-xs-12 col-sm-3 col-md-3 col-lg-3 control-label">
                                    Nome:
                                </label>
                                <div class="input-group col-xs-12 col-sm-9 col-md-9 col-lg-9">
                                    <input type="text" ng-model="vm.nome" maxlength="100" validate-on="blur" name="Responsavel" class="form-control"
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                            <div class="form-group">
                                <label class="col-xs-12 col-sm-3 col-md-3 col-lg-3 control-label">
                                    Telefone 1:
                                </label>
                                <div class="input-group col-xs-12 col-sm-9 col-md-9 col-lg-9">
                                    <input type="text" ui-br-phone-number ng-model="vm.telefone" maxlength="15" validate-on="blur" name="Telefone" class="form-control"
                                    />
                                </div>
                            </div>
                        </div>
                        <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                            <div class="form-group">
                                <label class="col-xs-12 col-sm-3 col-md-3 col-lg-3 control-label">
                                    Telefone 2:
                                </label>
                                <div class="input-group col-xs-12 col-sm-9 col-md-9 col-lg-9">
                                    <input type="text" ui-br-phone-number ng-model="vm.telefone2" maxlength="15" validate-on="blur" name="Telefone2" class="form-control"
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                            <div class="form-group">
                                <label class="col-xs-12 col-sm-3 col-md-3 col-lg-3 control-label">
                                    Celular:
                                </label>
                                <div class="input-group col-xs-12 col-sm-9 col-md-9 col-lg-9">
                                    <input type="text" ui-br-phone-number ng-model="vm.celular" maxlength="15" validate-on="blur" name="Celular" class="form-control"
                                    />
                                </div>
                            </div>
                        </div>
                        <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                            <div class="form-group">
                                <label class="col-xs-12 col-sm-3 col-md-3 col-lg-3 control-label">
                                    E-mail:
                                </label>
                                <div class="input-group col-xs-12 col-sm-9 col-md-9 col-lg-9">
                                    <input type="email" maxlength="200" ng-model="vm.email" validate-on="blur" name="Email" class="form-control"/>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12 text-right">
                            <div class="form-header">
                                <button type="button" class="mr-5 btn-labeled btn btn-info" ng-click="vm.adicionarContato()">
                                    <i class="fa fa-plus"></i> Adicionar
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="table-responsive" style="margin-top: 5px;">
                    <table class="table table-bordered table-hover col-xs-12">
                        <thead>
                            <tr>
                                <th width="15%;">Tipo</th>
                                <th width="40%">Nome</th>
                                <th width="10%">Telefone 1</th>
                                <th width="10%">Telefone 2</th>
                                <th width="10%">Celular</th>
                                <th width="15%">E-mail</th>
                                <th width="10%">Ações</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr ng-repeat="contato in vm.posto.postoContatos">
                                <td>{{contato.tipo == 1 ? 'Interno' : 'Externo'}}</td>
                                <td>{{contato.nome}}</td>
                                <td><input type="text" ng-model="contato.telefone" readonly\
                                    class="no-borders" ui-br-phone-number /></td>
                                <td><input type="text" ng-model="contato.telefone2" readonly\
                                    class="no-borders" ui-br-phone-number /></td>
                                <td><input type="text" ng-model="contato.celular" readonly\
                                    class="no-borders" ui-br-phone-number /></td>
                                <td>{{contato.email}}</td>
                                <td class="text-center" style="vertical-align: middle">
                                    <button type="button" uib-tooltip="Remover" class="btn btn-xs btn-danger"
                                        ng-click="vm.removerContato(contato)">
                                        <i class="fa fa-trash-o"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>