(function () {
  'use strict';

  angular.module('bbcWeb').directive('chartDirective', chartDirective);


  function chartDirective() {
    return {
      restrict: 'E',
      scope: {
        chartType: '@',
        chartData: '=',
        chartOptions: '='
      },
      template: '<div><canvas></canvas></div>',
      link: function (scope, element) {

        var chart;
        function createChart() {
          if (scope.chart) {
            scope.chart.destroy(); 
          }
          var ctx = element.find("canvas")[0].getContext('2d');
           scope.chart = new Chart(ctx.canvas, {
           type: scope.chartType, 
           data: scope.chartData,
           options: scope.chartOptions
         });
        }
      
        scope.$on('$resize', function () {
          if (scope.chart) scope.chart.resize();
        });

        scope.$watchGroup(['chartData', 'chartOptions'], function (newValues) {
          if (newValues && scope.chartData && scope.chartData.datasets) {
            createChart();
          }
        });

        scope.$on('$destroy', function () {
          if (chart) {
            chart.destroy();
          }
        });
      }
    };
  }
})();