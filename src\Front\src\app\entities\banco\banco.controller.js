(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('BancoController', BancoController);

    BancoController.inject = ['BaseService', '$rootScope', 'toastr', '$scope', 'PersistentDataService', '$timeout', '$state', 'PERFIL_ADMINISTRADOR'];

    function BancoController(BaseService, $rootScope, toastr, $scope, PersistentDataService, $timeout, $state, PERFIL_ADMINISTRADOR) {
        var vm = this;
        vm.headerItems = [{
            name: 'Cadastros'
        }, {
            name: 'Banco'
        }];

        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.perfil == PERFIL_ADMINISTRADOR;
        };

        vm.gridOptions = {
            data: [],
            onRegisterApi: BaseService.dataGrid.defaultOnRegisterApi,
            dataSource: {
                url: "Banco/ConsultarGridBancoCombo"
            },
            columnDefs: [{
                name: '<PERSON><PERSON>õ<PERSON>',
                width: 80,
                cellTemplate: '<div ng-if="!row.groupHeader" class="ui-grid-cell-contents" title="TOOLTIP">\
                                    <button ng-disabled="row.entity.ativo === 0" tooltip-placement="right" uib-tooltip="Editar" type="button" ui-sref="banco.banco-crud({link: row.entity.id})"\
                                        ng-class="{ \'btn btn-xs btn-info\': true }">\
                                        <i class="fa fa-edit"></i>\
                                    </button>\
                                    <button type="button" tooltip-placement="right" uib-tooltip-html="row.entity.ativo===0 ? \'Ativar\' : \'Inativar\'" \
                                        ng-click="grid.appScope.vm.alterarStatus(row.entity.id)" \
                                        ng-class="row.entity.ativo === 1 ? \'btn btn-xs btn-success\' : \'btn btn-xs btn-danger\'"> \
                                        <i ng-class="row.entity.ativo === 1 ? \'fa fa-check\' : \'fa fa-times\'"></i>\
                                    </button>\
                                </div>'
            }, {
                name: 'Código',
                width: 80,
                primaryKey: true,
                field: 'id'
            }, {
                name: 'Nome',
                width: '*',
                field: 'nome'
            }]
        };

        vm.alterarStatus = function (id) {
            BaseService.post('Banco', 'AlterarStatus', {
                id: id
            }).then(function (response) {
                if (!response) {
                    toastr.error('Erro de comunicação com o servidor, tente novamente em alguns minutos.')
                    return
                }

                if (!response.success) {
                    toastr.error(response.message)
                    return
                }

                toastr.success(response.message)

                vm.gridOptions.dataSource.refresh();
            });
        };

        // Controle de aba!!
        $scope.$on('$stateChangeStart', function () {
            PersistentDataService.store('BancoController', vm, "Banco", "BancoCrudController", "banco.index");
        });

        var selfScope = PersistentDataService.get('BancoController');
        var filho = PersistentDataService.get('BancoCrudController');
        if (angular.isDefined(filho)) {
            $timeout(function () {
                $state.go('banco.banco-crud', {
                    link: filho.data.banco.Idbanco > 0 ? 'editar' : 'novo'
                });
            }, 50);
        } else if (angular.isDefined(selfScope)) {
            angular.extend(vm, selfScope.data);

            $timeout(function () {
                vm.gridOptions.dataSource.refresh();
            }, 450);
        }
    }
})();