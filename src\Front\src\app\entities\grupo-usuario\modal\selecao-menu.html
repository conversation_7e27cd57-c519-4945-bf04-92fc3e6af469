<style>
    .modal-dialog {
        width: 380px;
    }

    .bototoesBaixoFixo {
        width: 100%;
        position: fixed;
        bottom: -19px;
        left: 0;
        border-top-left-radius: 0;
        border-top-right-radius: 0;
    }
</style>
<div class="modal-header">
    <button type="button" class="close" ng-click="vm.close(true)" aria-label="Close">
        <em class="fa fa-times"></em>
    </button>
    <h3 class="modal-title" id="modal-title">Menus do módulo {{vm.modulo}}</h3>
</div>
<div class="modal-body" style="min-height: 310px; max-height: 310px; display: block; padding-top: 0; padding-left: 26px; overflow-y: scroll;">
    <div ng-show="!vm.isLoading" class="row">
        <div class="ibox" style="max-height: 310px">
            <div ng-repeat="pai in vm.agrupadosPorPai" class="col-xs-12">
                <h4>{{pai.DescricaoPai}}</h4>
                <div ng-repeat="filho in pai.menus" class="col-xs-12" style="height: 25px;">
                    <div class="col-xs-2">
                        <button type="button" ng-show="filho.selecionado" tooltip-placement="right" uib-tooltip="Desmarcar" ng-click="vm.desmarcar(filho)"
                            class="btn btn-success btn-xs">
                            <i ng-class="'fa fa-check'"></i>
                        </button>
                        <button style="width: 24px;" type="button" ng-show="!filho.selecionado" tooltip-placement="right" uib-tooltip="Marcar" ng-click="vm.marcar(filho)"
                            class="btn btn-danger btn-xs">
                            <i ng-class="'fa fa-times'"></i>
                        </button>
                    </div>
                    <div class="col-xs-10">{{filho.menu}}</div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="modal-footer" style="display: flow-root;">
    <div class="text-right">
        <button ng-if="vm.temAlgumMenuSelecionadoIgual(true) || vm.menusEstao(true)" type="button" tooltip-placement="right" ng-click="vm.setarTodos(false)"
            class="btn btn-danger btn-labeled btn-sm">
            <i class="fa fa-list-ul"></i>
            Desmarcar todos
        </button>
        <button ng-if="vm.menusEstao(false) || vm.menusEstaoIndefinidos()" type="button" tooltip-placement="right" ng-click="vm.setarTodos(true)"
            class="btn btn-success btn-labeled btn-sm">
            <i class="fa fa-list-ul"></i>
            Marcar todos
        </button>
        <button type="button" ng-click="vm.close(false)" class="btn btn-labeled btn-default btn-sm">
            <span class="btn-label">
                <i class="fa fa-check"></i>
            </span>Aplicar
        </button>
    </div>
</div>