(function () {
    'use strict';

    angular.module('bbcWeb').controller('ConfiguracaoAtualizacaoAutomaticaPrecoCombustivelController', ConfiguracaoAtualizacaoAutomaticaPrecoCombustivelController);

    ConfiguracaoAtualizacaoAutomaticaPrecoCombustivelController.$inject = ['toastr', '$rootScope', 'BaseService', '$state', '$stateParams', '$window', 'PERFIL_ADMINISTRADOR', '$scope', '$timeout', 'PersistentDataService', 'DefaultsService', '$uibModal'];

    function ConfiguracaoAtualizacaoAutomaticaPrecoCombustivelController(
        toastr, $rootScope,
        BaseService, $state,
        $stateParams, $window,
        PERFIL_ADMINISTRADOR,
        $scope, $timeout,
        PersistentDataService
    ) {
        var vm = this;
        vm.configuracao = {};

        vm.headerItems = [{ name: 'Cadastro<PERSON>' }, { name: 'Parâmetros gerais', link: 'parametros.index' }];

        vm.loadEdit = function () {
            BaseService.get('Parametros', 'ConfiguracaoAtualizacaoAutomaticaPrecoCombustivel', {})
                .then(function (response) {
                    if (!response) {
                        toastr.error('Erro de comunicação com o servidor, tente novamente em alguns minutos.');
                        return;
                    }

                    if (!response.success) {
                        toastr.error(response.message);
                        return;
                    }

                    vm.configuracao = response.data;
                });
        };

        vm.save = function (form) {
            if (!form.$valid) {
                toastr.error('Todos os campos obrigatórios devem ser preenchidos corretamente com seus respectivos valores.');
                return;
            }

            if (vm.saving) return;

            var request = {}
            request.valorMaior = vm.configuracao.valorMaior
            request.valorMenor = vm.configuracao.valorMenor
            request.valorMaiorHabilitado = vm.configuracao.valorMaiorHabilitado
            request.valorMenorHabilitado = vm.configuracao.valorMenorHabilitado

            vm.saving = true;

            BaseService.post('Parametros', 'SalvarConfiguracaoAtualizacaoAutomaticaPrecoCombustivel', request)
                .then(function (response) {
                    vm.saving = false;
                    if (!response) {
                        toastr.error('Erro de comunicação com o servidor, tente novamente em alguns minutos.');
                        return;
                    }
                    if (!response.success) {
                        toastr.error(response.message);
                        return;
                    }
                    toastr.success(response.message);
                    $state.go('parametros.index');
                })
        };

        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.administrador === true;
        };

        vm.onClickVoltar = function (wizard) {
            var ativoIndex = wizard.getActivePosition();
            if (ativoIndex === 1) $state.go('parametros.index');
            wizard.go(ativoIndex - 1);
        };

        $scope.$on('$stateChangeStart', function (arg1, toState) {
            if (toState.name === 'parametros.index')
                PersistentDataService.remove('ConfiguracaoAtualizacaoAutomaticaPrecoCombustivelController');
            else
                PersistentDataService.store('ConfiguracaoAtualizacaoAutomaticaPrecoCombustivelController', vm, "Administração - Parâmetros aprovação automática", null, "parametros.ConfiguracaoAtualizacaoAutomaticaPrecoCombustivelController", vm.configuracaoSla.id);
        });

        var selfScope = PersistentDataService.get('ConfiguracaoAtualizacaoAutomaticaPrecoCombustivelController');

        if (angular.isDefined(selfScope)) angular.extend(vm, selfScope.data);
        else vm.loadEdit();

        $timeout(function () {
            PersistentDataService.remove('ConfiguracaoAtualizacaoAutomaticaPrecoCombustivelController');
        }, 15);

    }
})();
