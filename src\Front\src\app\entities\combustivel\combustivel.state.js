(function () {
    'use strict';

    angular.module('bbcWeb.combustivel.state', []).config(routeConfig);

    function routeConfig($urlRouterProvider, $stateProvider) {
        $stateProvider.state('combustivel', {
            abstract: true,
            url: "/combustivel",
            templateUrl: "app/layout/content.html"
        }).state('combustivel.index', {
            url: '/index',
            templateUrl: 'app/entities/combustivel/combustivel.html'
        }).state('combustivel.combustivel-crud', {
            url: '/:link',
            templateUrl: 'app/entities/combustivel/combustivel-crud.html'
        });
    }
})();