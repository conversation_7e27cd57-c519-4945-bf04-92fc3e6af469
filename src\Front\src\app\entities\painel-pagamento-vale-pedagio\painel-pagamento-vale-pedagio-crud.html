<div id="PainelPagamentoValePedagioCrudController" ng-controller="PainelPagamentoValePedagioCrudController as vm">
    <form-header ng-submit="return" items="vm.headerItems" head="'Pagamento de vale pedágio'"></form-header>
    <div class="wrapper wrapper-content animated fadeIn mb-0 pb-0">
        <div class="row">
            <div class="col-lg-12">
                <div class="ibox animated fadeIn">
                    <div class="ibox-title">
                        <h5>Pagamento de vale pedágio</h5>
                        <div ibox-tools></div>
                    </div>
                    <div class="ibox-content">
                        <form role="form">
                            <div form-wizard steps="2">
                                <div class="form-wizard">
                                    <ol class="row">
                                        <li ng-click="wizard.go(1); vm.gridOptions.dataSource.refresh();" class="fixLRpg col-sm-6" ng-class="{'active': wizard.active(1)}">
                                            <h4>Transações</h4>
                                        </li>
                                        <li ng-click="wizard.go(2); vm.gridPagamentoValePedagioHistoricoOptions.dataSource.refresh();" class="fixLRpg col-sm-6" ng-class="{'active': wizard.active(2)}">
                                            <h4>Histórico Pedágio</h4>
                                        </li>
                                    </ol>
                                    <br/>
                                </div>
                                <div id="activateTab1" ng-show="wizard.active(1)">
                                    <div ng-include="'app/entities/painel-pagamento-vale-pedagio/abas/principal.html'" class="form-horizontal"> </div>
                                </div>
                                <div id="activateTab2" ng-show="wizard.active(2)">
                                    <div ng-include="'app/entities/painel-pagamento-vale-pedagio/abas/historico-pedagio.html'" class="form-horizontal"> </div>
                                </div>
                            </div>
                            <div class="row clearfix"> </div>
                            <hr dark="true"></hr>
                            <br/>
                            <div class="row">
                                <div class="form-group">
                                    <div class="col-md-12 col-lg-12 text-right">
                                        <button type="button" ng-click="vm.onClickVoltar(wizard)" class="btn btn-labeled btn-default">
                                            <span class="btn-label">
                                                <i class="fa fa-arrow-circle-left"></i>
                                            </span>
                                            Voltar
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>