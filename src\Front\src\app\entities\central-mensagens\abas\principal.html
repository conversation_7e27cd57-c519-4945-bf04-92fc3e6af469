﻿<div class="form-horizontal">
    <hr />
    <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label">Código:</label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <input type="text" ng-model="vm.mensagem.id" class="form-control" disabled
                            value="{{vm.isNew()}}" />
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-5 col-lg-3 control-label">
                        <span class="text-danger mr-5">*</span>Código da aplicação:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-7 col-lg-9">
                        <ui-select name="codigoAplicacao" ng-model="vm.mensagem.codigoAplicacao" ats-ui-select-validator
                            required-message="'Código da aplicação é obrigatório'" ng-disabled="vm.edicao"
                            ng-change="vm.changeCodigoAplicacao(vm.mensagem.codigoAplicacao)" required>
                            <ui-select-match>
                                <span>{{$select.selected.descricao}}</span>
                            </ui-select-match>
                            <ui-select-choices
                                repeat="ex.id as ex in vm.comboCodigoAplicacao.data | propsFilter: {descricao: $select.search}">
                                <div ng-bind-html="ex.descricao | highlight: $select.search"></div>
                            </ui-select-choices>
                        </ui-select>
                    </div>
                </div>
            </div>
        </div>
        <div class="row" id="exibirConfigMensagemDock" ng-if="vm.mensagem.codigoAplicacao == 2">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label">
                        <span class="text-danger mr-5">*</span>Mensagem Origem:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <textarea type="text" required required-message="'Mensagem origem é obrigatória'"
                            ng-model="vm.mensagem.textoMensagemOriginal" maxlength="5000"
                            ng-disabled="vm.edicao"
                            name="Descricao" class="form-control"></textarea>
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label">
                        <span class="text-danger mr-5">*</span>Mensagem tratada:
                    </label>
                    <div class="input-group col-xs-12 col-md-8">
                        <toggle-switch on-label="Sim" off-label="Não" ng-model="vm.mensagem.mensagemTratada" class="switch"></toggle-switch>
                    </div>
                </div>
            </div>
        </div>
        <div class="row" id="exibirConfigMensagemNaoDock" ng-if="vm.mensagem.codigoAplicacao != 2">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label">
                        <span class="text-danger mr-5">*</span>Código da mensagem:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <ui-select name="Tipo" ng-model="vm.mensagem.codigoMensagem" ats-ui-select-validator
                            required-message="'Código da mensagem é obrigatório'" required>
                            <ui-select-match>
                                <span>{{$select.selected.descricao}}</span>
                            </ui-select-match>
                            <ui-select-choices
                                repeat="ex.id as ex in vm.comboCodigoMensagem | propsFilter: {descricao: $select.search}">
                                <div ng-bind-html="ex.descricao | highlight: $select.search"></div>
                            </ui-select-choices>
                        </ui-select>
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label">
                        <span class="text-danger mr-5">*</span>Descrição da mensagem:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <textarea type="text" required required-message="'Descrição da mensagem é obrigatória'"
                            ng-model="vm.mensagem.descricaoMensagem" maxlength="1000"
                            name="Descricao" class="form-control"></textarea>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label">
                        <span class="text-danger mr-5">*</span>Tipo da mensagem:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <ui-select ng-disabled="vm.desabilitaCampoTipoMensagem || vm.mensagem.codigoAplicacao == 2" name="Tipo" ng-model="vm.mensagem.tipoMensagem" ats-ui-select-validator
                            required-message="'Tipo da mensagem é obrigatório'" required
                            ng-change="vm.changeTipoMensagem(vm.mensagem.tipoMensagem)">
                            <ui-select-match>
                                <span>{{$select.selected.descricao}}</span>
                            </ui-select-match>
                            <ui-select-choices
                                repeat="ex.id as ex in vm.comboTipoMensagem.data | propsFilter: {descricao: $select.search}">
                                <div ng-bind-html="ex.descricao | highlight: $select.search"></div>
                            </ui-select-choices>
                        </ui-select>
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label id="textMensagem" class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label">
                        <span class="text-danger mr-5">*</span>
                        {{ vm.mensagem.codigoAplicacao == 2 ? 'Mensagem de retorno do cliente' : 'Mensagem' }}:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">  
                        <textarea type="text" required required-message="'Mensagem é obrigatória'"
                            ng-model="vm.mensagem.textoMensagem" maxlength="1000"
                            name="TextoMensagem" class="form-control" ></textarea>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row" id="exibirConfigMensagemImagemNaoDock" ng-if="vm.mensagem.codigoAplicacao != 2">
            <br>
            <hr-label dark="true" title="'Período de vigência da mensagem'"></hr-label>
            <br> 
            <br>
            <div ng-if="vm.loaded" class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label">
                        <span class="text-danger mr-5">*</span>Período mensagem:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <input date-range-picker class="form-control date-picker" type="text" name="periodo"
                                            ng-model="vm.periodoMensagem" options="vm.dateOptions"
                                            style="background-color: white !important;"
                                            readonly required required-message="'Período mensagem é obrigatório'" />
                    </div>
                </div>
            </div>
        </div>
        <div class="row" id="exibirImagemTitle" style="display: none;">
            <hr-label dark="true" title="'Imagem da mensagem'"></hr-label>
            <br>
        </div>
        <div class="row" id="exibirImagem" style="display: none;">
            <div class="col-xs-12 col-md-12" style="text-align: center;">
                <div class="row">
                    <center>
                        <img ng-show="!vm.imagemUpload.filename" style="height: 150px;width: 150px;"
                            ng-src="" />
                        <img ng-show="vm.imagemUpload.filename" style="height: 150px;width: 150px;"
                            ng-src="data:{{ vm.imagemUpload.filetype }};base64,{{ vm.imagemUpload.base64 }}" />
                        <div style="margin-bottom: 10px;">
                            <label style="margin-top: 2%;" tooltip-placement="left"
                                uib-tooltip="Apenas arquivos nos formatos JPG, JPEG,PNG e GIF são aceitos!"
                                title="{{vm.imagemSelecionadaInput == null ? 'Selecione (Máximo 512Kb)' : vm.imagemSelecionadaInput }}"
                                for="Foto" class="sllipse btn btn-warning btn-sm" style="color: #FFA500;">Selecione
                                <input id="Foto" name="foto" type="file" ng-model="vm.imagemUpload" do-not-parse-if-oversize
                                    base-sixty-four-input onload="vm.onLoadImageUploader" ng-show="false"
                                    accept="image/png, image/jpeg, image/gif">
                            </label>
                            <button type="button" style="margin-top: 2%;" tooltip-placement="right"
                                uib-tooltip="Remover imagem"
                                ng-click="vm.imagemUpload = undefined; vm.mensagem.imagemMensagem = undefined; vm.mensagem.imagemPerfilB64 = undefined; vm.imagemSelecionadaInput = undefined;"
                                class="btn btn-default btn-sm">
                                <i class="fa fa-close"></i>
                            </button>
                        </div>
                    </center>
                </div>
            </div>
        </div>
    </div>
</div>