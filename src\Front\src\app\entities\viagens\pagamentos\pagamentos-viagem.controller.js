(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('PagamentosViagemController', PagamentosViagemController);

        PagamentosViagemController.inject = ['BaseService', '$rootScope', 'toastr','$stateParams', '$scope', 'PersistentDataService', '$timeout', '$state', 'PERFIL_ADMINISTRADOR', '$uibModal', 'oitozero.ngSweetAlert'];

    function PagamentosViagemController(BaseService, $rootScope, toastr, $scope, $stateParams, PersistentDataService, $timeout, $state, PERFIL_ADMINISTRADOR, $uibModal, SweetAlert) {
        var vm = this;
        vm.headerItems = [{
            name: 'Movimentações'
        }, {
            name: 'Painel de pagamento viagem', 
            link: 'viagens.index'
        }, {
             name: $stateParams.link == 'editar' ? 'Pagamentos' : 'Pagamentos'
        }];

        vm.listaPagamentoHistoricoOptions = [];

        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.perfil == PERFIL_ADMINISTRADOR;
        };

        vm.onClickVoltar = function (wizard) {
            var ativoIndex = wizard.getActivePosition();
            if (ativoIndex == 1)
                $state.go('viagens.index');

            wizard.go(ativoIndex - 1);
        };

        function init(){
            carregaHistoricoPagamentos();
        }

        vm.codViagem = $stateParams.link;

        vm.gridOptions = {
            data: [],
            onRegisterApi: BaseService.dataGrid.defaultOnRegisterApi,
            dataSource: {
                url: "Viagem/ConsultarPagamentosViagem",
                params: function () {
                    return {
                        ViagemId: $stateParams.link
                    }
                }
            },
            columnDefs: [
                {
                    name: 'Ações',
                    width: '100',
                    cellTemplate: '<div class="ui-grid-cell-contents" align="center" title="TOOLTIP">\
                        <button tooltip-placement="right" uib-tooltip="Visualizar detalhes" type="button" \
                                ui-sref="viagens.transacoes-pagamento({viagem: grid.appScope.vm.codViagem , pagamento:row.entity.id})"\
                                ng-class="{ \'btn btn-xs btn-info\': true }">\
                                <i class="fa fa-eye"></i>\
                        </button>\
                    </div>'
                },
                {
                    name: 'Codigo',
                    displayName: 'Código',
                    width: '*',
                    minWidth: 120,
                    field: 'id',
                    type: 'number',
                    enableFiltering: true
                },
                {
                    name: 'PagamentoExternoId',
                    displayName: 'Pagamento Externo Id',
                    width: '*',
                    minWidth: 200,
                    field: 'pagamentoExternoId',
                    type: 'number',
                    enableFiltering: true
                },
                {
                    name: 'Status',
                    displayName: 'Status',
                    width: '*',
                    minWidth: 120,
                    field: 'status',
                    serverField: 'Status',
                    enableGrouping: false,
                    enableFiltering: true,
                    enum: true,
                    enumTipo: 'EStatusPagamentoEvento'
                },
                {
                    name: 'FormaPagamento',
                    displayName: 'Forma Pagamento',
                    width: '*',
                    minWidth: 150,
                    field: 'formaPagamento',
                    serverField: 'FormaPagamento',
                    enableGrouping: false,
                    enableFiltering: true,
                    enum: true,
                    enumTipo: 'EFormaPagamentoEvento'
                },
                {
                    name: 'Tipo',
                    displayName: 'Tipo de Pagamento',
                    width: '*',
                    minWidth: 150,
                    field: 'tipo',
                    serverField: 'Tipo',
                    enableGrouping: false,
                    enableFiltering: true,
                    enum: true,
                    enumTipo: 'ETipoEvento'
                },
                {
                    name: 'ValorParcela',
                    displayName: 'Valor Parcela',
                    width: '*',
                    minWidth: 120,
                    field: 'valorParcela',
                    enableFiltering: true
                },
                {
                    name: 'ValorTransferenciaMotorista',
                    displayName: 'Valor Transferência Motorista',
                    width: '*',
                    minWidth: 200,
                    field: 'valorTransferenciaMotorista',
                    enableFiltering: true
                },
                {
                    name: 'ValorTarifaBbc',
                    displayName: 'Valor Tarifa BBC',
                    width: '*',
                    minWidth: 120,
                    field: 'valorTarifaBbc',
                    enableFiltering: true
                },
                {
                    name: 'ValorTarifaPix',
                    displayName: 'Valor Tarifa Pix',
                    width: '*',
                    minWidth: 120,
                    field: 'valorTarifaPix',
                    enableFiltering: true
                },
                {
                    name: 'Mensagem',
                    displayName: 'Mensagem',
                    width: '*',
                    minWidth: 210,
                    field: 'mensagem',
                    type: 'text',
                    enableFiltering: true
                },
                {
                    name: 'ContadorReenvio',
                    displayName: 'Contador Reenvio',
                    width: '*',
                    minWidth: 150,
                    field: 'contadorReenvio',
                    type: 'number',
                    enableFiltering: true
                },
                {
                    name: 'ContadorVerificacaoStatusPix',
                    displayName: 'Contador Verificação Status Pix',
                    width: '*',
                    minWidth: 200,
                    field: 'contadorVerificacaoStatusPix',
                    type: 'number',
                    enableFiltering: true
                },
                {
                    name: 'DataCadastro',
                    displayName: 'Data Cadastro',
                    minWidth: 150,
                    field: 'dataCadastro',
                    type: 'date',
                    enableFiltering: false
                },
                {
                    name: 'DataAlteracao',
                    displayName: 'Data Alteração',
                    width: '*',
                    minWidth: 150,
                    field: 'dataAlteracao',
                    type: 'date',
                    enableFiltering: false
                },
                {
                    name: 'DataBaixa',
                    displayName: 'Data Baixa',
                    minWidth: 150,
                    field: 'dataBaixa',
                    type: 'date',
                    enableFiltering: false
                },
                {
                    name: 'Data Cancelamento',
                    displayName: 'Data Cancelamento',
                    minWidth: 150,
                    field: 'dataCancelamento',
                    type: 'date',
                    enableFiltering: false
                },
            ]
        };

     
        function carregaHistoricoPagamentos(codViagem) {
            BaseService.get("Viagem", "ConsultarListaPagamentosHistoricoViagem", {
                ViagemId: codViagem
            }).then(function (response) {
                if (response.success) {
                    vm.listaPagamentoHistoricoOptions = response.data;
                }
            });
        };

        vm.gridPagamentoHistoricoOptions = {
            data: [],
            onRegisterApi: BaseService.dataGrid.getGridApiRegistration("gridPagamentoHistoricoOptions"),
            dataSource: {
                url: "Viagem/ConsultarPagamentosHistoricoViagem",
                params: function () {
                    return {
                        ViagemId: $stateParams.link
                    }  
                }
            },
            columnDefs: [
                {
                    name: 'Ações',
                    width: '100',
                    cellTemplate: '<div class="ui-grid-cell-contents" align="center" title="TOOLTIP">\
                        <button tooltip-placement="right" uib-tooltip="Visualizar detalhes" type="button" \
                                ui-sref="viagens.transacoes-pagamento-historico({viagem: grid.appScope.vm.codViagem , pagamentoHistorico:row.entity.id , pagamentoEvento: row.entity.pagamentoEventoId})"\
                                ng-class="{ \'btn btn-xs btn-info\': true }">\
                                <i class="fa fa-eye"></i>\
                        </button>\
                    </div>'
                },
                {
                    name: 'Codigo',
                    displayName: 'Código',
                    width: '*',
                    minWidth: 120,
                    field: 'id',
                    type: 'number',
                    enableFiltering: true
                },
                {
                    name: 'Pagamento Evento Id',
                    displayName: 'Pagamento Evento Id',
                    width: '*',
                    minWidth: 120,
                    field: 'pagamentoEventoId',
                    type: 'number',
                    hide: true,
                    enableFiltering: true
                },
                {
                    name: 'PagamentoExternoId',
                    displayName: 'Pagamento Externo Id',
                    width: '*',
                    minWidth: 200,
                    field: 'pagamentoExternoId',
                    type: 'number',
                    enableFiltering: true
                },
                {
                    name: 'Status',
                    displayName: 'Status',
                    width: '*',
                    minWidth: 120,
                    field: 'status',
                    serverField: 'Status',
                    enableGrouping: false,
                    enableFiltering: true,
                    enum: true,
                    enumTipo: 'EStatusPagamentoEvento',
                },
                {
                    name: 'FormaPagamento',
                    displayName: 'Forma Pagamento',
                    width: '*',
                    minWidth: 150,
                    field: 'formaPagamento',
                    serverField: 'FormaPagamento',
                    enableGrouping: false,
                    enableFiltering: true,
                    enum: true,
                    enumTipo: 'EFormaPagamentoEvento'
                },
                {
                    name: 'Tipo',
                    displayName: 'Tipo de Pagamento',
                    width: '*',
                    minWidth: 150,
                    field: 'tipo',
                    serverField: 'Tipo',
                    enableGrouping: false,
                    enableFiltering: true,
                    enum: true,
                    enumTipo: 'ETipoEvento'
                },
                {
                    name: 'ValorParcela',
                    displayName: 'Valor Parcela',
                    width: '*',
                    minWidth: 120,
                    field: 'valorParcela',
                    enableFiltering: true
                },
                {
                    name: 'DataCadastro',
                    displayName: 'Data Cadastro',
                    minWidth: 150,
                    field: 'dataCadastro',
                    type: 'date',
                    enableFiltering: false
                },
                {
                    name: 'DataAlteracao',
                    displayName: 'Data Alteração',
                    width: '*',
                    minWidth: 150,
                    field: 'dataAlteracao',
                    type: 'date',
                    enableFiltering: false
                },
                {
                    name: 'DataBaixa',
                    displayName: 'Data Baixa',
                    minWidth: 150,
                    field: 'dataBaixa',
                    type: 'date',
                    enableFiltering: false
                },
                {
                    name: 'Data Cancelamento',
                    displayName: 'Data Cancelamento',
                    minWidth: 150,
                    field: 'dataCancelamento',
                    type: 'date',
                    enableFiltering: false
                },
                {
                    name: 'ValorTransferenciaMotorista',
                    displayName: 'Valor Transferência Motorista',
                    width: '*',
                    minWidth: 200,
                    field: 'valorTransferenciaMotorista',
                    enableFiltering: true
                },
                {
                    name: 'ValorTarifaBbc',
                    displayName: 'Valor Tarifa BBC',
                    width: '*',
                    minWidth: 120,
                    field: 'valorTarifaBbc',
                    enableFiltering: true
                },
                {
                    name: 'ValorTarifaPix',
                    displayName: 'Valor Tarifa Pix',
                    width: '*',
                    minWidth: 120,
                    field: 'valorTarifaPix',
                    enableFiltering: true
                },
                {
                    name: 'Mensagem',
                    displayName: 'Mensagem',
                    width: '*',
                    minWidth: 210,
                    field: 'mensagem',
                    type: 'text',
                    enableFiltering: true
                },
                {
                    name: 'ContadorReenvio',
                    displayName: 'Contador Reenvio',
                    width: '*',
                    minWidth: 150,
                    field: 'contadorReenvio',
                    type: 'number',
                    enableFiltering: true
                },
                {
                    name: 'ContadorVerificacaoStatusPix',
                    displayName: 'Contador Verificação Status Pix',
                    width: '*',
                    minWidth: 200,
                    field: 'contadorVerificacaoStatusPix',
                    type: 'number',
                    enableFiltering: true
                }
            ]
        };

        init();

    }
})();