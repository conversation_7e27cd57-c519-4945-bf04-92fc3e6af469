<div ng-controller="EmpresaController as vm">
    <form-header items="vm.headerItems" head="'Empresa'" state="empresa">
    </form-header>
    <div class="wrapper-content animated fadeIn filter-position overflow-hidden">
        <div class="row">
            <div class="col-lg-12">
                <div class="ibox float-e-margins">
                    <div style="height: 38px;" class="ibox-title">
                        <h5>Empresa</h5>
                        <div ibox-tools></div>
                    </div>
                    <div class="ibox-content">
                        <div class="pull-right">
                            <button tooltip-placement="top" ng-click="vm.atualizaTela();"
                                uib-tooltip="Atualizar" type='button' class="btn btn-labeled btn-default">
                                <i class="fa fa-refresh"></i>
                                <span class="pl-5">Atualizar</span>
                            </button>
                            <button ng-if="vm.isAdministrador()" tooltip-placement="top"
                                ui-sref="empresa.crud({link: 'novo'})" uib-tooltip="Cadastrar " type='button'
                                class="btn btn-labeled btn-primary ">
                                <span class="btn-label text-right">
                                    <i class="fa fa-plus"></i>
                                </span>
                                <span class="pl-5">Novo</span>
                            </button>
                        </div>
                        <div class="row"></div>
                        <hr>
                        <div ui-grid="vm.gridOptions" ng-style="{height: vm.gridOptions.getGridHeight()}" class="grid" style="width: 100%;"
                            ui-grid-pinning ui-grid-save-state ui-grid-pagination ui-grid-auto-resize
                            ui-grid-resize-columns ui-grid-grouping>
                        </div>

                        <div class="row">
                            <div class="row">
                                <div class="col-xs-12 col-md-12">
                                    <div class="pull-right mt-15">
                                        <!-- <button type="button" class="btn btn-danger" ng-disabled="vm.atualizando"
                                            ng-click="vm.reprovaProtocolo();">
                                            <span class="btn-label">
                                                <i class="fa fa-times-circle"></i>
                                            </span>
                                            Reprovar marcados
                                        </button> -->
                                        <!-- <button type="button" class="btn btn-success" ng-disabled="vm.atualizando"
                                            ng-click="vm.aprovaProtocolo();">
                                            <span class="btn-label">
                                                <i class="fa fa-check-circle"></i>
                                            </span>
                                            Aprovar marcados
                                        </button> -->
                                        <button type="button" ng-disabled="vm.desabilitarBtnRelatorio"
                                            class="btn btn-labeled btn-primary" ng-click="vm.abrirModalRelatorio('gridOptions', vm)"">
                                            <i class="fa fa-file-pdf-o"></i> Exportar Relatório
                                        </button>
                                    </div>

                                    <div id="exportable-xls">
                                        <table style="font-family:Arial, Helvetica, sans-serif" ng-show="false" id="exportable"
                                               class="table table-bordered" width="100%">
                                            <thead>
                                            <tr>
                                                <th style="text-align: left"
                                                    ng-repeat="option in vm.modalRelatorioOptions"
                                                    ng-if="option.enabled && option.field">
                                                    {{option.name}}
                                                </th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            <tr style="text-align: left"
                                                ng-repeat="item in vm.dadosRelatorio">
                                                <td ng-repeat="option in vm.modalRelatorioOptions"
                                                    ng-if="option.enabled && option.field">
                                                    {{option.pipe ? (option.pipe(item[option.field])) : (item[option.field])}}
                                                </td>
                                            </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>