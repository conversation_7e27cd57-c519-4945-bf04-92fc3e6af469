(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('BloqueioSpdController', BloqueioSpdController);

    BloqueioSpdController.inject = ['BaseService', '$rootScope', 'toastr', '$scope', 'PersistentDataService', '$timeout', '$state', 'PERFIL_ADMINISTRADOR'];

    function BloqueioSpdController(BaseService, $rootScope, toastr, $scope, PersistentDataService, $timeout, $state, PERFIL_ADMINISTRADOR) {
        var vm = this;
        vm.headerItems = [{
            name: '<PERSON><PERSON><PERSON><PERSON>'
        }, {
            name: 'Liberação de bloqueios SPD'
        }];

        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.perfil == PERFIL_ADMINISTRADOR;
        };

        vm.gridOptions = {
            data: [],
            onRegisterApi: BaseService.dataGrid.defaultOnRegisterApi,
            dataSource: {
                url: "BloqueioSpd/ConsultarGridBloqueioSpd"
            },
            columnDefs: [{
                name: 'Ações',
                width: 80,
                cellTemplate: '<div ng-if="!row.groupHeader" class="ui-grid-cell-contents" title="TOOLTIP">\
                                    <button ng-disabled="row.entity.ativo===0" tooltip-placement="right" uib-tooltip="Editar" type="button" ui-sref="bloqueio-spd.bloqueio-spd-crud({link: row.entity.id})"\
                                        ng-class="{ \'btn btn-xs btn-info\': true }">\
                                        <i class="fa fa-edit"></i>\
                                    </button>\
                                    <button ng-if = "row.entity.ativo===0" type="button" tooltip-placement="right" uib-tooltip="Ativar" ng-click="grid.appScope.vm.alterarStatus(row.entity.id, row.entity.ativo)" ng-class="\'btn btn-xs btn-danger\'">\ <i ng-class="\'fa fa-times\'"></i>\
                                    </button>\
                                    <button ng-if = "row.entity.ativo===1" type="button" tooltip-placement="right" uib-tooltip="Inativar" ng-click="grid.appScope.vm.alterarStatus(row.entity.id, row.entity.ativo)" ng-class="\'btn btn-xs btn-success\'">\ <i ng-class="\'fa fa-check\'"></i>\
                                    </button>\
                                </div>'
            }, {
                name: 'Código',
                width: 80,
                type: 'number',
                primaryKey: true,
                field: 'id'
            }, {
                name: 'Código SPD',
                displayName: 'Código SPD',
                minWidth: 120,
                type: 'number',
                field: 'codigo'
            }, {
                name: 'Descrição',
                displayName: 'Descrição',
                width: '*',
                minWidth: 250,
                field: 'descricao'
            }]
        };

        vm.alterarStatus = function (id, ativo) {
            BaseService.post('BloqueioSpd', "AlterarStatus", {
                id: id
            }).then(function (response) {
                if (!response) {
                    toastr.error('Erro de comunicação com o servidor, tente novamente em alguns minutos.')
                    return
                }

                if (!response.success) {
                    toastr.error(response.message)
                    return
                }

                toastr.success(response.message)

                vm.gridOptions.dataSource.refresh();
            });
        };

        // Controle de aba!!
        $scope.$on('$stateChangeStart', function () {
            PersistentDataService.store('BloqueioSpdController', vm, "Liberação de bloqueios SPD", "BloqueioSpdCrudController", "bloqueio-spd.index");
        });

        var selfScope = PersistentDataService.get('BloqueioSpdController');
        var filho = PersistentDataService.get('BloqueioSpdCrudController');
        if (angular.isDefined(filho)) {
            $timeout(function () {
                $state.go('bloqueio-spd.bloqueio-spd-crud', {
                    link: filho.data.vm.liberacaoBloqueioSpd.Id > 0 ? 'editar' : 'novo'
                });
            }, 50);
        } else if (angular.isDefined(selfScope)) {
            angular.extend(vm, selfScope.data);

            $timeout(function () {
                vm.gridOptions.dataSource.refresh();
            }, 450);
        }
    }
})();