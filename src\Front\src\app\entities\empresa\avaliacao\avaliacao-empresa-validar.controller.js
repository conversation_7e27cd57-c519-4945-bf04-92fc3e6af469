(function() {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('AvaliacaoEmpresaValidarController', AvaliacaoEmpresaValidarController);

    AvaliacaoEmpresaValidarController.inject = [
        '$stateParams',
        '$uibModal',
        '$scope',
        '$rootScope',
        '$state',
        'BaseService',
        'HeaderItemsService',
        'toastr'
    ];

    function AvaliacaoEmpresaValidarController($stateParams, $uibModal, $scope, $rootScope, $state, BaseService, AvaliacaoEmpresaService, toastr) {
        var vm = this;

        vm.empresa = {};

        const EMPRESA_CONTROLLER = "Empresa";
        const STRING_VAZIA = "";

        vm.headerItems = AvaliacaoEmpresaService.getHeader();
        vm.statusDropdown = AvaliacaoEmpresaService.getStatusToDropdown();
        vm.statusAtual = undefined;
        vm.saving = false;

        vm.load = function() {
            consultarDadosEmpresa();
        }

        vm.clickSalvar = function() {
             if (!validarParaSalvar())
                 return;

             if (parseInt(vm.empresa.statusCadastro) === 0) {
                realizarAtualizacaoEmpresa();
             } else {
                abrirModalUsuario();
             }
        }

        function validarParaSalvar() {
            if (parseInt(vm.empresa.statusCadastro) === parseInt(vm.statusAtual)) {
                toastr.warning("A empresa já se encontra no Status selecionado.");
                return false;
            }

            if (parseInt(vm.empresa.statusCadastro) === 2) {
                toastr.warning("Selecione um status que seja diferente de Pendente de Validação.");
                return false;
            }

            if (angular.isUndefined(vm.empresa.parecerInterno) || vm.empresa.parecerInterno === null || vm.empresa.parecerInterno === STRING_VAZIA) {
                toastr.warning("Informe um parecer interno.");
                return false;
            }

            if (angular.isUndefined(vm.empresa.parecerExterno) || vm.empresa.parecerExterno === null || vm.empresa.parecerExterno === STRING_VAZIA) {
                toastr.warning("Informe um parecer externo.");
                return false;
            }

            return true;
        }

        function abrirModalUsuario() {
            $uibModal.open({
                animation: true,
                scope: $scope,
                ariaLabelledBy: 'Usuário',
                ariaDescribedBy: 'modal-body',
                templateUrl: 'app/entities/empresa/modal/vinculo-primeiro-usuario-empresa/vinculo-primeiro-usuario-empresa-modal.html',
                controller: 'VinculoPrimeiroUsuarioEmpresaModalController',
                controllerAs: 'vm',
                size: 'lg',
                backdrop: 'static',
                resolve: {
                    empresa: function () {
                        return vm.empresa;
                    },
                    isFromEmpresa: function () {
                        return false;
                    },
                    isFromAvaliacaoEmpresa: function() {
                        return true;
                    },
                    isBloqueadoStatusAtualEmpresa: function() {
                        return vm.statusAtual == 0 ? true : false;
                    }
                }
            }).result.then(function () {
                
            });
        }

        function realizarAtualizacaoEmpresa() {
            vm.saving = true;
            if (parseInt(vm.empresa.statusCadastro) === 0) {
                vm.empresa.usuarioBloqueioId = $rootScope.usuarioLogado.idUsuario;
                vm.empresa.dataBloqueio = new Date();
                vm.empresa.usuarioValidacaoId = $rootScope.usuarioLogado.idUsuario;
                vm.empresa.dataValidacao = new Date();
                vm.empresa.tivo = 0;
            }

            if (parseInt(vm.statusAtual) === 0 && parseInt(vm.empresa.statusCadastro) === 1) {
                vm.empresa.usuarioDesbloqueioId = $rootScope.usuarioLogado.idUsuario;
                vm.empresa.dataDesbloqueio = new Date();
            }

            if (parseInt(vm.empresa.statusCadastro) === 1) {
                vm.empresa.usuarioValidacaoId = $rootScope.usuarioLogado.idUsuario;
                vm.empresa.dataValidacao = new Date();
            }
            BaseService.post(EMPRESA_CONTROLLER, 'Cadastrar', vm.empresa).then(function(response) {
                vm.saving = false;
                if (response.success) {
                    enviarEmailValidacao();
                    toastr.success(response.data.mensagem);
                    $state.go('empresa-avaliacao.avaliacao');
                } else {
                    toastr.error(response.message);
                }
            });
        }

        function enviarEmailValidacao() {
            BaseService.get(EMPRESA_CONTROLLER, 'EnviarEmailValidacaoEmpresa', {
                destinatario: vm.empresa.email, 
                statusCadastro: vm.empresa.statusCadastro, 
                parecerExterno: vm.empresa.parecerExterno, 
                usuario: "", 
                senha: ""
            }).then(function(response) {
                if (!response.success)
                    toastr.error("Não foi possível enviar e-mail ao usuário: " + response.message);
            });
        }

        function consultarDadosEmpresa() {
            BaseService.get(EMPRESA_CONTROLLER, 'DadosParaValidar', { id: $stateParams.link }).then(function (response) {
                if (response.success) {
                    vm.empresa = response.data;
                    vm.statusAtual = vm.empresa.statusCadastro;
                }
                else 
                    toastr.error(response.message);
            });
        }

        vm.load();
    }
})();