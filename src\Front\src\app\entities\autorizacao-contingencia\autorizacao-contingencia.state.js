(function () {
    'use strict';

    angular.module('bbcWeb.autorizacao-contingencia.state', []).config(routeConfig);

    function routeConfig($stateProvider) {
        $stateProvider
            .state('autorizacao-contingencia', {
                url: "/autorizacao-contingencia",
                abstract: true,
                templateUrl: "app/layout/content.html"})
            .state('autorizacao-contingencia.index', {
                url: '/index',
                templateUrl: 'app/entities/autorizacao-contingencia/autorizacao-contingencia.html'});
    }
})();