(function () {
    'use strict';

    angular.module('bbcWeb').controller('ConfiguracaoSlaController', ConfiguracaoSlaController);

    ConfiguracaoSlaController.$inject = ['toastr', '$rootScope', 'BaseService', '$state', '$stateParams', '$scope', '$timeout', 'PersistentDataService'];

    function ConfiguracaoSlaController(
        toastr,
        $rootScope,
        BaseService,
        $state,
        $stateParams,
        $scope,
        $timeout,
        PersistentDataService
    ) {
        var vm = this;
        vm.configuracaoSla = {};
        vm.menusPai = [];

        vm.headerItems = [{
            name: 'Cadastro<PERSON>'
        }, {
            name: 'Parâmetros gerais',
            link: 'parametros.index'
        }];

        var selfScope = PersistentDataService.get('ConfiguracaoSlaController');

        vm.loadEdit = function () {
            BaseService.get('Parametros', 'ConsultarParametroConfiguracaoSla', {

            }).then(function (response) {
                if (!response.success) {
                    toastr.error(response.message);
                    return;
                } else {
                    vm.configuracaoSla = response.data.result;
                }
            });
        };

        vm.save = function (form) {
            if (!form.$valid) {
                toastr.error('Todos os campos obrigatórios devem ser preenchidos corretamente com seus respectivos valores');
                return;
            }

            if (vm.isSaving === true)
                return;

            var objConfigSla = {}

            objConfigSla.Id = vm.configuracaoSla.id > 0 ? vm.configuracaoSla.id : 0;
            objConfigSla.farolVerdeHoras = vm.configuracaoSla.farolVerdeHoras;
            objConfigSla.emailsFarolVerde = vm.configuracaoSla.emailsFarolVerde;
            objConfigSla.farolAmareloHoras = vm.configuracaoSla.farolAmareloHoras;
            objConfigSla.emailsFarolAmarelo = vm.configuracaoSla.emailsFarolAmarelo;
            objConfigSla.emailsFarolVermelho = vm.configuracaoSla.emailsFarolVermelho;

            vm.isSaving = true;

            BaseService.post('Parametros', 'SalvarConfiguracaoSla', objConfigSla).then(function (response) {
                vm.isSaving = false;

                if (!response.data.sucesso)
                    return toastr.error("Erro ao salvar parametros");
                else {
                    toastr.success('Configuração de SLA salva com sucesso!');
                    $state.go('parametros.index');
                }
            });
        };

        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.administrador == true;
        };

        function init() {
            vm.loadEdit();
        };

        vm.onClickVoltar = function (wizard) {

            var ativoIndex = wizard.getActivePosition();
            if (ativoIndex == 1)
                $state.go('parametros.index');

            wizard.go(ativoIndex - 1);
        };

        init();

        $scope.$on('$stateChangeStart', function (arg1, toState) {
            if (toState.name === 'parametros.index.index')
                PersistentDataService.remove('ConfiguracaoSlaController');
            else
                PersistentDataService.store('ConfiguracaoSlaController', vm, "Administração - Parâmetros SLA", null, "parametros.configuracao-sla", vm.configuracaoSla.id);
        });

        if (angular.isDefined(selfScope))
            angular.extend(vm, selfScope.data);
        else {

            vm.loadEdit($stateParams.link);

        }

        $timeout(function () {
            PersistentDataService.remove('ConfiguracaoSlaController');
        }, 15);

    }
})();
