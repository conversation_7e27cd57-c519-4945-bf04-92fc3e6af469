<div ng-controller="EmprestimoController as vm">
    <form-header items="vm.headerItems" head="'Empréstimo'" state="emprestimo">
    </form-header>
    <div class="wrapper-content animated fadeIn filter-position overflow-hidden">
        <div class="row">
            <div class="col-lg-12">
                <div class="ibox float-e-margins">
                    <div style="height: 38px;" class="ibox-title">
                        <h5>Empréstimo</h5>
                        <div ibox-tools></div>
                    </div>
                    <div class="ibox-content">
                        <div class="pull-right">
                            <button tooltip-placement="top" ng-click="vm.gridOptions.dataSource.refresh();" uib-tooltip="Atualizar" type='button' class="btn btn-labeled btn-default">
                                <i class="fa fa-refresh"></i>
                                <span class="pl-5 ">Atualizar</span>
                            </button>
                            <button tooltip-placement="top" ui-sref="emprestimo.crud({link: 'novo'})" uib-tooltip="Cadastrar " type='button' class="btn btn-labeled btn-primary ">
                                <span class="btn-label text-right">
                                    <i class="fa fa-plus"></i>
                                </span>
                                <span class="pl-5 ">Novo</span>
                            </button>
                        </div>
                        <div class="row"></div>
                        <hr>
                        <div ui-grid="vm.gridOptions" ng-style="{height: vm.gridOptions.getGridHeight()}" class="grid" style="width: 100%;" ui-grid-pinning ui-grid-save-state ui-grid-pagination ui-grid-auto-resize ui-grid-resize-columns ui-grid-grouping>
                        </div>
                        <div class="pull-right mt-15">
                            <button type="button" ng-disabled="vm.desabilitarBtnRelatorio" class="btn btn-xs btn-primary" ng-click="vm.consultarDadosRelatorio(1)"><i class="fa fa-file-excel-o"></i> Exportar em Excel</button>
                            <button type="button" ng-disabled="vm.desabilitarBtnRelatorio" class="btn btn-xs btn-primary" ng-click="vm.consultarDadosRelatorio(2)"><i class="fa fa-file-pdf-o"></i> Exportar em PDF</button>
                        </div>
                        <div class="row"></div>
                        <div  id="exportable-xls">
                            <table style="font-family:Arial, Helvetica, sans-serif" ng-show="false" id="exportable" class="table table-bordered" width="100%">
                                <thead>
                                    <tr>
                                        <th>Código</th>
                                        <th>Data empréstimo</th>
                                        <th>Portador</th>
                                        <th>CPF / CNPJ Portador</th>
                                        <th>Taxa retenção</th>
                                        <th>Valor aquisição</th>
                                        <th>Valor pago</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr ng-repeat="item in vm.emprestimosRelatorio">
                                        <td>{{item.id}}</td>
                                        <td>{{item.dataEmprestimo}}</td>
                                        <td>{{item.portador}}</td>
                                        <td>{{item.cpfCnpjPortador}}</td>
                                        <td>{{item.taxaRetencao}}</td>
                                        <td>{{item.valorAquisicao}}</td>
                                        <td>{{item.valorPago}}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>