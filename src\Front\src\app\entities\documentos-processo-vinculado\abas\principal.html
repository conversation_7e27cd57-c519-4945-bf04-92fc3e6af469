<div class="form-horizontal">
    <hr-label dark="true"></hr-label><br><br>
    <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label">
                        Código:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-7 col-lg-3">
                        <input type="text" ats-numeric ng-model="vm.documentoProcessoVinculado.id"
                        name="id" class="form-control" ng-disabled="true" />
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-md-3 control-label" style="text-align: right; padding-top: 10px;">
                        <span class="text-danger mr-5">*</span>Documento:
                    </label>
                    <div class="input-group col-xs-12 col-md-9">
                        <input type="text" maxlength="100" ng-model="vm.documentoProcessoVinculado.documento" ng-disabled="vm.naoEditavel" 
                        required required-message="'Nome do documento é obrigatório'" class="form-control" ng-paste="filtrarColagem($event)" ng-keypress="vm.bloquearCaracteresIndesejados($event)"  />
                    </div>
                </div>
            </div> 
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-md-3 control-label" style="text-align: right; padding-top: 10px;">
                        <span class="text-danger mr-5">*</span>Processo vinculado:
                    </label>
                    <div class="input-group col-xs-12 col-md-9">
                        <ui-select
                            name="processoComb"
                            ats-ui-select-validator
                            validate-on="blur"
                            ng-model="vm.documentoProcessoVinculado.processoVinculadoId"
                            ng-disabled="vm.naoEditavel"
                            required-message="'Processo vinculado é obrigatório'"
                            required>
                            <ui-select-match>
                                <span>{{$select.selected.descricao}}</span>
                            </ui-select-match>
                            <ui-select-choices repeat="ex.id as ex in vm.cmbProcessoVinculado.data | propsFilter: {descricao: $select.search}">
                                <div ng-bind-html="ex.descricao | highlight: $select.search"></div>
                            </ui-select-choices>
                        </ui-select>
                    </div>
                </div>
            </div>                                  
        </div>
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-md-3 control-label" style="text-align: right; padding-top: 10px;">
                        <span class="text-danger mr-5">*</span>Tipo:
                    </label>
                    <div class="input-group col-xs-12 col-md-9">
                        <ui-select
                            name="tipoComb"
                            ats-ui-select-validator
                            validate-on="blur"
                            ng-model="vm.documentoProcessoVinculado.tipo"
                            ng-disabled="vm.naoEditavel" 
                            required-message="'Tipo é obrigatório'"
                            required>
                            <ui-select-match>
                                <span>{{$select.selected.descricao}}</span>
                            </ui-select-match>
                            <ui-select-choices repeat="ex.id as ex in vm.cmbTipo.data | propsFilter: {descricao: $select.search}">
                                <div ng-bind-html="ex.descricao | highlight: $select.search"></div>
                            </ui-select-choices>
                        </ui-select>
                    </div>
                </div>
            </div> 
            <div class="col-xs-12 col-md-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-md-3">Obrigatório</label>
                    <div class="input-group col-xs-12 col-md-9">
                        <toggle-switch on-label="Sim" off-label="Não" ng-model="vm.documentoProcessoVinculado.obrigatorio" class="switch"></toggle-switch>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>