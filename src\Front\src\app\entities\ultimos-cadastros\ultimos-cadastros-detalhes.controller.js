(function() {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('UltomosCadastrosDetalhesController', UltomosCadastrosDetalhesController);

        UltomosCadastrosDetalhesController.inject = [
        '$scope',
        'toastr',
        'BaseService',
        'PERFIL_ADMINISTRADOR',
        'PERFIL_EMPRESA',
        '$window',
        'PersistentDataService',
        '$rootScope',
        '$timeout',
        '$uibModal',
        '$stateParams',
        'TIPO_PROPRIETARIO'
    ];

    function UltomosCadastrosDetalhesController(
        $scope,
        toastr,
        BaseService,
        PERFIL_ADMINISTRADOR,
        PERFIL_EMPRESA,
        $window,
        PersistentDataService,
        $rootScope,
        $timeout,
        $uibModal,
        $stateParams,
        TIPO_PROPRIETARIO
    ) {

        var vm = this;    
        vm.fila = [];
        vm.motoristaBase = {};
        vm.sizeWizardMotorista = 'col-lg-4';
        vm.sizeWizardCaminhao = 'col-lg-4';
        vm.rowspanTable = "7";
        vm.showWizardProprietario = true;        
        $scope.TIPO_PROPRIETARIO = TIPO_PROPRIETARIO;

        vm.headerItems = [{
            name: 'Cadastros'
        }, {
            name: 'Últimos cadastros'
        }];

        vm.load = function(id) {
            consultarPorId(id, function(motorista){  
                if (motorista.IsGestorFrota) {
                    vm.sizeWizardMotorista = 'col-lg-6';
                    vm.sizeWizardCaminhao = 'col-lg-6';
                    vm.rowspanTable = "6";
                    vm.showWizardProprietario = false;
                }

                vm.motoristaBase = motorista;
            });
        }

        function consultarPorId(id, callback) {
            BaseService.get('MotoristaBaseAts', 'GetDetalhesUltimosCadastros', {
                idMotoristaBase: id
            }).then(function(response) {
                if (!response.success) {
                    toastr.error(response.message);
                    return;
                }

                if (angular.isFunction(callback)) 
                    callback(angular.fromJson(response.data));
            });
        }
        
        vm.load($stateParams.link); 
    }
})();