(function () {
    'use strict';

    angular.module('bbcWeb').directive('consultaPadrao', consultaPadrao);

    consultaPadrao.$inject = ['$window'];

    function consultaPadrao($window) {
        var directive = {
            bindToController: true,
            controller: ConsultaPadraoController,
            controllerAs: 'vm',
            templateUrl: 'app/components/consulta-padrao/consulta-padrao.html',
            restrict: 'AE',
            scope: {
                tableDefinition: '=tabledefinition',
                onSelectValue: '=onselectvalue',
                dependsField: '=dependsField'
            }
        };

        return directive;
    }

    /* @ngInject */
    function ConsultaPadraoController($scope, $timeout, BaseService) {
        var self = this;
        this.selectValue = function (id, text, desiredTextCfg, entity) {
            this.tableDefinition.selectedValue = id;
            this.tableDefinition.selectedEntity = entity;

            if (angular.isDefinedNotNull(desiredTextCfg) && angular.isFunction(desiredTextCfg.rowFn)) {
                this.tableDefinition.selectedText = desiredTextCfg.rowFn(desiredTextCfg.row);
            } else this.tableDefinition.selectedText = text;

            // Executa a função genérica que foi definida no table definition
            if (angular.isFunction(self.tableDefinition.executeAfterSelection))
                self.tableDefinition.executeAfterSelection();

            this.onSelectValue();
            if(angular.isFunction(this.tableDefinition.callBack))
                this.tableDefinition.callBack();
        };

        self.clear = function () {
            self.selectedValue = null;
            self.selectedText = "";
            self.selectedEntity = undefined;
            if (angular.isFunction(self.tableDefinition.clearFunction))
            retorno = self.tableDefinition.clearFunction();
        };

        self.gridOptions = {
            onRegisterApi: BaseService.dataGrid.defaultOnRegisterApi,
            dataSource: {
                url: self.tableDefinition.url,
                params: function () {
                    var retorno = {};

                    if (angular.isFunction(self.tableDefinition.externalParamsMethod))
                        retorno = self.tableDefinition.externalParamsMethod();

                    retorno.Filters = [];
                    
                    var parametros = {};

                    if (angular.isFunction(self.tableDefinition.paramsMethod))
                        parametros = self.tableDefinition.paramsMethod();

                    for (var propertyName in parametros) {
                        if (angular.isUndefined(parametros[propertyName]) || parametros[propertyName] === null) {
                            continue;
                        }

                        if (!isNaN(parametros[propertyName]) && parseInt(parametros[propertyName]) < 0)
                            continue;

                        retorno.Filters.push({
                            Campo: propertyName.toString(),
                            Operador: 8, // Exact/Equal
                            Valor: parametros[propertyName],
                            CampoTipo: isNaN(parametros[propertyName]) ? 1 : 2 //String
                        });
                    }


                  /*  if (!self.tableDefinition.removeAtivoFilter)
                        retorno.Filters.push({
                            Campo: "Ativo",
                            Operador: 8, // Exact/Equal
                            Valor: 1,
                            CampoTipo: 2 //Número
                        });*/

                    return retorno;
                }
            },
            columnDefs: self.tableDefinition.columnDefs || []
        };

        self.tableDefinition.refreshData = self.gridOptions.dataSource.refresh;

        // Precisamos verificar se existe uma coluna com o botão de 'Selecionar'
        // caso não, devemos cria-lo
        var existeColunaAcao = self.gridOptions.columnDefs.some(function (item) {
            return item.btnAction;
        });
        if (!existeColunaAcao) {
            self.gridOptions.columnDefs.push({
                name: 'Ações',
                btnAction: true,
                width: '8%',
                cellTemplate: '<div class="ui-grid-cell-contents"">\
                                      <center>\
                                        <button type="button" ng-click="grid.appScope.vm.selectValue(row.entity[grid.appScope.vm.tableDefinition.desiredValue], row.entity[grid.appScope.vm.tableDefinition.desiredText], {row: row.entity, rowFn: grid.appScope.vm.tableDefinition.desiredTextFn}, row.entity)" ng-class="{ \'btn btn-xs btn-secondary btn-secondaryHover\': true }">\
                                            <i class="fa fa-check"></i>\
                                        </button>\
                                      </center>\
                                  </div>'
            });
        }
    }
})();