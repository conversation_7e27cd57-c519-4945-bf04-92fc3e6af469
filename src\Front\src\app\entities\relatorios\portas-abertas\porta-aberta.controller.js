(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('RelatorioPortasAbertasController', RelatorioPortasAbertasController);

    RelatorioPortasAbertasController.inject = [
        '$rootScope',
        'BaseService',
        'PERFIL_ADMINISTRADOR',
        'PERFIL_EMPRESA',
        '$window',
        '$http',
        'URL_SERVER_DEV',
        'toastr',
        '$scope',
        'PersistentDataService'
    ];

    function RelatorioPortasAbertasController(
        $rootScope,
        BaseService,
        PERFIL_ADMINISTRADOR,
        PERFIL_EMPRESA,
        $window,
        $http,
        URL_SERVER_DEV,
        toastr,
        $scope,
        PersistentDataService
    ) {

        var vm = this;
        vm.headerItems = [{ name: 'Relatórios' }, { name: 'Portas Abertas' }];

        //Iniciamos os objetos
        vm.portaAberta = { dataInicial: new Date() };
        vm.portaAberta.empresas = [];
        vm.portaAberta.ImeiSelected = [];
        vm.dataInicial = { opened: false };
        vm.dataFinal = { opened: false };

        vm.componenteVisivel = function (block) {
            if (block === 'blockEmpresa') {
                if ($rootScope.usuarioLogado.perfil === PERFIL_ADMINISTRADOR)
                    return true;
                return false;
            }
        };

        vm.clear = function ($event) {
            $event.stopPropagation();
            if ($event.currentTarget.id == 'clearEmpresa') {
                vm.portaAberta.empresas.selected = undefined;
                vm.portaAberta.ImeiSelected = undefined;
                vm.portaAberta.Imeis = undefined;
            } else if ($event.currentTarget.id == 'clearImei')
                vm.portaAberta.ImeiSelected = undefined;
        };

        vm.loadAll = function () {
            if ($rootScope.usuarioLogado.perfil === PERFIL_ADMINISTRADOR) {
                var controllerEmpresa = 'EmpresaAts';
                var methodEmpresa = 'Consultar';

                BaseService.get(controllerEmpresa, methodEmpresa).then(function (response) {
                    vm.portaAberta.empresas = angular.fromJson(response.data);
                });
            } else {
                vm.portaAberta.empresas.selected = $rootScope.usuarioLogado.idEmpresa;
                vm.getImeis();
            }
        };

        vm.getImeis = function () {

            vm.portaAberta.ImeiSelected = undefined;

            var paramsHardware = {
                idEmpresa: $rootScope.usuarioLogado.perfil === PERFIL_ADMINISTRADOR ?
                    vm.portaAberta.empresas.selected : $rootScope.usuarioLogado.idEmpresa
            }

            if (!angular.isUndefined(paramsHardware.idEmpresa))
                BaseService.get("HardwareAts", "GetImeisPorEmpresa", paramsHardware).then(function (response) {
                    if (!angular.isUndefined(response.data))
                        vm.portaAberta.Imeis = angular.fromJson(response.data);
                });
        };

        vm.dataInicial = function () {
            vm.dataInicial.opened = true;
        };

        vm.dataFinal = function () {
            vm.dataFinal.opened = true;
        };

        vm.minDate = function () {
            var minDate = this.portaAberta.dataInicial;
            return minDate;
        };

        vm.changeDataInicial = function () {
            if (vm.portaAberta.dataFinal < vm.portaAberta.dataInicial)
                vm.portaAberta.dataFinal = vm.portaAberta.dataInicial;
        };
 
        function gerarRelatorio(isPDF) {
            if (!angular.isDefined(vm.portaAberta.empresas.selected) || vm.portaAberta.empresas.selected <= 0) {
                toastr.error("Selecione uma empresa.");
                return;
            }
            if (!angular.isDefined(vm.portaAberta.dataInicial) || !angular.isDefined(vm.portaAberta.dataFinal)) {
                toastr.error("Data inicial e final são obrigatórias.");
                return;
            }

            var params = {
                imeis: vm.portaAberta.ImeiSelected,
                IdEmpresa: $rootScope.usuarioLogado.perfil === PERFIL_ADMINISTRADOR ?
                    vm.portaAberta.empresas.selected : $rootScope.usuarioLogado.idEmpresa,
                DataInicial: vm.portaAberta.dataInicial,
                DataFinal: vm.portaAberta.dataFinal,
                tipoArquivo: isPDF ? 1 : 2
            };

            $http.get(URL_SERVER_DEV + '/HardwareAts/GerarRelatorioPortaAberta', { params: params, responseType: 'arraybuffer' })
                .success(function (data) {
                    var file = new Blob([data], { type: isPDF ? 'application/pdf' : 'application/vnd.ms-excel' });
                    var fileURL = URL.createObjectURL(file);
                    if (isPDF)
                        $window.open(fileURL);
                    else {
                        var a = document.createElement("a");
                        document.body.appendChild(a);
                        a.style = "display: none";
                        a.href = fileURL;
                        a.download = "Abertura-Portas-ATS.xls";
                        a.click();
                        $window.URL.revokeObjectURL(fileURL);
                    }
                });
        };

        vm.gerarPDF = function () { return gerarRelatorio(true); };

        vm.gerarExcel = function () { return gerarRelatorio(false); };

        // DO NOT TOUCH!!
        $scope.$on('$stateChangeStart', function (_, toParams) {
            PersistentDataService.store('RelatorioPortasAbertasController', vm, 'Relatório - Porta aberta', null, 'relatorio.portas-abertas');
        });
        var selfScope = PersistentDataService.get('RelatorioPortasAbertasController');
        if (angular.isDefined(selfScope))
            angular.extend(vm, selfScope.data);
        else
            vm.loadAll();
        // DO NOT TOUCH!!

    }
})();