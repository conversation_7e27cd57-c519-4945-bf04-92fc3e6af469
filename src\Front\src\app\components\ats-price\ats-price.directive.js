(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .directive('atsPrice', atsPrice);

    function atsPrice() {
        return {
            bindToController: true,
            require: 'ngModel',
            controller: function () {},
            controllerAs: 'vm',
            link: function (scope, element, attrs, ctrl) {
                var format = {
                    prefix: '',
                    centsSeparator: ',',
                    thousandsSeparator: '.'
                };

                ctrl.$parsers.unshift(function (value) {
                    element.priceFormat(format);
                    return value != "0,0" ? element[0].value : undefined;
                });

                ctrl.$formatters.unshift(function (value) {
                    element[0].value = ctrl.$modelValue;
                    element.priceFormat(format);
                    return value ? element[0].value : undefined;
                })
            },
            restrict: 'AE',
            scope: {}
        };
    }
})();