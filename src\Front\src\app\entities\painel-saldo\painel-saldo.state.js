(function () {
    'use strict';

    angular.module('bbcWeb.painel-saldo.state', []).config(routeConfig).run(loadCSS);

    function routeConfig($urlRouterProvider, $stateProvider) {
        $stateProvider
            .state('painel-saldo', {
                url: "/painel-saldo",
                abstract: true,  
                templateUrl: "app/layout/content.html"
            })
            .state('painel-saldo.index', {
                url: "/index",
                templateUrl: "app/entities/painel-saldo/painel-saldo.html"
            });
    }
    function loadCSS($document) {
        var link = $document[0].createElement("link");
        link.href = "app/entities/painel-saldo/painel-saldo.css";  // Caminho correto do CSS
        link.type = "text/css";
        link.rel = "stylesheet";
        $document[0].head.appendChild(link);
    }
})();