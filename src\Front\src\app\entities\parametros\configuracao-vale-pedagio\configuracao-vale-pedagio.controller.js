(function () {
    'use strict';

    angular.module('bbcWeb').controller('ConfiguracaoValePedagioController', ConfiguracaoValePedagioController);

    ConfiguracaoValePedagioController.$inject = ['toastr', '$rootScope', 'BaseService', '$state', '$stateParams', '$scope', '$timeout', 'PersistentDataService'];

    function ConfiguracaoValePedagioController(
        toastr,
        $rootScope,
        BaseService,
        $state,
        $stateParams,
        $scope,
        $timeout,
        PersistentDataService
    ) {
        var vm = this;
        vm.configuracaoValePedagio = {};
        vm.tipoValorMaximoComplemento = true;

        vm.headerItems = [{
            name: 'Cadastros'
        }, {
            name: 'Parâmetros gerais',
            link: 'parametros.index'
        }];

        vm.loadEdit = function () {
            BaseService.get('Parametros', 'ConsultaParametrosConfiguracaoValePedagio', {})
                .then(function (response) {
                    if (!response) {
                        toastr.error('Erro de comunicação com o servidor, tente novamente em alguns minutos.');
                        return;
                    }

                    if (!response.success) {
                        toastr.error(response.message);
                        return;
                    }

                    vm.configuracaoValePedagio = response.data;
                    if (vm.configuracaoValePedagio.tipoDoValorParametroValorMaxPagComplemento == 4) 
                    {
                        vm.tipoValorMaximoComplemento = true
                    } else 
                    {
                        vm.tipoValorMaximoComplemento = false
                    }
                     
                });
        };

        vm.resetValueValorMaximoComp = function () {
            vm.configuracaoValePedagio.valorMaximoPagamentoComplemento = null;
        }


        vm.save = function (form) {
            if (!form.$valid) {
                toastr.error('Todos os campos obrigatórios devem ser preenchidos corretamente com seus respectivos valores.');
                return;
            }

            if (vm.saving) return;

            var request = {}
            request.contaValePedagio = vm.configuracaoValePedagio.contaValePedagio
            request.contaTarifaValePedagio = vm.configuracaoValePedagio.contaTarifaValePedagio
            request.configuracaoTentativaReenvioValePedagio = vm.configuracaoValePedagio.configuracaoTentativaReenvioValePedagio
            request.configuracaoCancelamentoPagamentoPedagio = vm.configuracaoValePedagio.configuracaoCancelamentoPagamentoPedagio
            request.valorMaximoPagamentoComplemento = vm.configuracaoValePedagio.valorMaximoPagamentoComplemento
            request.valorMaximoPagamentoValePedagio = vm.configuracaoValePedagio.valorMaximoPagamentoValePedagio
            request.tipoValorMaximoPagamentoComplemento = vm.tipoValorMaximoComplemento ? 4 : 2;

            vm.saving = true;

            BaseService.post('Parametros', 'SalvarConfiguracaoValePedagio', request)
                .then(function (response) {
                    vm.saving = false;
                    if (!response) {
                        toastr.error('Erro de comunicação com o servidor, tente novamente em alguns minutos.');
                        return;
                    }
                    if (!response.success) {
                        toastr.error(response.message);
                        return;
                    }
                    toastr.success(response.message);
                    $state.go('parametros.index');
                })
        };


        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.administrador == true;
        };

        function init() {
            vm.loadEdit();
        };

        vm.onClickVoltar = function (wizard) {

            var ativoIndex = wizard.getActivePosition();
            if (ativoIndex == 1)
                $state.go('parametros.index');

            wizard.go(ativoIndex - 1);
        };

        init();

    
    }
})();
