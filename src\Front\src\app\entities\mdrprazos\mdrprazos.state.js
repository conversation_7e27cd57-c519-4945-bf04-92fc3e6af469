(function () {
    'use strict';

    angular.module('bbcWeb.mdrprazos.state', []).config(routeConfig);

    function routeConfig($urlRouterProvider, $stateProvider) {
        $stateProvider.state('mdrprazos', {
            abstract: true,
            url: "/mdrprazos",
            templateUrl: "app/layout/content.html"
        }).state('mdrprazos.index', {
            url: '/index',
            templateUrl: 'app/entities/mdrprazos/mdrprazos.html'
        }).state('mdrprazos.mdrprazos-crud', {
            url: '/:link',
            templateUrl: 'app/entities/mdrprazos/mdrprazos-crud.html'
        });
    }
})();