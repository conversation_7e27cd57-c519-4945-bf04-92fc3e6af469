<div id="EmpresaCadastreSeController" ng-controller="EmpresaCadastreSeController as vm">
    <form id="formAbaRepresentanteLegal" name="formAbaRepresentanteLegal"
        ng-submit="vm.adicionarRepLegal(formAbaRepresentanteLegal)" data-toggle="validator" role="form" novalidate>

        <div style="height: 100%;">
            <div class="animated fadeInRight">
                <div class="ibox-title" style="background-color: #056233; border-radius: 14px;">
                    <h5 style="color: white">Dados do representante legal</h5>
                </div>
                <div class="form-group" style="line-height: 20px; padding-top: 15px; padding-left: 5px;">
                    <div class="col-xm-12 col-sm-12 col-md-6 col-lg-6">
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-12 col-md-3 col-lg-3 control-label mt-4"
                                style="text-align: left;">
                                Nome:</label>
                            <div class="intput-group col-xs-12 col-sm-12 col-md-9 col-lg-9">
                                <input class="form-control" type="text" ng-model="vm.repLegal.nome"
                                    focus-me="shouldBeOpen" data-error="Nome é obrigatório" maxlength="200"
                                    validate-on="blur" required />
                                <!-- <div class="help-block with-errors"></div> -->
                            </div>
                        </div>
                    </div>

                    <div class="col-xm-12 col-sm-12 col-md-6 col-lg-6">
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-12 col-md-3 col-lg-3 control-label mt-4"
                                style="text-align: left;">
                                CPF:</label>
                            <div class="intput-group col-xs-12 col-sm-12 col-md-9 col-lg-9">
                                <input type="text" class="form-control" ui-br-cpf-mask ng-model="vm.repLegal.cpfCnpj"
                                    focus-me="shouldBeOpen" data-error="CPF é obrigatório"
                                    validate-on="blur" name="CPF representante" required />
                                <!-- <div class="help-block with-errors"></div> -->
                            </div>
                        </div>
                    </div>

                    <div class="col-xm-12 col-sm-12 col-md-6 col-lg-6">
                        <div class="form-group">
                            <label id="SexoRepresentante" class="col-xs-12 col-sm-12 col-md-3 col-lg-3 control-label mt-4"
                                style="text-align: left;">
                                Sexo:</label>
                            <div class="intput-group col-xs-12 col-sm-12 col-md-9 col-lg-9">
                                <ui-select name="SexoRepresentante" required ui-select-required
                                    ng-model="vm.repLegal.sexo" data-error="Campo obrigatório">
                                    <ui-select-match>
                                        <span>{{$select.selected.descricao}}</span>
                                    </ui-select-match>
                                    <ui-select-choices
                                        repeat="ex.id as ex in vm.comboSexo.data | propsFilter: {descricao: $select.search}">
                                        <div ng-bind-html="ex.descricao | highlight: $select.search"></div>
                                    </ui-select-choices>
                                </ui-select>
                            </div>
                        </div>
                    </div>

                    <div class="col-xm-12 col-sm-12 col-md-6 col-lg-6">
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-12 col-md-3 col-lg-3 control-label mt-4"
                                style="text-align: left;">
                                E-mail:</label>
                            <div class="intput-group col-xs-12 col-sm-12 col-md-9 col-lg-9">
                                <input type="email" class="form-control" ng-model="vm.repLegal.email"
                                    focus-me="shouldBeOpen" data-error="E-mail é obrigatório" maxlength="100"
                                    validate-on="blur" name="Email representante" required />
                                <!-- <div class="help-block with-errors"></div> -->
                            </div>
                        </div>
                    </div>

                    <div class="col-xm-12 col-sm-12 col-md-6 col-lg-6">
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-12 col-md-3 col-lg-3 control-label mt-4"
                                style="text-align: left;">
                                RNTRC:</label>
                            <div class="intput-group col-xs-12 col-sm-12 col-md-9 col-lg-9">
                                <input class="form-control" type="text" ng-model="vm.repLegal.rntrc"
                                    focus-me="shouldBeOpen" maxlength="8" validate-on="blur"
                                    name="RNTRC representante" />
                            </div>
                        </div>
                    </div>


                    <div class="col-xm-12 col-sm-12 col-md-6 col-lg-6">
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-12 col-md-3 col-lg-3 control-label mt-4"
                                style="text-align: left;">
                                Celular:</label>
                            <div class="intput-group col-xs-12 col-sm-12 col-md-9 col-lg-9">
                                <input class="form-control" type="text" ng-model="vm.repLegal.celular"
                                    ui-br-phone-number focus-me="shouldBeOpen" data-error="Celular é obrigatório"
                                    validate-on="blur" name="Celular representante" required />
                                <!-- <div class="help-block with-errors"></div> -->
                            </div>
                        </div>
                    </div>

                    <div class="col-xm-12 col-sm-12 col-md-6 col-lg-6">
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-12 col-md-3 col-lg-3 control-label mt-4"
                                style="text-align: left;">
                                Telefone:</label>
                            <div class="intput-group col-xs-12 col-sm-12 col-md-9 col-lg-9">
                                <input class="form-control" type="text" ng-model="vm.repLegal.telefone"
                                    ui-br-phone-number focus-me="shouldBeOpen" data-error="Telefone é obrigatório"
                                    validate-on="blur" name="Telefone representante" required />
                                <!-- <div class="help-block with-errors"></div> -->
                            </div>
                        </div>
                    </div>

                    <div class="col-xm-12 col-sm-12 col-md-6 col-lg-6">
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-12 col-md-3 col-lg-3 control-label mt-4"
                                style="text-align: left;">
                                Número identidade:</label>
                            <div class="intput-group col-xs-12 col-sm-12 col-md-9 col-lg-9">
                                <input class="form-control" type="text" ng-model="vm.repLegal.numeroIdentidade"
                                    ats-numeric onkeypress="return event.charCode >= 48 && event.charCode <= 57"
                                    focus-me="shouldBeOpen" data-error="Número identidade é obrigatório"
                                    validate-on="blur" name="Número identidade representante" required />
                                <!-- <div class="help-block with-errors"></div> -->
                            </div>
                        </div>
                    </div>

                    <div class="col-xm-12 col-sm-12 col-md-6 col-lg-6">
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-12 col-md-3 col-lg-3 control-label mt-4"
                                style="text-align: left;">
                                Data nascimento:</label>
                            <div class="intput-group col-xs-12 col-sm-12 col-md-9 col-lg-9">
                                <div class="input-group">
                                    <input type="text" name="Data nascimento representante"
                                        invalid-message="'Data nascimento é inválida'"
                                        ng-click="vm.datePickerOpennasc = !vm.datePickerOpennasc" class="form-control"
                                        max-date="new Date()" current-text="Hoje" clear-text="Limpar"
                                        close-text="Fechar" uib-datepicker-popup="dd/MM/yyyy" validate-on="blur"
                                        datepicker-append-to-body="true" ng-model="vm.repLegal.dataNascimento"
                                        is-open="vm.datePickerOpennasc" data-error="Data nascimento é obrigatório"
                                        required />
                                    <!-- <div class="help-block with-errors"></div> -->
                                    <span class="input-group-btn" style="vertical-align: top;">
                                        <button type="button" class="btn btn-default" style="left: 1px;"
                                            ng-click="vm.datePickerOpennasc = !vm.datePickerOpennasc">
                                            <i class="fa fa-calendar"></i>
                                        </button>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xm-12 col-sm-12 col-md-6 col-lg-6">
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-12 col-md-3 col-lg-3 control-label mt-4"
                                style="text-align: left;">
                                Emissão identidade:</label>
                            <div class="intput-group col-xs-12 col-sm-12 col-md-9 col-lg-9">
                                <div class="input-group">
                                    <input type="text" name="Emissão identidade representante"
                                        invalid-message="'Emissão identidade é inválida'"
                                        ng-click="vm.datePickerOpenemissao = !vm.datePickerOpenemissao"
                                        class="form-control" max-date="new Date()" current-text="Hoje"
                                        clear-text="Limpar" close-text="Fechar" uib-datepicker-popup="dd/MM/yyyy"
                                        datepicker-append-to-body="true" ng-model="vm.repLegal.emissaoIdentidade"
                                        is-open="vm.datePickerOpenemissao" validate-on="blur"
                                        data-error="Emissão identidade é obrigatório" required />
                                    <!-- <div class="help-block with-errors"></div> -->
                                    <span class="input-group-btn" style="vertical-align: top;">
                                        <button type="button" class="btn btn-default" style="left: 1px;"
                                            ng-click="vm.datePickerOpenemissao = !vm.datePickerOpenemissao">
                                            <i class="fa fa-calendar"></i>
                                        </button>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xm-12 col-sm-12 col-md-6 col-lg-6">
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-12 col-md-3 col-lg-3 control-label mt-4"
                                style="text-align: left;">
                                Orgão emissão:</label>
                            <div class="intput-group col-xs-12 col-sm-12 col-md-9 col-lg-9">
                                <input class="form-control" type="text" ng-model="vm.repLegal.orgaoEmissor"
                                    focus-me="shouldBeOpen" data-error="Orgão emissão é obrigatório" validate-on="blur"
                                    name="Orgão emissão representante" required />
                                <!-- <div class="help-block with-errors"></div> -->
                            </div>
                        </div>
                    </div>

                    <div class="col-xm-12 col-sm-12 col-md-6 col-lg-6">
                        <div class="form-group">
                            <label id="UfEmissao" class="col-xs-12 col-sm-12 col-md-3 col-lg-3 control-label mt-4"
                                style="text-align: left;">
                                UF Emissão:</label>
                            <div class="intput-group col-xs-12 col-sm-12 col-md-9 col-lg-9">
                                <ui-select name="UfEmissao" ats-ui-select-validator validate-on="blur"
                                    ng-model="vm.repLegal.ufEmissao" riquired-message="'UF emissão é obrigatório'"
                                    required>
                                    <ui-select-match>
                                        <span>{{$select.selected.descricao}}</span>
                                    </ui-select-match>
                                    <ui-select-choices
                                        repeat="ex.id as ex in vm.estados | propsFilter: {descricao: $select.search}">
                                        <div ng-bind-html="ex.descricao | highlight: $select.search"></div>
                                    </ui-select-choices>
                                </ui-select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


            <div class="animated fadeInRight">
                <div class="ibox-title" style="background-color: #056233; border-radius: 14px;">
                    <h5 style="color: white">Endereço</h5>
                </div>
                <div class="form-group" style="line-height: 20px; padding-top: 15px; padding-left: 5px;">

                    <div class="col-xm-12 col-sm-12 col-md-6 col-lg-6">
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-12 col-md-3 col-lg-3 control-label mt-4"
                                style="text-align: left;">
                                CEP:</label>
                            <div class="intput-group col-xs-12 col-sm-12 col-md-9 col-lg-9">
                                <input class="form-control" type="text" ng-model="vm.repLegal.cep" ui-br-cep-mask
                                    focus-me="shouldBeOpen" data-error="CEP é obrigatório" validate-on="blur"
                                    name="CEP representante" required
                                    ng-blur="vm.buscarEnderecoRepLegal(vm.repLegal.cep)" />
                                <!-- <div class="help-block with-errors"></div> -->
                            </div>
                        </div>
                    </div>

                    <div class="col-xm-12 col-sm-12 col-md-6 col-lg-6">
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-12 col-md-3 col-lg-3 control-label mt-4"
                                style="text-align: left;">
                                Endereço:</label>
                            <div class="intput-group col-xs-12 col-sm-12 col-md-9 col-lg-9">
                                <input class="form-control" type="text" ng-model="vm.repLegal.endereco"
                                    focus-me="shouldBeOpen" data-error="Endereço é obrigatório" maxlength="200"
                                    validate-on="blur" name="Endereço representante" required />
                                <!-- <div class="help-block with-errors"></div> -->
                            </div>
                        </div>
                    </div>

                    <div class="col-xm-12 col-sm-12 col-md-6 col-lg-6">
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-12 col-md-3 col-lg-3 control-label mt-4"
                                style="text-align: left;">
                                Bairro:</label>
                            <div class="intput-group col-xs-12 col-sm-12 col-md-9 col-lg-9">
                                <input class="form-control" type="text" ng-model="vm.repLegal.bairro"
                                    focus-me="shouldBeOpen" data-error="Bairro é obrigatório" maxlength="100"
                                    validate-on="blur" name="Bairro representante" required />
                                <!-- <div class="help-block with-errors"></div> -->
                            </div>
                        </div>
                    </div>

                    <div class="col-xm-12 col-sm-12 col-md-6 col-lg-6">
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-12 col-md-3 col-lg-3 control-label mt-4"
                                style="text-align: left;">
                                Número:</label>
                            <div class="intput-group col-xs-12 col-sm-12 col-md-9 col-lg-9">
                                <input class="form-control" type="text" ng-model="vm.repLegal.enderecoNumero"
                                    ats-numeric focus-me="shouldBeOpen" data-error="Número é obrigatório" maxlength="10"
                                    validate-on="blur" name="Número representante" required />
                                <!-- <div class="help-block with-errors"></div> -->
                            </div>
                        </div>
                    </div>

                    <div class="col-xm-12 col-sm-12 col-md-6 col-lg-6">
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-12 col-md-3 col-lg-3 control-label mt-4"
                                style="text-align: left;">Complemento:</label>
                            <div class="intput-group col-xs-12 col-sm-12 col-md-9 col-lg-9">
                                <input class="form-control" type="text" ng-model="vm.repLegal.complemento"
                                    focus-me="shouldBeOpen" maxlength="100" validate-on="blur"
                                    name="Complemento representante" />
                            </div>
                        </div>
                    </div>

                    <div class="col-xm-12 col-sm-12 col-md-6 col-lg-6">
                        <div class="form-group">
                            <label id="EstadoRepresentante" class="col-xs-12 col-sm-12 col-md-3 col-lg-3 control-label mt-4"
                                style="text-align: left;">
                                Estado:</label>
                            <div class="intput-group col-xs-12 col-sm-12 col-md-9 col-lg-9">
                                <ui-select name="EstadoRepresentante" ats-ui-select-validator validate-on="blur"
                                    ng-model="vm.repLegal.estadoId" required-message="'Estado é obrigatório'" required
                                    ng-change="vm.estadoChangeRepLegal(vm.repLegal.estadoId)">
                                    <ui-select-match>
                                        <span>{{$select.selected.descricao}}</span>
                                    </ui-select-match>
                                    <ui-select-choices
                                        repeat="ex.id as ex in vm.estadosRepLegal | propsFilter: {descricao: $select.search}">
                                        <div ng-bind-html="ex.descricao | highlight: $select.search"></div>
                                    </ui-select-choices>
                                </ui-select>
                            </div>
                        </div>
                    </div>

                    <div class="col-xm-12 col-sm-12 col-md-6 col-lg-6">
                        <div class="form-group">
                            <label id="CidadeRepresentante" class="col-xs-12 col-sm-12 col-md-3 col-lg-3 control-label mt-4"
                                style="text-align: left;">
                                Cidade:</label>
                            <div class="intput-group col-xs-12 col-sm-12 col-md-9 col-lg-9">
                                <ui-select name="CidadeRepresentante" ats-ui-select-validator validate-on="blur"
                                    ng-model="vm.repLegal.cidadeId" ng-disabled="vm.cidadesDisabledRepLegal"
                                    required-message="'Cidade é obrigatório'" required>
                                    <ui-select-match>
                                        <span>{{$select.selected.descricao}}</span>
                                    </ui-select-match>
                                    <ui-select-choices
                                        repeat="ex.id as ex in vm.cidadesRepLegal | propsFilter: {descricao: $select.search}">
                                        <div ng-bind-html="ex.descricao | highlight: $select.search"></div>
                                    </ui-select-choices>
                                </ui-select>
                            </div>
                        </div>
                    </div>

                    <!--<div class="col-xs-12">
                        <div class="form-group" style="padding-right: 10px;">
                            <div class="text-right">
                                <button type="submit" class="mr-5 btn-labeled btn btn-info">
                                    <i class="fas fa-arrow-down"></i> Inserir Representante Legal
                                </button>
                            </div>
                        </div>
                    </div>
                    <div>
                        <div ng-show="true">
                            <div class="table-responsive" style="overflow: inherit;">
                                <table class="table table-bordered table-hover">
                                    <thead>
                                        <tr>
                                            <th width="80%">Portador</th>
                                            <th width="15%">CPF</th>
                                            <th width="5%">Ações</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr ng-repeat="repLegais in vm.repLegalList">
                                            <td>{{repLegais.nome}}</td>
                                            <td>{{repLegais.cpf}}</td>
                                            <td class="text-center" style="vertical-align: middle">
                                                <button type="button" uib-tooltip="Remover"
                                                    class="btn btn-xs btn-danger"
                                                    ng-click="vm.removerRepLegal(repLegais)">
                                                    <i class="fa fa-trash-o"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>-->
                </div>
            </div>
        </div>
    </form>
</div>