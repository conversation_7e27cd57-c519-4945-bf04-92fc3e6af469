<!doctype html>
<html ng-app="bbcWeb">

<head ng-controller="HeaderController as vm">
    <!-- Global site tag (gtag.js) - Google Analytics -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=UA-154573376-3"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag() { dataLayer.push(arguments); }
        gtag('js', new Date());

        gtag('config', 'UA-154573376-3');
    </script>

    <meta charset="utf-8">
    <title>{{vm.htmlTitle}}</title>

    <!-- Google Tag Manager -->
    <script>(function (w, d, s, l, i) {
        w[l] = w[l] || []; w[l].push({
            'gtm.start':
                new Date().getTime(), event: 'gtm.js'
        }); var f = d.getElementsByTagName(s)[0],
            j = d.createElement(s), dl = l != 'dataLayer' ? '&l=' + l : ''; j.async = true; j.src =
                'https://www.googletagmanager.com/gtm.js?id=' + i + dl; f.parentNode.insertBefore(j, f);
        })(window, document, 'script', 'dataLayer', 'GTM-MWNVHF7');</script>
    <!-- End Google Tag Manager -->

    <meta name="description" content="{{ vm.htmlTitle}}">
    <meta name="viewport" content="width=device-width, user-scalable=no">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta http-equiv="cache-control" content="max-age=0" />
    <meta http-equiv="cache-control" content="no-cache" />
    <meta http-equiv="expires" content="0" />
    <meta http-equiv="expires" content="Tue, 01 Jan 1980 1:00:00 GMT" />
    <meta http-equiv="pragma" content="no-cache" />
    <!-- Place favicon.ico and apple-touch-icon.png in the root directory -->
    <link href="{{vm.favicon}}" rel="shortcut icon" type="image/x-icon" />
    <link rel="stylesheet" href="assets/css/jquery-ui.css">

    <!-- build:css({.tmp/serve,src}) styles/vendor.css -->
    <!-- bower:css -->
    <!-- run `gulp inject` to automatically populate bower styles dependencies -->
    <!-- endbower -->
    <!-- endbuild -->

    <!-- build:css({.tmp/serve,src}) styles/app.css -->
    <!-- inject:css -->
    <!-- css files will be automatically insert here -->
    <!-- endinject -->
    <!-- endbuild -->
</head>

<body id="bodyid" style="overflow-y:auto; " ng-controller="MainController as main">

    <!-- Google Tag Manager (noscript) -->
    <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-MWNVHF7" height="0" width="0"
            style="display:none;visibility:hidden"></iframe></noscript>
    <!-- End Google Tag Manager (noscript) -->

    <div ng-if="empresaCadastrSe">
        <div ng-include="'app/empresa-cadastre-se/cadastre-se.html'"></div>
    </div>
    <div ng-if="!empresaCadastrSe">
        <div ui-view="login" id="view-login" style="overflow-y:hidden;overflow-x:hidden; ">
            <div id="wrapper">
                <!-- Navigation -->
                <div ng-include="'app/layout/sidebar.html'"></div>
                <!-- Page wraper -->
                <!-- ng-class with current state name give you the ability to extended customization your view -->
                <div id="page-wrapper" class="gray-bg {{$state.current.name}}">
                    <!-- Page wrapper -->
                    <div id="navBarTopo" ng-include="'app/layout/topnavbar.html'"></div>
                    <!-- Main view  -->
                    <div ui-view></div>
                    <!-- Footer -->
                    <div id="rodape" ng-include="'app/layout/footer.html'"></div>
                </div>
                <!-- End page wrapper-->
            </div>
        </div>
    </div>

<script type="text/javascript">
    window.paceOptions = {
        document: true, // disabled
        eventLag: true,
        restartOnPushState: true,
        restartOnRequestAfter: true,
        ajax: {
            trackMethods: [ 'POST','GET']
        }
    };
</script>

    <!--[if lt IE 10]>
      <p class="browsehappy">You are using an <strong>outdated</strong> browser. Please <a href="http://browsehappy.com/">upgrade your browser</a> to improve your experience.</p>
    <![endif]-->
    <!-- Wrapper-->

    <!-- End wrapper-->
    <!-- build:js(src) scripts/vendor.js -->
    <!-- bower:js -->
    <!-- run `gulp inject` to automatically populate bower script dependencies -->
    <!-- endbower -->
    <!-- endbuild -->

    <!-- build:js({.tmp/serve,.tmp/partials,src}) scripts/app.js -->
    <!-- inject:js -->
    <!-- js files will be automatically insert here -->
    <!-- endinject -->
    <script src="./assets/lib/angular-locale_pt-br.js"></script>
    <script src="./assets/lib/polyfiller.js"></script>
    <script src="./assets/lib/circle-progress.js"></script>
    <script src="./assets/lib/markerclusterer.js"></script>
    <script src="./assets/lib/jquery.priceformat.min.js"></script>
    <script src="./assets/lib/swal.js"></script>
    <script src="./assets/lib/angular-base64-upload.js"></script>
    <script src="./assets/lib/sistema.js"></script>
    <script src="./assets/lib/jspdf.js"></script>
    <script src="./assets/lib/jspdf-autotable.js"></script>
    <script src="./assets/lib/chart.js"></script>
    <script src="./assets/lib/bootstrap-datetimepicker.js"></script>
    <script src="./assets/lib/xlsx.js">
    </script>
    <script src="./assets/lib/jszip.js">
    </script>
	<script src="./env.js"/>
    <script src="./assets/lib/xlsx.full.min.js">
    </script>
    <!-- inject:partials -->
    <!-- angular templates will be automatically converted in js and inserted here -->
    <!-- endinject -->
    <!-- endbuild -->

    <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script>
</body>

</html>