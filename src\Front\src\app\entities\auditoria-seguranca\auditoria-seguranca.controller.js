(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('AuditoriaSegurancaController', AuditoriaSegurancaController);

    AuditoriaSegurancaController.inject = ['BaseService', '$rootScope', 'toastr', '$scope', 'PersistentDataService', '$timeout', '$state', 'PERFIL_ADMINISTRADOR'];

    function AuditoriaSegurancaController(BaseService, $rootScope, toastr, $scope, PersistentDataService, $timeout, $state, PERFIL_ADMINISTRADOR) {
        var vm = this;
        vm.headerItems = [{
            name: 'Administração'
        }, {
            name: 'Auditoria de segurança' 
        }];

        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.perfil == PERFIL_ADMINISTRADOR;
        };

        vm.gridOptions = {
            data: [],
            onRegisterApi: BaseService.dataGrid.defaultOnRegisterApi,
            dataSource: {
                url: "AuditoriaSeguranca/ConsultarGridAuditoriaSeguranca"
            },
            columnDefs: [{
                name: 'usuarioId',
                displayName: 'Código do usuário',
                width: 160,
                type: 'number',
                primaryKey: true,
                field: 'usuarioId'
            }, {
                name: 'NomeUsuario',
                displayName: 'Nome do usuário',
                width: '*',
                field: 'nomeUsuario',
                serverField: 'Usuario.Nome'
            }, {
                name: 'Menu',
                displayName: 'Menu',
                width: '*',
                field: 'menu'
            }, {
                name: 'DataTentativa',
                displayName: 'Data da tentativa',
                width: '*',
                field: 'dataAcesso',
                enableFiltering: false
            }]
        };

        vm.alterarStatus = function (id) {
            BaseService.post('Fabricante', 'AlterarStatus', {
                id: id
            }).then(function (response) {
                if (!response) {
                    toastr.error('Erro de comunicação com o servidor, tente novamente em alguns minutos.')
                    return
                }

                if (!response.success) {
                    toastr.error(response.message)
                    return
                }

                toastr.success(response.message)

                vm.gridOptions.dataSource.refresh();
            });
        };

        // Controle de aba!!
        $scope.$on('$stateChangeStart', function () {
            PersistentDataService.store('FabricanteController', vm, "Fabricante", "FabricanteCrudController", "fabricante.index");
        });

        var selfScope = PersistentDataService.get('FabricanteController');
        var filho = PersistentDataService.get('FabricanteCrudController');
        if (angular.isDefined(filho)) {
            $timeout(function () {
                $state.go('fabricante.fabricante-crud', {
                    link: filho.data.fabricante.IdFabricante > 0 ? 'editar' : 'novo'
                });
            }, 50);
        } else if (angular.isDefined(selfScope)) {
            angular.extend(vm, selfScope.data);

            $timeout(function () {
                vm.gridOptions.dataSource.refresh();
            }, 450);
        }
    }
})();