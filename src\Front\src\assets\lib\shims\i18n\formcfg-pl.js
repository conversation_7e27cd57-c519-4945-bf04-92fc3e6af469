webshims.validityMessages.pl={typeMismatch:{defaultMessage:"Wprowad\u017a poprawn\u0105 warto\u015b\u0107.",email:"Wprowad\u017a poprawny adres e-mail.",url:"Wprowad\u017a poprawny adres URL."},badInput:{defaultMessage:"Wprowad\u017a poprawn\u0105 warto\u015b\u0107.",number:"Wprowad\u017a numer.",date:"Wprowad\u017a dat\u0119.",time:"Wprowad\u017a czas.",range:"Niepoprawny zakres.",month:"Wprowad\u017a poprawny miesi\u0105c.","datetime-local":"Wprowad\u017a dat\u0119 i czas."},rangeUnderflow:{defaultMessage:"Warto\u015b\u0107 musi by\u0107 wi\u0119ksza lub r\xf3wna {%min}.",date:"Warto\u015b\u0107 musi by\u0107 wi\u0119ksza lub r\xf3wna {%min}.",time:"Warto\u015b\u0107 musi by\u0107 wi\u0119ksza lub r\xf3wna {%min}.","datetime-local":"Warto\u015b\u0107 musi by\u0107 wi\u0119ksza lub r\xf3wna {%min}.",month:"Warto\u015b\u0107 musi by\u0107 wi\u0119ksza lub r\xf3wna {%min}."},rangeOverflow:{defaultMessage:"Warto\u015b\u0107 musi by\u0107 mniejsza lub r\xf3wna {%max}.",date:"Warto\u015b\u0107 musi by\u0107 mniejsza lub r\xf3wna {%max}.",time:"Warto\u015b\u0107 musi by\u0107 mniejsza lub r\xf3wna {%max}.","datetime-local":"Warto\u015b\u0107 musi by\u0107 mniejsza lub r\xf3wna {%max}.",month:"Warto\u015b\u0107 musi by\u0107 mniejsza lub r\xf3wna {%max}."},stepMismatch:"Nieprawid\u0142owe dane.",tooLong:"Mo\u017cna wpisa\u0107 maksymalnie {%maxlength} znaki(\xf3w). Wpisano {%valueLen}.",patternMismatch:"Niew\u0142a\u015bciwe dane. {%title}",valueMissing:{defaultMessage:"Prosz\u0119 wype\u0142ni\u0107 pole.",checkbox:"Zaznacz to pole je\u015bli chcesz przej\u015b\u0107 dalej.",select:"Wybierz opcj\u0119..",radio:"Zaznacz opcj\u0119."}},webshims.formcfg.pl={numberFormat:{".":".",",":","},numberSigns:".-",dateSigns:"-",timeSigns:":. ",dFormat:"-",patterns:{d:"yy-mm-dd"},month:{currentText:"Bie\u017c\u0105cy miesi\u0105c"},week:{currentText:"Bie\u017c\u0105cy tydzie\u0144"},date:{closeText:"Ok",clear:"Czy\u015b\u0107",prevText:"Poprzedni",nextText:"Nast\u0119pny",currentText:"Dzi\u015b",monthNames:["Stycze\u0144","Luty","Marzec","Kwiece\u0144","Maj","Czerwiec","Lipiec","Sierpie\u0144","Wrzesie\u0144","Pa\u017adziernik","Listopad","Grudzie\u0144"],monthNamesShort:["Sty","Lut","Mar","Kwi","Maj","Cze","Lip","Sie","Wrz","Pa\u017a","Lis","Gru"],dayNames:["Niedziela","Poniedzia\u0142ek","Wtorek","\u015aroda","Czwartek","Pi\u0105tek","Sobota"],dayNamesShort:["Nie","Pon","Wto","\u015aro","Czw","Pi\u0105","Sob"],dayNamesMin:["Nd","Pn","Wt","\u015ar","Cz","Pt","So"],weekHeader:"Tdz",firstDay:1,isRTL:!1,showMonthAfterYear:!0,yearSuffix:""}};