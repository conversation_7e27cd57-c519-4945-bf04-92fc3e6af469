﻿(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('CentralMensagensController', CentralMensagensController);

        CentralMensagensController.inject = [
        'BaseService',
        '$rootScope',
        'toastr',
        '$scope',
        'PersistentDataService',
        '$timeout',
        '$state',
        'PERFIL_ADMINISTRADOR'
    ];

    function CentralMensagensController(
        BaseService,
        $rootScope,
        toastr,
        $scope,
        PersistentDataService,
        $timeout,
        $state,
        PERFIL_ADMINISTRADOR) {

        var vm = this;

        vm.mensagem = '';

        vm.headerItems = [{
            name: 'Cadastro<PERSON>'
        }, {
            name: 'Central de mensagens'
        }];

        vm.enumStatus = [
            { id: 1, descricao: 'Mensagens tratadas' },
            { id: 0, descricao: 'Mensagens não tratadas' },
            { id: 100, descricao: 'Todas' }
        ]

        vm.status = 100;

        vm.gridOptions = {
            data: [],  
            onRegisterApi: BaseService.dataGrid.defaultOnRegisterApi,
            dataSource: {
                url: "Mensagem/ConsultarGridMensagem",
                params: function () {
                    return {
                        status: vm.status,
                        mensagem: vm.mensagem
                    }
                },
            },
            columnDefs: [{
                name: 'Ações',
                width: 80,
                cellTemplate: '<div ng-if="!row.groupHeader" class="ui-grid-cell-contents" title="TOOLTIP">\
                                    <button ng-disabled="row.entity.ativo===0" tooltip-placement="right" uib-tooltip="Editar" type="button" ui-sref="central-mensagens.central-mensagens-crud({link: row.entity.id})"\
                                        ng-class="{ \'btn btn-xs btn-info\': true }">\
                                        <i class="fa fa-edit"></i>\
                                    </button>\
                                    <button ng-show="row.entity.codigoAplicacao!=2" type="button" tooltip-placement="right" uib-tooltip-html="row.entity.ativo===0 ? \'Ativar\' : \'Inativar\'"\
                                        ng-click="grid.appScope.vm.alterarStatus(row.entity.id, row.entity.ativo)"\
                                        ng-class="row.entity.ativo===1 ? \'btn btn-xs btn-success\' : \'btn btn-xs btn-danger\'">\
                                            <i ng-class="row.entity.ativo===1 ? \'fa fa-check\' : \'fa fa-times\'"></i>\
                                    </button>\
                                </div>'
            }, {
                name: 'Código',
                width: 75,
                type: 'number',
                primaryKey: true,
                field: 'id'
            },{
                name: 'Mensagem origem',
                displayName: 'Mensagem origem',
                width: 400,
                type: 'text',
                field: 'textoMensagemOriginal',
                enableFiltering: false
            }, {
                name: 'Código da aplicação',
                displayName: 'Código da aplicação',
                width: 135,
                field: 'codigoAplicacao',
                enum: true,
                cellTemplate: '<div class="ui-grid-cell-contents" align="left">\
                                     <p ng-show="row.entity.codigoAplicacao == 0"> Posto </p>\
                                     <p ng-show="row.entity.codigoAplicacao == 1"> CIOT </p>\
                                     <p ng-show="row.entity.codigoAplicacao == 2"> Dock </p>\
                               </div>',
                enumTipo: 'ECodigoAplicacao'
            }, {
                name: 'Tipo mensagem',
                displayName: 'Tipo mensagem',
                width: '*',
                minWidth: 135,
                field: 'tipoMensagem',
                enum: true,
                enumTipo: 'ETipoMensagem',
                cellTemplate: '<div class="ui-grid-cell-contents" align="left">\
                                     <p ng-show="row.entity.tipoMensagem == 0"> Texto </p>\
                                     <p ng-show="row.entity.tipoMensagem == 1"> Imagem </p>\
                               </div>',
            }, {
                name: 'Mensagem traduzida',
                displayName: 'Mensagem traduzida',
                width: 400,
                type: 'text',
                field: 'textoMensagemPadrao',
                enableFiltering: false
            }, {
                name: 'Mensagem retorno cliente',
                displayName: 'Mensagem retorno cliente',
                width: 400,
                type: 'text',
                field: 'textoMensagem',
                enableFiltering: false
            }, {
                name: 'Status',
                displayName: 'Status',
                width: 150,
                enum: true,
                field: 'mensagemTratada',
                cellTemplate: '<div class="ui-grid-cell-contents" align="left">\
                                        <p ng-show="row.entity.mensagemTratada === 0"> Mensagem não tratada </p>\
                                        <p ng-show="row.entity.mensagemTratada === 1"> Mensagem tratada </p>\
                                   </div>',
                enumTipo: 'EMensagensTratadasNaoTratadas',
                enableFiltering: false
            },{
                name: 'Data inícial',
                displayName: 'Data inícial',
                width: '*',
                minWidth: 110,
                field: 'dataInicioMensagem',
                enableFiltering: false
            }, {
                name: 'Data final',
                displayName: 'Data final',
                width: '*',
                minWidth: 110,
                field: 'dataFimMensagem',
                enableFiltering: false
            },{
                name: 'Descrição da mensagem',
                displayName: 'Descrição da mensagem',
                width: 270,
                type: 'text',
                field: 'descricaoMensagem'
            }]
        };

        vm.alterarStatus = function (id) {
            BaseService.post('Mensagem', 'AlterarStatus', {
                id: id
            }).then(function (response) {
                if (!response) {
                    toastr.error('Erro de comunicação com o servidor, tente novamente em alguns minutos.')
                    return
                }

                if (!response.success) {
                    toastr.error(response.message)
                    return
                }

                toastr.success(response.message)

                vm.gridOptions.dataSource.refresh();
            });
        };

        vm.atualizaTela = function () {
            vm.gridOptions.dataSource.refresh();
        }
    }
})();