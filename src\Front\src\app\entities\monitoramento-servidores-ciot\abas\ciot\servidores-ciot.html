<style>
  .full-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9999; /* ou um valor maior do que o z-index de outros elementos na página */
} 
</style>
<div id="ServidoresCiotController" ng-controller="ServidoresCiotController as vm">
  <div class="ibox-content">
    <div class="pull-right">
        <button class="btn btn-labeled btn-default" style="pointer-events: none">
          <span class="pl-5 ">Timer: {{ vm.counter | secondsToMinutes }}</span>
        </button>
        <button ng-if="!vm.isFullScreen" tooltip-placement="top" ng-click="vm.atualizaTela();" uib-tooltip="Atualizar" type='button' class="btn btn-labeled btn-default">
            <i class="fa fa-refresh"></i>
            <span class="pl-5 ">Atualizar</span>
        </button>
        <button ng-if="!vm.isFullScreen" tooltip-placement="top" ui-sref="monitoramento-servidores-ciot.servidor-ciot-crud({link: 'novo'})" uib-tooltip="Cadastrar " type='button'
            class="btn btn-labeled btn-primary ">
            <span class="btn-label text-right">
                <i class="fa fa-plus"></i>
            </span>
            <span class="pl-5">Novo</span>
        </button>
        <button ng-if="!vm.isFullScreen" class="btn btn-primary">Expandir tela</button>
    </div>
    <div class="row"></div><br>
    <div ui-grid="vm.gridOptions" ng-style="{height: vm.gridOptions.getGridHeight()}" class="grid" style="width: 100%;" ui-grid-pinning ui-grid-save-state
        ui-grid-pagination ui-grid-auto-resize ui-grid-resize-columns ui-grid-grouping>
    </div>
  </div> 
</div>