<div>
    <form name="formPainelCiot" role="form" novalidate ng-submit="vm.retificar();" show-validation>
        <div class="modal-header">
            <button type="button" class="close" ng-click="$dismiss()" aria-label="Close">
                <em class="fa fa-times"></em>
            </button>
            <h3 class="modal-title" id="modal-title">Retificar CIOT</h3>
        </div>
        <div class="modal-body bd-example-modal-xl">
            <div class="ibox-content">
                <div class="row">
                    <div class="col-sm-12 col-md-12 col-lg-12">
                        <div class="row">
                            <div class="row">
                                <div class="text-left">
                                    <div class="form-header">
                                        <consulta-padrao-modal directivesizes="'col-sm-6 col-md-6 col-lg-6'"
                                            labelsize="'col-sm-3 col-md-3 col-lg-3 control-label fixLeftLabel'"
                                            tabledefinition="vm.consultaVeiculo" idname="Veiculo" idmodel="Veiculo"
                                            label="'Veículo:'" function="vm.cadastrarVeiculo" ngshowadd="true"
                                            placeholder="'Selecione um veículo'"
                                            required-message="'Veículo é obrigatório'">
                                        </consulta-padrao-modal>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <div class="form-header" style="width: 98%;">
                                        <button type="button" ng-disabled="vm.disabledFields()"
                                            class="mr-5 btn-labeled btn btn-info" ng-click="vm.adicionarVeiculo()">
                                            <i class="fas fa-arrow-down"></i>
                                            <span class="pl-5">Inserir Veículo</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <br />
                            <div class="table-responsive">
                                <table class="table table-bordered table-hover">
                                    <thead>
                                        <tr>
                                            <th width="25%">Placa</th>
                                            <th width="25%">RNTRC</th>
                                            <th width="5%">Ações</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr ng-repeat="veiculo in vm.veiculosList">
                                            <td>{{veiculo.placa}}</td>
                                            <td>{{vm.rntrc}}</td>
                                            <td class="text-center" style="vertical-align: middle">
                                                <button type="button" ng-disabled="vm.disabledFields()"
                                                    uib-tooltip="Remover" class="btn btn-xs btn-danger"
                                                    ng-click="vm.removerVeiculo(veiculo)">
                                                    <i class="fa fa-trash-o"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button type="submit" ng-disabled="vm.veiculosList.length == 0 || vm.saving" class="btn btn-success">
                Retificar
            </button>
        </div>
    </form>
</div>