(function () {
    'use strict';

    angular.module('bbcWeb.painel-pagamento-abastecimento.state', []).config(routeConfig);

    function routeConfig($stateProvider) {
        $stateProvider
            .state('painel-pagamento-abastecimento', {
                url: "/painel-pagamento-abastecimento",
                abstract: true,
                templateUrl: "app/layout/content.html"})
            .state('painel-pagamento-abastecimento.index', {
                url: '/index',
                templateUrl: 'app/entities/painel-pagamento-abastecimento/painel-pagamento-abastecimento.html'});
    }
})();