<div class="form-horizontal">
    <hr/>
    <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
        <div class="row">
            <div class="col-xs-12 col-md-4">
                <div class="form-group">
                    <label class="col-xs-12 col-md-4 control-label">Contas:</label>
                    <div class="input-group col-xs-12 col-md-4">
                        <ui-select name="Conta" ng-model="vm.conta.contas" ats-ui-select-validator>
                            <ui-select-match>
                                <span>{{$select.selected.descricao}}</span>
                            </ui-select-match>
                            <ui-select-choices repeat="ex.id as ex in vm.contas | filter: $select.search">
                                <div ng-bind-html="ex.descricao | highlight: $select.search"></div>
                            </ui-select-choices>
                        </ui-select>
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-4 col-lg-4">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-12 col-md-4 control-label">Tipo de Extrato:</label>
                    <div class="input-group col-xs-12 col-sm-12 col-md-8 col-lg-6">
                        <ui-select name="Conta" ng-model="vm.tipoExtratoSelected" ats-ui-select-validator>
                            <ui-select-match>
                                <span>{{$select.selected.descricao}}</span>
                            </ui-select-match>
                            <ui-select-choices repeat="ex.id as ex in vm.tipoExtrato | filter: $select.search">
                                <div ng-bind-html="ex.descricao | highlight: $select.search"></div>
                            </ui-select-choices>
                        </ui-select>
                    </div>
                </div>
            </div>

            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-4">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-12 col-md-4 col-lg-3 control-label">Saldo:</label>
                    <div class="input-group col-xs-12 col-sm-12 col-md-4 col-lg-6">
                        <input type="text" disabled="true" ng-model="vm.saldo" class="form-control" />
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-xs-12 col-md-4">
                <div class="form-group">
                    <label class="col-xs-12 col-md-4 control-label pull-left pt-10"
                        style="padding-top: 10px;">Período:</label>
                    <div class="col-xs-12 col-md-8 input-group">
                        <input date-range-picker class="form-control date-picker" ui-date-mask="DD/MM/YYYY - DD/MM/YYYY"
                            type="text" ng-model="vm.date" options="vm.dateOptions" id="periodoDatePicker" />
                    </div>
                </div>
            </div>
        </div>

            <div class="row"></div>
            <hr>
            <div ui-grid="vm.gridOptionsExtrato" ng-style="{height: vm.gridOptionsExtrato.getGridHeight()}" class="grid" style="width: 100%;"
                 ui-grid-pinning ui-grid-save-state ui-grid-pagination ui-grid-auto-resize ui-grid-resize-columns
                 ui-grid-grouping>
            </div>

        <div class="pull-right mt-15" style="padding-bottom: 20px; padding-top: 10px;">
            <button type="button" ng-disabled="vm.desabilitarBtnRelatorio" class="btn btn-labeled btn-primary"
                ng-click="vm.abrirModalRelatorio('gridOptionsExtrato', vm)">
                <i class="fa fa-file-pdf-o"></i> Exportar Relatório
            </button>
        </div>
        <div id="exportable-xls">
            <table style="font-family:Arial, Helvetica, sans-serif" ng-show="false" id="exportable"
                   class="table table-bordered" width="100%">
                <thead>
                <tr>
                    <th style="text-align: left"
                        ng-repeat="option in vm.modalRelatorioOptions"
                        ng-if="option.enabled && option.field">
                        {{option.name}}
                    </th>
                </tr>
                </thead>
                <tbody>
                <tr style="text-align: left"
                    ng-repeat="item in vm.extratoRelatorio">
                    <td ng-repeat="option in vm.modalRelatorioOptions"
                        ng-if="option.enabled && option.field">
                        {{option.pipe ? ((item[option.field]) == 1 ? "Crédito" : "Débito") : (item[option.field])}}
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>