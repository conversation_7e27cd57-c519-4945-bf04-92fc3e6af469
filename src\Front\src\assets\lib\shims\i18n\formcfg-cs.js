webshims.validityMessages.cs={typeMismatch:{defaultMessage:"Pros\xedm vlo\u017ete platnou hodnotu.",email:"Pros\xedm vlo\u017ete emailovou adresu.",url:"Pros\xedm vlo\u017ete URL."},badInput:{defaultMessage:"Pros\xedm vlo\u017ete platnou hodnotu.",number:"Pros\xedm vlo\u017ete \u010d\xedslo.",date:"Pros\xedm vlo\u017ete datum.",time:"Pros\xedm vlo\u017ete \u010das.",range:"Neplatn\xe1 vstupn\xed hodnota.",month:"Pros\xedm vlo\u017ete platnou hodnotu.","datetime-local":"Pros\xedm vlo\u017ete datum a \u010das."},rangeUnderflow:{defaultMessage:"Hodnota mus\xed b\xfdt v\u011bt\u0161\xed nebo rovna {%min}.",date:"Datum mus\xed b\xfdt od {%min}.",time:"\u010cas mus\xed b\xfdt od {%min}.","datetime-local":"Datum a \u010das mus\xed b\xfdt od {%min}.",month:"M\u011bs\xedc mus\xed b\xfdt od {%min}."},rangeOverflow:{defaultMessage:"Hodnota mus\xed b\xfdt v\u011bt\u0161\xed nebo rovna {%mas}.",date:"Datum mus\xed b\xfdt od {%mas}.",time:"\u010cas mus\xed b\xfdt od {%mas}.","datetime-local":"Datum a \u010das mus\xed b\xfdt od {%mas}.",month:"M\u011bs\xedc mus\xed b\xfdt od {%mas}."},stepMismatch:"Neplatn\xe1 vstupn\xed hodnota.",tooLong:"Maxim\xe1ln\u011b m\u016f\u017eete vlo\u017eit {%maxlength} znak\u016f. Zadali jste {%valueLen}.",patternMismatch:"Neplatn\xe1 vstupn\xed hodnota. {%title}",valueMissing:{defaultMessage:"Pros\xedm vypl\u0148te toto pole",checkbox:"Pros\xedm za\u0161krtn\u011bte toto pol\xed\u010dko, pokud chcete pokra\u010dovat.",select:"Pros\xedm zvolte mo\u017enost.",radio:"Pros\xedm zvolte mo\u017enost."}},webshims.formcfg.cs={numberFormat:{".":",",",":" "},numberSigns:".-",dateSigns:".",timeSigns:":. ",dFormat:".",patterns:{d:"dd.mm.yy"},month:{currentText:"Tento m\u011bs\xedc"},week:{currentText:"Tento t\xfdden"},time:{currentText:"Nyn\xed"},date:{closeText:"Hotovo",clear:"Smazat",prevText:"P\u0159edchoz\xed",nextText:"Dal\u0161\xed",currentText:"Dne\u0161ek",monthNames:["Leden","\xdanor","B\u0159ezen","Duben","Kv\u011bten","\u010cerven","\u010cervenec","Srpen","Z\xe1\u0159\xed","\u0158\xedjen","Listopad","Prosinec"],monthNamesShort:["Led","\xdan","B\u0159e","Dub","Kv\u011b","\u010cer","\u010cec","Srp","Z\xe1\u0159","\u0158\xedj","Lis","Pro"],dayNames:["Ned\u011ble","Pond\u011bl\xed","\xdater\xfd","St\u0159eda","\u010ctvrtek","P\xe1tek","Sobota"],dayNamesShort:["NE","PO","\xdaT","ST","\u010cT","P\xc1","SO"],dayNamesMin:["NE","PO","\xdaT","ST","\u010cT","P\xc1","SO"],weekHeader:"T\xfdden",firstDay:1,isRTL:!1,showMonthAfterYear:!1,yearSuffix:""}};