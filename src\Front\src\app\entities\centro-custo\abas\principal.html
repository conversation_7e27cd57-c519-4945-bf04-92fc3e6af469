<div class="form-horizontal">
    <hr />
    <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label">Código:</label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <input type="text" ng-model="vm.centroCusto.id" class="form-control" disabled
                            value="{{vm.isNew()}}" />
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-5 col-lg-3 control-label">
                        <span class="text-danger mr-5">*</span>Descrição:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-7 col-lg-9">
                        <input type="text" required ng-model="vm.centroCusto.descricao"
                                required-message="'Descrição é obrigatória'"
                                ng-keypress="vm.bloquearCaracteresIndesejados($event)"
                                maxlength="200" name="descricao" class="form-control" />
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-5 col-lg-3 control-label">
                        <span class="text-danger mr-5">*</span>Código externo:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-7 col-lg-9">
                        <input type="text" required ng-model="vm.centroCusto.codigoExterno"
                            required-message="'Código externo é obrigatório'"
                            maxlength="100" name="codigoexterno" class="form-control" />
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-5 col-lg-3 control-label">
                        <span class="text-danger mr-5">*</span>Centro de custo:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-7 col-lg-9">
                        <input type="text" required ng-model="vm.centroCusto.codigoCentroCusto"
                            required-message="'É necessário informar uma despesa'" maxlength="50" name="CentroCusto" class="form-control" ng-keypress="vm.bloquearCaracteresIndesejados($event)" />
                    </div>
                </div>
            </div>
            <consulta-padrao-modal 
                idname="EmpresaUser" 
                idmodel="EmpresaUser" ng-show="vm.isAdmin()" 
                ng-disabled="vm.disabledFields()" 
                tabledefinition="vm.consultaEmpresa" 
                label="'Empresa:'" placeholder="'Selecione uma Empresa'" 
                required-message="'Empresa é obrigatório'" ng-required="true">
            </consulta-padrao-modal>
            <consulta-padrao-modal 
                idname="Filial" 
                idmodel="Filial"
                ng-disabled="true"
                tabledefinition="vm.consultaFilial" 
                label="'Filial:'" placeholder="'Selecione uma Filial'" 
                required-message="'Filial é obrigatório'" ng-required="false" ng-disabled="true">
            </consulta-padrao-modal>
        </div>
    </div>
</div>