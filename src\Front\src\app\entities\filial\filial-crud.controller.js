(function () {
    'use strict';

    angular.module('bbcWeb').controller('FilialCrudController', FilialCrudController);

    FilialCrudController.$inject = ['toastr', '$rootScope', 'BaseService', '$state', '$stateParams', '$window', 'PERFIL_ADMINISTRADOR', '$scope', '$timeout', 'PersistentDataService', 'DefaultsService', '$uibModal'];

    function FilialCrudController(
        toastr, $rootScope,
        BaseService, $state,
        $stateParams, $window,
        PERFIL_ADMINISTRADOR,
        $scope, $timeout,
        PersistentDataService,
        DefaultsService,
        $uibModal
    ) {
        var vm = this;
        vm.filial = {};        
        vm.listaCentroCusto = {};

        vm.filial.filialCentroCusto = [];
        vm.menusPai = [];
        vm.listaCentroCusto.listaCentroCustos = [];


        vm.estados = [];
        vm.cidades = [];
        vm.estadosDisabled = true;
        vm.cidadesDisabled = true;

        vm.headerItems = [{
            name: 'Cada<PERSON><PERSON>'
        }, {
            name: 'Filiais',
            link: 'filial.index'
        }, {
            name: $stateParams.link == 'novo' ? 'Novo' : 'Editar'
        }];

        var selfScope = PersistentDataService.get('FilialCrudController');


        function carregarFuncoesIniciais() {
            carregarEstados();
            carregaCentroCusto();

        };

        if ($stateParams.link == 'novo')
            vm.filial.Id = 'Auto';

        vm.isNew = function () {
            return $stateParams.link == 'novo';
        };

        vm.load = function () {
            carregarFuncoesIniciais();
            carregaCentroCusto();
        };

        vm.loadEdit = function (id) {
            BaseService.get('filial', 'ConsultarPorId', {
                idFilial: id
            }).then(function (response) {
                if (!response.success) {
                    toastr.error(response.message);
                    return;
                } else {
                    carregarCidades(response.data.estadoId);
                    carregaCentroCusto();
                    vm.filial = response.data;
                    vm.filial.Id = response.data.id;
                    vm.filial.NomeFantasia = response.data.nomeFantasia;
                    vm.filial.RazaoSocial = response.data.razaoSocial;
                    vm.filial.Cnpj = response.data.cnpj;
                    vm.filial.Email = response.data.email;
                    vm.filial.Celular = response.data.celular;
                    vm.filial.Operacao = response.data.operacao;
                    vm.filial.Telefone = response.data.telefone;
                    vm.filial.Cep = response.data.cep;
                    vm.filial.EstadoId = response.data.estadoId;
                    vm.filial.EmpresaId = response.data.empresaId;
                    vm.filial.CidadeId = response.data.cidadeId;
                    vm.filial.Endereco = response.data.endereco;
                    vm.filial.Bairro = response.data.bairro;
                    vm.filial.EnderecoNumero = response.data.enderecoNumero;
                    vm.filial.Complemento = response.data.complemento;
                    vm.filial.Faturar = response.data.faturar;
                    vm.consultaEmpresa.selectedValue = response.data.empresaId;
                    vm.consultaEmpresa.selectedText = response.data.empresa.nomeFantasia; 
                    if (response.data.horarioLimiteAbastecimentoInicio != null) {
                        vm.filial.horarioLimiteAbastecimentoInicio = new Date('2022-02-02 '+ response.data.horarioLimiteAbastecimentoInicio);
                        vm.filial.horarioLimiteAbastecimentoFim = new Date('2022-02-02 '+ response.data.horarioLimiteAbastecimentoFim);
                    }

                    for(var x = 0; x < vm.filial.filialCentroCusto.length; x++){
            
                        vm.listaCentroCusto.listaCentroCustos.push(vm.filial.filialCentroCusto[x].centroCustoId)
                    }
                }
            });
        };

        function carregarEmpresas() {
            vm.consultaEmpresa = {
                columnDefs: [{
                    name: 'Código',
                    field: 'id',
                    width: 80,
                    type: 'number',
                    primaryKey: true
                }, {
                    name: 'Nome Fantasia',
                    field: 'nomeFantasia',
                    width: '*',
                    minWidth: 150
                }, {
                    name: 'Razão Social',
                    field: 'razaoSocial',
                    width: '*',
                    minWidth: 150
                }, {
                    name: 'Email',
                    field: 'email',
                    width: '*',
                    minWidth: 150
                }],
                desiredValue: 'id',
                desiredText: 'nomeFantasia',
                url: 'Empresa/ConsultarGridEmpresaCombo',
                paramsMethod: function () {
                    return {}
                },
            };
        }

        function carregaCentroCusto(){
            vm.consultaCentroCusto = {
                columnDefs: [{
                    name: 'Código',
                    field: 'id',
                    width: 80,
                    type: 'number',
                    primaryKey: true
                }, {
                    name: 'Descrição',
                    field: 'descricao',
                    width: '*',
                    minWidth: 150
                }],
                desiredValue: 'id',
                desiredText: 'descricao',
                url: 'CentroCusto/ConsultarGridCentroCusto',
                paramsMethod: function () {
                    return {
                        filtraCentroCustoSemFilial: true
                    }
                },
            };
        }

        vm.adicionarCentroCusto = function () {
            var permiteAdicionar = false;

            if (!vm.consultaCentroCusto.selectedText)
                return toastr.error("Nenhum Centro de custo foi selecionado.");
           
            var objetosValidos = _.filter(vm.filial.filialCentroCusto, function (v) {
                return v.centroCustoId === vm.consultaCentroCusto.selectedValue;
            });

            if (objetosValidos.length > 0) {
                toastr.error("Este Centro de custo já foi adicionado.");
                return;
            }

            if (vm.consultaCentroCusto.selectedValue != undefined && vm.consultaCentroCusto.selectedText != "") {
                permiteAdicionar = true;
            }

            if (permiteAdicionar) {
                var centroCusto = {
                    descricao: vm.consultaCentroCusto.selectedText,
                    centroCustoId: vm.consultaCentroCusto.selectedValue
                }
                if(vm.filial.filialCentroCusto == undefined)
                {
                    vm.filial.filialCentroCusto = [];
                }
                vm.filial.filialCentroCusto.push(centroCusto);
                vm.listaCentroCusto.listaCentroCustos.push(centroCusto.centroCustoId);
                BaseService.post('CentroCusto', "AlterarVinculoFilial", {
                    id: vm.consultaCentroCusto.selectedValue,
                    filialId: vm.filial.id
                }).then(function (response) {
                    if (!response.success) {
                        toastr.error(response.message);
                        vm.removerCentroCusto(response.data);
                    }
                });
                vm.clearconsultaCentroCusto();
            }
        };

        vm.clearconsultaCentroCusto = function () {
            vm.consultaCentroCusto.selectedEntity = undefined;
            vm.consultaCentroCusto.selectedValue = undefined;
            vm.consultaCentroCusto.selectedText = "";
        };

        vm.removerCentroCusto = function (centroCusto) {
            for (var i = 0; i < vm.filial.filialCentroCusto.length; i++) {
                if (vm.filial.filialCentroCusto[i].id == centroCusto.id) {
                    var index = vm.filial.filialCentroCusto.indexOf((vm.filial.filialCentroCusto[i]));
                    vm.filial.filialCentroCusto.splice(index, 1)
                    vm.listaCentroCusto.listaCentroCustos.splice(index, 1)

                } else {
                    if (vm.filial.filialCentroCusto[i] == centroCusto) {
                        var index = vm.filial.filialCentroCusto.indexOf((vm.filial.filialCentroCusto[i]));
                        vm.filial.filialCentroCusto.splice(index, 1)
                        vm.listaCentroCusto.listaCentroCustos.splice(index, 1)

                    }
                }   
            }

            if (vm.filial.filialCentroCusto.length < 1) {
                vm.consultaCentroCusto.selectedEntity = undefined;
                vm.consultaCentroCusto.selectedValue = undefined;
                vm.consultaCentroCusto.selectedText = undefined;
            }
            BaseService.post('CentroCusto', "AlterarVinculoFilial", {
                id: centroCusto.id,
                filialId: null
            }).then(function (response) {
            });
        };

        vm.buscarEndereco = function (cep) {
            if (cep) {
                limparEndereco();
                BaseService.getEndereco(cep).then(function (response) {
                    if (response && response.erro) {
                        toastr.warning("Endereço não localizado.");
                    }

                    carregarEstados();

                    $timeout(function () {
                        var estado = _.find(vm.estados, ['sigla', response.uf]);

                        if (angular.isDefined(estado))

                            vm.filial.EstadoId = estado.id;
                        carregarCidades(vm.filial.EstadoId);
                        $timeout(function () {
                            var cidade = _.find(vm.cidades, ['descricao', response.localidade]);
                            if (angular.isDefined(cidade))
                                vm.filial.CidadeId = cidade.id;
                        }, 1500);
                    }, 1500);

                    $timeout(function () {
                        if (angular.isDefined(response.logradouro))
                            vm.filial.Endereco = response.logradouro;
                    }, 1500);

                    $timeout(function () {
                        if (angular.isDefined(response.bairro))
                            vm.filial.Bairro = response.bairro;
                    }, 1500);
                });
            }
        };

        function limparEndereco() {
            vm.filial.EstadoId = null;
            vm.filial.CidadeId = null;
            vm.estados = null;
            vm.cidades = null;
            vm.filial.Endereco = null;
            vm.filial.Bairro = null;
            vm.filial.EnderecoNumero = null;
            vm.filial.Complemento = null;
        };

        vm.estadoChange = function (estadoId) {
            vm.cidades = null;
            vm.cidadesDisabled = true;
            vm.filial.CidadeId = null;
            carregarCidades(estadoId);
        }

        function carregarEstados() {
            BaseService.get("Endereco", "ConsultarEstados")
                .then(function (response) {
                    if (response.success) {
                        vm.estados = response.data;
                        vm.estadosDisabled = false;
                    }
                });
        };

        function carregarCidades(EstadoId) {
            BaseService.get("Endereco", "ConsultarCidadesPorEstado", {
                estadoId: EstadoId
            }).then(function (response) {
                if (response.success) {
                    vm.cidades = response.data;
                    vm.cidadesDisabled = false;
                }
            });
        };

        vm.save = function (form) {
            if (!form.$valid) {
                toastr.error('Todos os campos obrigatórios devem ser preenchidos corretamente com seus respectivos valores');
                return;
            }

            if ((vm.filial.horarioLimiteAbastecimentoInicio == null && vm.filial.horarioLimiteAbastecimentoFim != null) || (vm.filial.horarioLimiteAbastecimentoInicio != null && vm.filial.horarioLimiteAbastecimentoFim == null)) {
                toastr.error("Horário início ou fim de limite de abastecimento está nulo");
                return;
            }

            if (vm.isSaving === true) {
                return;
            }

            if (vm.isAdmin) {
                vm.filial.EmpresaId = vm.consultaEmpresa.selectedValue;
            }

            vm.filial.centroCustoId = vm.consultaCentroCusto.selectedValue;

            if ( vm.filial.Id == "Auto") {
                vm.filial.Id = "0";
            }

            vm.filial.Faturar = vm.filial.Faturar ? 1 : 0;

            vm.isSaving = true;
            if (vm.filial.horarioLimiteAbastecimentoInicio != null && typeof(vm.filial.horarioLimiteAbastecimentoInicio) != "string" 
            && vm.filial.horarioLimiteAbastecimentoFim != null && typeof(vm.filial.horarioLimiteAbastecimentoInicio) != "string") {
                vm.filial.horarioLimiteAbastecimentoInicio = vm.filial.horarioLimiteAbastecimentoInicio.toLocaleTimeString();
                vm.filial.horarioLimiteAbastecimentoFim = vm.filial.horarioLimiteAbastecimentoFim.toLocaleTimeString();
            }
            BaseService.post('Filial', 'Salvar', vm.filial).then(function (response) {
                vm.isSaving = false;
                if (response.success) {
                    toastr.success(response.message);
                    $state.go('filial.index');
                } else
                    toastr.error(response.message);
            });
        };

        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.administrador == true;
        };

        function init() {
            carregarEmpresas();
            carregarFuncoesIniciais();
        };


        vm.onClickAvancar = function (wizard) {
            wizard.go(wizard.getActivePosition() + 1);
        };

        vm.onClickVoltar = function (wizard) {

            var ativoIndex = wizard.getActivePosition();
            if (ativoIndex == 1)
                $state.go('filial.index');

            wizard.go(ativoIndex - 1);
        };

        init();

        $scope.$on('$stateChangeStart', function (arg1, toState) {
            if (toState.name === 'filial.index')
                PersistentDataService.remove('FilialCrudController');
            else
                PersistentDataService.store('FilialCrudController', vm, "Cadastro - Filial", null, "filial.filial-crud", vm.filial.id);
        });

        if (angular.isDefined(selfScope))
            angular.extend(vm, selfScope.data);
        else {
            if (!vm.isNew())
                vm.loadEdit($stateParams.link);
            else {
                vm.filial = 0;
                vm.modulos = 1;
            }
        }

        $timeout(function () {
            PersistentDataService.remove('FilialCrudController');
        }, 15);
    }
})();