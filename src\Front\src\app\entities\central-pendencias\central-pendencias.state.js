(function () {
    'use strict';

    angular.module('bbcWeb.central-pendencias.state', []).config(routeConfig);

    function routeConfig($urlRouterProvider, $stateProvider) {
        $stateProvider.state('central-pendencias', {
            abstract: true,
            url: "/central-pendencias",
            templateUrl: "app/layout/content.html"
        }).state('central-pendencias.index', {
            url: '/index',
            templateUrl: 'app/entities/central-pendencias/central-pendencias.html'
        }).state('central-pendencias.central-pendencias-crud', {
            url: '/:link',
            templateUrl: 'app/entities/central-pendencias/frete/central-pendencias-crud.html'
        })
        .state('central-pendencias.central-pendencias-vp-crud', {
            url: '/vale-pedagio/:link',
            templateUrl: 'app/entities/central-pendencias/vale-pedagio/central-pendencias-crud.html'
        });
    }
})();