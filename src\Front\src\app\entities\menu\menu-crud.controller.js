(function() {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('MenuCrudController', MenuCrudController);

    function MenuCrudController(toastr,
        BaseService,
        PersistentDataService,
        $scope,
        $stateParams,
        $state,
        $rootScope,
        $timeout) {
        var vm = this;

        vm.menu = {};
        
        vm.load = function () {
            carregarFuncoesIniciais();
        }

        vm.loadEdit = function (id) {
            vm.carregandoEdit = true;
            carregarFuncoesIniciais();

            // $timeout(function () {
            //     consultarPorId(id, function (autorizadoraEdit) {
            //         carregarEstados();
            //         carregarCidades(autorizadoraEdit.EstadoId);

            //         vm.autorizadora = autorizadoraEdit;
                    
            //     });
            // }, 1000);

            $timeout(function () {
                vm.carregandoEdit = false;
            }, 6000);
        }

        vm.save = function (form) {
            vm.loading = true;

            if (!form.$valid) {
                toastr.error('Todos os campos obrigatórios e/ou inválidos devem ser preenchidos corretamente com seus respectivos valores');
                return;
            }

            vm.saving = true;

            BaseService.post('Menu', 'Salvar', vm.menu).then(function (response) {
                if (response.success) {
                    toastr.success(response.message);
                    $state.go('menu.index');
                    vm.saving = false;
                } else {
                    toastr.error(response.message);
                    vm.saving = false;
                }
            });
        }

        vm.isNew = function () {
            return $stateParams.link == 'novo';
        };   
        
        function carregarFuncoesIniciais() {
            //carregarEstados();
        }

        function limparCadastro() {
            vm.menu.Id = null;
            vm.menu.Descricao = null;
            vm.modulo = null;
            vm.menu.MenuPai = null;
            vm.menu.isMenuPai = null;
        }

        var selfScope = PersistentDataService.get('MenuCrudController');

        if ($stateParams.link == 'novo')
            vm.menu.Id = 'Auto';

        if (angular.isDefined(selfScope))
            angular.extend(vm, selfScope.data);
        else
        if (!vm.isNew())
            vm.loadEdit($stateParams.link);
        else
            vm.load();
    }
})();