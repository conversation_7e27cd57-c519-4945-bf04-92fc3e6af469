(function () {
    'use strict';

    angular.module('bbcWeb')
        .constant('uiSelectConfig', {
            theme: 'bootstrap',
            searchEnabled: true,
            sortable: false,
            placeholder: '', // Empty by default, like HTML tag <select>
            refreshDelay: 1000, // In milliseconds
            closeOnSelect: true,
            skipFocusser: false,
            dropdownPosition: 'auto',
            removeSelected: true,
            resetSearchInput: true,
            generateId: function () {
                var latestId = 0;
                return latestId++;
            },
            appendToBody: true,
            spinnerEnabled: false,
            spinnerClass: 'glyphicon-refresh ui-select-spin'
        });

})();