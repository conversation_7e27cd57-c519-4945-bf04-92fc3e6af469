(function () {
    'use strict';

    angular.module('bbcWeb.auditoria-seguranca.state', []).config(routeConfig);

    function routeConfig($urlRouterProvider, $stateProvider) {
        $stateProvider.state('auditoria-seguranca', {
            abstract: true,
            url: "/auditoria-seguranca",
            templateUrl: "app/layout/content.html"
        }).state('auditoria-seguranca.index', {
            url: '/index',
            templateUrl: 'app/entities/auditoria-seguranca/auditoria-seguranca.html'
        });
    }
})();