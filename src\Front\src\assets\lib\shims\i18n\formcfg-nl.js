webshims.validityMessages.nl={typeMismatch:{defaultMessage:"Voer een geldige waarde in.",email:"Voer een geldig e-mailadres in.",url:"Voer een geldige website url in."},badInput:{defaultMessage:"Voer een geldige waarde in.",number:"Voer een nummer in.",date:"Voer een datum in.",time:"Voer een tijd in.",range:"Voer een bereik in.",month:"Voer een maand in.","datetime-local":"Voer een lokale datumtijd in."},rangeUnderflow:{defaultMessage:"Waarde moet groter dan of gelijk zijn aan {%min}.",date:"Datum moet op of na {%min} zijn.",time:"Tijd moet op of na {%min} zijn.","datetime-local":"Datumtijd moet op of na {%min} zijn.",month:"Maand moet op of na {%min} zijn."},rangeOverflow:{defaultMessage:"Waarde moet kleiner dan of gelijk zijn aan {%max}.",date:"Datum moet op of voor {%max} zijn.",time:"Tijd moet op of voor {%max} zijn.","datetime-local":"Waarde moet kleiner dan of gelijk zijn aan {%max}.",month:"Maand moet op of voor {%max} zijn."},stepMismatch:"Ongeldige invoer.",tooLong:"Voer maximaal {%maxlength} karakter(s) in. {%valueLen} is te lang.",tooShort:"Voer minimaal {%minlength} karakter(s) in. {%valueLen} is te kort.",patternMismatch:"Voer een waarde in met de gevraagde opmaak: {%title}.",valueMissing:{defaultMessage:"Vul dit veld in.",checkbox:"Vink dit vakje aan als u wilt doorgaan.",select:"Selecteer een item in de lijst.",radio:"Selecteer \xe9\xe9n van deze opties."}},webshims.formcfg.nl={numberFormat:{",":".",".":","},numberSigns:",",dateSigns:"-",timeSigns:":. ",dFormat:"-",patterns:{d:"dd-mm-yy"},month:{currentText:"Huidige maand"},time:{currentText:"Nu"},date:{closeText:"Sluiten",clear:"Wissen",prevText:"Vorige",nextText:"Volgende",currentText:"Vandaag",monthNames:["januari","februari","maart","april","mei","juni","juli","augustus","september","oktober","november","december"],monthNamesShort:["jan","feb","mrt","apr","mei","jun","jul","aug","sep","okt","nov","dec"],dayNames:["zondag","maandag","dinsdag","woensdag","donderdag","vrijdag","zaterdag"],dayNamesShort:["zon","maa","din","woe","don","vri","zat"],dayNamesMin:["zo","ma","di","wo","do","vr","za"],weekHeader:"Wk",firstDay:1,isRTL:!1,showMonthAfterYear:!1,yearSuffix:""}};