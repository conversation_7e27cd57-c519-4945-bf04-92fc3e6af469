<div class="form-horizontal">
    <hr />
    <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label">Código:</label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <input type="text" ng-model="vm.transportador.id" class="form-control" disabled
                            value="{{vm.isNew()}}" />
                    </div>
                </div>
            </div>
            
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-3 col-md-4 col-lg-3">
                        <span class="text-danger mr-5">*</span>{{vm.labelCpfCnpj}}:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <input type="text" ui-mask="{{vm.mascaraCpfCnpj}}" ng-disabled="!vm.isNew()" required
                            ng-model="vm.transportador.cpfCnpj" ng-blur="vm.onChangeCpfCnpj()" name="{{vm.labelCpfCnpj}}"
                            class="form-control" />
                    </div>
                </div>
            </div>

        </div>
        <div class="row">

            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-5 col-lg-3 control-label">
                        <span class="text-danger mr-5">*</span>Nome:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-7 col-lg-9">
                        <input type="text" required ng-model="vm.transportador.nome"
                            required-message="'Nome é obrigatório'"
                            maxlength="200" name="Nome" class="form-control"  />
                    </div>
                </div>
            </div>

            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-3 col-md-4 col-lg-3">
                        <span class="text-danger mr-5">*</span>Tipo de pessoa:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-9 col-lg-9">
                        <ui-select name="Tipo" ng-model="vm.transportador.tipoPessoa" ats-ui-select-validator
                            ng-disabled="!vm.isNew()" validate-on="blur"
                            ng-change="vm.tipoPessoaChange(vm.transportador.tipoPessoa); vm.transportador.cpfCnpj = null;"
                            required-message="'Tipo de pessoa é obrigatório'" required>
                            <ui-select-match>
                                <span>{{$select.selected.descricao}}</span>
                            </ui-select-match>
                            <ui-select-choices
                                repeat="ex.id as ex in vm.comboTipoPessoa.data | propsFilter: {descricao: $select.search}">
                                <div ng-bind-html="ex.descricao | highlight: $select.search"></div>
                            </ui-select-choices>
                        </ui-select>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-5 col-lg-3 control-label">
                        CIOT TAC Agregado:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-9 col-lg-9" style="padding-top: 5px !important; padding-right: 0px !important;">
                        <toggle-switch on-label="Sim" off-label="Não" ng-model="vm.transportador.ciotTacAgregado" class="switch">
                        </toggle-switch>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>