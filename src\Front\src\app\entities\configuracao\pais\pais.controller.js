(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', PaisController);

    PaisController.inject = [
        'BaseService',
        'toastr',
        '$scope',
        'PersistentDataService',
        '$timeout',
        '$state'
    ];

    function PaisController(
        BaseService,
        toastr,
        $scope,
        PersistentDataService,
        $timeout,
        $state
    ) {
        var vm = this;
        vm.headerItems = [{ name: 'Configuração' }, { name: 'País' }];

        vm.gridOptions = {
            data: [],
            onRegisterApi: BaseService.dataGrid.defaultOnRegisterApi,
            dataSource: {
                url: "PaisAts/ConsultarGrid",
                params: function () { return { nome: vm.nome, bacen: vm.bacen }; }
            },
            columnDefs: [{
                name: '<PERSON>ódigo',
                width: 80,
                primaryKey: true,
                field: 'IdPais'
            }, {
                name: 'Nome',
                field: 'Nome',
                width: '*',
                minWidth: 250,
                enableGrouping: false
            }, {
                name: 'Sigla',
                field: 'Sigla',
                width: '*',
                minWidth: 160,
                enableGrouping: false
            }, {
                name: '<PERSON><PERSON>õ<PERSON>',
                width: 80,
                cellTemplate: '<div ng-if="!row.groupHeader" class="ui-grid-cell-contents" title="TOOLTIP">\
                                    <button ng-disabled="!row.entity.Ativo" type="button" ui-sref="configuracao.pais-crud({link: row.entity.IdPais})"\
                                        ng-class="{ \'btn btn-xs btn-info\': true }">\
                                        <i class="fa fa-edit"></i>\
                                    </button>\
                                    <button type="button" ng-click="grid.appScope.vm.alterarStatus(row.entity.IdPais, row.entity.Ativo)" ng-class="row.entity.Ativo ? \'btn btn-xs btn-success\' : \'btn btn-xs btn-danger\'">\
                                        <i ng-class="row.entity.Ativo ? \'fa fa-check\' : \'fa fa-times\'"></i>\
                                    </button>\
                                </div>'
            }]
        };

        vm.alterarStatus = function (id, ativo) {
            BaseService.post('PaisAts', ativo ? "Inativar" : "Reativar", {
                IdPais: id
            }).then(function (response) {
                response.success ? toastr.success(response.message) : toastr.error(response.message);
                vm.gridOptions.dataSource.refresh();
            });
        };

        vm.refresh = function() {
            vm.gridOptions.dataSource.refresh()
        };

        // Controle de aba!!
        $scope.$on('$stateChangeStart', function () {
            PersistentDataService.store('PaisController', vm, "Listagem de países", "PaisCrudController", "configuracao.pais");
        });
        var selfScope = PersistentDataService.get('PaisController');
        var filho = PersistentDataService.get('PaisCrudController');
        if (angular.isDefined(filho)) {
            $timeout(function () {
                $state.go('configuracao.pais-crud', {
                    link: filho.data.Pais.IdPais > 0 ? 'editar' : 'novo'
                });
            }, 50);
        } else if (angular.isDefined(selfScope))
            angular.extend(vm, selfScope.data);
    }
})();