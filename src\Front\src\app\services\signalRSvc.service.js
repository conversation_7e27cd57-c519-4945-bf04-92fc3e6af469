(function () {
    'use strict';

    angular.module('bbcWeb').factory('SignalRSvc', SignalRSvc);

    SignalRSvc.$inject = ['$scope', '$http', '$rootSocpe', '$log'];

    function SignalRSvc($scope, $http, $rootScope, $log) {
        var svc = {
            registed: false,
            proxy: null,
            connect: function (hubName, onConnected) {
                // //Getting the connection object
                // var connection = $scope.hubConnection();

                // //Creating proxy
                // svc.proxy = connection.createHubProxy(hubName);

                // //Starting connection
                // connection.start().done(function () {
                //     if (angular.isFunction(onConnected)) onConnected(svc);
                // });
            },
            disconnect: function () {
                // svc.proxy.connection.hub.stop();
            },
            registerEvents: function (listeners, senders) {
                // if (svc.proxy == null) throw 'O socket não está conectado!';
                // else {
                //     if (angular.isArray(listeners)) {
                //         listeners.forEach(function (listener) {
                //             svc.proxy.on(listener.action, listener.callback);
                //         });
                //     } else
                //         $log.info('Socket conectado, porém sem listeners registrados');

                //     if (angular.isArray(senders)) {
                //         senders.forEach(function (sender) {
                //             svc[sender.action] = function (params) {
                //                 svc.proxy.invoke(sender.action, params);
                //             };
                //         });
                //     }
                // }
            },
            onHubConnected: function () {},
            //onError
            onError: function () {

            },
            onStateChanged: function () {

            }
        };

        return svc;
    }
})();

// svc.proxy.on('acceptGreet', function (message) {
//     //$rootScope.$emit("acceptGreet",message);
// });