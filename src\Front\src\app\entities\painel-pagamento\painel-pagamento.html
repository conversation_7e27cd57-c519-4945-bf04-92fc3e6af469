<div ng-controller="PainelPagamentoController as vm">
    <form-header items="vm.headerItems" head="'Painel de pagamento'" state="painel-pagamento">
    </form-header>
    <div class="wrapper-content animated fadeIn filter-position overflow-hidden">
        <div class="row">
            <div class="col-lg-12">
                <div class="ibox float-e-margins">
                    <div style="height: 38px;" class="ibox-title">
                        <h5>Painel de pagamento</h5>
                        <div ibox-tools></div>
                    </div>
                    <div class="ibox-content">
                        <div form-wizard steps="2">
                            <div class="form-wizard">
                                <ol class="row">
                                    <li ng-click="wizard.go(1); vm.atualizaTela()" class="fixLRpg col-sm-6" ng-class="{'active': vm.isAdmin() ? wizard.active(1) : wizard.active(2)}" ng-show="vm.isAdmin()">
                                        <h4>JSL</h4>
                                    </li>
                                    <li ng-click="!vm.isAdmin() ? undefined : wizard.go(2); vm.atualizaTelaBbc()" class="fixLRpg col-sm-{{vm.isAdmin() ? '6' : '12'}}" ng-class="{'active': vm.isAdmin() ? wizard.active(2) : wizard.active(1)}">
                                        <h4>BBC</h4>
                                    </li>
                                </ol>
                                <br/>
                            </div>
                            <div id="activateTab1" ng-show="vm.isAdmin() ? wizard.active(1) : wizard.active(2)">
                                <div ng-include="'app/entities/painel-pagamento/abas/pagamentos/pagamento-jsl.html'" class="form-horizontal"> </div>
                            </div>
                            <div id="activateTab2" ng-show="vm.isAdmin() ? wizard.active(2) : wizard.active(1)">
                                <div ng-include="'app/entities/painel-pagamento/abas/pagamentos/pagamento-bbc.html'" class="form-horizontal"> </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>