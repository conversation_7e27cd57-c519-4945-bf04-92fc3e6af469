<div ng-controller="RetencaoController as vm">
    <form-header items="vm.headerItems" head="'Retenção'" state="retencao">
    </form-header>
    <div class="wrapper-content animated fadeIn filter-position overflow-hidden">
        <div class="row">
            <div class="col-lg-12">
                <div class="ibox float-e-margins">
                    <div style="height: 38px;" class="ibox-title">
                        <h5>Retenção</h5>
                        <div ibox-tools></div>
                    </div>
                    <div class="ibox-content">
                        <div class="row">
                            <div>
                                <div class="col-xs-12 col-sm-12 col-md-7 col-lg-6">
                                    <label class="col-xs-12 col-sm-12 col-md-2 col-lg-2 control-label mt-5"
                                        style="text-align: left;">Período:</label>
                                    <div class="input-group col-xs-12 col-sm-12 col-md-10 col-lg-10">
                                        <input date-range-picker class="form-control date-picker" type="text"
                                            ng-model="vm.date" options="vm.dateOptions" />
                                        <span class="input-group-btn">
                                            <button type="button" class="btn btn-primary"
                                                ng-click="vm.atualizaTela();">Consultar</button>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row"></div>
                        <hr>
                        <div ui-grid="vm.gridOptions" ng-style="{height: vm.gridOptions.getGridHeight()}" class="grid" style="width: 100%;"
                            ui-grid-auto-fit-columns ui-grid-pagination ui-grid-resize-columns>
                        </div>
                        <div class="pull-right mt-15">
                            <button type="button" ng-disabled="vm.desabilitarBtnRelatorio"
                                class="btn btn-xs btn-primary" ng-click="vm.consultarDadosRelatorio(1)"><i
                                    class="fa fa-file-excel-o"></i> Exportar em Excel</button>
                            <button type="button" ng-disabled="vm.desabilitarBtnRelatorio"
                                class="btn btn-xs btn-primary" ng-click="vm.consultarDadosRelatorio(2)"><i
                                    class="fa fa-file-pdf-o"></i> Exportar em PDF</button>
                        </div>
                        <div class="row"></div>
                        <div id="exportable-xls">
                            <table style="font-family:Arial, Helvetica, sans-serif" ng-show="false" id="exportable"
                                class="table table-bordered" width="100%">
                                <thead>
                                    <tr>
                                        <th>Código</th>
                                        <th>Portador</th>
                                        <th>CPF/CNPJ Portador</th>
                                        <th>Pagamento</th>
                                        <th>Status</th>
                                        <th>Valor</th>
                                        <th>Data Integração</th>
                                        <th>Data Cadastro</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr ng-repeat="item in vm.retencaoRelatorio">
                                        <td>{{item.id}}</td>
                                        <td>{{item.nome}}</td>
                                        <td>{{item.cpfCnpj}}</td>
                                        <td>{{item.pagamentoId}}</td>
                                        <td>{{item.status}}</td>
                                        <td>{{item.valor}}</td>
                                        <td>{{item.dataIntegracao}}</td>
                                        <td>{{item.dataCadastro}}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>