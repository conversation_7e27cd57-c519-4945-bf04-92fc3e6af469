(function () {
    'use strict';

    angular.module('bbcWeb').controller('ConfiguracaoMonitoramentoCiotController', ConfiguracaoMonitoramentoCiotController);

    ConfiguracaoMonitoramentoCiotController.$inject = ['toastr', '$rootScope', 'BaseService', '$state', '$stateParams', '$scope', '$timeout', 'PersistentDataService'];

    function ConfiguracaoMonitoramentoCiotController(
        toastr,
        $rootScope,
        BaseService,
        $state,
        $stateParams,
        $scope,
        $timeout,
        PersistentDataService
    ) {
        var vm = this;
        vm.configuracaoMonitorametoCiot = {};

        vm.configuracaoMonitorametoCiot.obrigaValorFrete = 0;

        vm.headerItems = [{
            name: 'Cadastros'
        }, {
            name: 'Parâmetros gerais',
            link: 'parametros.index'
        }];

        vm.loadEdit = function () {
            BaseService.get('Parametros', 'ConsultaParametrosConfiguracaoMonitoramentoCiot', {})
                .then(function (response) {
                    if (!response) {
                        toastr.error('Erro de comunicação com o servidor, tente novamente em alguns minutos.');
                        return;
                    }

                    if (!response.success) {
                        toastr.error(response.message);
                        return;
                    }
                    
                    vm.configuracaoMonitorametoCiot = response.data;
                    setDataFim(response.data.dataInicioObrigaValorFrete);
                });
        };

        
        function setDataFim(data) {
            if (data != null) {
                vm.configuracaoMonitorametoCiot.dataInicioObrigaValorFrete = new Date(data);
            }
        }
        

        vm.checkForEmailEnd = function(event, tipo) {
            if (event.key === ' ' || event.key === 'Enter') {
                if (tipo === 'forcada') {
                    vm.configuracaoMonitorametoCiot.emailsNotificacaoInternaContingenciaForcada = processEmails(vm.configuracaoMonitorametoCiot.emailsNotificacaoInternaContingenciaForcada);
                } else if (tipo === 'automatica') {
                    vm.configuracaoMonitorametoCiot.emailsNotificacaoInternaContingenciaAutomatica = processEmails(vm.configuracaoMonitorametoCiot.emailsNotificacaoInternaContingenciaAutomatica);
                }
            }
        };
    
        function processEmails(emailString) {
            if (!emailString) return '';
    
            // Remove espaços e quebras de linha no início e no fim da string
            emailString = emailString.trim();
    
            // Divide a string em partes utilizando espaços, quebras de linha ou ponto e vírgula como delimitadores
            var emails = emailString.split(/[\s;\n]+/).filter(Boolean);
    
            // Valida e formata os emails
            var formattedEmails = emails.map(function(email) {
                if (isValidEmail(email)) {
                    return email.trim() + ';';
                } else {
                    return email.trim();
                }
            });
    
            // Junta os emails formatados em uma única string
            return formattedEmails.join(' ');
        }
    
        function isValidEmail(email) {
            // Expressão regular para validar email
            var emailPattern = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
            return emailPattern.test(email);
        }


        vm.save = function (form) {
            if (!form.$valid) {
                toastr.error('Todos os campos obrigatórios e/ou inválidos devem ser preenchidos corretamente com seus respectivos valores!');
                return;
            }

            if (vm.saving) return;

            var request = {}
            request.emailsNotificacaoInternaContingenciaForcada = vm.configuracaoMonitorametoCiot.emailsNotificacaoInternaContingenciaForcada
            request.emailsNotificacaoInternaContingenciaAutomatica = vm.configuracaoMonitorametoCiot.emailsNotificacaoInternaContingenciaAutomatica
            request.tempoChecagemAmbiente = vm.configuracaoMonitorametoCiot.tempoChecagemAmbiente
            request.quantidadeErroCiot = vm.configuracaoMonitorametoCiot.quantidadeErroCiot
            request.tempoNotificacaoExterna = vm.configuracaoMonitorametoCiot.tempoNotificacaoExterna
            request.periodoReenvioCiot = vm.configuracaoMonitorametoCiot.periodoReenvioCiot
            request.obrigaValorFrete = vm.configuracaoMonitorametoCiot.obrigaValorFrete ? 1 : 0
            
            request.dataInicioObrigaValorFrete = vm.configuracaoMonitorametoCiot.dataInicioObrigaValorFrete

            vm.saving = true;
            
            BaseService.post('Parametros', 'SalvarConfiguracaoMonitoramentoCiot', request)
                .then(function (response) {
                    vm.saving = false;
                    if (!response) {
                        toastr.error('Erro de comunicação com o servidor, tente novamente em alguns minutos.');
                        return;
                    }
                    if (!response.success) {
                        toastr.error(response.message);
                        return;
                    }
                    toastr.success(response.message);
                    $state.go('parametros.index');
                })
        };
        
        vm.ctrlObrigaValorFreteChange = function () {
            if (vm.configuracaoMonitorametoCiot.obrigaValorFrete == false) {
                vm.configuracaoMonitorametoCiot.dataInicioObrigaValorFrete = null;
            }
        }

        
        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.administrador == true;
        };

        function init() {
            vm.loadEdit();
        };

        vm.onClickVoltar = function (wizard) {

            var ativoIndex = wizard.getActivePosition();
            if (ativoIndex == 1)
                $state.go('parametros.index');

            wizard.go(ativoIndex - 1);
        };

        init();

        $scope.$on('$stateChangeStart', function (arg1, toState) {
            if (toState.name === 'parametros.index')
                PersistentDataService.remove('ConfiguracaoMonitoramentoCiotController');
            else
                PersistentDataService.store('ConfiguracaoMonitoramentoCiotController', vm, "Administração - Parâmetros Monitoramento CIOT", null, "parametros.configuracao-monitoramento-ciot", vm.configuracaoMonitorametoCiot.id);
        });


        $timeout(function () {
            PersistentDataService.remove('ConfiguracaoMonitoramentoCiotController');
        }, 15);

    }
})();
