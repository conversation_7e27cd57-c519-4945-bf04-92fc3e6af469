(function () {
    'use strict';

    angular.module('bbcWeb').controller('CentralNotificacaoCrudController', CentralNotificacaoCrudController);

    CentralNotificacaoCrudController.$inject = ['toastr', '$rootScope', 'BaseService', '$state', '$stateParams', '$window', 'PERFIL_ADMINISTRADOR', '$scope', '$timeout', 'PersistentDataService', 'DefaultsService', '$uibModal'];

    function CentralNotificacaoCrudController(
        toastr, $rootScope,
        BaseService, $state,
        $stateParams, $window,
        PERFIL_ADMINISTRADOR,
        $scope, $timeout,
        PersistentDataService,
        DefaultsService,
        $uibModal
    ) {
        var vm = this;
        vm.notificacao = [];
        vm.menusPai = [];

        vm.cmbStatus = {
            data: [{ id: 1, descricao: 'Habilitar' }, { id: 2, descricao: 'Desabilitar' }]
        };

        vm.cmbTipo = {
            data: [{ id: 0, descricao: 'Adiantamento' }, { id: 1, descricao: 'Saldo' }, { id: 2, descricao: 'Complemento' }, { id: 3, descricao: 'Avulso' }, { id: 4, descricao: 'Tarifa ANTT' }]
        };

        vm.headerItems = [{
            name: 'Movimentações'
        }, {
            name: 'Central de notificações',
            link: 'central-notificacoes.index'
        }];

        var selfScope = PersistentDataService.get('CentralNotificacaoCrudController');

        if ($stateParams.link == 'novo')
            vm.notificacao.id = 'Auto';

        vm.isNew = function () {
            return $stateParams.link == 'novo';
        };

        vm.loadEdit = function (id) {
            BaseService.get('CentralNotificacoes', 'ConsultarPorId', {
                idCentralNotificacoes: id
            }).then(function (response) {
                if (!response.success) {
                    toastr.error(response.message);
                    return;
                } else {
                    vm.notificacao = response.data;
                }
            });
        };

        vm.save = function (form) {
            if (!form.$valid) {
                toastr.error('Todos os campos obrigatórios devem ser preenchidos corretamente com seus respectivos valores');
                return;
            }

            if (vm.isSaving === true)
                return;

            var notificacao = {}

            notificacao.Id = vm.notificacao.id == "Auto" ? 0 : vm.notificacao.id;
            notificacao.Descricao = vm.notificacao.descricao;
            notificacao.Status = vm.notificacao.status;
            notificacao.Tipo = vm.notificacao.tipo;
            notificacao.PagamentoEventoId = vm.notificacao.pagamentoEventoId;

            vm.isSaving = true;

            BaseService.post('CentralNotificacoes', 'Salvar', notificacao).then(function (response) {
                vm.isSaving = false;
                if (!response) {
                    toastr.error('Erro de comunicação com o servidor, tente novamente em alguns minutos.')
                    return
                }

                if (!response.success) {
                    toastr.error(response.message)
                    return
                }

                toastr.success(response.message)
                $state.go('central-notificacoes.index');
            });
        };

        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.administrador == true;
        };

        function init() {
            if (vm.isNew()) {
                vm.notificacao.id = 'Auto';
            }
        }

        vm.onClickAvancar = function (wizard) {
            wizard.go(wizard.getActivePosition() + 1);
        };

        vm.onClickVoltar = function (wizard) {

            $state.go('central-notificacoes.index');
        };

        init();

        $scope.$on('$stateChangeStart', function (arg1, toState) {
            if (toState.name === 'central-notificacoes.index')
                PersistentDataService.remove('CentralNotificacaoController');
            else
                PersistentDataService.store('CentralNotificacaoCrudController', vm, "Movimentações - Central de notificação", null, "central-notificacoes.central-notificacoes-crud", vm.notificacao.id);
        });

        if (angular.isDefined(selfScope))
            angular.extend(vm, selfScope.data);
        else {
            if (!vm.isNew())
                vm.loadEdit($stateParams.link);
            else {
                vm.modulos = 1;
            }
        }

        $timeout(function () {
            PersistentDataService.remove('CentralNotificacaoController');
        }, 15);

        vm.consultaEmpresa = {
            columnDefs: [{
                name: 'Cód.',
                field: 'id',
                width: 60,
                type: 'number',
                primaryKey: true
            }, {
                name: 'Nome Fantasia',
                field: 'nomeFantasia',
                width: '*'
            }, {
                name: 'Razão Social',
                field: 'razaoSocial',
                width: '*'
            }, {
                name: 'Email',
                field: 'email',
                width: 120
            }],
            desiredValue: 'id',
            desiredText: 'nomeFantasia',
            url: 'Empresa/ConsultarGridEmpresaCombo',
            paramsMethod: function () {
                return {}
            }
        }
    }
})();
