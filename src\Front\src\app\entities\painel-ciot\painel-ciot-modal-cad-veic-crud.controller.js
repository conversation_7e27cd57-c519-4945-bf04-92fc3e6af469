(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('PainelCiotModalCadVeicCrudController', PainelCiotModalCadVeicCrudController);

    PainelCiotModalCadVeicCrudController.$inject = ['$uibModalInstance', 'toastr', 'BaseService', 'PortadorPropId', 'PortadorPropCpfCnpj', 'Rntrc', '$rootScope'];

    function PainelCiotModalCadVeicCrudController($uibModalInstance, toastr, BaseService, PortadorPropId, PortadorPropCpfCnpj, Rntrc, $rootScope) {
        var vm = this;
        vm.modal = {};

        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.administrador == true;
        };

        vm.salvarVeiculo = function (form) {
            if (!form.$valid) {
                toastr.error('Todos os campos obrigatórios e/ou inválidos devem ser preenchidos corretamente com seus respectivos valores');
                return;
            }

            if (vm.isAdmin) {
                vm.modal.empresaId = vm.consultaEmpresa.selectedValue;
            }

            BaseService.post('Veiculo', 'CadastrarVeiculoCiot', {
                portadorProprietarioId: PortadorPropId,
                portadorProprietarioCpfCnpj: PortadorPropCpfCnpj,
                placa: vm.modal.placa,
                renavam: vm.modal.renavam,
                empresaId: vm.modal.empresaId || 0
            }).then(function (response) {
                if (response.sucesso) {
                    toastr.success('Registro salvo com sucesso!');
                    $uibModalInstance.close({
                        placa: vm.modal.placa,
                        rntrc: Rntrc
                    });
                    return;
                } else {
                    return toastr.error(response.mensagem);
                }
            });
        };

        function carregarEmpresas() {
            vm.consultaEmpresa = {
                columnDefs: [{
                    name: 'Cód.',
                    field: 'id',
                    width: 60,
                    type: 'number',
                    primaryKey: true
                }, {
                    name: 'Nome Fantasia',
                    field: 'nomeFantasia',
                    width: '*'
                }, {
                    name: 'Razão Social',
                    field: 'razaoSocial',
                    width: '*'
                }, {
                    name: 'Email',
                    field: 'email',
                    width: 120
                }],
                desiredValue: 'id',
                desiredText: 'nomeFantasia',
                url: 'Empresa/ConsultarGridEmpresaCombo',
                paramsMethod: function () {
                    return {}
                }
            };
        };

        carregarEmpresas();
    }
})();
