(function () {
    'use strict';

    angular.module('bbcWeb').controller('ModeloCrudController', ModeloCrudController);

    ModeloCrudController.$inject = ['toastr', '$rootScope', 'BaseService', '$state', '$stateParams', '$window', 'PERFIL_ADMINISTRADOR', '$scope', '$timeout', 'PersistentDataService', 'DefaultsService', '$uibModal'];

    function ModeloCrudController(
        toastr, $rootScope,
        BaseService, $state,
        $stateParams, $window,
        PERFIL_ADMINISTRADOR,
        $scope, $timeout,
        PersistentDataService,
        DefaultsService,
        $uibModal
    ) {
        var vm = this;
        vm.modelo = [];
        vm.menusPai = [];

        vm.headerItems = [{
            name: 'Cadastros'
        }, {
            name: 'Modelo de veículo',
            link: 'modelo-veiculo.index'
        }, {
            name: $stateParams.link == 'novo' ? 'Novo' : 'Editar'
        }];

        var selfScope = PersistentDataService.get('ModeloCrudController');

        if ($stateParams.link == 'novo')
            vm.modelo.id = 'Auto';

        vm.isNew = function () {
            return $stateParams.link == 'novo';
        };


        vm.loadEdit = function (id) {
            BaseService.get('Modelo', 'ConsultarPorId', {
                idModelo: id
            }).then(function (response) {
                if (!response.success) {
                    toastr.error(response.message);
                    return;
                } else {
                    vm.modelo = response.data;
                    vm.modelo.mediaMinima = vm.modelo.mediaMinima.replace(",", ".");
                    vm.modelo.mediaSugerida = vm.modelo.mediaSugerida.replace(",", ".");
                    vm.consultaFabricante.selectedValue = response.data.fabricante.id;
                    vm.consultaFabricante.selectedText = response.data.fabricante.nome;
                }
            });
        };          

        vm.save = function (form) {
            if (!form.$valid) {
                toastr.error('Todos os campos obrigatórios devem ser preenchidos corretamente com seus respectivos valores');
                return;
            }

            if (vm.isSaving === true)
                return;

            //var modelo = {}

            vm.modelo.id = vm.modelo.id == "Auto" ? 0 : vm.modelo.id;
            vm.modelo.fabricanteId = vm.modelo.fabricanteId ? vm.modelo.fabricanteId : vm.consultaFabricante.selectedValue; 
            vm.modelo.mediaMinima = vm.modelo.mediaMinima.toString().replace('.',',');
            vm.modelo.mediaSugerida = vm.modelo.mediaSugerida.toString().replace('.',',');

            vm.isSaving = true;

            BaseService.post('modelo', 'Salvar', vm.modelo).then(function (response) {
                vm.isSaving = false;

                if (response && !response.sucesso)
                    return toastr.error(response.mensagem);
                else {
                    toastr.success('Modelo salvo com sucesso!');
                    $state.go('modelo-veiculo.index');
                }
            });
        };

        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.administrador == true;
        };

        function init() {
            if (vm.isNew()) {
                vm.modelo.id = 'Auto';
            }

            if (vm.isAdmin() && vm.isNew())
                vm.consultaEmpresa;
        };

        vm.onClickAvancar = function (wizard) {
            wizard.go(wizard.getActivePosition() + 1);
        };

        vm.onClickVoltar = function (wizard) {

            var ativoIndex = wizard.getActivePosition();
            if (ativoIndex == 1)
                $state.go('modelo-veiculo.index');

            wizard.go(ativoIndex - 1);
        };

        init();

        $scope.$on('$stateChangeStart', function (arg1, toState) {
            if (toState.name === 'modelo-veiculo.index')
                PersistentDataService.remove('ModeloCrudController');
            else
                PersistentDataService.store('ModeloCrudController', vm, "Cadastro - Modelos", null, "modelo-veiculo.modelo-veiculo-crud", vm.modelo.id);
        });

        if (angular.isDefined(selfScope))
            angular.extend(vm, selfScope.data);
        else {
            if (!vm.isNew())
                vm.loadEdit($stateParams.link);
            else {
                vm.modelo = 0;
                vm.modulos = 1;
            }
        }

        $timeout(function () {
            PersistentDataService.remove('ModeloController');
        }, 15);

        vm.consultaFabricante = {
            columnDefs: [{
                name: 'Código',
                width: 80,
                type : 'number',
                primaryKey: true,
                field: 'id'
            }, {
                name: 'Fabricante',
                width: '*',
                minWidth: 150,
                field: 'nome'
            }],
            desiredValue: 'id',
            desiredText: 'nome',
            url: 'Fabricante/ConsultarGridFabricante',
            paramsMethod: function () {
                return {}
            }
        };

        vm.consultaEmpresa = {
            columnDefs: [{
                name: 'Cód.',
                field: 'id',
                width: 60,
                type: 'number',
                primaryKey: true
            }, {
                name: 'Nome Fantasia',
                field: 'nomeFantasia',
                width: '*'
            }, {
                name: 'Razão Social',
                field: 'razaoSocial',
                width: '*'
            }, {
                name: 'Email',
                field: 'email',
                width: 120
            }],
            desiredValue: 'id',
            desiredText: 'nomeFantasia',
            url: 'Empresa/ConsultarGridEmpresaCombo',
            paramsMethod: function () {
                return {}
            }
        }
    }
})();
