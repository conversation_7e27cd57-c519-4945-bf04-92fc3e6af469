(function () {
    'use strict';

    angular.module('bbcWeb').controller('TipoEmpresaCrudController', TipoEmpresaCrudController);

    TipoEmpresaCrudController.$inject = ['toastr', '$rootScope', 'BaseService', '$state', '$stateParams', '$window', 'PERFIL_ADMINISTRADOR', '$scope', '$timeout', 'PersistentDataService', 'DefaultsService', '$uibModal'];

    function TipoEmpresaCrudController(
        toastr, $rootScope,
        BaseService, $state,
        $stateParams, $window,
        PERFIL_ADMINISTRADOR,
        $scope, $timeout,
        PersistentDataService,
        DefaultsService,
        $uibModal
    ) {
        var vm = this;
        vm.tipoEmpresa = [];
        vm.menusPai = [];

        vm.headerItems = [{
            name: '<PERSON>ada<PERSON><PERSON>'
        }, {
            name: 'Tipo de empresa',
            link: 'tipo-empresa.index'
        }, {
            name: $stateParams.link == 'novo' ? 'Novo' : 'Editar'
        }];

        var selfScope = PersistentDataService.get('TipoEmpresaCrudController');

        if ($stateParams.link == 'novo')
            vm.tipoEmpresa.id = 'Auto';

        vm.isNew = function () {
            return $stateParams.link == 'novo';
        };

        vm.loadEdit = function (id) {
            BaseService.get('tipoEmpresa', 'ConsultarPorId', {
                tipoEmpresaId: id
            }).then(function (response) {
                if (!response.success) {
                    toastr.error(response.message);
                    return;
                } else {
                    vm.tipoEmpresa = response.data;
                }
            });
        };

        vm.save = function (form) {
            if (!form.$valid) {
                toastr.error('Todos os campos obrigatórios devem ser preenchidos corretamente com seus respectivos valores');
                return;
            }

            if (vm.isSaving === true)
                return;

            var tipoEmpresa = {}

            tipoEmpresa.Id = vm.tipoEmpresa.id == "Auto" ? 0 : vm.tipoEmpresa.id;
            tipoEmpresa.Nome = vm.tipoEmpresa.nome;
            tipoEmpresa.Ativo = vm.tipoEmpresa.ativo;

            vm.isSaving = true;

            BaseService.post('tipoEmpresa', 'Salvar', tipoEmpresa).then(function (response) {
                vm.isSaving = false;

                if (response && !response.sucesso)
                    return toastr.error(response.mensagem);
                else {
                    toastr.success('Tipo de empresa salvo com sucesso!');
                    $state.go('tipo-empresa.index');
                }
            });
        };

        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.administrador == true;
        };

        function init() {
            if (vm.isNew()) {
                vm.tipoEmpresa.id = 'Auto';
            }
        };

        vm.onClickAvancar = function (wizard) {
            wizard.go(wizard.getActivePosition() + 1);
        };

        vm.onClickVoltar = function (wizard) {

            var ativoIndex = wizard.getActivePosition();
            if (ativoIndex == 1)
                $state.go('tipo-empresa.index');

            wizard.go(ativoIndex - 1);
        };

        init();

        $scope.$on('$stateChangeStart', function (arg1, toState) {
            if (toState.name === 'tipo-empresa.index')
                PersistentDataService.remove('TipoEmpresaCrudController');
            else
                PersistentDataService.store('TipoEmpresaCrudController', vm, "Cadastro - TipoEmpresas", null, "tipoEmpresa.tipoEmpresa-crud", vm.tipoEmpresa.id);
        });

        if (angular.isDefined(selfScope))
            angular.extend(vm, selfScope.data);
        else {
            if (!vm.isNew())
                vm.loadEdit($stateParams.link);
            else {
                vm.TipoEmpresa = 0;
                vm.modulos = 1;
            }
        }

        $timeout(function () {
            PersistentDataService.remove('TipoEmpresaController');
        }, 15);

        vm.consultaEmpresa = {
            columnDefs: [{
                name: 'Cód.',
                field: 'id',
                width: 60,
                type: 'number',
                primaryKey: true
            }, {
                name: 'Nome Fantasia',
                field: 'nomeFantasia',
                width: '*'
            }, {
                name: 'Razão Social',
                field: 'razaoSocial',
                width: '*'
            }, {
                name: 'Email',
                field: 'email',
                width: 120
            }],
            desiredValue: 'id',
            desiredText: 'nomeFantasia',
            url: 'Empresa/ConsultarGridEmpresaCombo',
            paramsMethod: function () {
                return {}
            }
        }
    }
})();
