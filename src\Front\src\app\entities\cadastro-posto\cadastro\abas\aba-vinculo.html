<div class="form-horizontal" ng-show="vm.isAdmin">
    <hr />
    <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
        <div>
            <div class="form-group">
                <consulta-padrao-modal directivesizes="'col-xs-12 col-sm-12 col-md-8 col-lg-6'"
                    idname="combustivel" idmodel="Combustivel"
                    labelsize="'col-xs-12 col-sm-12 col-md-4 col-lg-3 mt-5'" tabledefinition="vm.consultaCombustivel"
                    label="'Combustível:'" placeholder="'Selecione um combustível'"
                    required-message="'Combustivel é obrigatório'" validate-on="blur" ng-required="vm.posto.vinculoCombustivelList.length === 0">
                </consulta-padrao-modal>
                <div class="col-xs-12 col-sm-12 col-md-4 col-lg-4">
                    <label class="col-xs-12 col-sm-3 col-md-3 col-lg-3 control-label">
                        Código de produto:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-9 col-lg-9">
                        <input type="text" ng-model="vm.nfe" maxlength="50" validate-on="blur" name="nfe" class="form-control"
                        />
                    </div>
                </div>
                <div class="col-xs-12 col-sm-12 col-md-2 col-lg-2 text-right">
                    <div class="form-header">
                        <button type="button" class="mr-5 btn-labeled btn btn-info" ng-click="vm.adicionarCombustivelProduto()">
                            <i class="fa fa-plus"></i> Adicionar
                        </button>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="table-responsive" style="margin-top: 5px;">
                    <table class="table table-bordered table-hover col-xs-12">
                        <thead>
                            <tr>
                                <th width="60%">Combustível</th>
                                <th width="20%">Código produto</th>
                                <th width="10%">Ações</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr ng-repeat="combustivelProduto in vm.posto.postoCombustivelProduto">
                                <td>{{combustivelProduto.nomeCombustivel}}</td>
                                <td>{{combustivelProduto.codigoProduto}}</td>
                                <td class="text-center" style="vertical-align: middle">
                                    <button type="button" uib-tooltip="Editar" class="btn btn-xs btn-info"
                                        ng-click="vm.editarCombustivelProduto(combustivelProduto)" 
                                        ng-disabled="combustivelProduto.id == '0'">
                                        <i class="fa fa-edit"></i>
                                    </button>
                                    <button type="button" uib-tooltip="Remover" class="btn btn-xs btn-danger"
                                        ng-click="vm.removerCombustivelProduto(combustivelProduto)">
                                        <i class="fa fa-trash-o"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>