(function () {
    'use strict';

    angular.module('bbcWeb').controller('FabricanteCrudController', FabricanteCrudController);

    FabricanteCrudController.$inject = ['toastr', '$rootScope', 'BaseService', '$state', '$stateParams', '$window', 'PERFIL_ADMINISTRADOR', '$scope', '$timeout', 'PersistentDataService', 'DefaultsService', '$uibModal'];

    function FabricanteCrudController(
        toastr, $rootScope,
        BaseService, $state,
        $stateParams, $window,
        PERFIL_ADMINISTRADOR,
        $scope, $timeout,
        PersistentDataService,
        DefaultsService,
        $uibModal
    ) {
        var vm = this;
        vm.fabricante = [];
        vm.menusPai = [];

        vm.headerItems = [{
            name: 'Cadastros'
        }, {
            name: 'Fabricante',
            link: 'fabricante.index'
        }, {
            name: $stateParams.link == 'novo' ? 'Novo' : 'Editar'
        }];

        var selfScope = PersistentDataService.get('FabricanteCrudController');

        if ($stateParams.link == 'novo')
            vm.fabricante.id = 'Auto';

        vm.isNew = function () {
            return $stateParams.link == 'novo';
        };

        vm.loadEdit = function (id) {
            BaseService.get('fabricante', 'ConsultarPorId', {
                idfabricante: id
            }).then(function (response) {
                if (!response.success) {
                    toastr.error(response.message);
                    return;
                } else {
                    vm.fabricante = response.data;
                }
            });
        };

        vm.save = function (form) {
            if (!form.$valid) {
                toastr.error('Todos os campos obrigatórios devem ser preenchidos corretamente com seus respectivos valores');
                return;
            }

            if (vm.isSaving === true)
                return;

            var fabricante = {}

            fabricante.Id = vm.fabricante.id == "Auto" ? 0 : vm.fabricante.id;
            fabricante.Nome = vm.fabricante.nome;
            fabricante.Ativo = vm.fabricante.ativo;

            vm.isSaving = true;

            BaseService.post('Fabricante', 'Salvar', fabricante).then(function (response) {
                vm.isSaving = false;
                if (!response) {
                    toastr.error('Erro de comunicação com o servidor, tente novamente em alguns minutos.')
                    return
                }

                if (!response.success) {
                    toastr.error(response.message)
                    return
                }
                
                toastr.success(response.message)
                $state.go('fabricante.index');
            });
        };

        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.administrador == true;
        };

        function init() {
            if (vm.isNew()) {
                vm.fabricante.id = 'Auto';
            }
        }

        vm.onClickAvancar = function (wizard) {
            wizard.go(wizard.getActivePosition() + 1);
        };

        vm.onClickVoltar = function (wizard) {

            var ativoIndex = wizard.getActivePosition();
            if (ativoIndex == 1)
                $state.go('fabricante.index');

            wizard.go(ativoIndex - 1);
        };

        init();

        $scope.$on('$stateChangeStart', function (arg1, toState) {
            if (toState.name === 'fabricante.index')
                PersistentDataService.remove('FabricanteCrudController');
            else
                PersistentDataService.store('FabricanteCrudController', vm, "Cadastro - Fabricantes", null, "fabricante.fabricante-crud", vm.fabricante.id);
        });

        if (angular.isDefined(selfScope))
            angular.extend(vm, selfScope.data);
        else {
            if (!vm.isNew())
                vm.loadEdit($stateParams.link);
            else {
                vm.Fabricante = 0;
                vm.modulos = 1;
            }
        }

        $timeout(function () {
            PersistentDataService.remove('FabricanteController');
        }, 15);

        vm.consultaEmpresa = {
            columnDefs: [{
                name: 'Cód.',
                field: 'id',
                width: 60,
                type: 'number',
                primaryKey: true
            }, {
                name: 'Nome Fantasia',
                field: 'nomeFantasia',
                width: '*'
            }, {
                name: 'Razão Social',
                field: 'razaoSocial',
                width: '*'
            }, {
                name: 'Email',
                field: 'email',
                width: 120
            }],
            desiredValue: 'id',
            desiredText: 'nomeFantasia',
            url: 'Empresa/ConsultarGridEmpresaCombo',
            paramsMethod: function () {
                return {}
            }
        }
    }
})();
