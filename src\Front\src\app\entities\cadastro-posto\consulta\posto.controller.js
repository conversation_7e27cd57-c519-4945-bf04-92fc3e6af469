(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('PostoController', PostoController);

        PostoController.inject = [
        '$scope',
        'toastr',
        'BaseService',
        'PersistentDataService',
        '$rootScope',
        '$timeout',
        '$state'
    ];

    function PostoController(
        $scope,
        toastr,
        BaseService,
        PersistentDataService,
        $rootScope,
        $timeout,
        $state) {

        var vm = this;
        vm.consultaPosto = {};
        const POSTO_CONTROLLER = "Posto";
        vm.enviandoRequisicao = false;

        vm.headerItems = [{
            name: '<PERSON><PERSON><PERSON><PERSON>'
        }, {
            name: 'Posto'
        }];

        vm.alterarStatus = function (entity, ativo) {
            BaseService.post('Posto', "AlterarStatus", entity).then(function (response) {
                response.success ? (ativo === 1 ? toastr.success('Posto inativado com sucesso!') : toastr.success('Posto reativado com sucesso!')) : toastr.error(response.message);
                vm.gridOptions.dataSource.refresh();
            });
        };

        vm.reprovarCadastro = function (id) {
            vm.enviandoRequisicao = true;
            BaseService.post('Posto', "ReprovarCredencimento",  {
                id: id
            }).then(function (response) {
                response.success ? 
                toastr.success('Posto reprovado com sucesso!') 
                : toastr.error(response.message);
                vm.enviandoRequisicao = false;
                vm.gridOptions.dataSource.refresh();
            });
        };

        vm.aprovarCadastro = function (id) {
            vm.enviandoRequisicao = true;
            BaseService.post('Posto', "AprovarCredencimento", {
                id: id
            }).then(function (response) {
                response.success ? 
                toastr.success('Posto aprovado com sucesso!') 
                : toastr.error(response.message);
                vm.enviandoRequisicao = false;
                vm.gridOptions.dataSource.refresh();
            });
        };

        vm.isAdministrador = function () {
            return isAdministrador();
        }

        function getDataSourceGrid() {
            return { autoBind: true, url: POSTO_CONTROLLER + "/ConsultarGridPosto", }
        }

        function isAdministrador() {
            return $rootScope.usuarioLogado.administrador;
        }

        vm.gridOptions = {
            data: [],
            onRegisterApi: BaseService.dataGrid.defaultOnRegisterApi,
            dataSource: getDataSourceGrid(),
            columnDefs: [
                {
                    name: 'Ações', width: 100, enableColumnMenu: false, cellTemplate: '<div ng-if="!row.groupHeader" class="ui-grid-cell-contents" title="TOOLTIP">\
                        <button ng-disabled="row.entity.ativo===0" title="Editar" type="button" ui-sref="posto.crud({link: row.entity.id})"\
                            ng-class="{ \'btn btn-xs btn-info\': true }">\
                            <i class="fa fa-edit"></i>\
                        </button>\
                        <button ng-if="grid.appScope.vm.isAdministrador()" type="button" title="Ativar/Desativar"\
                            ng-click="grid.appScope.vm.alterarStatus(row.entity, row.entity.ativo)"\
                            ng-class="row.entity.ativo===1 ? \'btn btn-xs btn-success\' : \'btn btn-xs btn-danger\'">\
                            <i ng-class="row.entity.ativo===1 ? \'fa fa-check\' : \'fa fa-times\'"></i>\
                        </button>\
                        <button type="button" mwl-confirm class="btn btn-xs btn-warning" message="Aprovar este credenciamento?"\
                            tooltip-placement="\'auto\'" uib-tooltip="Aprovar/Reprovar credenciamento"\
                            ng-disabled="row.entity.statusCadastro!==0 || grid.appScope.vm.enviandoRequisicao"\
                            confirm-text="Sim"\
                            cancel-text="Não"\
                            confirm-button-type="success"\
                            cancel-button-type="danger"\
                            on-confirm="grid.appScope.vm.aprovarCadastro(row.entity.id)"\
                            on-cancel="grid.appScope.vm.reprovarCadastro(row.entity.id)">\
                            <i ng-class="\'fa fa-id-card-o\'"></i>\
                        </button>\
                    </div>'
                },
                { 
                    displayName: 'Farol SLA', 
                    width: 100, 
                    field: 'farolSla',
                    enableFiltering: true,
                    enum: true,
                    enumTipo: 'EFarolSla',
                    cellTemplate: '<div class="ui-grid-cell-contents" align="center">\
                                        <p ng-show="row.entity.farolSla === 2">\
                                            <i ng-class="\'fa fa-circle\'" style="color: #f9b66d;" tooltip-placement="\'auto\'" uib-tooltip="Limite do prazo de credenciamento"></i>\
                                        </p>\
                                        <p ng-show="row.entity.farolSla === 3">\
                                            <i ng-class="\'fa fa-circle\'" style="color: red;" tooltip-placement="\'auto\'" uib-tooltip="Fora do prazo de credenciamento"></i>\
                                        </p>\
                                        <p ng-show="row.entity.farolSla === 1">\
                                            <i ng-class="\'fa fa-circle\'" style="color: #5CB85C;" tooltip-placement="\'auto\'" uib-tooltip="Dentro do prazo de credenciamento"></i>\
                                        </p>\
                                </div>'
                },
                { name: 'Código', width: 80, primaryKey: true, type: 'number', field: 'id' },
                { displayName: 'Razão social', field: 'razaoSocial', width: '*', minWidth: 150 },
                { displayName: 'CNPJ', field: 'cnpj', width: 145 },
                { 
                    name: 'Status cadastro', 
                    field: 'statusCadastro', 
                    width: 120, 
                    enableFiltering: true,
                    enum: true,
                    enumTipo: 'EStatusCredenciamentoPosto',
                    cellTemplate: '<div class="ui-grid-cell-contents" align="left">\
                                        <p ng-show="row.entity.statusCadastro === 3"> Bloqueado </p>\
                                        <p ng-show="row.entity.statusCadastro === 1"> Aprovado </p>\
                                        <p ng-show="row.entity.statusCadastro === 2"> Reprovado </p>\
                                        <p ng-show="row.entity.statusCadastro === 0"> Aguardando Aprovação </p>\
                                </div>'
                },
            ]
        }

        // Controle de aba!!
        $scope.$on('$stateChangeStart', function () {
            PersistentDataService.store('PostoController', vm, "Posto", "PostoCrudController", "posto.index");
        });

        var selfScope = PersistentDataService.get('PostoController');
        var filho = PersistentDataService.get('PostoCrudController');
        if (angular.isDefined(filho)) {
            $timeout(function () {
                $state.go('posto.crud', {
                    link: filho.data.posto.id > 0 ? 'editar' : 'novo'
                });
            }, 50);
        } else if (angular.isDefined(selfScope)) {
            angular.extend(vm, selfScope.data);

            $timeout(function () {
                vm.gridOptions.dataSource.refresh();
            }, 450);
        }
    }
})();