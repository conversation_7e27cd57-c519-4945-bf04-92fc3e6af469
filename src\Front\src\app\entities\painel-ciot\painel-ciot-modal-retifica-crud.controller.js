(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('PainelCiotModalRetificaCrudController', PainelCiotModalRetificaCrudController);

    PainelCiotModalRetificaCrudController.$inject = ['$uibModalInstance',
        'toastr', '$rootScope', 'BaseService', '$state', '$stateParams', '$window', 'PERFIL_ADMINISTRADOR',
        '$scope', '$timeout', 'PersistentDataService', 'DefaultsService', '$uibModal', 'CiotId', 'Ciot', 'CpfCnpjProprietario', 'ValorTarifas', 'QuantidadeTarifas', 'Rntrc', 'CpfCnpjCliente'];

    function PainelCiotModalRetificaCrudController(
        $uibModalInstance,
        toastr, $rootScope,
        BaseService, $state,
        $stateParams, $window,
        PERFIL_ADMINISTRADOR,
        $scope, $timeout,
        PersistentDataService,
        DefaultsService,
        $uibModal,
        CiotId,
        Ciot,
        CpfCnpjProprietario,
        ValorTarifas,
        QuantidadeTarifas,
        Rntrc,
        CpfCnpjCliente) {
        var vm = this;

        vm.veiculosList = [];
        vm.rntrc = Rntrc;

        vm.saving = false;

        vm.loadEdit = function () {
            BaseService.get('PainelCiot', 'ConsultaVeiculosCiot', {
                idOperacaoTransporte: CiotId
            }).then(function (response) {
                if (!response.success) {
                    toastr.error(response.message);
                    return;
                } else {
                    vm.veiculosList = response.data.veiculosList;
                }
            });
        };

        vm.loadEdit();


        vm.consultaVeiculo = {
            columnDefs: [{
                name: 'Código',
                field: 'id',
                width: 80,
                type: 'number',
                primaryKey: true
            }, {
                name: 'Placa',
                field: 'placa',
                enableGrouping: true,
                width: 80
            }, {
                name: 'RENAVAM',
                field: 'renavam',
                enableGrouping: true,
                width: '*',
                minWidth: 150
            }],
            desiredValue: 'id',
            desiredText: 'placa',
            url: 'Veiculo/ConsultarGridVeiculoPortadorCiotCombo',
            paramsMethod: function () {
                return {
                    CpfCnpjProprietario: CpfCnpjProprietario
                }
            }
        };

        vm.adicionarVeiculo = function () {
            var permiteAdicionar = false;
            var objetosValidados = _.filter(vm.veiculosList, function (v) {
                return v.placa === vm.consultaVeiculo.selectedEntity.placa;
            });

            if (objetosValidados.length > 0) {
                toastr.error("Este veículo já foi adicionado.");
                return;
            }

            if (vm.consultaVeiculo.selectedEntity != undefined && vm.consultaVeiculo.selectedEntity.placa != undefined
                && vm.consultaVeiculo.desiredValue != "") {
                permiteAdicionar = true;
            }

            if (vm.veiculosList.length >= 5) {
                permiteAdicionar = false;
                toastr.error("Não é possível informar mais de 5 veículos.");
            }

            if (permiteAdicionar) {
                vm.veiculosList.push(angular.copy(vm.consultaVeiculo.selectedEntity));
                vm.clearConsultaVeiculo();
            }
            else
                toastr.error("Por favor, informe o veículo.");
        };

        vm.infoVeiculosRequest = function () {
            return vm.veiculosList.map(function (veiculo) {
                return {
                    placa: veiculo.placa,
                    rntrc: vm.rntrc
                };
            });
        };

        vm.clearConsultaVeiculo = function () {
            vm.consultaVeiculo.selectedEntity = undefined;
            vm.consultaVeiculo.selectedValue = undefined;
            vm.consultaVeiculo.selectedText = "";
        };

        vm.removerVeiculo = function (veiculo) {
            for (var i = 0; i < vm.veiculosList.length; i++) {
                if (vm.veiculosList[i].placa == veiculo.placa) {
                    var index = vm.veiculosList.indexOf((vm.veiculosList[i]));
                    vm.veiculosList.splice(index, 1)
                }
            }
            vm.consultaVeiculo.selectedText = undefined;
        };

        vm.cadastrarVeiculo = function () {
            $uibModal.open({
                animation: true,
                ariaLabelledBy: 'modal-title',
                ariaDescribedBy: 'modal-body',
                templateUrl: 'app/entities/painel-ciot/modal/modal-cadastro-veiculo.html',
                controller: 'PainelCiotModalCadVeicCrudController',
                controllerAs: 'vm',
                size: 'md',
                windowClass: 'modal-top',
                resolve: {
                    PortadorPropId: function () {
                        return 0;
                    },
                    PortadorPropCpfCnpj: function () {
                        return CpfCnpjProprietario;
                    },
                    Rntrc: function () {
                        return vm.rntrc;
                    },
                }
            }).result.then(function (veiculo) {
                vm.veiculosList.push(veiculo);
            });
        };

        vm.retificar = function () {
            var request = {
                Ciot: Ciot,
                Veiculos: vm.infoVeiculosRequest(),
                SenhaAlteracao: CiotId,
                QuantidadeTarifas: QuantidadeTarifas,
                ValorTarifas: ValorTarifas,
                CpfCnpjClienteAdmOrCompanyGroup: CpfCnpjCliente
            };

            vm.saving = true;

            BaseService.post('PainelCiot', 'RetificarCiotOperacaoTransporte', request).then(function (response) {
                if (response.success) {
                    toastr.success('Operação realizada com sucesso!');
                    $uibModalInstance.close();
                } else {
                    toastr.error(response.message);
                    vm.saving = false;
                }
            });
        };



    }
})();