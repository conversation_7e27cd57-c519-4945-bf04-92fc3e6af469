(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('EmpresaCadastreSeController', EmpresaCadastreSeController);

    EmpresaCadastreSeController.$inject = ['toastr', 'BaseService', '$scope', '$timeout', '$rootScope'];

    function EmpresaCadastreSeController(
        toastr,
        BaseService,
        $scope, $timeout, $rootScope) {

        var vm = this;

        const MENSAGEM_PADRAO_VALIDACAO = "CAMPOS MARCADOS COM (*) SÃO OBRIGATÓRIOS";
        const PROPRIEDADE_VAZIA = "";

        vm.comboSexo = {
            data: [{ id: 1, descricao: 'Masculino' }, { id: 2, descricao: 'Feminino' }, { id: 3, descricao: 'Outros' }, { id: 4, descricao: 'Indefinido' }]
        };

        $('#formAbaRepresentanteLegal').validator();

        vm.empresa = {};
        vm.repLegalList = [];
        vm.empresa.repLegalList = [];

        vm.estados = [];
        vm.cidades = [];
        vm.estadosDisabled = true;
        vm.cidadesDisabled = true;

        vm.estadosRepLegal = [];
        vm.cidadesRepLegal = [];
        vm.estadosDisabledRepLegal = true;
        vm.cidadesDisabledRepLegal = true;

        document.body.style.background = "";
        document.body.style.backgroundImage = "";
        document.body.style.backgroundColor = "white";

        function carregarEstados() {
            BaseService.get("Endereco", "ConsultarEstados")
                .then(function (response) {
                    if (response.success) {
                        vm.estados = response.data;
                        vm.estadosDisabled = false;
                    }
                });
        };

        function carregarEstadosRepLegal() {
            BaseService.get("Endereco", "ConsultarEstados")
                .then(function (response) {
                    if (response.success) {
                        vm.estadosRepLegal = response.data;
                        vm.estadosDisabledRepLegal = false;
                    }
                });
        };

        function carregarCidades(EstadoId) {
            BaseService.get("Cidade", "ConsultarCidadesPorEstado", {
                estadoId: EstadoId
            }).then(function (response) {
                if (response.success) {
                    vm.cidades = response.data;
                    vm.cidadesDisabled = false;
                }
            });
        };

        function carregarCidadesRepLegal(EstadoId) {
            BaseService.get("Cidade", "ConsultarCidadesPorEstado", {
                estadoId: EstadoId
            }).then(function (response) {
                if (response.success) {
                    vm.cidadesRepLegal = response.data;
                    vm.cidadesDisabledRepLegal = false;
                }
            });
        };

        vm.estadoChange = function (estadoId) {
            vm.cidades = null;
            vm.cidadesDisabled = true;
            vm.empresa.cidadeId = null;
            carregarCidades(estadoId);
        };

        vm.estadoChangeRepLegal = function (estadoId) {
            vm.cidadesRepLegal = null;
            vm.cidadesDisabledRepLegal = true;
            vm.repLegal.cidadeId = null;
            carregarCidadesRepLegal(estadoId);
        };

        function limparEndereco() {
            vm.empresa.estadoId = null;
            vm.empresa.cidadeId = null;
            vm.estados = null;
            vm.cidades = null;
            vm.empresa.endereco = null;
            vm.empresa.bairro = null;
            vm.empresa.enderecoNumero = null;
            vm.empresa.complemento = null;
        };

        function limparEnderecoRepLegal() {
            vm.repLegal.estadoId = null;
            vm.repLegal.cidadeId = null;
            vm.estadosRepLegal = null;
            vm.cidadesRepLegal = null;
            vm.repLegal.endereco = null;
            vm.repLegal.bairro = null;
            vm.repLegal.enderecoNumero = null;
            vm.repLegal.complemento = null;
        };

        vm.buscarEndereco = function (cep) {
            if (cep) {
                limparEndereco();
                BaseService.getEndereco(cep).then(function (response) {
                    if (response && response.erro) {
                        toastr.warning("Endereço não localizado.");
                    }

                    carregarEstados();

                    $timeout(function () {
                        var estado = _.find(vm.estados, ['sigla', response.uf]);

                        if (angular.isDefined(estado))

                            vm.empresa.estadoId = estado.id;
                        carregarCidades(vm.empresa.estadoId);
                        $timeout(function () {
                            var cidade = _.find(vm.cidades, ['descricao', response.localidade]);
                            if (angular.isDefined(cidade))
                                vm.empresa.cidadeId = cidade.id;
                        }, 1500);
                    }, 1500);

                    $timeout(function () {
                        if (angular.isDefined(response.logradouro))
                            vm.empresa.endereco = response.logradouro;
                    }, 1500);

                    $timeout(function () {
                        if (angular.isDefined(response.bairro))
                            vm.empresa.bairro = response.bairro;
                    }, 1500);

                });
            }
        };

        vm.buscarEnderecoRepLegal = function (cep) {
            if (cep) {
                limparEnderecoRepLegal();
                BaseService.getEndereco(cep).then(function (response) {
                    if (response && response.erro) {
                        toastr.warning("Endereço não localizado.");
                    }

                    carregarEstadosRepLegal();

                    $timeout(function () {
                        var estado = _.find(vm.estadosRepLegal, ['sigla', response.uf]);

                        if (angular.isDefined(estado))

                            vm.repLegal.estadoId = estado.id;
                        carregarCidadesRepLegal(vm.repLegal.estadoId);
                        $timeout(function () {
                            var cidade = _.find(vm.cidadesRepLegal, ['descricao', response.localidade]);
                            if (angular.isDefined(cidade))
                                vm.repLegal.cidadeId = cidade.id;
                        }, 1500);
                    }, 1500);

                    $timeout(function () {
                        if (angular.isDefined(response.logradouro))
                            vm.repLegal.endereco = response.logradouro;
                    }, 1500);

                    $timeout(function () {
                        if (angular.isDefined(response.bairro))
                            vm.repLegal.bairro = response.bairro;
                    }, 1500);

                });
            }
        };

        vm.adicionarRepLegal = function (form) {
            var permiteAdicionar = false;

                
            vm.setValidadeFieldsUiSelect(['SexoRepresentante', 'UfEmissao', 'EstadoRepresentante', 'CidadeRepresentante']);

            if (!form.$valid) {
                toastr.error('Preencha todos os campos obrigatórios da aba (Representante Legal) com seus respectivos valores!');
                return;
            }

            var objetosValidos = _.filter(vm.empresa.repLegalList, function (v) {
                return v.cpfCnpj === vm.repLegal.cpfCnpj;
            });
            if (objetosValidos.length > 0) {
                toastr.error("Este representante já foi adicionado.");
                return;
            }

            if (vm.adicionarEnabled()) {
                permiteAdicionar = true;
            }

            if (permiteAdicionar) {
                vm.repLegal.cpf = vm.mascaraCpfCnpj(vm.repLegal.cpfCnpj);

                vm.repLegalList.push(angular.copy(vm.repLegal));
                $rootScope.repLegalList = vm.repLegalList;
                vm.clearConsultaRepLegal(form);
            }
            else
                toastr.error("Por favor, informe os campos obrigatórios.");
        };

        vm.setValidadeFieldsUiSelect = function (arrayFields) {
            if (!arrayFields.length)
                return;

            arrayFields.forEach(function (field) {
                var id = field;
                var field = $('[name=' + field + ']'),
                    input = field[0].children[6],
                    span = field[0].children[0].children[0];

                $(input).on('blur', function () {
                    if (field[0].children[0].innerText)
                        span.style.borderColor = '';
                    document.getElementById(id).style.color = '#616161';
                });

                if (!field[0].children[0].innerText && field && span) {
                    span.style.borderWidth = '1px';
                    span.style.borderColor = '#ED5565';
                    document.getElementById(id).style.color = '#ed5868';
                }

            });
        };

        vm.clearConsultaRepLegal = function (form) {
            vm.repLegal = {};
        };

        vm.mascaraCpfCnpj = function (valor) {
            if (valor.length > 11)
                return valor.replace(/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/g, "\$1.\$2.\$3\/\$4\-\$5");
            else
                return valor.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/g, "\$1.\$2.\$3\-\$4");
        };

        vm.removerRepLegal = function (repLegal) {
            for (var i = 0; i < vm.repLegalList.length; i++) {
                if (vm.repLegalList[i].id == repLegal.id) {
                    var index = vm.repLegalList.indexOf((vm.repLegalList[i]));
                    vm.repLegalList.splice(index, 1);

                    $rootScope.repLegalList = vm.repLegalList;
                }
            }
        };

        vm.adicionarEnabled = function () {
            return (vm.repLegal.nome != null && vm.repLegal.nome != PROPRIEDADE_VAZIA && vm.repLegal.cpfCnpj != null && vm.repLegal.cpfCnpj != PROPRIEDADE_VAZIA && vm.repLegal.sexo != undefined && vm.repLegal.email != null && vm.repLegal.email != PROPRIEDADE_VAZIA && vm.repLegal.telefone != null && vm.repLegal.telefone != PROPRIEDADE_VAZIA
                && vm.repLegal.celular != null && vm.repLegal.celular != PROPRIEDADE_VAZIA && vm.repLegal.nomeMae != null && vm.repLegal.nomeMae != PROPRIEDADE_VAZIA && vm.repLegal.nomePai != null && vm.repLegal.nomePai != PROPRIEDADE_VAZIA && vm.repLegal.numeroIdentidade != null && vm.repLegal.numeroIdentidade != PROPRIEDADE_VAZIA && vm.repLegal.orgaoEmissor != null && vm.repLegal.orgaoEmissor != PROPRIEDADE_VAZIA
                && vm.repLegal.ufEmissao != null && vm.repLegal.ufEmissao != PROPRIEDADE_VAZIA && vm.repLegal.emissaoIdentidade != null && vm.repLegal.emissaoIdentidade != PROPRIEDADE_VAZIA && vm.repLegal.dataNascimento != null && vm.repLegal.dataNascimento != PROPRIEDADE_VAZIA && vm.repLegal.endereco != null && vm.repLegal.endereco != PROPRIEDADE_VAZIA && vm.repLegal.bairro != null && vm.repLegal.bairro != PROPRIEDADE_VAZIA && vm.repLegal.enderecoNumero != null && vm.repLegal.enderecoNumero != PROPRIEDADE_VAZIA
                && vm.repLegal.cep != null && vm.repLegal.cep != PROPRIEDADE_VAZIA && vm.repLegal.cidadeId != null && vm.repLegal.estadoId != null)
        };

        init();

        function init() {
            carregarEstados();
            //carregarEstadosRepLegal();
        };

        vm.save = function (form) {
            if (!form.$valid) {
                vm.saving = false;
                toastr.error('Todos os campos obrigatórios e/ou inválidos devem ser preenchidos corretamente com seus respectivos valores!');
                return;
            }

            vm.saving = true;

            //vm.empresa.repLegalList = $rootScope.repLegalList;

            BaseService.post('Empresa', 'Cadastre', vm.empresa).then(function (response) {
                vm.saving = false;
                if (response && response.success) {
                    toastr.success('Informações enviadas com sucesso! Seu cadastro foi enviado para aprovação e você receberá um e-mail após a validação.');
                    $timeout(function () {
                        window.location.replace('index.html');
                    }, 2000);
                } else {
                    toastr.error(response.message != "" ? response.message : "Não foi possível realizar o cadastro, tente novamente.");
                }
                vm.saving = false;
            });
            
        };

        vm.fnVoltar = function (wizard) {
            return wizard.getActivePosition() == 1 ? window.location.replace('index.html') : wizard.go(wizard.getActivePosition() - 1);
        };
    }
})();
