﻿(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('CentralPendenciaController', CentralPendenciaController);

        CentralPendenciaController.inject = [
        'BaseService',
        '$rootScope',
        'toastr',
        '$state',
        '$scope',
        'PersistentDataService',
        '$timeout',
        'PERFIL_ADMINISTRADOR',
        'oitozero.ngSweetAlert'
    ];

    function CentralPendenciaController(
        BaseService, 
        $rootScope, 
        toastr, 
        $state,
        $scope, 
        PersistentDataService, 
        $timeout, 
        PERFIL_ADMINISTRADOR,
        SweetAlert) {
        var vm = this;
        vm.headerItems = [{ name: 'Movimentações' }, { name: 'Central de pendências' }];
        vm.pagamentosRelatorio = [];
        vm.desabilitarBtnRelatorio = false;    
        vm.reenviando = false;
        vm.reenviandoValePedagio = false;

        vm.perfil = 1;

        vm.consultarDadosRelatorio = function (extensao) {
            if (extensao === 1)
                exportarEmExcel();

            if (extensao === 2)
                exportarEmPdf();
        };

        vm.atualizaTelaFrete = function () {
            vm.gridFreteOptions.dataSource.refresh();
        }

        vm.atualizaTelaValePedagio = function () {
            vm.gridValePedagioOptions.dataSource.refresh();
        }

        vm.novo = function () {
            toastr.info('Função indisponível no momento!');
        }

        vm.dataFrete = {
            startDate: moment().add(-7, 'days'),
            endDate: moment()
        };

        vm.dataValePedagio = {
            startDate: moment().add(-7, 'days'),
            endDate: moment()
        };
        vm.wizardAgora = 0;
        vm.dateOptions = {
            timePicker: false,
            timePicker24Hour: false,
            applyClass: 'btn-primary',
            locale: {
                applyLabel: "Aplicar",
                fromLabel: "De",
                format: "DD/MM/YYYY",
                toLabel: "Até",
                cancelLabel: 'Cancelar',
                customRangeLabel: 'Período',
                monthNames: ['Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho', 'Julho', 'Agosto', 'Setembro',
                    'Outubro', 'Novembro', 'Dezembro'],
                daysOfWeek: ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb']
            },
            ranges: {
                'Últimos 1 dias': [moment().add(-1, 'days'), moment()],
                'Últimos 7 dias': [moment().add(-7, 'days'), moment()]
            }
        };

        if ($rootScope.usuarioLogado.empresaId == null || $rootScope.usuarioLogado.empresaId == undefined) {
            vm.usuAdm = true;
        } else {
            vm.usuAdm = null;
        }

        vm.perfil = vm.usuAdm ? 1 : 0;

        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.perfil == PERFIL_ADMINISTRADOR;
        }; 

        $scope.$watch('vm.consultaEmpresa.selectedValue', function (newValue) {
            vm.empresaConsulta = vm.consultaEmpresa.selectedValue;
        });

        vm.reenviarPagamento = function (entity) {
            if(entity.status === 0){
                toastr.error("Pagamentos com status fechado, não podem ser reenviados!");   
                return;
            }
            vm.reenviando = true;

            var msg = 'Deseja reenviar o pagamento?';

            return Sistema.Msg.confirm(msg, function () {
                BaseService.get('CentralPendencias', 'ReenviarPagamentoEvento', {PagamentoEventoId: entity.id}).then(function (response) {
                    response.sucesso ? toastr.success('Pagamento efetuado com sucesso!') : toastr.error(response.mensagem);
                    vm.reenviando = false;
                    vm.gridFreteOptions.dataSource.refresh();
                    
                });
            }, function(){
                vm.reenviando = false;            
            })
        };
        var oldDateObj = new Date();
        vm.newDateObj = new Date(oldDateObj.getTime() - 30*60000);
        
        vm.apareceReenvio = function (entity){
            var dataCadastro = new Date(entity.dataCadastro)
            if(!vm.usuAdm) return;
            return entity.formaPagamento === 4 && (entity.status === 3 || (entity.status === 5 && dataCadastro < vm.newDateObj)) 
                || (entity.formaPagamento === 1 && (entity.status === 3 || entity.status === 2))
        }
        
        vm.gridFreteOptions = {
            data: [],
            enableFiltering: true,
            onRegisterApi: BaseService.dataGrid.getGridApiRegistration("gridFreteOptions"),
            urlRelatorio: "PainelPagamento/DadosRelatorioGridPagamentos",
            params: function () {
                return {
                    dataInicial: vm.dataFrete.startDate.format('DD/MM/YYYY').toString(),
                    dataFinal: vm.dataFrete.endDate.format('DD/MM/YYYY').toString(),
                    perfil: vm.perfil,
                    idEmpresa: vm.empresaConsulta
                }
            },
            dataSource: {
                autoBind: false,
                url: "CentralPendencias/ConsultarGridCentralPendencias",
                params: function () {
                    return {
                        dataInicial: vm.dataFrete.startDate.format('DD/MM/YYYY').toString(),
                        dataFinal: vm.dataFrete.endDate.format('DD/MM/YYYY').toString(),
                        perfil: vm.perfil,
                        EmpresaId: vm.empresaConsulta
                    }
                }
            },
            columnDefs: [{
                name: 'Ações',
                width: '8%',
                cellTemplate: '<div ng-if="!row.groupHeader" class="ui-grid-cell-contents" align="left" title="TOOLTIP">\
                    <button tooltip-placement="right" uib-tooltip="Visualizar"\
                            ng-disabled="row.entity.status=== 5"\
                            type="button" ui-sref="central-pendencias.central-pendencias-crud({link: row.entity.id})"\
                            ng-class="{ \'btn btn-xs btn-info\': true }">\
                        <i class="fa fa-eye"></i>\
                    </button>\
                    <button tooltip-placement="right" ng-show="grid.appScope.vm.apareceReenvio(row.entity)"\
                            ng-disabled="row.entity.contadorReenvio <= grid.appScope.vm.quantidadeReenvioPagamentoFrete-1 || grid.appScope.vm.reenviando"\
                            uib-tooltip="Reenviar pagamento" type="button"\
                            ng-click="grid.appScope.vm.reenviarPagamento(row.entity)"\
                            ng-class="row.entity.status=== 1 ? \'btn btn-xs btn-danger\' : \'btn btn-xs btn-danger\'">\
                        <i ng-class="\'fa fa-paper-plane-o\'"></i>\
                    </button>\
                    </div>\
                </div>'
            },
            {
                name: 'Status',
                displayName: 'Status',
                width: 170,
                field: 'status',
                serverField: 'status',
                enableFiltering: true,
                enum: true,
                enumTipo: 'EStatusTransacao',
                cellTemplate: '<div class="ui-grid-cell-contents" align="left">\
                                        <p ng-show="row.entity.status === 0"> Fechada </p>\
                                        <p ng-show="row.entity.status === 1"> Aberta </p>\
                                        <p ng-show="row.entity.status === 2"> Pendente </p>\
                                        <p ng-show="row.entity.status === 3"> Erro </p>\
                                        <p ng-show="row.entity.status === 4"> Cancelado </p>\
                                        <p ng-show="row.entity.status === 5"> Processando </p>\
                                        <p ng-show="row.entity.status === 6"> Não Concluído </p>\
                                   </div>'
            },
            {
                name: 'Forma Pagamento',
                displayName: 'Forma de pagamento',
                width: 170,
                field: 'formaPagamento',
                serverField: 'formaPagamento',
                enableFiltering: true,
                enum: true,
                enumTipo: 'EFormaPagamentoEvento',
                cellTemplate: '<div class="ui-grid-cell-contents" align="left">\
                                        <p ng-show="row.entity.formaPagamento === 1"> Depósito </p>\
                                        <p ng-show="row.entity.formaPagamento === 4"> Pix </p>\
                                   </div>'
            },
            {
                name: 'CodigoExterno',
                displayName: 'Código externo',
                type: 'number',
                width: '*',
                minWidth: 150,
                field: 'pagamentoExternoId',
                serverField: 'pagamentoExternoId',
                enableFiltering: true
            },
            {
                name: 'Empresa',
                displayName: 'Empresa',
                width: '*',
                minWidth: 150,
                field: 'empresaNome',
                serverField: 'viagem.empresa.NomeFantasia',
                enableFiltering: true
            },
            {
                name: 'Contratado',
                displayName: 'Contratado',
                width: 175,
                field: 'nomeProprietario',
                serverField: 'viagem.portadorProprietario.nome',
                enableFiltering: true
            },
            {
                name: 'Evento',
                displayName: 'Evento',
                width: 175,
                field: 'tipo',
                serverField: 'tipo',
                enableFiltering: true,
                enum: true,
                enumTipo: 'ETipoEvento',
                cellTemplate: '<div class="ui-grid-cell-contents" align="left">\
                                        <p ng-show="row.entity.tipo === 0"> Adiantamento </p>\
                                        <p ng-show="row.entity.tipo === 1"> Saldo </p>\
                                        <p ng-show="row.entity.tipo === 2"> Complemento </p>\
                                        <p ng-show="row.entity.tipo === 3"> Avulso </p>\
                                        <p ng-show="row.entity.tipo === 4"> Tarifa ANTT </p>\
                                        <p ng-show="row.entity.tipo === 5"> Cancelamento </p>\
                                   </div>'
            },
            {
                name: 'Valor',
                displayName: 'Valor',
                width: 175,
                field: 'valor',
                serverField: 'valor',
                enableFiltering: false
            },
            {
                name: 'FilialExternoId',
                displayName: 'Filial externo Id',
                width: 175,
                field: 'filialExternoId',
                serverField: 'viagem.filialExternoId',
                enableFiltering: true
            },
            {
                name: 'CPF/CNPJ Contratado',
                displayName: 'CPF/CNPJ Contratado',
                width: 175,
                field: 'cpfcnpjProprietario',
                serverField: 'viagem.portadorProprietario.cpfCnpj',
                enableFiltering: true
            },
            {
                name: 'Motorista',
                displayName: 'Motorista',
                width: 175,
                field: 'nomeMotorista',
                serverField: 'viagem.portadorMotorista.nome',
                enableFiltering: true
            },
            {
                name: 'CPF/CNPJ Motorista',
                displayName: 'CPF/CNPJ Motorista',
                width: 175,
                field: 'cpfcnpjMotorista',
                serverField: 'viagem.portadorMotorista.cpfCnpj',
                enableFiltering: true
            },
            {
                name: 'MotivoPendencia',
                displayName: 'Motivo pendência',
                width: 175,
                field: 'motivoPendencia',
                serverField: 'motivoPendencia',
                enableFiltering: false
            },
            {
                name: 'reenvio',
                displayName: 'N° de tentativas de envio',
                width: 175,
                field: 'contadorReenvio',
                serverField: 'contadorReenvio',
                enableFiltering: false
            },
            {
                name: 'ID Viagem',
                displayName: 'Código viagem',
                width: 170,
                field: 'viagemId',
                serverField: 'viagemId',
                type: 'number',
                enableFiltering: true
            },
            {
                name: 'CIOT',
                displayName: 'CIOT',
                width: 170,
                field: 'ciot',
                serverField: 'Viagem.ciotViagemId',
                type: 'number',
                enableFiltering: true
            },
            {
                name: 'ID Transação',
                displayName: 'Código transação',
                width: '*',
                minWidth: 150,
                field: 'id',
                serverField: 'id',
                type: 'number',
                enableFiltering: true
            }]
        };

        vm.reenviarPagamentoPedagio = function(entity){

            var msg = 'Deseja reenviar o pagamento?';

            return Sistema.Msg.confirm(msg, function () {
                vm.reenviandoValePedagio = true;
                BaseService.get('Pedagio', 'ReenviarPagamentoPedagio', {  IdPagamentoValePedagio: entity.id}).then(function (response) {
                    response.sucesso ? toastr.success(response.mensagem) : toastr.error(response.mensagem);
                    vm.gridValePedagioOptions.dataSource.refresh();
                    vm.reenviandoValePedagio = false;
                });
            }, function(){
                vm.reenviandoValePedagio = false;            
            })
        }

        vm.consultarParametroQuantidadeReenvio = function(){
            BaseService.get('Parametros', 'GetMaxTentativasReenvioPedagio').then(function (response) {
                vm.quantidadeReenvio = response;
            });
        }

        vm.consultarParametroQuantidadeReenvioPagamentoFrete = function(){
            BaseService.get('Parametros', 'GetMaxTentativasReenvioFrete').then(function (response) {
                vm.quantidadeReenvioPagamentoFrete = response;
            });
        }

        vm.gridValePedagioOptions = {
            data: [],
            enableFiltering: true,
            onRegisterApi: BaseService.dataGrid.getGridApiRegistration("gridValePedagioOptions"),
            dataSource: {
                autoBind: false,
                url: "CentralPendencias/ConsultarGridCentralPendenciasValePedagio",
                params: function () {
                    return {
                        dataInicial: vm.dataValePedagio.startDate.format('DD/MM/YYYY').toString(),
                        dataFinal: vm.dataValePedagio.endDate.format('DD/MM/YYYY').toString(),
                        status: vm.status,
                        EmpresaId: vm.empresaConsulta
                    }
                
                }
            },
            columnDefs: [{
                name: 'Ações',
                width: '5%',
                cellTemplate: '<div ng-if="!row.groupHeader" class="ui-grid-cell-contents" align="center" title="TOOLTIP">\
                    <button tooltip-placement="right" uib-tooltip="Visualizar"\
                            ng-disabled="row.entity.status=== 5"\
                            type="button" ui-sref="central-pendencias.central-pendencias-vp-crud({link: row.entity.id})"\
                            ng-class="{ \'btn btn-xs btn-info\': true }">\
                        <i class="fa fa-eye"></i>\
                    </button>\
                    <button tooltip-placement="right"\
                            ng-disabled="row.entity.contadorReenvio <= grid.appScope.vm.quantidadeReenvio-1 || grid.appScope.vm.reenviandoValePedagio"\
                            uib-tooltip="Reenviar pagamento" type="button"\
                            ng-click="grid.appScope.vm.reenviarPagamentoPedagio(row.entity)"\
                            class="btn btn-xs btn-danger"\
                            <i ng-class="\'fa fa-paper-plane-o\'"></i>\
                    </button>\
                    </div>\
                </div>'
            },
                {
                    name: 'id',
                    displayName: 'Código',
                    width: '150',
                    field: 'id',
                    type: 'number',
                    enableFiltering: true
                },
                {
                    name: 'pagamentoExternoId',
                    displayName: 'Código Requisição',
                    width: 150,
                    field: 'pagamentoExternoId',
                    type: 'number',
                    enableFiltering: true
                },
                {
                    name: 'CodigoValePedagio',
                    displayName: 'Código Vale Pedágio',
                    width: 170,
                    field: 'codigoValePedagio',
                    type: 'number',
                    enableFiltering: true
                },
                {
                    name: 'EmpresaNome',
                    displayName: 'Empresa',
                    width: 200,
                    field: 'empresaNome',
                    serverField: 'empresa.razaoSocial',
                    enableFiltering: true
                },
                {
                    name: 'mensagem',
                    displayName: 'Mensagem',
                    width: '*',
                    minWidth: 200,
                    field: 'mensagem',
                    serverField: 'ocorrencia',
                    enableFiltering: true
                },
                {
                    name: 'Status',
                    displayName: 'Status',
                    width: 220,
                    field: 'status',
                    enableFiltering: true,
                    serverField: 'StatusPagamentoPedagio',
                    enum: true,
                    enumTipo: 'EStatusPagamentoPedagioConsultaCentralPendencias'
                },
                {
                    name: 'Valor',
                    displayName: 'Valor',
                    width: '175',
                    field: 'valor',
                    type: 'number',
                    enableFiltering: true
                },
                {
                    name: 'FormaPagamento',
                    displayName: 'Forma de Pagamento',
                    width: 170,
                    field: 'formaPagamento',
                    enableFiltering: true,
                    enum: true,
                    enumTipo: 'EFormaPagamentoPedagio'
                },
                {
                    name: 'DataCadastro',
                    displayName: 'Data de Cadastro',
                    width: '*',
                    minWidth: 150,
                    field: 'dataCadastro',
                    enableFiltering: false
                },
                {
                    name: 'contadorReenvio',
                    displayName: 'Contador Reenvio',
                    width: '*',
                    minWidth: 150,
                    field: 'contadorReenvio',
                    enableFiltering: false
                }]
        };
        vm.consultarParametroQuantidadeReenvio();
        vm.consultarParametroQuantidadeReenvioPagamentoFrete();
        vm.consultaEmpresa = {
            columnDefs: [{
                name: 'Cód.',
                field: 'id',
                width: 60,
                type: 'number',
                primaryKey: true
            }, {
                name: 'Nome Fantasia',
                field: 'nomeFantasia',
                width: '*'
            }, {
                name: 'Razão Social',
                field: 'razaoSocial',
                width: '*'
            }, {
                name: 'Email',
                field: 'email',
                width: 120
            }],
            desiredValue: 'id',
            desiredText: 'nomeFantasia',
            url: 'Empresa/ConsultarGridEmpresaCombo',
            paramsMethod: function () {
                return {}
            }
        };
        }
})();