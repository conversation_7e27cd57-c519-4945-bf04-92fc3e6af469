(function () {
    'use strict';

    angular.module('bbcWeb').controller('BloqueioSpdCrudController', BloqueioSpdCrudController);

    BloqueioSpdCrudController.$inject = ['toastr', '$rootScope', 'BaseService', '$state', '$stateParams', '$window', 'PERFIL_ADMINISTRADOR', '$scope', '$timeout', 'PersistentDataService', 'DefaultsService', '$uibModal'];

    function BloqueioSpdCrudController(toastr, $rootScope,BaseService, $state,$stateParams, $window,PERFIL_ADMINISTRADOR,$scope, $timeout,PersistentDataService,DefaultsService,$uibModal) {
        var vm = this;
        vm.liberacaoBloqueioSpd = {};


        vm.filial = {};
        vm.menusPai = [];

        vm.estados = [];
        vm.cidades = [];
        vm.estadosDisabled = true;
        vm.cidadesDisabled = true;

        vm.headerItems = [{
            name: 'Cadastros'
        }, {
            name: 'Liberação de bloqueios SPD',
            link: 'bloqueio-spd.index'
        }, {
            name: $stateParams.link == 'novo' ? 'Novo' : 'Editar'
        }];

        var selfScope = PersistentDataService.get('BloqueioSpdCrudController');

        vm.isNew = function () {
            return $stateParams.link == 'novo';
        };

        vm.loadEdit = function (id) {
            BaseService.get('BloqueioSpd', 'ConsultarPorId', {
                idBloqueioSpd: id
            }).then(function (response) {
                if (!response.success) {
                    toastr.error(response.message);
                    return;
                } else {
                    vm.liberacaoBloqueioSpd.Id = response.data.id;
                    vm.liberacaoBloqueioSpd.Codigo = response.data.codigo;
                    vm.liberacaoBloqueioSpd.Descricao = response.data.descricao;
                    vm.liberacaoBloqueioSpd.Ativo = response.data.ativo;
                    vm.liberacaoBloqueioSpd.EmpresaId = response.data.empresaId;
                    vm.consultaEmpresa.selectedValue = response.data.empresaId;
                    vm.consultaEmpresa.selectedText = response.data.empresa.nomeFantasia;
                }
            });
        };

        vm.save = function (form) {
            if (!form.$valid) {
                toastr.error('Todos os campos obrigatórios devem ser preenchidos corretamente com seus respectivos valores.');
                return;
            }

            if (vm.isSaving === true) {
                return;
            }

            if (vm.isAdmin) {
                vm.liberacaoBloqueioSpd.EmpresaId = vm.consultaEmpresa.selectedValue;
            }

            if (vm.isNew()) {
                vm.liberacaoBloqueioSpd.Ativo = 1;
                vm.liberacaoBloqueioSpd.Id = 0;
            }

            vm.isSaving = true;
            BaseService.post('BloqueioSpd', 'Salvar', vm.liberacaoBloqueioSpd).then(function (response) {
                vm.isSaving = false;
                if (response.success) {
                    toastr.success(response.message);
                    $state.go('bloqueio-spd.index');
                } else
                    toastr.error(response.message);
            });
        };

        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.administrador == true;
        };

        function carregarEmpresas() {
            vm.consultaEmpresa = {
                columnDefs: [{
                    name: 'Código',
                    field: 'id',
                    width: 80,
                    type: 'number',
                    primaryKey: true
                }, {
                    name: 'Nome Fantasia',
                    field: 'nomeFantasia',
                    width: '*',
                    minWidth: 150
                }, {
                    name: 'Razão Social',
                    field: 'razaoSocial',
                    width: '*',
                    minWidth: 150
                }, {
                    name: 'Email',
                    field: 'email',
                    width: '*',
                    minWidth: 150
                }],
                desiredValue: 'id',
                desiredText: 'nomeFantasia',
                url: 'Empresa/ConsultarGridEmpresaCombo',
                paramsMethod: function () {
                    return {}
                },
            };
        };

        vm.onClickVoltar = function (wizard) {
            $state.go('bloqueio-spd.index');
        };

        carregarEmpresas();

        $scope.$on('$stateChangeStart', function (arg1, toState) {
            if (toState.name === 'bloqueio-spd.index')
                PersistentDataService.remove('BloqueioSpdCrudController');
            else
                PersistentDataService.store('BloqueioSpdCrudController', vm, "Cadastro - Liberação de bloqueios SPD", null, "bloqueio-spd.bloqueio-spd-crud", vm.liberacaoBloqueioSpd.Id);
        });

        if (angular.isDefined(selfScope))
            angular.extend(vm, selfScope.data);
        else
            if (!vm.isNew())
                vm.loadEdit($stateParams.link);
            else {
                vm.liberacaoBloqueioSpd.Id = "Auto";

            }

        $timeout(function () {
            PersistentDataService.remove('BloqueioSpdController');
        }, 15);
    }
})();