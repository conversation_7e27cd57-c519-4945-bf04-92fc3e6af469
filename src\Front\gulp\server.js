'use strict';

var path = require('path');
var gulp = require('gulp');
var conf = require('./conf');
var gulpNgConfig = require('gulp-ng-config');

var browserSync = require('browser-sync');
var browserSyncSpa = require('browser-sync-spa');

var util = require('util');

var proxyMiddleware = require('http-proxy-middleware');

function browserSyncInit(baseDir, browser) {
    browser = browser === undefined ? 'default' : browser;

    var routes = null;
    if (baseDir === conf.paths.src || (util.isArray(baseDir) && baseDir.indexOf(conf.paths.src) !== -1)) {
        routes = {
            '/bower_components': 'bower_components'
        };
    }

    var server = {
        baseDir: baseDir,
        routes: routes
    };

    /*
     * You can add a proxy to your backend by uncommenting the line below.
     * You just have to configure a context which will we redirected and the target url.
     * Example: $http.get('/users') requests will be automatically proxified.
     *
     * For more details and option, https://github.com/chimurai/http-proxy-middleware/blob/v0.9.0/README.md
     */
    // server.middleware = proxyMiddleware('/users', {target: 'http://jsonplaceholder.typicode.com', changeOrigin: true});

    browserSync.instance = browserSync.init({
        startPath: '/',
        server: server,
        browser: browser
    });
}

browserSync.use(browserSyncSpa({
    selector: '[ng-app]' // Only needed for angular apps
}));

// gulp.task('serve', ['watch'], function() {
//     browserSyncInit([path.join(conf.paths.tmp, '/serve'), conf.paths.src]);
// });

// gulp.task('serve:dist', ['build-dev'], function() {
//     browserSyncInit(conf.paths.dist);
// });


gulp.task('configServe:dev', function () {
    gulp.src(path.join(conf.paths.src, '/app/environment.json'))
        .pipe(gulpNgConfig('atsWeb.environment', {
            environment:  window.location.protocol +'//'+ window.location.host+'/Web/'
        }))
        .pipe(gulp.dest(path.join(conf.paths.src, '/app/')));
});

gulp.task('configServe:qa', function () {
    gulp.src(path.join(conf.paths.src, '/app/environment.json'))
        .pipe(gulpNgConfig('atsWeb.environment', {
            environment: 'qa'
        }))
        .pipe(gulp.dest(path.join(conf.paths.src, '/app/')));
});

gulp.task('configServe:homolog', function () {
    gulp.src(path.join(conf.paths.src, '/app/environment.json'))
        .pipe(gulpNgConfig('atsWeb.environment', {
            environment: 'homolog'
        }))
        .pipe(gulp.dest(path.join(conf.paths.src, '/app/')));
});

gulp.task('configServe:prod', function () {
    gulp.src(path.join(conf.paths.src, '/app/environment.json'))
        .pipe(gulpNgConfig('atsWeb.environment', {
            environment: 'prod'
        }))
        .pipe(gulp.dest(path.join(conf.paths.src, '/app/')));
});

gulp.task('start-serve', ['watch'], function () {
    browserSyncInit([path.join(conf.paths.tmp, '/serve'), conf.paths.src]);
});

gulp.task('serve', ['start-serve']);
gulp.task('serve-dev', ['configServe:dev', 'start-serve']);
gulp.task('serve-qa', ['configServe:qa', 'start-serve']);

gulp.task('serve:dist', ['build-dev'], function () {
    browserSyncInit(conf.paths.dist);
});