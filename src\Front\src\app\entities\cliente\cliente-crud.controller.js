(function () {
    'use strict';

    angular.module('bbcWeb').controller('ClienteCrudController', ClienteCrudController);

    ClienteCrudController.$inject = ['toastr', '$rootScope', 'BaseService', '$state', '$stateParams', '$window', 'PERFIL_ADMINISTRADOR', '$scope', '$timeout', 'PersistentDataService', 'DefaultsService', '$uibModal'];

    function ClienteCrudController(
        toastr, $rootScope,
        BaseService, $state,
        $stateParams, $window,
        PERFIL_ADMINISTRADOR,
        $scope, $timeout,
        PersistentDataService,
        DefaultsService,
        $uibModal
    ) {
        var vm = this;
        vm.cliente = {};
        vm.menusPai = [];

        vm.estados = [];
        vm.cidades = [];
        vm.estadosDisabled = true;
        vm.cidadesDisabled = true;

        vm.headerItems = [{
            name: '<PERSON><PERSON><PERSON><PERSON>'
        }, {
            name: 'Clientes',
            link: 'cliente.index'
        }, {
            name: $stateParams.link == 'novo' ? 'Novo' : 'Editar'
        }];

        var selfScope = PersistentDataService.get('ClienteCrudController');


        function carregarFuncoesIniciais() {
            carregarEstados();
        };

        if ($stateParams.link == 'novo')
            vm.cliente.Id = 'Auto';

        vm.isNew = function () {
            return $stateParams.link == 'novo';
        };

        vm.load = function () {
            carregarFuncoesIniciais();
        };

        vm.loadEdit = function (id) {
            BaseService.get('Cliente', 'ConsultarPorId', {
                idCliente: id
            }).then(function (response) {
                if (!response.success) {
                    toastr.error(response.message);
                    return;
                } else {
                    carregarCidades(response.data.estadoId);
                    vm.cliente.Id = response.data.id;
                    vm.cliente.NomeFantasia = response.data.nomeFantasia;
                    vm.cliente.RazaoSocial = response.data.razaoSocial;
                    vm.cliente.CpfCnpj = response.data.cpfCnpj;
                    vm.cliente.Email = response.data.email;
                    vm.cliente.Celular = response.data.celular;
                    vm.cliente.Telefone = response.data.telefone;
                    vm.cliente.Cep = response.data.cep;
                    vm.cliente.EstadoId = response.data.estadoId;
                    vm.cliente.EmpresaId = response.data.empresaId;
                    vm.cliente.CidadeId = response.data.cidadeId;
                    vm.cliente.Endereco = response.data.endereco;
                    vm.cliente.Bairro = response.data.bairro;
                    vm.cliente.EnderecoNumero = response.data.enderecoNumero;
                    vm.cliente.Complemento = response.data.complemento;

                    vm.consultaEmpresa.selectedValue = response.data.empresaId;
                    vm.consultaEmpresa.selectedText = response.data.empresa.nomeFantasia;
                }
            });
        };

        function carregarEmpresas() {
            vm.consultaEmpresa = {
                columnDefs: [{
                    name: 'Código',
                    field: 'id',
                    width: 80,
                    type: 'number',
                    primaryKey: true
                }, {
                    name: 'Nome Fantasia',
                    field: 'nomeFantasia',
                    width: '*',
                    minWidth: 150
                }, {
                    name: 'Razão Social',
                    field: 'razaoSocial',
                    width: '*',
                    minWidth: 150
                }, {
                    name: 'Email',
                    field: 'email',
                    width: '*',
                    minWidth: 150
                }],
                desiredValue: 'id',
                desiredText: 'nomeFantasia',
                url: 'Empresa/ConsultarGridEmpresaCombo',
                paramsMethod: function () {
                    return {}
                },
            };
        };

        vm.buscarEndereco = function (cep) {
            if (cep) {
                limparEndereco();
                BaseService.getEndereco(cep).then(function (response) {
                    if (response && response.erro) {
                        toastr.warning("Endereço não localizado.");
                    }

                    carregarEstados();

                    $timeout(function () {
                        var estado = _.find(vm.estados, ['sigla', response.uf]);

                        if (angular.isDefined(estado))

                            vm.cliente.EstadoId = estado.id;
                        carregarCidades(vm.cliente.EstadoId);
                        $timeout(function () {
                            var cidade = _.find(vm.cidades, ['descricao', response.localidade]);
                            if (angular.isDefined(cidade))
                                vm.cliente.CidadeId = cidade.id;
                        }, 1500);
                    }, 1500);

                    $timeout(function () {
                        if (angular.isDefined(response.logradouro))
                            vm.cliente.Endereco = response.logradouro;
                    }, 1500);

                    $timeout(function () {
                        if (angular.isDefined(response.bairro))
                            vm.cliente.Bairro = response.bairro;
                    }, 1500);

                });
            }
        };

        function limparEndereco() {
            vm.cliente.EstadoId = null;
            vm.cliente.CidadeId = null;
            vm.estados = null;
            vm.cidades = null;
            vm.cliente.Endereco = null;
            vm.cliente.Bairro = null;
            vm.cliente.EnderecoNumero = null;
            vm.cliente.Complemento = null;
        };

        vm.estadoChange = function (estadoId) {
            vm.cidades = null;
            vm.cidadesDisabled = true;
            vm.cliente.CidadeId = null;
            carregarCidades(estadoId);
        };

        function carregarEstados() {
            BaseService.get("Endereco", "ConsultarEstados")
                .then(function (response) {
                    if (response.success) {
                        vm.estados = response.data;
                        vm.estadosDisabled = false;
                    }
                });
        };

        function carregarCidades(EstadoId) {
            BaseService.get("Endereco", "ConsultarCidadesPorEstado", {
                estadoId: EstadoId
            }).then(function (response) {
                if (response.success) {
                    vm.cidades = response.data;
                    vm.cidadesDisabled = false;
                }
            });
        };

        vm.save = function (form) {
            if (!form.$valid) {
                toastr.error('Todos os campos obrigatórios devem ser preenchidos corretamente com seus respectivos valores');
                return;
            }

            if (vm.isSaving === true) {
                return;
            }

            if (vm.isAdmin) {
                vm.cliente.EmpresaId = vm.consultaEmpresa.selectedValue;
            }
            if (vm.cliente.Id == "Auto") {
                vm.cliente.Id = "0";
            }
            vm.isSaving = true;
            BaseService.post('Cliente', 'SaveCliente', vm.cliente).then(function (response) {
                vm.isSaving = false;
                if (response.success) {
                    toastr.success(response.message);
                    $state.go('cliente.index');
                } else
                    toastr.error(response.message);
            });
        };

        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.administrador == true;
        };

        function init() {
            carregarEmpresas();
            carregarFuncoesIniciais();

            if (vm.isNew()) {
                vm.cliente.Id = 'Auto';
            }

            if (vm.isNew()) {
                vm.cliente.Id = 'Auto';
            }
        }; 

        vm.onClickAvancar = function (wizard) {
            wizard.go(wizard.getActivePosition() + 1);
        };

        vm.onClickVoltar = function (wizard) {

            var ativoIndex = wizard.getActivePosition();
            if (ativoIndex == 1)
                $state.go('cliente.index');

            wizard.go(ativoIndex - 1);
        };

        init();

        $scope.$on('$stateChangeStart', function (arg1, toState) {
            if (toState.name === 'cliente.index')
                PersistentDataService.remove('ClienteCrudController');
            else
                PersistentDataService.store('ClienteCrudController', vm, "Cadastro - Veículo", null, "cliente.cliente-crud", vm.cliente.Id);
        });

        if (angular.isDefined(selfScope))
            angular.extend(vm, selfScope.data);
        else
            if (!vm.isNew())
                vm.loadEdit($stateParams.link);
            else {
                vm.Cliente = 0;
                vm.modulos = 1;

            }

        $timeout(function () {
            PersistentDataService.remove('ClienteController');
        }, 15);
    }
})();