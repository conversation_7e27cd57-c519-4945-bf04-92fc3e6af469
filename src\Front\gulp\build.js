'use strict';

var path = require('path');
var gulp = require('gulp');
var conf = require('./conf');
var revFormat = require('gulp-rev-format');
var replace = require('gulp-string-replace');
var terser = require('gulp-terser');
var obfuscator = require('gulp-javascript-obfuscator'); 
var $ = require('gulp-load-plugins')({
    pattern: ['gulp-*', 'main-bower-files', 'del']
});

var ambiente = "";

var $ = require('gulp-load-plugins')({
    pattern: ['gulp-*', 'main-bower-files', /*'uglify-save-license',*/ 'del']
});

function guid() {
    function s4() {
        return Math.floor((1 + Math.random()) * 0x10000)
            .toString(16)
            .substring(1);
    }
    return s4() + s4() + s4() + s4() +
        s4() + s4() + s4() + s4();
}

gulp.task('partials', function() {
    return gulp.src([
            path.join(conf.paths.src, '/app/**/*.html'),
            path.join(conf.paths.tmp, '/serve/app/**/*.html')
        ])
        .pipe($.minifyHtml({
            empty: true,
            spare: true,
            quotes: true
        }))
        .pipe($.angularTemplatecache('templateCacheHtml.js', {
            module: 'bbcWeb',
            root: 'app'
        }))
        .pipe(gulp.dest(conf.paths.tmp + '/partials/'));
});

gulp.task('html', ['inject', 'partials'], function() {
    var partialsInjectFile = gulp.src(path.join(conf.paths.tmp, '/partials/templateCacheHtml.js'), {
        read: false
    });
    var partialsInjectOptions = {
        starttag: '<!-- inject:partials -->',
        ignorePath: path.join(conf.paths.tmp, '/partials'),
        addRootSlash: false
    };

    var htmlFilter = $.filter('*.html', {
        restore: true
    });
    var jsFilter = $.filter('**/*.js', {
        restore: true
    });
    var cssFilter = $.filter('**/*.css', {
        restore: true
    });
    var assets, retorno;
    var guidCode = guid();
    console.log(guidCode);

    return gulp.src([path.join(conf.paths.tmp, '/serve/*.html'), path.join('!' + conf.paths.src, '/assets/**/*.js')])
        .pipe($.inject(partialsInjectFile, partialsInjectOptions))
        .pipe(assets = $.useref.assets())
        .pipe(jsFilter)
        .pipe($.sourcemaps.init())
        .pipe($.ngAnnotate())
        /*.pipe($.uglify({
            preserveComments: $.uglifySaveLicense
        })).on('error', conf.errorHandler('Uglify'))*/
        .pipe($.sourcemaps.write('maps'))
        .pipe(jsFilter.restore)
        .pipe(cssFilter)
        .pipe($.sourcemaps.init())
        .pipe($.replace('../../bower_components/bootstrap/fonts/', '../fonts/'))
        .pipe($.replace('../../bower_components/fontawesome/webfonts/', '../fonts/'))
        .pipe($.minifyCss({
            processImport: false
        }))
        .pipe($.sourcemaps.write('maps'))
        .pipe(cssFilter.restore)
        .pipe(assets.restore())
        .pipe($.useref())
        .pipe($.revReplace())
        .pipe(htmlFilter)
        .pipe($.minifyHtml({
            empty: true,
            spare: true,
            quotes: true,
            conditionals: true
        }))
        .pipe(htmlFilter.restore)
        .pipe(gulp.dest(path.join(conf.paths.dist, '/')))
        .pipe($.size({
            title: path.join(conf.paths.dist, '/'),
            showFiles: true
        }));
});

gulp.task('html-no-map', ['inject', 'partials'], function() {
  var partialsInjectFile = gulp.src(path.join(conf.paths.tmp, '/partials/templateCacheHtml.js'), {
      read: false
  });
  var partialsInjectOptions = {
      starttag: '<!-- inject:partials -->',
      ignorePath: path.join(conf.paths.tmp, '/partials'),
      addRootSlash: false
  };

  var htmlFilter = $.filter('*.html', {
      restore: true
  });
  var jsFilter = $.filter('**/*.js', {
      restore: true
  });
  var cssFilter = $.filter('**/*.css', {
      restore: true
  });
  var assets, retorno;
  var guidCode = guid();

  return gulp.src([path.join(conf.paths.tmp, '/serve/*.html'), path.join('!' + conf.paths.src, '/assets/**/*.js')])
      .pipe($.inject(partialsInjectFile, partialsInjectOptions))
      .pipe(assets = $.useref.assets())
      .pipe($.rev())
      .pipe(revFormat({
          prefix: '',
          suffix: guidCode,
          lastExt: false
      }))
      .pipe(jsFilter)
      // Condicionalmente aplicar ofuscação
      .pipe($.ngAnnotate())  // Anotação Angular (se necessário)
      .pipe($.if(function(file) {
          return file.path.includes('app');  // Verifica se o arquivo contém "app" no nome (ou outro padrão)
      }, obfuscator({
          compact: true,
          controlFlowFlattening: true,
          stringArray: true,
          stringArrayEncoding: ['base64']
      })))  // Ofusca o arquivo específico
      .pipe(terser())  // Também pode usar terser se quiser compactar
      .pipe(jsFilter.restore)
      .pipe(cssFilter)
      .pipe($.replace('../../bower_components/bootstrap/fonts/', '../fonts/'))
      .pipe($.replace('../../bower_components/fontawesome/webfonts/', '../fonts/'))
      .pipe($.minifyCss({
          processImport: false
      }))
      .pipe(cssFilter.restore)
      .pipe(assets.restore())
      .pipe($.useref())
      .pipe($.revReplace())
      .pipe(htmlFilter)
      .pipe($.minifyHtml({
          empty: true,
          spare: true,
          quotes: true,
          conditionals: true
      }))
      .pipe(htmlFilter.restore)
      .pipe(gulp.dest(path.join(conf.paths.dist, '/')))
      .pipe($.size({
          title: path.join(conf.paths.dist, '/'),
          showFiles: true
      }));
});

// Only applies for fonts from bower dependencies
// Custom fonts are handled by the "other" task
gulp.task('fonts', function() {

    gulp.src(['bower_components/angular-ui-grid/*.{ttf, woff}'])
        .pipe($.flatten())
        .pipe(gulp.dest(path.join(conf.paths.dist, '/styles/')));

    return gulp.src($.mainBowerFiles()
            .concat('bower_components/bootstrap/fonts/*.{eot,svg,ttf,woff,woff2}')
            .concat('bower_components/fontawesome/webfonts/*.{eot,svg,ttf,woff,woff2}')
            .concat('bower_components/fontawesome/svgs/solid/*.{eot,svg,ttf,woff,woff2}'))
        .pipe($.filter('*.{eot,svg,ttf,woff,woff2}'))
        .pipe($.flatten())
        .pipe(gulp.dest(path.join(conf.paths.dist, '/fonts/')));
});

gulp.task('other', function() {
    var fileFilter = $.filter(function(file) {
        return file.stat.isFile();
    });

    return gulp.src([
        path.join(conf.paths.src, '/**/*'),
        path.join('!' + conf.paths.src, '/**/*.{html,css,js,less,json}'),
        path.join('!' + conf.paths.src, '/assets/lib/multi.js')
    ]).pipe(fileFilter).pipe(gulp.dest(path.join(conf.paths.dist, '/')));
});

gulp.task('clean', function() {
    return $.del([
        path.join(conf.paths.dist, '/'), 
        path.join(conf.paths.tmp, '/'),
        path.join(conf.paths.src, '/assets/lib/temp-files/') // Exemplo: diretórios temporários adicionais
    ]);
});

gulp.task('clean:dist', function() {
    return $.del([path.join(conf.paths.dist, '/')]);
});

gulp.task('clean:tmp', function() {
    return $.del([path.join(conf.paths.tmp, '/')]);
});


gulp.task('webshim', function() {
    return gulp.src(conf.paths.src + '/assets/lib/*').pipe(gulp.dest(conf.paths.dist + '/scripts/'));
});

gulp.task('build', ['html-no-map', 'fonts', 'other'], function() {
    gulp.src(conf.paths.src + '/assets/lib/shims/**').pipe(gulp.dest(conf.paths.dist + '/scripts/shims'));
    //$.del(path.join(conf.paths.dist, '/assets/lib'));

    gulp.src([conf.paths.src + '/assets/lib/*.js', "!" + conf.paths.src + '/assets/lib/multi.js'])
        .pipe(gulp.dest(conf.paths.dist + '/assets/lib/'));

    gulp.src(conf.paths.src + '/assets/css/*.css')
        .pipe(gulp.dest(conf.paths.dist + '/assets/css/'));

    gulp.src(conf.paths.dist + '/*.html')
        .pipe(replace('http://localhost:51001/', ambiente))
        .pipe(gulp.dest(conf.paths.dist + '/'));
});