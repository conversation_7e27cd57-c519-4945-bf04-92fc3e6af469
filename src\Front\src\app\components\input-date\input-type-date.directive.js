(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .directive('input', function () {
            return {
                restrict: 'E',
                priority: -1,
                link: function (scope, element, attrs) {
                    // Atualiza os campos date via webShim,
                    // esta correção se deve ao fato de que
                    // o firefox não está 100% integrado com o
                    // HTML5, então não existe type=date no FF
                    // e o webShim, arruma isso
                    if (attrs.type == 'date') $('input[type="date"]').updatePolyfill();
                }
            };
        });
})();