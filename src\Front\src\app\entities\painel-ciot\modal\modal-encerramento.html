<style>
    .custom-modal-width .modal-dialog {
        width: 80%;
        max-width: none;
    }
</style>
<div>
    <form id="formEncerramento" name="formEncerramento" novalidate show-validation ng-submit="vm.adicionarViagem(formEncerramento)">
        <div class="modal-header">
            <button type="button" class="close" ng-click="$dismiss()" aria-label="Close">
                <em class="fa fa-times"></em>
            </button>
            <h3 class="modal-title" id="modal-title">Encerramento do CIOT</h3>
        </div>
        <div class="modal-body bd-example-modal-xl">
            <div class="row">
                <consulta-padrao-modal labelsize="'control-label col-xs-12 col-sm-3 col-md-4 col-lg-3'"
                    tabledefinition="vm.consultaViagem" idname="Viagem" idmodel="Viagem" label="'Viagem:'"
                    functionclear="vm.limpaInfosViagem"
                    validate-on="blur" placeholder="'Selecione uma Viagem'">
                </consulta-padrao-modal>
            </div>
            <div class="row">
                <consulta-padrao-modal labelsize="'control-label col-xs-12 col-sm-3 col-md-4 col-lg-3'"
                    tabledefinition="vm.consultaCidadeOrigem" idname="Origem" idmodel="Origem" label="'Cidade origem:'"
                    validate-on="blur" placeholder="'Selecione uma Cidade'" ng-required="true" ng-disabled="vm.disableFields"
                    required-message="'Cidade origem é obrigatório'" required>
                </consulta-padrao-modal>
                <consulta-padrao-modal labelsize="'control-label col-xs-12 col-sm-3 col-md-4 col-lg-3'"
                    tabledefinition="vm.consultaCidadeDestino" idname="Destino" idmodel="Destino"
                    label="'Cidade destino:'" placeholder="'Selecione uma Cidade'" ng-required="true"
                    ng-disabled="vm.disableFields" required-message="'Cidade destino é obrigatório'" required>
                </consulta-padrao-modal>
            </div>
            <div class="row">
                <consulta-padrao-modal labelsize="'control-label col-xs-12 col-sm-3 col-md-4 col-lg-3'"
                    tabledefinition="vm.consultaNaturezaCarga" idname="Natureza" idmodel="Natureza"
                    label="'Natureza carga:'" placeholder="'Selecione uma Natureza Carga'" 
                    ng-required="true" ng-disabled="vm.disableFieldNaturezaCarga" required-message="'Natureza Carga é obrigatório'">
                </consulta-padrao-modal>
                <div class="col-sm-12 col-md-6 col-lg-6">
                    <div class="form-group">
                        <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label text-right">
                            <span class="text-danger mr-5">*</span>Peso da carga:</label>
                        <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                            <span class="input-group-addon">KG</span>
                            <div class="message-required-size">
                                <input type="text" ats-price maxlength="9" placeholder="Informe o valor do peso da carga"
                                    ng-model="vm.pesoCarga" class="form-control" id="PesoCarga" validate-on="blur"
                                    name="PesoCarga" required-message="'Peso da carga é obrigatório'" ng-disabled="vm.disableFieldPesoCarga"
                                    ng-required="true" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                    <div class="form-group">
                        <label class="control-label col-xs-12 col-sm-3 col-md-4 col-lg-3 text-right"><span class="text-danger mr-5">*</span>Valor tarifa ANTT:</label>
                        <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                            <span class="input-group-addon">R$ </span>
                            <div class="message-required-size">
                                <input type="text" maxlength="16" placeholder="Informe o valor da tarifa"
                                    ng-model="vm.valorTarifaAntt" ats-price name="Valor das Tarifas" 
                                    class="form-control" validate-on="blur" id="Valor das Tarifas" validate-on="blur"
                                    ng-required="true" required-message="'Valor da tarifa é obrigatório'" />
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-sm-12 col-md-6 col-lg-6">
                    <div class="form-group">
                        <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label text-right">
                            Valor do frete:
                        </label>
                        <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                            <span class="input-group-addon">R$</span>
                            <div class="message-required-size">
                                <input type="text" ng-model="vm.valorFrete" ats-price validate-on="blur" ng-disabled="vm.disableFieldValorFrete"
                                    class="form-control" id="ValorFrete" name="ValorFrete" placeholder="Informe o valor do frete"/>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12 col-md-6 col-lg-6">
                    <div class="form-group">
                        <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label text-right">
                            Valor dos impostos:
                        </label>
                        <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                            <span class="input-group-addon">R$</span>
                            <input type="text" ats-price validate-on="blur"
                                ng-model="vm.valorImposto" class="form-control" 
                                placeholder="Informe o valor do imposto"/>
                        </div>
                    </div>
                </div>
                <div class="col-sm-12 col-md-6 col-lg-6">
                    <div class="form-group">
                        <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label text-right">Valor das despesas:</label>
                        <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                            <span class="input-group-addon">R$</span>
                            <input type="text" ng-model="vm.valorDespesas" ats-price validate-on="blur" class="form-control"
                            placeholder="Informe o valor da despesa" />
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12 col-md-6 col-lg-6">
                    <div class="form-group">
                        <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label text-right">Valor do combustível:</label>
                        <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                            <span class="input-group-addon">R$</span>
                            <input type="text" ng-model="vm.valorCombustivel" ats-price validate-on="blur" 
                             placeholder="Informe o valor do combustível" class="form-control" />
                        </div>
                    </div>
                </div>
                <div class="col-sm-12 col-md-6 col-lg-6">
                    <div class="form-group">
                        <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label text-right">Valor dos pedágios:</label>
                        <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                            <span class="input-group-addon">R$</span>
                            <input type="text" ng-model="vm.valorPedagio" ats-price
                            placeholder="Informe o valor do pedágio" class="form-control" />
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="ibox-title" style="margin-bottom: 11px; margin-top: 10px;">
                    <div class="pull-left">
                        <button type="submit"
                            class="mr-5 btn-labeled btn btn-info">
                            <span class="btn-label text-left"><i class="fa fa-plus"></i></span>
                            <span class="pl-5">Adicionar Viagem</span>
                        </button>
                    </div>
                    <div class="form-group" style="padding-left: 135px;">
                        <button ng-click="vm.limpar()"
                            type='button' class="mr-5 btn-labeled btn btn-danger">
                            <span class="pl-3">Limpar</span>
                        </button>
                    </div>
                </div>
            </div>
            <div class="table-responsive">
                <table class="table table-striped col-xs-12">
                    <thead>
                        <tr>
                            <th scope="col">Cidade origem</th>
                            <th scope="col">Cidade destino</th>
                            <th scope="col">Natureza da carga</th>
                            <th scope="col">Peso da carga</th>
                            <th scope="col">Valor do frete</th>
                            <th scope="col">Valor da tarifa ANTT</th>
                            <th scope="col">Valor do impostos</th>
                            <th scope="col">Valor da despesas</th>
                            <th scope="col">Valor do combustível</th>
                            <th scope="col">Valor do pedágio</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr ng-repeat="viagem in vm.viagensOperacaoTransporte">
                            <td>{{viagem.nomeOrigem}}</td>
                            <td>{{viagem.nomeDestino}}</td>
                            <td>{{viagem.codigoNaturezaCarga}}</td>
                            <td>{{viagem.peso}}</td>
                            <td>R${{viagem.valorFrete}}</td>
                            <td>R$ 
                                <input type="text" ng-model="viagem.valorTarifa" 
                                class="" style="background: none; width: 60% !important;" ui-number-mask="2"/>
                            </td>
                            <td>R$ 
                                <input type="text" ng-model="viagem.valorImposto"
                                class="" style="background: none;width: 60% !important;" ui-number-mask="2"/>
                            </td>
                            <td>R$ 
                                <input type="text" ng-model="viagem.valorDespesas" 
                                class="" style="background: none;width: 60% !important;" ui-number-mask="2"/>
                            </td>
                            <td>R$ 
                                <input type="text" ng-model="viagem.valorCombustivel" 
                                class="" style="background: none;width: 60% !important;" ui-number-mask="2"/>
                            </td>
                            <td>R$ 
                                <input type="text" ng-model="viagem.valorPedagio" 
                                class="" style="background: none;width: 60% !important;" ui-number-mask="2"/>
                            </td>
                            <td class="text-center" style="vertical-align: middle">
                                <button type="button"
                                    ng-disabled=""
                                    uib-tooltip="Remover" class="btn btn-xs btn-danger"
                                    ng-click="vm.removerViagem(this)">
                                    <i class="fa fa-trash-o"></i>
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </form>
    <div class="modal-footer">
        <button type="button" ng-click="vm.encerrar()" ng-disabled="vm.viagensOperacaoTransporte.length == 0 || vm.saving" class="btn btn-success">
            Encerrar
        </button>
    </div>
</div>