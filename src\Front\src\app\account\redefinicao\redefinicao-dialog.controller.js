(function() {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('RedefinicaoDialogController', RedefinicaoDialogController);
    RedefinicaoDialogController.inject = ['toastr', 'BaseService', '$log', '$uibModalStack', '$rootScope', '$window'];

    function RedefinicaoDialogController(toastr, BaseService, $log, $uibModalStack, $rootScope, $window) {
        var vm = this;

        vm.senha = {
            SenhaAtual: null,
            NovaSenha: null,
            ConfirmarNovaSenha: null
        };

        vm.isLoading = false;

        vm.submit = function() {
            vm.isLoading = true;
            try {
                if (vm.senha.NovaSenha === vm.senha.ConfirmarNovaSenha) {
                    var objSenha = {
                        UsuarioId: $window.localStorage.getItem('idUsuario'),
                        SenhaAntiga: vm.senha.Senha<PERSON>tual,
                        ConfirmacaoSenha: vm.senha.ConfirmarNovaSenha,
                        NovaSenha: vm.senha.NovaSenha
                    };
                    BaseService.post('Usuario', 'AlterarSenhaUsuario', objSenha).then(function(response) {
                        if (response.success) {
                            toastr.success(response.message);
                            var modalAberta = $uibModalStack.getTop();
                            $uibModalStack.dismiss(modalAberta.key);
                        } else {
                            vm.isLoading = false;
                            toastr.error(response.message)
                        }
                    });
                } else
                    toastr.error('Os valores para nova senha e a confirmação devem ser os mesmos');
                    vm.isLoading = false;
            } catch (error) {
                toastr.error(error);
                vm.isLoading = false;
            }
        }
    }
})();