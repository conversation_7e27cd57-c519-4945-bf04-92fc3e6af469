<div ng-controller="LogSmsController as vm">
    <form-header items="vm.headerItems" head="'SMS Enviadas'"></form-header>
    <div class="animated fadeIn filter-position">
        <div class="row">
            <div class="col-lg-12">
                <div class="ibox-title">
                    <h5>SMS Enviadas</h5>
                </div>
                <div class="ibox">
                    <div class="ibox-content">
                        <div ng-style="{height: vm.gridOptions.getGridHeight()}" ui-grid="vm.gridOptions" class="grid" style="width: 100%;" ui-grid-pinning ui-grid-save-state
                            ui-grid-pagination ui-grid-auto-resize ui-grid-resize-columns ui-grid-grouping>
                        </div>
                        <div class="modal-footer">
                            <button type="button" ng-disabled="vm.disableButtons"  class="btn btn-xs btn-primary" ng-click="vm.gerarRalatorio('pdf')"><i class="fa fa-file-pdf-o"></i> Exportar em PDF</button>
                            <button type="button" ng-disabled="vm.disableButtons"  class="btn btn-xs btn-primary" ng-click="vm.gerarRalatorio('xls')"><i class="fa fa-file-excel-o"></i> Exportar em Excel</button>
                        </div>
                    </div>                    
                </div>                
            </div>
        </div>
    </div>
</div>