.fBold {
  font-weight: bold;
}

.w100 {
  width: 100%;
}

.checkbox label:after,
.radio label:after {
  content: '';
  display: table;
  clear  : both;
}

.checkbox .cr,
.radio .cr {
  position     : relative;
  display      : inline-block;
  border       : 1px solid #a9a9a9;
  border-radius: .25em;
  width        : 1.3em;
  height       : 1.3em;
  float        : left;
  margin-right : .5em;
}

.radio .cr {
  border-radius: 50%;
}

.checkbox .cr .cr-icon,
.radio .cr .cr-icon {
  position   : absolute;
  font-size  : .8em;
  line-height: 0;
  top        : 50%;
  left       : 20%;
}

.radio .cr .cr-icon {
  margin-left: 0.04em;
}

.checkbox label input[type="checkbox"],
.radio label input[type="radio"] {
  display: none;
}

.checkbox label input[type="checkbox"]+.cr>.cr-icon,
.radio label input[type="radio"]+.cr>.cr-icon {
  transform : scale(3) rotateZ(-20deg);
  opacity   : 0;
  transition: all .3s ease-in;
}

.checkbox label input[type="checkbox"]:checked+.cr>.cr-icon,
.radio label input[type="radio"]:checked+.cr>.cr-icon {
  transform: scale(1) rotateZ(0deg);
  opacity  : 1;
}

.checkbox label input[type="checkbox"]:disabled+.cr,
.radio label input[type="radio"]:disabled+.cr {
  opacity: .5;
}

.rAlign {
  text-align: right;
}

// Painel de gestão de veículos
.selectedRow {
  background-color: lightcoral;
}

.hrComLabelDivPai {
  width        : 100%;
  height       : 16px;
  border-bottom: 1px solid #eeeeee;
  text-align   : center
}

.hrComLabelDivPai span {
  font-weight     : 100;
  font-size       : 20px;
  color           : rgb(202, 202, 202);
  background-color: white;
  padding         : 0 10px;
}

/*	start styles for the ContextMenu	*/
.context_menu {
  background-color: white;
  border          : 1px solid gray;
}

.context_menu_item {
  padding: 3px 6px;
}

.context_menu_item:hover {
  background-color: #CCCCCC;
}

.context_menu_separator {
  background-color: gray;
  height          : 1px;
  margin          : 0;
  padding         : 0;
}

/*	end styles for the ContextMenu	*/

.fRight {
  float: right;
}

.textAligncenter {
  text-align: center;
}

.fontColorGreen {
  color: green;
}

.fontWei500 {
  font-weight: 500;
}


// .gm-style-iw {
//   width     : 330px; 
//   min-height: 250px;
//   height    : 350px;
// }

#GestaoVeiculoController ng-map {
  display: block;
  width  : 100%;
  height : 100%;
  z-index: 99999999999;
}

#GestaoVeiculoController .bordaCinza1px {
  border-color: #D7D7D7;
  border-style: solid;
  border-width: 1px;
}

#GestaoVeiculoController .bordaDireitaCinza1px {
  border-right-style: solid;
  border-right-width: 1px;
  border-right-color: #D7D7D7;
}

#GestaoVeiculoController .bordaBottomCinza1px {
  border-bottom-style: solid;
  border-bottom-width: 1px;
  border-bottom-color: #D7D7D7;
}

#GestaoVeiculoController .fixBorderDireitaMapaOperacao {
  border-top-left-radius   : initial !important;
  border-bottom-left-radius: initial !important;
}

#GestaoVeiculoController .fixPaddingBootStrap {
  padding-left : 0px !important;
  padding-right: 0px !important;
}

#GestaoVeiculoController .bordaCinzaTop1px {
  border-top-color: #D7D7D7;
  border-top-style: solid;
  border-top-width: 1px;
}

#GestaoVeiculoController .bordaCinzaLeft1px {
  border-left-color: #D7D7D7;
  border-left-style: solid;
  border-left-width: 1px;
}

#GestaoVeiculoController .bordaCinzaRight1px {
  border-right-color: #D7D7D7;
  border-right-style: solid;
  border-right-width: 1px;
}

#GestaoVeiculoController .labelVermelha {
  color: #8E0000;
}

#GestaoVeiculoController .fontFina {
  font-weight: 400;
}

#GestaoVeiculoController #pBarCarreta strong {
  position   : absolute;
  left       : 0;
  width      : 100%;
  text-align : center;
  line-height: 40px;
  font-size  : 23px;
  margin-top : 30px;
}

#GestaoVeiculoController #pBarCavalo strong {
  position   : absolute;
  left       : 0;
  width      : 100%;
  text-align : center;
  line-height: 40px;
  font-size  : 23px;
  margin-top : 30px;
}

#GestaoVeiculoController #multiPlaca input {
  width: 100%;
}

#GestaoVeiculoController .font18 {
  font-size: 18px;
}

#GestaoVeiculoController .font16 {
  font-size: 16px;
}

#GestaoVeiculoController .lblTotalImplemento {
  font-size   : 16px;
  float       : right;
  margin-top  : 10px;
  margin-right: 17px;
}

#GestaoVeiculoController ::-webkit-scrollbar {
  width: 7px;
  /* for vertical scrollbars */
  height: 7px;
  /* for horizontal scrollbars */
  border-radius: 20px;
}

#GestaoVeiculoController ::-webkit-scrollbar-track {
  background   : rgba(0, 0, 0, 0.1);
  border-radius: 20px;
}

#GestaoVeiculoController ::-webkit-scrollbar-thumb {
  background   : rgba(0, 0, 0, 0.5);
  border-radius: 20px;
}

#GestaoVeiculoController .altura253 {
  height    : 253px;
  max-height: 253px;
  min-height: 253px;
}

#GestaoVeiculoController .fRight {
  float: right;
}

#GestaoVeiculoController .my-chart {
  width : 85px;
  height: 85px;
}

#GestaoVeiculoController .btnFullScreen {
  background-color         : white;
  color                    : black;
  cursor                   : pointer;
  font-size                : 15px;
  padding                  : 5px;
  border-bottom-left-radius: 6px;
}

#GestaoVeiculoController .painelOperacoesDireito {
  background-color: #ffffff;
  border-radius   : 4px;
  min-height      : 236px;
  max-height      : 236px;
}

#GestaoVeiculoController .fundoCorEEEE {
  background-color: #EEEEEE;
}

#GestaoVeiculoController .fundoCorBranco {
  background-color: #ffffff
}

#GestaoVeiculoController .autoOverFlow {
  overflow: auto;
}

#GestaoVeiculoController .marginZero {
  margin: 0;
}

#GestaoVeiculoController .lblOperacoes {
  background-color: #EEEEEE;
  height          : 29px;
}

#GestaoVeiculoController .leftAlign {
  text-align: left;
}

#GestaoVeiculoController .painelEsquerdo {
  background-color: #EEEEEE;
  border-radius   : 4px;
  min-height      : 593px;
}

#GestaoVeiculoController .logoEmpresa {
  height    : 100px;
  text-align: center;
}

#GestaoVeiculoController .borderTopd2dd2 {
  border-top-color: #d2d2d2
}

#GestaoVeiculoController .overFlowHidden {
  overflow: hidden;
}

#GestaoVeiculoController .rowTopImplemento {
  margin-left : 0;
  margin-right: 0;
}

#GestaoVeiculoController .width176 {
  max-height: 176px;
}

#GestaoVeiculoController .width100Percent {
  width: 100%;
}

#GestaoVeiculoController .maxWwidth237px {
  max-width: 237px;
}

#GestaoVeiculoController .maxHeigth110 {
  max-height: 110px;
}

#GestaoVeiculoController .hei70px {
  height: 70px;
}

.removeMargin {
  margin: 0 0 0px;
}

#GestaoVeiculoController .fullScreenBtn {
  font-size                : 17px;
  background-color         : white;
  padding                  : 4px;
  border-bottom-left-radius: 7px;
}

#GestaoVeiculoController .fullScreenBtn:hover {
  font-size: 17px;
  color    : red;
}

#GestaoVeiculoController .hei178 {
  min-height: 178px;
}

.lblMarkerPlaca {
  position        : absolute;
  background-color: white;
  padding         : 1px;
  margin-left     : 22px;
  z-index         : -1;
  margin-top      : 4px;
  width           : 82px;
  text-align      : center;
  border          : #686b6d solid 1px;
}

.pointerCursor {
  cursor: pointer;
}

// Fim Painel de gestão de veículos

/**
 *  Do not remove this comments bellow. It's the markers used by wiredep to inject
 *  less dependencies when defined in the bower.json of your dependencies
 */

// bower:less
// endbower

input[type=date]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  display           : none;
}

.placeholderclass::before {
  content         : attr(placeholder);
  background-color: #FFFFFF;
}

.placeholderclass:hover::before {
  width  : 0%;
  content: "";
}

.janelasAtivas {
  background            : orange;
  min-height            : 50px;
  height                : auto;
  right                 : 0;
  max-height            : 500px;
  overflow-y            : scroll;
  bottom                : 0px;
  border-top-left-radius: 10px;
  position              : fixed;
  display               : block;
  display               : table;
  width                 : 250px;
  color                 : white;
  z-index               : 999999;
  box-shadow            : -1px -1px 11px 2px #888888;
}

.janelasAtivas ul li {
  font-size    : 11px;
  text-overflow: clip;
  cursor       : pointer;
}

.janelasAtivas ul li:hover {
  text-decoration: underline;
}

.janelasAtivas ul li:focus {
  border: none;
}


/**
 *  If you want to override some bootstrap variables, you have to change values here.
 *  The list of variables are listed here bower_components/bootstrap/less/variables.less
 */

@navbar-inverse-link-color: #5AADBB;

/**
 *  Font-Awesome support
 *  Source: https://github.com/Swiip/generator-gulp-angular/issues/337
 */
@import "../../bower_components/fontawesome/less/fontawesome.less";
@import "../../bower_components/fontawesome/less/_icons.less";
@import "../../bower_components/fontawesome/less/_core.less";
@import "../../bower_components/fontawesome/less/_shims.less";
@import "../../bower_components/fontawesome/less/solid.less";
@import "../../bower_components/fontawesome/less/brands.less";
@import "../../bower_components/fontawesome/less/regular.less";
@fa-font-path: "../../bower_components/fontawesome/webfonts";

.fa,
.fas {
  font          : normal normal 900 14px/1 'Font Awesome 5 Free';
  font-size     : inherit;
  text-rendering: auto;
}


@import '../../bower_components/bootstrap/less/bootstrap.less';
@icon-font-path: '../../bower_components/bootstrap/fonts/';

.browsehappy {
  margin    : 0.2em 0;
  background: #ccc;
  color     : #000;
  padding   : 0.2em 0;
}

.ui-grid-pager-container {
  float       : left;
  padding-top : 2.5px;
  padding-left: 20px;
}

.ui-grid-pager-count-container {
  float     : right;
  margin-top: 5px;
  min-width : 50px;
}

.ui-grid-pager-control .ui-grid-pager-max-pages-number>* {
  vertical-align: baseline !important;
}

.ui-grid-pager-control .ui-grid-pager-max-pages-number {
  vertical-align: middle !important;
}

.thumbnail {
  height: 200px;

  img.pull-right {
    width: 50px;
  }
}

.menuActiveIcon {
  float     : left;
  margin-top: 3px;
}

.upperCase {
  text-transform: uppercase;
}

.btnAcoesNotificacaoPush {
  float     : right;
  margin-top: 5px;
}

.floatRight {
  float: right;
}

@nav-profil-pattern: url("../assets/images/patterns/header-profile.png") no-repeat;
@import "less/app/style";

.ui-grid-pinned-container.ui-grid-pinned-container-left .ui-grid-cell:last-child {
  box-sizing        : border-box;
  border-right      : 1px solid;
  border-width      : 1px;
  border-right-color: #e7e7e7;
  background-color  : white;
}

.ui-grid-pinned-container.ui-grid-pinned-container-left .ui-grid-header-cell:last-child {
  box-sizing        : border-box;
  border-right      : 1px solid;
  border-width      : 1px;
  border-right-color: #e7e7e7;
}

.ui-grid-header-cell .ui-grid-sort-priority-number {
  position   : initial;
  margin-left: -5px;
}

.grid {
  height: auto;
}

.loaderGrid {
  width   : 115px;
  position: absolute;
  z-index : 999;
  left    : 40%;
  top     : 45%;
}




#ui-grid-twbs #ui-grid-twbs .form-horizontal .form-group:before,
#ui-grid-twbs #ui-grid-twbs .form-horizontal .form-group:after,
#ui-grid-twbs #ui-grid-twbs .btn-toolbar:before,
#ui-grid-twbs #ui-grid-twbs .btn-toolbar:after,
#ui-grid-twbs #ui-grid-twbs .btn-group-vertical>.btn-group:before,
#ui-grid-twbs #ui-grid-twbs .btn-group-vertical>.btn-group:after {
  content: " ";
  display: table;
}

#ui-grid-twbs #ui-grid-twbs .form-horizontal .form-group:after,
#ui-grid-twbs #ui-grid-twbs .btn-toolbar:after,
#ui-grid-twbs #ui-grid-twbs .btn-group-vertical>.btn-group:after {
  clear: both;
}

.ui-grid {
  border               : 1px solid #e7e7e7;
  box-sizing           : content-box;
  -webkit-border-radius: 0px;
  -moz-border-radius   : 0px;
  border-radius        : 0px;
  -webkit-transform    : translateZ(0);
  -moz-transform       : translateZ(0);
  -o-transform         : translateZ(0);
  -ms-transform        : translateZ(0);
  transform            : translateZ(0);
}

.ui-grid-vertical-bar {
  position: absolute;
  right   : 0;
  width   : 0;
}

.ui-grid-header-cell:not(:last-child) .ui-grid-vertical-bar,
.ui-grid-cell:not(:last-child) .ui-grid-vertical-bar {
  width: 1px;
}

.ui-grid-scrollbar-placeholder {
  background-color: transparent;
}

.ui-grid-header-cell:not(:last-child) .ui-grid-vertical-bar {
  background-color: #e7e7e7;
}

.ui-grid-cell:not(:last-child) .ui-grid-vertical-bar {
  background-color: #e7e7e7;
}

.ui-grid-header-cell:last-child .ui-grid-vertical-bar {
  right           : -1px;
  width           : 1px;
  background-color: #e7e7e7;
}

.ui-grid-clearfix:before,
.ui-grid-clearfix:after {
  content: "";
  display: table;
}

.ui-grid-clearfix:after {
  clear: both;
}

.ui-grid-invisible {
  visibility: hidden;
}

.ui-grid-contents-wrapper {
  position: relative;
  height  : 100%;
  width   : 100%;
}

.ui-grid-sr-only {
  position: absolute;
  width   : 1px;
  height  : 1px;
  margin  : -1px;
  padding : 0;
  overflow: hidden;
  clip    : rect(0, 0, 0, 0);
  border  : 0;
}

.ui-grid-top-panel-background {
  background: White;
  background: -webkit-gradient(linear, left bottom, left top, color-stop(0, none), color-stop(1, none));
  background: -ms-linear-gradient(bottom, none, none);
  background: -moz-linear-gradient(center bottom, none 0%, none 100%);
  background: -o-linear-gradient(none, none);
  filter    : progid:DXImageTransform.Microsoft.gradient(startColorstr='none', endColorstr='none', GradientType=0);
}

.ui-grid-header {
  border-bottom: 1px solid #e7e7e7;
  box-sizing   : border-box;
}

.ui-grid-top-panel {
  position                          : relative;
  overflow                          : hidden;
  font-weight                       : bold;
  background                        : White;
  background                        : -webkit-gradient(linear, left bottom, left top, color-stop(0, none), color-stop(1, none));
  background                        : -ms-linear-gradient(bottom, none, none);
  background                        : -moz-linear-gradient(center bottom, none 0%, none 100%);
  background                        : -o-linear-gradient(none, none);
  filter                            : progid:DXImageTransform.Microsoft.gradient(startColorstr='none', endColorstr='none', GradientType=0);
  -webkit-border-top-right-radius   : -1px;
  -webkit-border-bottom-right-radius: 0;
  -webkit-border-bottom-left-radius : 0;
  -webkit-border-top-left-radius    : -1px;
  -moz-border-radius-topright       : -1px;
  -moz-border-radius-bottomright    : 0;
  -moz-border-radius-bottomleft     : 0;
  -moz-border-radius-topleft        : -1px;
  border-top-right-radius           : -1px;
  border-bottom-right-radius        : 0;
  border-bottom-left-radius         : 0;
  border-top-left-radius            : -1px;
  -moz-background-clip              : padding-box;
  -webkit-background-clip           : padding-box;
  background-clip                   : padding-box;
}

.ui-grid-header-viewport {
  overflow: hidden;
}

.ui-grid-header-canvas:before,
.ui-grid-header-canvas:after {
  content    : "";
  display    : table;
  line-height: 0;
}

.ui-grid-header-canvas:after {
  clear: both;
}

.ui-grid-header-cell-wrapper {
  position  : relative;
  display   : table;
  box-sizing: border-box;
  height    : 100%;
}

.ui-grid-header-cell-row {
  display : table-row;
  position: relative;
}

.ui-grid-header-cell {
  position           : relative;
  box-sizing         : border-box;
  background-color   : inherit;
  border-right       : 1px solid;
  border-color       : #e7e7e7;
  display            : table-cell;
  -webkit-user-select: none;
  -moz-user-select   : none;
  -ms-user-select    : none;
  user-select        : none;
  width              : 0;
}

.ui-grid-header-cell:last-child {
  border-right: 0;
}

.ui-grid-header-cell .sortable {
  cursor: pointer;
}

.ui-grid-header-cell .ui-grid-sort-priority-number {
  margin-left: -8px;
}

.ui-grid-header .ui-grid-vertical-bar {
  top   : 0;
  bottom: 0;
}

.ui-grid-column-menu-button {
  position  : absolute;
  right     : 1px;
  top       : 0;
  text-align: right;
}

.ui-grid-column-menu-button .ui-grid-icon-angle-down {
  vertical-align: sub;
}

.ui-grid-column-menu-button-last-col {
  margin-right: 25px;
}

.ui-grid-column-menu {
  position: absolute;
}

/* Slide up/down animations */
.ui-grid-column-menu .ui-grid-menu .ui-grid-menu-mid.ng-hide-add,
.ui-grid-column-menu .ui-grid-menu .ui-grid-menu-mid.ng-hide-remove {
  -webkit-transition: all 0.05s linear;
  -moz-transition   : all 0.05s linear;
  -o-transition     : all 0.05s linear;
  transition        : all 0.05s linear;
  display           : block !important;
}

.ui-grid-column-menu .ui-grid-menu .ui-grid-menu-mid.ng-hide-add.ng-hide-add-active,
.ui-grid-column-menu .ui-grid-menu .ui-grid-menu-mid.ng-hide-remove {
  -webkit-transform: translateY(-100%);
  -moz-transform   : translateY(-100%);
  -o-transform     : translateY(-100%);
  -ms-transform    : translateY(-100%);
  transform        : translateY(-100%);
}

.ui-grid-column-menu .ui-grid-menu .ui-grid-menu-mid.ng-hide-add,
.ui-grid-column-menu .ui-grid-menu .ui-grid-menu-mid.ng-hide-remove.ng-hide-remove-active {
  -webkit-transform: translateY(0);
  -moz-transform   : translateY(0);
  -o-transform     : translateY(0);
  -ms-transform    : translateY(0);
  transform        : translateY(0);
}

/* Slide up/down animations */
.ui-grid-menu-button .ui-grid-menu .ui-grid-menu-mid.ng-hide-add,
.ui-grid-menu-button .ui-grid-menu .ui-grid-menu-mid.ng-hide-remove {
  -webkit-transition: all 0.05s linear;
  -moz-transition   : all 0.05s linear;
  -o-transition     : all 0.05s linear;
  transition        : all 0.05s linear;
  display           : block !important;
}

.ui-grid-menu-button .ui-grid-menu .ui-grid-menu-mid.ng-hide-add.ng-hide-add-active,
.ui-grid-menu-button .ui-grid-menu .ui-grid-menu-mid.ng-hide-remove {
  -webkit-transform: translateY(-100%);
  -moz-transform   : translateY(-100%);
  -o-transform     : translateY(-100%);
  -ms-transform    : translateY(-100%);
  transform        : translateY(-100%);
}

.ui-grid-menu-button .ui-grid-menu .ui-grid-menu-mid.ng-hide-add,
.ui-grid-menu-button .ui-grid-menu .ui-grid-menu-mid.ng-hide-remove.ng-hide-remove-active {
  -webkit-transform: translateY(0);
  -moz-transform   : translateY(0);
  -o-transform     : translateY(0);
  -ms-transform    : translateY(0);
  transform        : translateY(0);
}

.ui-grid-filter-container {
  padding : 4px 10px;
  position: relative;
}

.ui-grid-filter-container .ui-grid-filter-button {
  position: absolute;
  top     : 0;
  bottom  : 0;
  right   : 0;
}

.ui-grid-filter-container .ui-grid-filter-button [class^="ui-grid-icon"] {
  position   : absolute;
  top        : 50%;
  line-height: 32px;
  margin-top : -16px;
  right      : 10px;
  opacity    : 0.66;
}

.ui-grid-filter-container .ui-grid-filter-button [class^="ui-grid-icon"]:hover {
  opacity: 1;
}

.ui-grid-filter-container .ui-grid-filter-button-select {
  position: absolute;
  top     : 0;
  bottom  : 0;
  right   : 0;
}

.ui-grid-filter-container .ui-grid-filter-button-select [class^="ui-grid-icon"] {
  position   : absolute;
  top        : 50%;
  line-height: 32px;
  margin-top : -16px;
  right      : 0px;
  opacity    : 0.66;
}

.ui-grid-filter-container .ui-grid-filter-button-select [class^="ui-grid-icon"]:hover {
  opacity: 1;
}

input[type="text"].ui-grid-filter-input {
  padding                           : 0;
  margin                            : 0;
  border                            : 0;
  width                             : 100%;
  border                            : 1px solid #e7e7e7;
  -webkit-border-top-right-radius   : 0px;
  -webkit-border-bottom-right-radius: 0;
  -webkit-border-bottom-left-radius : 0;
  -webkit-border-top-left-radius    : 0;
  -moz-border-radius-topright       : 0px;
  -moz-border-radius-bottomright    : 0;
  -moz-border-radius-bottomleft     : 0;
  -moz-border-radius-topleft        : 0;
  border-top-right-radius           : 0px;
  border-bottom-right-radius        : 0;
  border-bottom-left-radius         : 0;
  border-top-left-radius            : 0;
  -moz-background-clip              : padding-box;
  -webkit-background-clip           : padding-box;
  background-clip                   : padding-box;
}

input[type="text"].ui-grid-filter-input:hover {
  border: 1px solid #e7e7e7;
}

select.ui-grid-filter-select {
  padding                           : 0;
  margin                            : 0;
  border                            : 0;
  width                             : 90%;
  border                            : 1px solid #e7e7e7;
  -webkit-border-top-right-radius   : 0px;
  -webkit-border-bottom-right-radius: 0;
  -webkit-border-bottom-left-radius : 0;
  -webkit-border-top-left-radius    : 0;
  -moz-border-radius-topright       : 0px;
  -moz-border-radius-bottomright    : 0;
  -moz-border-radius-bottomleft     : 0;
  -moz-border-radius-topleft        : 0;
  border-top-right-radius           : 0px;
  border-bottom-right-radius        : 0;
  border-bottom-left-radius         : 0;
  border-top-left-radius            : 0;
  -moz-background-clip              : padding-box;
  -webkit-background-clip           : padding-box;
  background-clip                   : padding-box;
}

select.ui-grid-filter-select:hover {
  border: 1px solid #e7e7e7;
}

.ui-grid-filter-cancel-button-hidden select.ui-grid-filter-select {
  width: 100%;
}

.ui-grid-render-container {
  position                          : inherit;
  -webkit-border-top-right-radius   : 0;
  -webkit-border-bottom-right-radius: 0px;
  -webkit-border-bottom-left-radius : 0px;
  -webkit-border-top-left-radius    : 0;
  -moz-border-radius-topright       : 0;
  -moz-border-radius-bottomright    : 0px;
  -moz-border-radius-bottomleft     : 0px;
  -moz-border-radius-topleft        : 0;
  border-top-right-radius           : 0;
  border-bottom-right-radius        : 0px;
  border-bottom-left-radius         : 0px;
  border-top-left-radius            : 0;
  -moz-background-clip              : padding-box;
  -webkit-background-clip           : padding-box;
  background-clip                   : padding-box;
}

.ui-grid-render-container:focus {
  outline: none;
}

.ui-grid-viewport {
  min-height                : 20px;
  position                  : relative;
  overflow-y                : scroll;
  -webkit-overflow-scrolling: touch;
}

.ui-grid-viewport:focus {
  outline: none !important;
}

.ui-grid-canvas {
  position   : relative;
  padding-top: 1px;
}

.ui-grid-row {
  clear: both;
}

.ui-grid-row:nth-child(odd) .ui-grid-cell {
  background-color: #fdfdfd;
}

.ui-grid-row:nth-child(even) .ui-grid-cell {
  background-color: #fdfdfd;
}

.ui-grid-row:hover .ui-grid-cell {
  background-color: #f5f5f5;
}

.ui-grid-row:last-child .ui-grid-cell {
  border-bottom-color: #e7e7e7;
  border-bottom-style: solid;
}

.ui-grid-no-row-overlay {
  position                          : absolute;
  top                               : 0;
  bottom                            : 0;
  left                              : 0;
  right                             : 0;
  margin                            : 10%;
  background                        : White;
  background                        : -webkit-gradient(linear, left bottom, left top, color-stop(0, none), color-stop(1, none));
  background                        : -ms-linear-gradient(bottom, none, none);
  background                        : -moz-linear-gradient(center bottom, none 0%, none 100%);
  background                        : -o-linear-gradient(none, none);
  filter                            : progid:DXImageTransform.Microsoft.gradient(startColorstr='none', endColorstr='none', GradientType=0);
  -webkit-border-top-right-radius   : 0px;
  -webkit-border-bottom-right-radius: 0;
  -webkit-border-bottom-left-radius : 0;
  -webkit-border-top-left-radius    : 0;
  -moz-border-radius-topright       : 0px;
  -moz-border-radius-bottomright    : 0;
  -moz-border-radius-bottomleft     : 0;
  -moz-border-radius-topleft        : 0;
  border-top-right-radius           : 0px;
  border-bottom-right-radius        : 0;
  border-bottom-left-radius         : 0;
  border-top-left-radius            : 0;
  -moz-background-clip              : padding-box;
  -webkit-background-clip           : padding-box;
  background-clip                   : padding-box;
  border                            : 1px solid #e7e7e7;
  font-size                         : 2em;
  text-align                        : center;
}

.ui-grid-no-row-overlay>* {
  position: absolute;
  display : table;
  margin  : auto 0;
  width   : 100%;
  top     : 0;
  bottom  : 0;
  left    : 0;
  right   : 0;
  opacity : 0.66;
}

/* This file contains variable declarations (do not remove this line) */
/*-- VARIABLES (DO NOT REMOVE THESE COMMENTS) --*/
/**
* @section Grid styles
*/
/**
* @section Header styles
*/
/** @description Colors for header gradient */
/**
* @section Grid body styles
*/
/** @description Colors used for row alternation */
/**
* @section Sort arrow colors
*/
/**
* @section Scrollbar styles
*/
/**
* @section font library path
*/
/*-- END VARIABLES (DO NOT REMOVE THESE COMMENTS) --*/
.ui-grid-cell {
  overflow        : hidden;
  float           : left;
  background-color: inherit;
  border-right    : 1px solid;
  border-color    : #e7e7e7;
  box-sizing      : border-box;
}

.ui-grid-cell:last-child {
  border-right: 0;
}

.ui-grid-cell-contents {
  padding           : 5px;
  -moz-box-sizing   : border-box;
  -webkit-box-sizing: border-box;
  box-sizing        : border-box;
  white-space       : nowrap;
  -ms-text-overflow : ellipsis;
  -o-text-overflow  : ellipsis;
  text-overflow     : ellipsis;
  overflow          : hidden;
  height            : 100%;
}

.ui-grid-cell-contents-hidden {
  visibility: hidden;
  width     : 0;
  height    : 0;
  display   : none;
}

.ui-grid-row .ui-grid-cell.ui-grid-row-header-cell {
  background-color: #f0f0ee;
  border-bottom   : solid 1px #e7e7e7;
}

.ui-grid-footer-panel-background {
  background: White;
  background: -webkit-gradient(linear, left bottom, left top, color-stop(0, none), color-stop(1, none));
  background: -ms-linear-gradient(bottom, none, none);
  background: -moz-linear-gradient(center bottom, none 0%, none 100%);
  background: -o-linear-gradient(none, none);
  filter    : progid:DXImageTransform.Microsoft.gradient(startColorstr='none', endColorstr='none', GradientType=0);
}

.ui-grid-footer-panel {
  position                          : relative;
  border-bottom                     : 1px solid #e7e7e7;
  border-top                        : 1px solid #e7e7e7;
  overflow                          : hidden;
  font-weight                       : bold;
  background                        : White;
  background                        : -webkit-gradient(linear, left bottom, left top, color-stop(0, none), color-stop(1, none));
  background                        : -ms-linear-gradient(bottom, none, none);
  background                        : -moz-linear-gradient(center bottom, none 0%, none 100%);
  background                        : -o-linear-gradient(none, none);
  filter                            : progid:DXImageTransform.Microsoft.gradient(startColorstr='none', endColorstr='none', GradientType=0);
  -webkit-border-top-right-radius   : -1px;
  -webkit-border-bottom-right-radius: 0;
  -webkit-border-bottom-left-radius : 0;
  -webkit-border-top-left-radius    : -1px;
  -moz-border-radius-topright       : -1px;
  -moz-border-radius-bottomright    : 0;
  -moz-border-radius-bottomleft     : 0;
  -moz-border-radius-topleft        : -1px;
  border-top-right-radius           : -1px;
  border-bottom-right-radius        : 0;
  border-bottom-left-radius         : 0;
  border-top-left-radius            : -1px;
  -moz-background-clip              : padding-box;
  -webkit-background-clip           : padding-box;
  background-clip                   : padding-box;
}

.ui-grid-grid-footer {
  float: left;
  width: 100%;
}

.ui-grid-footer-viewport {
  overflow: hidden;
}

.ui-grid-footer-canvas {
  position: relative;
}

.ui-grid-footer-canvas:before,
.ui-grid-footer-canvas:after {
  content    : "";
  display    : table;
  line-height: 0;
}

.ui-grid-footer-canvas:after {
  clear: both;
}

.ui-grid-footer-cell-wrapper {
  position  : relative;
  display   : table;
  box-sizing: border-box;
  height    : 100%;
}

.ui-grid-footer-cell-row {
  display: table-row;
}

.ui-grid-footer-cell {
  overflow        : hidden;
  background-color: inherit;
  border-right    : 1px solid;
  border-color    : #e7e7e7;
  box-sizing      : border-box;
  display         : table-cell;
}

.ui-grid-footer-cell:last-child {
  border-right: 0;
}

input[type="text"].ui-grid-filter-input {
  padding                           : 0;
  margin                            : 0;
  border                            : 0;
  width                             : 100%;
  border                            : 1px solid #e7e7e7;
  -webkit-border-top-right-radius   : 0px;
  -webkit-border-bottom-right-radius: 0;
  -webkit-border-bottom-left-radius : 0;
  -webkit-border-top-left-radius    : 0;
  -moz-border-radius-topright       : 0px;
  -moz-border-radius-bottomright    : 0;
  -moz-border-radius-bottomleft     : 0;
  -moz-border-radius-topleft        : 0;
  border-top-right-radius           : 0px;
  border-bottom-right-radius        : 0;
  border-bottom-left-radius         : 0;
  border-top-left-radius            : 0;
  -moz-background-clip              : padding-box;
  -webkit-background-clip           : padding-box;
  background-clip                   : padding-box;
}

input[type="text"].ui-grid-filter-input:hover {
  border: 1px solid #e7e7e7;
}

.ui-grid-menu-button {
  z-index    : 2;
  position   : absolute;
  right      : 0;
  top        : 0;
  background : White;
  border     : 1px solid #e7e7e7;
  cursor     : pointer;
  height     : 31px;
  font-weight: normal;
}

.ui-grid-menu-button .ui-grid-icon-container {
  margin-top: 3px;
}

.ui-grid-menu-button .ui-grid-menu {
  right: 0;
}

.ui-grid-menu-button .ui-grid-menu .ui-grid-menu-mid {
  overflow: scroll;
  border  : 1px solid #e7e7e7;
}

.ui-grid-menu {
  z-index   : 2;
  position  : absolute;
  padding   : 0 10px 20px 10px;
  cursor    : pointer;
  box-sizing: border-box;
}

.ui-grid-menu .ui-grid-menu-inner {
  background           : White;
  border               : 1px solid #e7e7e7;
  position             : relative;
  white-space          : nowrap;
  -webkit-border-radius: 0px;
  -moz-border-radius   : 0px;
  border-radius        : 0px;
  -webkit-box-shadow   : 0 10px 20px rgba(0, 0, 0, 0.2), inset 0 12px 12px -14px rgba(0, 0, 0, 0.2);
  -moz-box-shadow      : 0 10px 20px rgba(0, 0, 0, 0.2), inset 0 12px 12px -14px rgba(0, 0, 0, 0.2);
  box-shadow           : 0 10px 20px rgba(0, 0, 0, 0.2), inset 0 12px 12px -14px rgba(0, 0, 0, 0.2);
}

.ui-grid-menu .ui-grid-menu-inner .ui-grid-menu-close-button {
  position           : absolute;
  right              : 0px;
  top                : 0px;
  display            : inline-block;
  margin-bottom      : 0;
  font-weight        : normal;
  text-align         : center;
  vertical-align     : middle;
  touch-action       : manipulation;
  cursor             : pointer;
  background-image   : none;
  border             : 1px solid transparent;
  white-space        : nowrap;
  padding            : 6px 12px;
  font-size          : 14px;
  line-height        : 1.42857143;
  border-radius      : 4px;
  -webkit-user-select: none;
  -moz-user-select   : none;
  -ms-user-select    : none;
  user-select        : none;
  padding            : 1px 1px;
  font-size          : 10px;
  line-height        : 1;
  border-radius      : 2px;
  color              : transparent;
  background-color   : transparent;
  border-color       : transparent;
}

.ui-grid-menu .ui-grid-menu-inner .ui-grid-menu-close-button:focus,
.ui-grid-menu .ui-grid-menu-inner .ui-grid-menu-close-button:active:focus,
.ui-grid-menu .ui-grid-menu-inner .ui-grid-menu-close-button.active:focus,
.ui-grid-menu .ui-grid-menu-inner .ui-grid-menu-close-button.focus,
.ui-grid-menu .ui-grid-menu-inner .ui-grid-menu-close-button:active.focus,
.ui-grid-menu .ui-grid-menu-inner .ui-grid-menu-close-button.active.focus {
  outline       : 5px auto -webkit-focus-ring-color;
  outline-offset: -2px;
}

.ui-grid-menu .ui-grid-menu-inner .ui-grid-menu-close-button:hover,
.ui-grid-menu .ui-grid-menu-inner .ui-grid-menu-close-button:focus,
.ui-grid-menu .ui-grid-menu-inner .ui-grid-menu-close-button.focus {
  color          : #333333;
  text-decoration: none;
}

.ui-grid-menu .ui-grid-menu-inner .ui-grid-menu-close-button:active,
.ui-grid-menu .ui-grid-menu-inner .ui-grid-menu-close-button.active {
  outline           : 0;
  background-image  : none;
  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  box-shadow        : inset 0 3px 5px rgba(0, 0, 0, 0.125);
}

.ui-grid-menu .ui-grid-menu-inner .ui-grid-menu-close-button.disabled,
.ui-grid-menu .ui-grid-menu-inner .ui-grid-menu-close-button[disabled],
fieldset[disabled] .ui-grid-menu .ui-grid-menu-inner .ui-grid-menu-close-button {
  cursor            : not-allowed;
  opacity           : 0.65;
  filter            : alpha(opacity=65);
  -webkit-box-shadow: none;
  box-shadow        : none;
}

a.ui-grid-menu .ui-grid-menu-inner .ui-grid-menu-close-button.disabled,
fieldset[disabled] a.ui-grid-menu .ui-grid-menu-inner .ui-grid-menu-close-button {
  pointer-events: none;
}

.ui-grid-menu .ui-grid-menu-inner .ui-grid-menu-close-button:focus,
.ui-grid-menu .ui-grid-menu-inner .ui-grid-menu-close-button.focus {
  color           : transparent;
  background-color: rgba(0, 0, 0, 0);
  border-color    : rgba(0, 0, 0, 0);
}

.ui-grid-menu .ui-grid-menu-inner .ui-grid-menu-close-button:hover {
  color           : transparent;
  background-color: rgba(0, 0, 0, 0);
  border-color    : rgba(0, 0, 0, 0);
}

.ui-grid-menu .ui-grid-menu-inner .ui-grid-menu-close-button:active,
.ui-grid-menu .ui-grid-menu-inner .ui-grid-menu-close-button.active,
.open>.dropdown-toggle.ui-grid-menu .ui-grid-menu-inner .ui-grid-menu-close-button {
  color           : transparent;
  background-color: rgba(0, 0, 0, 0);
  border-color    : rgba(0, 0, 0, 0);
}

.ui-grid-menu .ui-grid-menu-inner .ui-grid-menu-close-button:active:hover,
.ui-grid-menu .ui-grid-menu-inner .ui-grid-menu-close-button.active:hover,
.open>.dropdown-toggle.ui-grid-menu .ui-grid-menu-inner .ui-grid-menu-close-button:hover,
.ui-grid-menu .ui-grid-menu-inner .ui-grid-menu-close-button:active:focus,
.ui-grid-menu .ui-grid-menu-inner .ui-grid-menu-close-button.active:focus,
.open>.dropdown-toggle.ui-grid-menu .ui-grid-menu-inner .ui-grid-menu-close-button:focus,
.ui-grid-menu .ui-grid-menu-inner .ui-grid-menu-close-button:active.focus,
.ui-grid-menu .ui-grid-menu-inner .ui-grid-menu-close-button.active.focus,
.open>.dropdown-toggle.ui-grid-menu .ui-grid-menu-inner .ui-grid-menu-close-button.focus {
  color           : transparent;
  background-color: rgba(0, 0, 0, 0);
  border-color    : rgba(0, 0, 0, 0);
}

.ui-grid-menu .ui-grid-menu-inner .ui-grid-menu-close-button:active,
.ui-grid-menu .ui-grid-menu-inner .ui-grid-menu-close-button.active,
.open>.dropdown-toggle.ui-grid-menu .ui-grid-menu-inner .ui-grid-menu-close-button {
  background-image: none;
}

.ui-grid-menu .ui-grid-menu-inner .ui-grid-menu-close-button.disabled:hover,
.ui-grid-menu .ui-grid-menu-inner .ui-grid-menu-close-button[disabled]:hover,
fieldset[disabled] .ui-grid-menu .ui-grid-menu-inner .ui-grid-menu-close-button:hover,
.ui-grid-menu .ui-grid-menu-inner .ui-grid-menu-close-button.disabled:focus,
.ui-grid-menu .ui-grid-menu-inner .ui-grid-menu-close-button[disabled]:focus,
fieldset[disabled] .ui-grid-menu .ui-grid-menu-inner .ui-grid-menu-close-button:focus,
.ui-grid-menu .ui-grid-menu-inner .ui-grid-menu-close-button.disabled.focus,
.ui-grid-menu .ui-grid-menu-inner .ui-grid-menu-close-button[disabled].focus,
fieldset[disabled] .ui-grid-menu .ui-grid-menu-inner .ui-grid-menu-close-button.focus {
  background-color: transparent;
  border-color    : transparent;
}

.ui-grid-menu .ui-grid-menu-inner .ui-grid-menu-close-button .badge {
  color           : transparent;
  background-color: transparent;
}

.ui-grid-menu .ui-grid-menu-inner .ui-grid-menu-close-button>i {
  opacity: 0.75;
  color  : black;
}

.ui-grid-menu .ui-grid-menu-inner ul {
  margin         : 0;
  padding        : 0;
  list-style-type: none;
}

.ui-grid-menu .ui-grid-menu-inner ul li {
  padding: 0px;
}

.ui-grid-menu .ui-grid-menu-inner ul li button {
  min-width : 100%;
  padding   : 8px;
  text-align: left;
  background: transparent;
  border    : none;
}

.ui-grid-menu .ui-grid-menu-inner ul li button:hover,
.ui-grid-menu .ui-grid-menu-inner ul li button:focus {
  -webkit-box-shadow: inset 0 0 14px rgba(0, 0, 0, 0.2);
  -moz-box-shadow   : inset 0 0 14px rgba(0, 0, 0, 0.2);
  box-shadow        : inset 0 0 14px rgba(0, 0, 0, 0.2);
}

.ui-grid-menu .ui-grid-menu-inner ul li button.ui-grid-menu-item-active {
  -webkit-box-shadow: inset 0 0 14px rgba(0, 0, 0, 0.2);
  -moz-box-shadow   : inset 0 0 14px rgba(0, 0, 0, 0.2);
  box-shadow        : inset 0 0 14px rgba(0, 0, 0, 0.2);
  background-color  : #cecece;
}

.ui-grid-menu .ui-grid-menu-inner ul li:not(:last-child)>button {
  border-bottom: 1px solid #e7e7e7;
}

.ui-grid-sortarrow {
  right              : 5px;
  position           : absolute;
  width              : 20px;
  top                : 0;
  bottom             : 0;
  background-position: center;
}

.ui-grid-sortarrow.down {
  -webkit-transform: rotate(180deg);
  -moz-transform   : rotate(180deg);
  -o-transform     : rotate(180deg);
  -ms-transform    : rotate(180deg);
  transform        : rotate(180deg);
}

@font-face {
  font-family: 'ui-grid';
  src        : url('ui-grid.eot');
  src        : url('ui-grid.eot#iefix') format('embedded-opentype'), url('ui-grid.woff') format('woff'), url('ui-grid.ttf') format('truetype'), url('ui-grid.svg?#ui-grid') format('svg');
  font-weight: normal;
  font-style : normal;
}

/* Chrome hack: SVG is rendered more smooth in Windozze. 100% magic, uncomment if you need it. */
/* Note, that will break hinting! In other OS-es font will be not as sharp as it could be */
/*
@media screen and (-webkit-min-device-pixel-ratio:0) {
  @font-face {
    font-family: 'ui-grid';
    src        : url('@{font-path}ui-grid.svg?12312827#ui-grid') format('svg');
  }
}
*/
[class^="ui-grid-icon"]:before,
[class*=" ui-grid-icon"]:before {
  font-family    : "ui-grid";
  font-style     : normal;
  font-weight    : normal;
  speak          : none;
  display        : inline-block;
  text-decoration: inherit;
  width          : 1em;
  margin-right   : .2em;
  text-align     : center;
  /* opacity     : .8; */
  /* For safety - reset parent styles, that can break glyph codes*/
  font-variant  : normal;
  text-transform: none;
  /* fix buttons height, for twitter bootstrap */
  line-height: 1em;
  /* Animation center compensation - margins should be symmetric */
  /* remove if not needed */
  margin-left: .2em;
  /* you can be more comfortable with increased icons size */
  /* font-size: 120%; */
  /* Uncomment for 3D effect */
  /* text-shadow: 1px 1px 1px rgba(127, 127, 127, 0.3); */
}

.ui-grid-icon-blank::before {
  width  : 1em;
  content: ' ';
}

.ui-grid-icon-plus-squared:before {
  content: '\c350';
}

.ui-grid-icon-minus-squared:before {
  content: '\c351';
}

.ui-grid-icon-search:before {
  content: '\c352';
}

.ui-grid-icon-cancel:before {
  content: '\c353';
}

.ui-grid-icon-info-circled:before {
  content: '\c354';
}

.ui-grid-icon-lock:before {
  content: '\c355';
}

.ui-grid-icon-lock-open:before {
  content: '\c356';
}

.ui-grid-icon-pencil:before {
  content: '\c357';
}

.ui-grid-icon-down-dir:before {
  content: '\c358';
}

.ui-grid-icon-up-dir:before {
  content: '\c359';
}

.ui-grid-icon-left-dir:before {
  content: '\c35a';
}

.ui-grid-icon-right-dir:before {
  content: '\c35b';
}

.ui-grid-icon-left-open:before {
  content: '\c35c';
}

.ui-grid-icon-right-open:before {
  content: '\c35d';
}

.ui-grid-icon-angle-down:before {
  content: '\c35e';
}

.ui-grid-icon-filter:before {
  content: '\c35f';
}

.ui-grid-icon-sort-alt-up:before {
  content: '\c360';
}

.ui-grid-icon-sort-alt-down:before {
  content: '\c361';
}

.ui-grid-icon-ok:before {
  content: '\c362';
}

.ui-grid-icon-menu:before {
  content: '\c363';
}

.ui-grid-icon-indent-left:before {
  content: '\e800';
}

.ui-grid-icon-indent-right:before {
  content: '\e801';
}

.ui-grid-icon-spin5:before {
  content: '\ea61';
}

/*
* RTL Styles
*/
.ui-grid[dir=rtl] .ui-grid-header-cell,
.ui-grid[dir=rtl] .ui-grid-footer-cell,
.ui-grid[dir=rtl] .ui-grid-cell {
  float: right !important;
}

.ui-grid[dir=rtl] .ui-grid-column-menu-button {
  position: absolute;
  left    : 1px;
  top     : 0;
  right   : inherit;
}

.ui-grid[dir=rtl] .ui-grid-cell:first-child,
.ui-grid[dir=rtl] .ui-grid-header-cell:first-child,
.ui-grid[dir=rtl] .ui-grid-footer-cell:first-child {
  border-right: 0;
}

.ui-grid[dir=rtl] .ui-grid-cell:last-child,
.ui-grid[dir=rtl] .ui-grid-header-cell:last-child {
  border-right: 1px solid #e7e7e7;
  border-left : 0;
}

.ui-grid[dir=rtl] .ui-grid-header-cell:first-child .ui-grid-vertical-bar,
.ui-grid[dir=rtl] .ui-grid-footer-cell:first-child .ui-grid-vertical-bar,
.ui-grid[dir=rtl] .ui-grid-cell:first-child .ui-grid-vertical-bar {
  width: 0;
}

.ui-grid[dir=rtl] .ui-grid-menu-button {
  z-index    : 2;
  position   : absolute;
  left       : 0;
  right      : auto;
  background : White;
  border     : 1px solid #e7e7e7;
  cursor     : pointer;
  min-height : 27px;
  font-weight: normal;
}

.ui-grid[dir=rtl] .ui-grid-menu-button .ui-grid-menu {
  left : 0;
  right: auto;
}

.ui-grid[dir=rtl] .ui-grid-filter-container .ui-grid-filter-button {
  right: initial;
  left : 0;
}

.ui-grid[dir=rtl] .ui-grid-filter-container .ui-grid-filter-button [class^="ui-grid-icon"] {
  right: initial;
  left : 10px;
}

/*
   Animation example, for spinners
*/
.ui-grid-animate-spin {
  -moz-animation   : ui-grid-spin 2s infinite linear;
  -o-animation     : ui-grid-spin 2s infinite linear;
  -webkit-animation: ui-grid-spin 2s infinite linear;
  animation        : ui-grid-spin 2s infinite linear;
  display          : inline-block;
}

@-moz-keyframes ui-grid-spin {
  0% {
    -moz-transform   : rotate(0deg);
    -o-transform     : rotate(0deg);
    -webkit-transform: rotate(0deg);
    transform        : rotate(0deg);
  }

  100% {
    -moz-transform   : rotate(359deg);
    -o-transform     : rotate(359deg);
    -webkit-transform: rotate(359deg);
    transform        : rotate(359deg);
  }
}

@-webkit-keyframes ui-grid-spin {
  0% {
    -moz-transform   : rotate(0deg);
    -o-transform     : rotate(0deg);
    -webkit-transform: rotate(0deg);
    transform        : rotate(0deg);
  }

  100% {
    -moz-transform   : rotate(359deg);
    -o-transform     : rotate(359deg);
    -webkit-transform: rotate(359deg);
    transform        : rotate(359deg);
  }
}

@-o-keyframes ui-grid-spin {
  0% {
    -moz-transform   : rotate(0deg);
    -o-transform     : rotate(0deg);
    -webkit-transform: rotate(0deg);
    transform        : rotate(0deg);
  }

  100% {
    -moz-transform   : rotate(359deg);
    -o-transform     : rotate(359deg);
    -webkit-transform: rotate(359deg);
    transform        : rotate(359deg);
  }
}

@-ms-keyframes ui-grid-spin {
  0% {
    -moz-transform   : rotate(0deg);
    -o-transform     : rotate(0deg);
    -webkit-transform: rotate(0deg);
    transform        : rotate(0deg);
  }

  100% {
    -moz-transform   : rotate(359deg);
    -o-transform     : rotate(359deg);
    -webkit-transform: rotate(359deg);
    transform        : rotate(359deg);
  }
}

@keyframes ui-grid-spin {
  0% {
    -moz-transform   : rotate(0deg);
    -o-transform     : rotate(0deg);
    -webkit-transform: rotate(0deg);
    transform        : rotate(0deg);
  }

  100% {
    -moz-transform   : rotate(359deg);
    -o-transform     : rotate(359deg);
    -webkit-transform: rotate(359deg);
    transform        : rotate(359deg);
  }
}

/*---------------------------------------------------
    LESS Elements 0.9
  ---------------------------------------------------
    A set of useful LESS mixins
    More info at: http://lesselements.com
  ---------------------------------------------------*/