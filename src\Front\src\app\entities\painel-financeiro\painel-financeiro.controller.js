(function () {
    'use strict';

    angular.module('bbcWeb').controller('PainelFinanceiroController', PainelFinanceiroController);

    PainelFinanceiroController.inject = [
        'BaseService',
        '$rootScope',
        '$uibModal',
        'toastr',
        '$scope',
        '$timeout',
        'PersistentDataService',
        'oitozero.ngSweetAlert'
    ];

    function PainelFinanceiroController(
        BaseService,
        $rootScope,
        $uibModal,
        toastr,
        $scope,
        $timeout,
        PersistentDataService,
        SweetAlert) {
        var vm = this;
        
        vm.status = 0;
        vm.pagamentosPendente = [];
        vm.pagamentosPendenteId = [];
        vm.enumStatusLista = [
            {id: 0, descricao: 'Todos'},
            {id: 3, descricao: 'Em fila de pagamento'},
            {id: 4, descricao: 'Pagamento realizado'},
            {id: 6, descricao: 'Não efetivado'}
        ]
        vm.enumStatus = vm.enumStatusLista;
        vm.headerItems = [{name: 'Movimentações'}, {name: 'Painel financeiro'}];
        vm.dadosRelatorio = {};
        vm.modalRelatorioOptions = [{}];
        vm.modalRelatorioAbastecimentoOptions = [{}];
        vm.maisDetalhes = false;
        vm.dateOptions = {
            timePicker: false,
            applyClass: 'btn-primary',
            locale: {
                applyLabel: "Aplicar",
                fromLabel: "De",
                format: "DD/MM/YYYY",
                toLabel: "Até",
                cancelLabel: 'Cancelar',
                customRangeLabel: 'Período',
                monthNames: ['Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho', 'Julho', 'Agosto', 'Setembro',
                    'Outubro', 'Novembro', 'Dezembro'
                ],
                daysOfWeek: ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb']
            },
            ranges: {
                'Último dia': [moment().add(-1, 'days'), moment()],
                'Últimos 7 dias': [moment().add(-7, 'days'), moment()],
                'Último mês': [moment().subtract(1,'months').startOf('month'), moment().subtract(1,'months').endOf('month')]
            }
        };
        vm.date = {
            startDate: moment().add(-1, 'days'),
            endDate: moment().add(3, 'hours')
        };
        vm.gridOptions = {
            data: [],
            onRegisterApi: BaseService.dataGrid.defaultOnRegisterApi,
            urlRelatorio: "PagamentoAbastecimento/ConsultarGridPainelFinanceiroRelatorio",
            dataSource: {
                autoBind: false,
                url: "PagamentoAbastecimento/ConsultarGridPainelFinanceiro",
                params: function () {
                    return {
                        DtInicial: vm.date.startDate.format('DD/MM/YYYY').toString(),
                        DtFinal: vm.date.endDate.format('DD/MM/YYYY').toString(),
                        status: vm.status,
                        NumeroNota: vm.nota,
                        NumeroProtocolo: vm.protocolo,
                        empresaId: $rootScope.usuarioLogado.empresaId
                    }
                },
            },
            columnDefs: [
                {
                    name: 'Acoes',
                    displayName: 'Ações',
                    width: '8%',
                    cellTemplate: '<div ng-if="!row.groupHeader" class="ui-grid-cell-contents" title="TOOLTIP">\
                                    <div class="container-btn-action">\
                                        <button tooltip-placement="right" uib-tooltip="Exibir abastecimentos" type="button" ng-click="grid.appScope.vm.consultarProtocolo(row.entity.id)" ng-class="row.entity.status===\'Aberto\' ? \'btn btn-xs btn-info\' : \'btn btn-xs btn-info\'">\
                                            <i ng-class="\'fa fa-eye\'"></i>\
                                        </button>\
                                    \
                                </div>'
                },
                {
                    name: 'Código Abastecimento',
                    displayName: 'Código Abastecimento',
                    width: 100,
                    field: 'abastecimentoId',
                    visible: false,
                    serverField: 'abastecimentoId',
                    type: 'number',
                    enableFiltering: true
                },
                {
                    name: 'Codigo de Transacao',
                    displayName: 'Código de Transação',
                    width: 100,
                    field: 'id',
                    serverField: 'id',
                    type: 'number',
                    enableFiltering: true
                },
                {
                    name: 'Razao Social',
                    displayName: 'Razão Social',
                    width: 135,
                    field: 'razaoSocial',
                    serverField: 'razaoSocial',
                    enableFiltering: false,
                    enableSorting: false
                },
                {
                    name: 'Posto CNPJ',
                    displayName: 'Posto CNPJ',
                    width: 135,
                    field: 'cnpj',
                    pipe: function (input) {
                        return filterFormatCNPJ(input)
                    },
                    serverField: 'cnpj',
                    enableFiltering: false,
                    enableSorting: false
                },
                {
                    name: 'Tarifa',
                    displayName: 'Tarifa',
                    width: 100,
                    field: 'tarifa',
                    serverField: 'tarifa',
                    pipe: function(input){
                        return filterFormatReais(input)
                    },
                    cellTemplate: '<div class="ui-grid-cell-contents">\
                                    <input type="text" ng-model="row.entity.tarifa" readonly\
                                            class="no-borders" style="background: none;" ui-money-mask="3" />\
                               </div>',
                    enableFiltering: false,
                    enableSorting: false
                },
                {
                    name: 'Cashback',
                    displayName: 'Cashback',
                    width: 120,
                    pipe: function (input) {
                        return filterFormatReais(input)
                    },
                    field: 'cashbackTransacao',
                    serverField: 'cashbackTransacao',
                    cellTemplate: '<div class="ui-grid-cell-contents">\
                                    <input type="text" ng-model="row.entity.cashbackTransacao" readonly\
                                            class="no-borders" style="background: none;" ui-money-mask="3" />\
                               </div>',
                    enableFiltering: false,
                    enableSorting: false
                },
                {
                    name: 'Valor de Operacao',
                    displayName: 'Valor de Operação',
                    width: 135,
                    pipe: function (input) {
                        return filterFormatReais(input)
                    },
                    field: 'valorOperacao',
                    serverField: 'valorOperacao',
                    cellTemplate: '<div class="ui-grid-cell-contents">\
                                    <input type="text" ng-model="row.entity.valorOperacao" readonly\
                                            class="no-borders" style="background: none;" ui-money-mask="3" />\
                               </div>',
                    enableFiltering: false,
                    enableSorting: false
                },
                {
                    name: 'Data de Pagamento',
                    displayName: 'Data de Pagamento',
                    width: 140,
                    type: 'date',
                    field: 'dataPagamento',
                    serverField: 'dataPagamento',
                    enableFiltering: false,
                    enableSorting: false
                },
                {
                    name: 'Data de cadastro',
                    displayName: 'Data de cadastro',
                    width: 140,
                    type: 'date',
                    field: 'dataCadastro',
                    serverField: 'dataCadastro',
                    enableFiltering: false,
                    enableSorting: false
                },
                {
                    name: 'Status Financeiro',
                    displayName: 'Status Financeiro',
                    width: 170,
                    field: 'status',
                    serverField: 'status',
                    enableFiltering: false,
                    pipe: function (input) {
                        return filterStatusFinanceiro(input)
                    },
                    cellTemplate: '<div class="ui-grid-cell-contents" align="left">\
                                        <p ng-show="row.entity.status === \'Processando\'"> Processando </p>\
                                        <p ng-show="row.entity.status === \'PendenteAprovacao\'"> Autorização de remessa </p>\
                                        <p ng-show="row.entity.status === \'Aprovado\'"> Em fila de pagamento </p>\
                                        <p ng-show="row.entity.status === \'Baixado\'"> Pagamento realizado </p>\
                                        <p ng-show="row.entity.status === \'Cancelado\'"> Cancelado </p>\
                                        <p ng-show="row.entity.status === \'Erro\'"> Não efetivado </p>\
                                        <p ng-show="row.entity.status === \'Reprovado\'"> Reprovado </p>\
                                   </div>',
                    enableSorting: false
                },
                {
                    name: 'Status Pagamentos',
                    displayName: 'Status Pagamentos',
                    width: 170,
                    field: 'statusProtocolo',
                    serverField: 'statusProtocolo',
                    enableFiltering: false,
                    pipe: function (input) {
                        return filterStatusPagamento(input)
                    },
                    cellTemplate: '<div class="ui-grid-cell-contents" align="left">\
                                        <p ng-show="row.entity.statusProtocolo === \'Reprovado\'"> Reprovado </p>\
                                        <p ng-show="row.entity.statusProtocolo === \'Aprovado\'"> Aprovado </p>\
                                        <p ng-show="row.entity.statusProtocolo === \'Pendente\'"> Pendente </p>\
                                        <p ng-show="row.entity.statusProtocolo === \'Naopossui\'">  </p>\
                                   </div>',
                    enableSorting: false
                }]
        };

        $scope.$watch('vm.date.endDate', function () {
            vm.date.endDate = vm.date.endDate.add(-3, 'hours')
        })
        
        function filterStatusFinanceiro (input) {
            var correcao;
            switch (input) {
                case "PendenteAprovacao" :
                    correcao = "Autorizacao de remessa"
                    break;
                case "Aprovado" :
                    correcao = "Em fila de pagamento"
                    break;
                case "Baixado" :
                    correcao = "Pagamento realizado"
                    break;
                case "Erro" :
                    correcao = "Nao efetivado"
                    break;
                default :
                    correcao = input;
            }
            return correcao
        }

        function filterStatusPagamento (input) {
            var correcao;
            switch (input) {
                case "NaoPossui" :
                    correcao = ""
                    break;
                default :
                    correcao = input;
            }
            return correcao
        }

        function filterFormatCNPJ (input) {
            if (!input) return;
            return input.replace(/^(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/, "$1.$2.$3/$4-$5")
        }
        
        //.toLocaleString('pt-br', {maximumFractionDigits: 2, minimumFractionDigits: 2}) 
        // pra 2 casas dps da virgula
        function filterFormatReais(input) {
            if (!input) return;
            return "R$ " + (input).toLocaleString('pt-br', {
                maximumFractionDigits: 3, 
                minimumFractionDigits: 3
            }).replace(/\./g, '');
        }

        function downloadFile(base64Data, fileName, mimeType) {
            var blob = base64ToBlob(base64Data, mimeType);
            var url = URL.createObjectURL(blob);

            var a = document.createElement('a');
            a.href = url;
            a.download = fileName;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        function base64ToBlob(base64Data, mimeType) {
            var byteCharacters = atob(base64Data);
            var byteNumbers = new Array(byteCharacters.length);

            for (var i = 0; i < byteCharacters.length; i++) {
                byteNumbers[i] = byteCharacters.charCodeAt(i);
            }

            var byteArray = new Uint8Array(byteNumbers);
            return new Blob([byteArray], { type: mimeType });
        }

        function exportarEmPdf() {
            vm.desabilitarBtnRelatorio = true;
            vm.gridOptions.dataSource.consultarDadosRelatorio(function (response) {
                vm.dadosRelatorio = response.data.data.items;
                $timeout(function () {
                    BaseService.exportarTabelaEmPdfNovo(
                        "#exportable",
                        "Relatório Financeiro",
                        "BBC_Relatorio_Financeiro_" + vm.date.startDate.toDate().toLocaleDateString().replace("/","-") + "_a_" + vm.date.endDate.toDate().toLocaleDateString().replace("/","-")
                    )
                }, 500);
                vm.desabilitarBtnRelatorio = false;
            });
        }

        function exportarEmExcel(formatoXls) {
            vm.desabilitarBtnRelatorio = true;
            vm.gridOptions.dataSource.consultarDadosRelatorio(function (response) {
                vm.dadosRelatorio = response.data.data.items;
                $timeout(function () {
                    BaseService.exportarTabelaEmExcel2("exportable-xls", "Transacoes", formatoXls)
                }, 500);
                vm.desabilitarBtnRelatorio = false;
            });
        }

        function exportarEmTxt() {
            vm.desabilitarBtnRelatorio = true;
            vm.gridOptions.dataSource.consultarDadosRelatorio(function (response) {
                vm.dadosRelatorio = response.data.data.items;
                $timeout(function () {
                    BaseService.exportarTabelaEmTxt("exportable", "Transacoes")
                }, 500);
                vm.desabilitarBtnRelatorio = false;
            });
        }

        vm.setarStatusTodos = function () {
            vm.status = 0;
        }

        vm.consultarProtocolo = function (id) {
            $uibModal.open({
                animation: true,
                ariaLabelledBy: 'modal-title',
                ariaDescribedBy: 'modal-body',
                templateUrl: 'app/entities/painel-financeiro/modal-abastecimentos/modal-abastecimentos.html',
                controller: function ($uibModalInstance, $uibModalStack, $scope, BaseService, id) {
                    var vm = this;

                    vm.pagamentoAbastecimento = id;

                    vm.headerItems = [{
                        name: 'Abastecimentos'
                    }];

                    vm.gridAbastecimentosOptions = {
                        data: [],
                        onRegisterApi: BaseService.dataGrid.getGridApiRegistration("gridAbastecimentosOptions"),
                        dataSource: {
                            autoBind: true,
                            url: "Abastecimento/ConsultarGridLoteAbastecimentosControle",
                            params: function () {
                                return {
                                    PagamentoAbastecimento: vm.pagamentoAbastecimento,
                                }
                            },
                        },
                        columnDefs: [{
                            name: 'Codigo abastecimento',
                            displayName: 'Código abastecimento',
                            width: '*',
                            minWidth: 120,
                            field: 'id',
                            serverField: 'id',
                            primaryKey: true,
                            type: 'number',
                            enableFiltering: true
                        },
                            {
                                name: 'Codigo protocolo',
                                displayName: 'Código protocolo',
                                width: 120,
                                field: 'protocoloAbastecimentoId',
                                serverField: 'protocoloAbastecimentoId',
                                type: 'number',
                                enableFiltering: true
                            },
                            {
                                name: 'Nota fiscal',
                                displayName: 'NF',
                                width: 120,
                                field: 'notaFiscal',
                                serverField: 'notaFiscal',
                                enableFiltering: false,
                                enableSorting: false
                            },
                            {
                                name: 'Placa',
                                displayName: 'Placa',
                                width: 120,
                                field: 'placa',
                                serverField: 'placa',
                                enableFiltering: false,
                                enableSorting: false
                            },
                            {
                                name: 'Combustível',
                                displayName: 'Combustível',
                                width: 140,
                                field: 'combustivel',
                                serverField: 'combustivel',
                                enableFiltering: false,
                                enableSorting: false
                            },
                            {
                                name: 'Litragem',
                                displayName: 'Litragem',
                                width: 120,
                                field: 'litragem',
                                serverField: 'litragem',
                                cellTemplate: '<div class="ui-grid-cell-contents">\
                                                <input type="text" ng-model="row.entity.litragem" readonly\
                                                        class="no-borders" style="background: none;" ui-number-mask="3" />\
                                           </div>',
                                enableFiltering: false,
                                enableSorting: false
                            },
                            {
                                name: 'Valor abastecimento',
                                displayName: 'Valor abastecimento',
                                width: '*',
                                minWidth: 140,
                                field: 'valorAbastecimento',
                                serverField: 'valorAbastecimento',
                                enableFiltering: false,
                                enableSorting: false
                            },
                            {
                                name: 'Data abastecimento',
                                displayName: 'Data abastecimento',
                                width: 140,
                                field: 'dataCadastro',
                                serverField: 'dataCadastro',
                                enableFiltering: false,
                                enableSorting: false
                            }]
                    };

                    vm.fechar = function () {
                        $uibModalStack.dismissAll();
                    }

                },
                controllerAs: 'vm',
                keyboard: true,
                size: 'lg',
                resolve: {
                    id: id
                }
            }).result.then(function () {
            });
        };
        vm.exportarRelatorio = function (extensao) {
            switch (extensao) {
                case 1: {
                    exportarEmExcel(true)
                    break;
                }
                case 2: {
                    exportarEmPdf()
                    break;
                }
                case 3: {
                    exportarEmTxt()
                    break;
                }
                case 4: {
                    exportarEmExcel(false)
                    break;
                }
                default:
                    exportarEmPdf()
                    break;
            }
        }
        
        vm.abrirModalRelatorio = function (controllerPai) {
            $uibModal.open({
                animation: true,
                ariaLabelledBy: 'modal-title',
                ariaDescribedBy: 'modal-body',
                templateUrl: 'app/entities/painel-financeiro/modal-relatorios/modal-relatorios.html',
                controller: function ($uibModalInstance, $uibModalStack) {
                    var vm = this;

                    vm.modalRelatorioOptions = [{}];
                    vm.modalRelatorioAbastecimentoOptions = [{}];

                    vm.headerItems = [{name: 'Transações Financeiras'}];

                    //Campos do "Mais detalhes"
                    //Mesmos nomes de propriedades da classe export no back pro relatorio novo
                    vm.modalRelatorioAbastecimentoOptions = [
                        {
                            name: "Codigo abastecimento",
                            field: "codigo",
                            enabled: true
                        },
                        {
                            name: "Codigo protocolo",
                            field: "codigoProtocolo",
                            enabled: true
                        },
                        {
                            name: "Nota fiscal",
                            field: "nf",
                            enabled: true
                        },
                        {
                            name: "Placa",
                            field: "placa",
                            enabled: true
                        },
                        {
                            name: "Combustível",
                            field: "combustivel",
                            enabled: true
                        },
                        {
                            name: "Litragem",
                            field: "litragem",
                            enabled: true
                        },
                        {
                            name: "Valor abastecimento",
                            field: "valorAbastecimento",
                            enabled: true
                        },
                        {
                            name: "Data abastecimento",
                            field: "dataCadastro",
                            enabled: true
                        }
                    ]
                    for (var x in controllerPai.gridOptions.columnDefs) {
                        vm.modalRelatorioOptions[x] = {
                            name: controllerPai.gridOptions.columnDefs[x].displayName,
                            field: controllerPai.gridOptions.columnDefs[x].field,
                            pipe: controllerPai.gridOptions.columnDefs[x].pipe,
                            enabled: true
                        }
                    }

                    vm.fechar = function () {
                        controllerPai.modalRelatorioOptions = vm.modalRelatorioOptions;
                        controllerPai.modalRelatorioAbastecimentoOptions = vm.modalRelatorioAbastecimentoOptions;
                        $uibModalStack.dismissAll();
                    }

                    vm.exportarRelatorio = function (extensao) {

                        if (vm.modalRelatorioOptions.filter(function (x) {
                            return x.enabled
                        }).length < 2) {
                            toastr.error("Escolha alguma coluna para gerar o relatório.");
                            return;
                        }

                        if (controllerPai.date.endDate.diff(controllerPai.date.startDate, 'days') > 30) {
                            toastr.error("Escolha um período de no máximo 30 dias para gerar o relatório.")
                            return;
                        }

                        controllerPai.modalRelatorioOptions = vm.modalRelatorioOptions;
                        controllerPai.modalRelatorioAbastecimentoOptions = vm.modalRelatorioAbastecimentoOptions;
                        controllerPai.exportarRelatorio(extensao);
                       
                        $uibModalStack.dismissAll();
                    }

                },
                controllerAs: 'vm',
                keyboard: true,
                size: 'lg'
            }).result.then(function () {
            });
        };
        
        // ###################### Inicialização da Tela ######################
        
        $scope.$on('$stateChangeStart', function () {
            PersistentDataService.store('PainelFinanceiroController', vm, "Painel financeiro", "PainelFinanceiroController", "painel-financeiro.index");
        });
        
        $timeout(function () {
            PersistentDataService.remove('PainelFinanceiroController');
        }, 15);

        vm.load = function () {
        }

        vm.atualizaTela = function () {
            vm.gridOptions.dataSource.refresh();
        }

        // ###################### Inicialização da Tela ######################
    }
})();