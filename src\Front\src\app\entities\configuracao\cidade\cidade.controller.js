(function() {
    'use strict';

    angular.module('bbcWeb').controller('CidadeController', CidadeController);

    CidadeController.$inject = [
        '$scope',
        'PersistentDataService',
        'BaseService',
        'toastr',
        '$rootScope',
        '$state',
        '$timeout'
    ];

    function CidadeController(
        $scope,
        PersistentDataService,
        BaseService,
        toastr,
        $rootScope,
        $state,
        $timeout) {
        //Inicialização dos objetos e arrays
        var vm = this;
        vm.headerItems = [{
            name: 'Administração'
        }, {
            name: 'Cidade'
        }];
        vm.cidade = {};
        vm.cidades = [];
        vm.perfilUsuarioLogado = $rootScope.usuarioLogado.perfil;
        // Configurações da grid...
        vm.gridOptions = {
            data: [],
            onRegisterApi: BaseService.dataGrid.defaultOnRegisterApi,
            dataSource: {
                autoBind: true,
                url: "CidadeAts/ConsultarGrid"
            },
            columnDefs: [{
                    name: '<PERSON><PERSON><PERSON>',
                    primaryKey: true,
                    width: 80,
                    field: 'IdCidade',
                    type: 'number'
                },
                {
                    name: '<PERSON><PERSON>ri<PERSON>',
                    field: 'Nome',
                    width: '*',
                    minWidth: 250
                },
                {
                    name: 'Estado',
                    field: 'NomeEstado',
                    serverField: 'Estado.Nome',
                    width: '*',
                    minWidth: 250
                },
                {
                    name: 'Ações',
                    width: 80,
                    cellTemplate: '<div  ng-if="!row.groupHeader" class="ui-grid-cell-contents" title="TOOLTIP">\
                                    <button ng-disabled="!row.entity.Ativo" type="button" ui-sref="configuracao.cidade-crud({link: row.entity.IdCidade})"\
                                        ng-class="{ \'btn btn-xs btn-info\': true }">\
                                        <i class="fa fa-edit"></i>\
                                    </button>\
                                    <button type="button" ng-click="grid.appScope.vm.alterarStatus(row.entity.IdCidade, row.entity.Ativo)" ng-class="row.entity.Ativo ? \'btn btn-xs btn-success\' : \'btn btn-xs btn-danger\'">\
                                        <i ng-class="row.entity.Ativo ? \'fa fa-check\' : \'fa fa-times\'"></i>\
                                    </button>\
                                  </div>'
                }
            ]
        };
        // Fim config Grid
        vm.alterarStatus = function(id, ativo) {
            BaseService.post('CidadeAts', ativo ? "Inativar" : "Reativar", {
                idCidade: id
            }).then(function(response) {
                if (response.success)
                    toastr.success(response.message);
                else
                    toastr.error(response.message);

                vm.gridOptions.dataSource.refresh();
            });
        };

        // DO NOT TOUCH!!
        $scope.$on('$stateChangeStart', function(_, toParams) {
            PersistentDataService.store('CidadeController', vm, "Cidade", "CidadeCrudController", "configuracao.cidade");
        });
        var selfScope = PersistentDataService.get('CidadeController');
        var filho = PersistentDataService.get('CidadeCrudController');
        if (angular.isDefined(filho)) {
            $timeout(function() {
                $state.go('configuracao.cidade-crud', {
                    link: filho.data.cidade.IdCidade > 0 ? 'editar' : 'novo'
                });
            }, 50);
        } else if (angular.isDefined(selfScope))
            angular.extend(vm, selfScope.data);
        // DO NOT TOUCH!!
    }
})();