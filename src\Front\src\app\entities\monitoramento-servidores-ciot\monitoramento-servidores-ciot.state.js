(function () {
    'use strict';

    angular.module('bbcWeb.monitoramento-servidores-ciot.state', []).config(routeConfig);

    function routeConfig($stateProvider) {
        $stateProvider
            .state('monitoramento-servidores-ciot', {
                url: "/monitoramento-servidores-ciot",
                abstract: true,
                templateUrl: "app/layout/content.html"
            })
            .state('monitoramento-servidores-ciot.index', {
                url: '/index',
                templateUrl: 'app/entities/monitoramento-servidores-ciot/monitoramento-servidores-ciot.html'
            })
            .state('monitoramento-servidores-ciot.servidor-ciot-crud', {
                url: '/:id/:nome/:link/:tipoServidor',
                templateUrl: 'app/entities/monitoramento-servidores-ciot/abas/ciot/servidor-ciot-crud.html'
            });
    }
})();