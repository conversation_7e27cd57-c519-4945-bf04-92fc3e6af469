(function() {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('MenuController', MenuController);

    function MenuController(toastr,
        BaseService,
        PersistentDataService,
        $scope) {


        var vm = this;    
        vm.consultaMenu = {};

        vm.headerItems = [{
            name: 'Administra<PERSON>'
        }, {
            name: 'Menu'
        }];

        vm.gridOptions = {
            data: [],
            onRegisterApi: BaseService.dataGrid.defaultOnRegisterApi,
            dataSource: {
                autoBind: true,
                url: "Menu/ConsultarGridMenu"
            },
            columnDefs: [
                {
                    name: 'A<PERSON>õ<PERSON>',
                    width: 80,
                    enableColumnMenu: false,
                    cellTemplate: '<div ng-if="!row.groupHeader" class="ui-grid-cell-contents" title="TOOLTIP">\
                                        <button ng-disabled="!row.entity.Ativo" title="Editar" type="button" ui-sref="menu.crud({link: row.entity.Id})"\
                                            ng-class="{ \'btn btn-xs btn-info\': true }">\
                                            <i class="fa fa-edit"></i>\
                                        </button>\
                                        <button type="button"  ng-click="grid.appScope.vm.alterarStatus(row.entity.Id, row.entity.Ativo)" ng-class="row.entity.Ativo ? \'btn btn-xs btn-success\' : \'btn btn-xs btn-danger\'">\
                                            <i ng-class="row.entity.Ativo ? \'fa fa-check\' : \'fa fa-times\'"></i>\
                                        </button>\
                                    </div>'
                },{
                    name: 'Código',
                    displayname: 'Cód.',
                    width: 80,
                    primaryKey: true,
                    field: 'Id'
                },
                {
                    displayName: 'Descrição',
                    name: 'Descrição',
                    field: 'Descricao',
                    width: '*',
                    minWidth: 250,
                    enableGrouping: false,
                },
                {
                    displayName: 'Menu Pai',
                    name: 'Menu Pai',
                    field: 'IsMenuPai',
                    width: '*',
                    minWidth: 150,
                    enableGrouping: false,
                    enableColumnMenu: false
                }   

            ]
        };


           // DO NOT TOUCH!!
           $scope.$on('$stateChangeStart', function () {
            PersistentDataService.store('MenuController', vm, "Menu - Cadastro de Menu", "MenuController", "menu.index");
        });


    }
})();