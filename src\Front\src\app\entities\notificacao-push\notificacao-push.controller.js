(function () {
    'use strict';

    angular.module('bbcWeb').controller('NotificacaoPushController', NotificacaoPushController);

    NotificacaoPushController.$inject = [
        '$scope',
        'PersistentDataService',
        'BaseService',
        'toastr',
        '$window',
        'PERFIL_ADMINISTRADOR',
        'PERFIL_EMPRESA',
        '$rootScope',
        '$state',
        '$timeout'
    ];

    function NotificacaoPushController(
        $scope,
        PersistentDataService,
        BaseService,
        toastr,
        $window,
        PERFIL_ADMINISTRADOR,
        PERFIL_EMPRESA,
        $rootScope,
        $state,
        $timeout) {
        //Inicialização dos objetos e arrays
        var vm = this;
        vm.headerItems = [{
            name: 'Cadastros'
        }, {
            name: 'Notificação Push'
        }];
        vm.noti = {};
        vm.consultaEmpresa = {};
        vm.consultaFilial = {};
        vm.perfilUsuarioLogado = $rootScope.usuarioLogado.perfil;


        // Configurações da grid...
        vm.gridOptions = {
            data: [],
            onRegisterApi: BaseService.dataGrid.defaultOnRegisterApi,
            dataSource: {
                autoBind: true,
                url: "NotificacaoPushAts/ConsultarGrid",
                params: function () {
                    return {
                        idEmpresa: vm.consultaEmpresa.selectedValue,
                        idFilial: vm.consultaFilial.selectedValue,
                        descricao: vm.noti.Descricao
                    };
                }
            },
            columnDefs: [{
                    name: 'Cód.',
                    width: "7%",
                    primaryKey: true,
                    field: 'IdNotificacaoPush',
                    type: 'number'
                },
                {
                    name: 'Descrição',
                    field: 'Descricao',
                    width: '25%',
                    enableGrouping: false
                },
                {
                    name: 'Empresa',
                    serverField: 'Empresa.RazaoSocial',
                    width: '26%',
                    field: 'RazaoSocialEmpresa'
                },
                {
                    name: 'Filial',
                    serverField: 'Filial.RazaoSocial',
                    width: '26%',
                    field: 'RazaoSocialFilial'
                },
                {
                    name: 'Ações',
                    width: '9%',
                    enableColumnMenu: false,
                    cellTemplate: '<div  ng-if="!row.groupHeader" class="ui-grid-cell-contents" title="TOOLTIP">\
                                    <button ng-disabled="!row.entity.Ativo" type="button" ui-sref="gestao-logistica-notificacao-push.notificacao-crud({link: row.entity.IdNotificacaoPush})"\
                                        ng-class="{ \'btn btn-xs btn-info\': true }">\
                                        <i class="fa fa-edit"></i>\
                                    </button>\
                                    <button type="button" ng-click="grid.appScope.vm.alterarStatus(row.entity.IdNotificacaoPush, row.entity.Ativo)" ng-class="row.entity.Ativo ? \'btn btn-xs btn-success\' : \'btn btn-xs btn-danger\'">\
                                        <i ng-class="row.entity.Ativo ? \'fa fa-check\' : \'fa fa-times\'"></i>\
                                    </button>\
                                </div>'
                }
            ]
        };
        // Fim config Grid

        vm.alterarStatus = function (id, ativo) {
            BaseService.post('NotificacaoPushAts', ativo ? "Inativar" : "Reativar", {
                IdNotificacaoPush: id
            }).then(function (response) {
                if (response.success)
                    toastr.success(response.message);
                else
                    toastr.error(response.message);

                vm.gridOptions.dataSource.refresh();
            });
        };

        vm.empresaVisivel = function () {
            return $rootScope.usuarioLogado.perfil == PERFIL_ADMINISTRADOR;
        };

        vm.filialVisivel = function () {
            return $rootScope.usuarioLogado.perfil == PERFIL_ADMINISTRADOR || $rootScope.usuarioLogado.perfil == PERFIL_EMPRESA;
        };

        $scope.$watch('vm.consultaEmpresa.selectedValue', function (newValue, oldValue) {
            if (angular.isDefined(newValue)) {
                vm.consultaFilial.selectedValue = undefined;
                vm.consultaFilial.selectedText = undefined;
                vm.consultaFilial.values = [];
            }
        });

        vm.consultaFilial = {
            columnDefs: [{
                name: 'Cód.',
                field: 'IdFilial',
                width: '6%',
                primaryKey: true
            }, {
                name: 'Razão Social',
                field: 'RazaoSocial',
                width: '40%'
            }, {
                name: 'CNPJ',
                field: 'CNPJ',
                enableGrouping: true,
                width: '40%'
            }],
            desiredValue: 'IdFilial',
            desiredText: 'RazaoSocial',
            url: 'FilialAts/ConsultarGrid',
            paramsMethod: function () {
                return {
                    IdEmpresa: vm.consultaEmpresa.selectedValue
                }
            }
        };

        vm.consultaEmpresa = {
            columnDefs: [{
                name: 'Código',
                field: 'IdEmpresa',
                width: '6%',
                primaryKey: true
            }, {
                name: 'Razão Social',
                field: 'RazaoSocial',
                enableGrouping: true,
                width: '40%'
            }, {
                name: 'CNPJ',
                field: 'CNPJ',
                enableGrouping: true,
                width: '40%'
            }],
            desiredValue: 'IdEmpresa',
            desiredText: 'RazaoSocial',
            url: 'EmpresaAts/ConsultarGrid'
        };

        vm.consultaEmpresa.selectedValue = $window.localStorage.getItem("idEmpresa").toFixedType();

        // DO NOT TOUCH!!
        $scope.$on('$stateChangeStart', function (_, toParams) {
            PersistentDataService.store('NotificacaoPushController', vm, "Notificações push", "NotificacaoPushCrudController", "gestao-logistica-notificacao-push.notificacao");
        });
        var selfScope = PersistentDataService.get('NotificacaoPushController');
        var filho = PersistentDataService.get('NotificacaoPushCrudController');
        if (angular.isDefined(filho)) {
            $timeout(function () {
                $state.go('gestao-logistica-notificacao-push.notificacao-crud', {
                    link: filho.data.notificacaoPush.IdNotificacaoPush > 0 ? 'editar' : 'novo'
                });
            }, 50);
        } else if (angular.isDefined(selfScope))
            angular.extend(vm, selfScope.data);
        // DO NOT TOUCH!!
    }
})();