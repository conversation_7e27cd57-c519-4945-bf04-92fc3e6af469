.input-picker .ws-picker-body,.input-picker .ws-button-row,.input-picker .picker-grid,.input-picker .picker-list,.input-picker .ws-options button{zoom:1}.input-picker .ws-picker-body:before,.input-picker .ws-button-row:before,.input-picker .picker-grid:before,.input-picker .picker-list:before,.input-picker .ws-options button:before,.input-picker .ws-picker-body:after,.input-picker .ws-button-row:after,.input-picker .picker-grid:after,.input-picker .picker-list:after,.input-picker .ws-options button:after{display:table;clear:both;content:' '}.input-picker[data-class~=show-week] .ws-week,.show-week .input-picker .ws-week{display:table-cell}.input-picker[data-class~=show-yearbtns] .ws-picker-header,.show-yearbtns .input-picker .ws-picker-header{margin:0 4.23077em}.input-picker[data-class~=show-yearbtns] button.ws-year-btn,.show-yearbtns .input-picker button.ws-year-btn{display:inline-block}.input-picker[data-class~=hide-btnrow] .ws-button-row,.hide-btnrow .input-picker .ws-button-row{display:none}.input-picker[data-class~=show-selectnav] .ws-picker-header>button:after,.show-selectnav .input-picker .ws-picker-header>button:after,.input-picker[data-class~=show-uparrow] .ws-picker-header>button:after,.show-uparrow .input-picker .ws-picker-header>button:after{display:inline-block}.input-picker[data-class~=show-selectnav] .ws-picker-header>select,.show-selectnav .input-picker .ws-picker-header>select{display:inline-block}.input-picker[data-class~=show-selectnav] .ws-picker-header>button,.show-selectnav .input-picker .ws-picker-header>button{width:auto}.input-picker[data-class~=show-selectnav] .ws-picker-header>button>span,.show-selectnav .input-picker .ws-picker-header>button>span{display:none}.input-picker .ws-button-row>button{background:#ccc;padding:.38462em .61538em;display:inline-block;border:.07692em solid transparent}.input-picker{overflow:visible;font-size:13px;outline:0;text-align:center;font-family:sans-serif;width:29.23077em;min-width:20.76923em;max-width:98vw}.input-picker .ws-po-outerbox{-webkit-transform:translate(0,30%);transform:translate(0,30%)}.input-picker[data-vertical=bottom] .ws-po-outerbox{-webkit-transform:translate(0,-30%);transform:translate(0,-30%)}.input-picker.time-popover,.input-picker.datetime-local-popover{width:31.92308em}.input-picker.time-popover .ws-prev,.input-picker.time-popover .ws-next,.input-picker.time-popover .ws-super-prev,.input-picker.time-popover .ws-super-next{display:none}.input-picker.ws-size-2{width:51.92308em;min-width:51.53846em}.input-picker.ws-size-3{width:75.76923em;min-width:75.53846em}.input-picker.color-popover{width:590px;min-width:575px}.input-picker abbr[title]{cursor:help}.input-picker li,.input-picker button{font-size:1em;line-height:1.23077em;color:#000;transition:all 400ms}.input-picker .ws-focus,.input-picker :focus{outline:1px dotted #000}.input-picker .ws-po-box{position:relative;padding:1.15385em 1.53846em;direction:ltr}.input-picker .ws-picker-controls{position:absolute;top:1.15385em}.input-picker .ws-picker-controls>button{box-sizing:content-box;border:.07692em solid #ccc;padding:0;width:1.84615em;height:1.84615em;background:#eee;z-index:1;color:#333}.input-picker .ws-picker-controls>button.ws-year-btn:after,.input-picker .ws-picker-controls>button:before{display:inline-block;content:"";width:0;height:0;border-style:solid;margin-top:.29231em}.input-picker .ws-picker-controls>button:hover{border-color:#666;color:#000}.input-picker .ws-picker-controls>button[disabled]{opacity:.4;border-color:#eee;color:#ddd}.input-picker .prev-controls,.input-picker .ws-po-box[dir=rtl] .next-controls{left:1.53846em;right:auto}.input-picker .prev-controls>.ws-year-btn:after,.input-picker .prev-controls>button:before,.input-picker .ws-po-box[dir=rtl] .next-controls>.ws-year-btn:after,.input-picker .ws-po-box[dir=rtl] .next-controls>button:before{border-width:.35em .6em .35em 0;border-color:transparent #333 transparent transparent;margin-left:-.1em}.input-picker .prev-controls>.ws-year-btn,.input-picker .ws-po-box[dir=rtl] .next-controls>.ws-year-btn{margin-right:.23077em;margin-left:0}.input-picker .prev-controls>.ws-year-btn[disabled],.input-picker .ws-po-box[dir=rtl] .next-controls>.ws-year-btn[disabled]{display:none}.input-picker .next-controls,.input-picker .ws-po-box[dir=rtl] .prev-controls{right:1.53846em;left:auto}.input-picker .next-controls>button:before,.input-picker .ws-po-box[dir=rtl] .prev-controls>button:before{margin-left:.11538em}.input-picker .next-controls>.ws-year-btn:after,.input-picker .next-controls>button:before,.input-picker .ws-po-box[dir=rtl] .prev-controls>.ws-year-btn:after,.input-picker .ws-po-box[dir=rtl] .prev-controls>button:before{border-width:.35em 0 .35em .6em;border-color:transparent transparent transparent #333;margin-right:-.1em}.input-picker .next-controls>.ws-year-btn,.input-picker .ws-po-box[dir=rtl] .prev-controls>.ws-year-btn{margin-left:.23077em;margin-right:0}.input-picker .next-controls>.ws-year-btn[disabled],.input-picker .ws-po-box[dir=rtl] .prev-controls>.ws-year-btn[disabled]{display:none}.input-picker.ws-po-visible .ws-picker-controls>button:after,.input-picker.ws-po-visible .ws-picker-controls>button:before{content:" "}.input-picker .ws-po-box[dir=rtl]{direction:rtl}.input-picker.time-popover .ws-picker-body{padding-top:2.76923em}.input-picker .ws-picker-body{position:relative;padding:3.07692em 0 0;zoom:1;margin:0 -.76923em}.input-picker .ws-button-row{position:relative;margin:.76923em 0 0;border-top:.07692em solid #eee;padding:.76923em 0 0;text-align:left;z-index:2}.input-picker .ws-button-row>button{border:.07692em solid #ccc;background-color:#ddd;background-image:linear-gradient(to bottom,#ececec 0,#ddd 100%);transition:border-color 200ms linear;float:left}.input-picker .ws-button-row>button.ws-empty{float:right}.input-picker .ws-po-box[dir=rtl] .ws-button-row>button{float:right}.input-picker .ws-po-box[dir=rtl] .ws-button-row>button.ws-empty{float:left}.input-picker[data-currentview=setMonthList] .ws-picker-header>select,.input-picker[data-currentview=setYearList] .ws-picker-header>select{max-width:90%}.input-picker[data-currentview=setDayList] .ws-picker-header>select{max-width:40%}.input-picker[data-currentview=setDayList] .ws-picker-header>.month-select{max-width:50%}.input-picker.time-popover .ws-picker-header{top:-2.30769em}.input-picker.time-popover .ws-picker-header button{font-size:1.15385em}.input-picker .ws-picker-header{position:absolute;top:-3.07692em;right:0;left:0;margin:0 2.69231em}.input-picker .ws-picker-header>button{display:inline-block;width:100%;margin:0;padding:.30769em 0;font-weight:700;color:#000}.input-picker .ws-picker-header>button>.month-digit,.input-picker .ws-picker-header>button>.monthname-short{display:none}.input-picker .ws-picker-header>button:after{content:" ";margin:-.1em .5em 0;width:0;height:0;border-style:solid;border-width:0 .3em .6em;border-color:transparent transparent #333;vertical-align:middle}.input-picker .ws-picker-header>button:hover{text-decoration:underline}.input-picker .ws-picker-header>button[disabled]:after{display:none!important}.input-picker .ws-picker-header>button[disabled]:hover{text-decoration:none}.input-picker .picker-grid{position:relative;zoom:1;overflow:hidden;margin:0 -.15385em}.input-picker .picker-grid .monthname,.input-picker .picker-grid .month-digit{display:none}.input-picker.ws-size-1 .picker-list{float:none;width:auto}.input-picker .picker-list{position:relative;zoom:1;width:22.30769em;float:left;margin:0 10px;background:#fff}.input-picker .picker-list tr{border:0}.input-picker .picker-list th,.input-picker .picker-list td{padding:.15385em;text-align:center}.input-picker .picker-list.day-list td{padding:.03846em .15385em}.input-picker .picker-list.day-list td>button{padding:.42308em 0}.input-picker .picker-list.time-list>.ws-picker-header>button>.monthname{display:inline}.input-picker .picker-list.time-list td{padding:.07692em .38462em}.input-picker .picker-list.time-list td>button{padding:.52692em 0}.input-picker .picker-list td>button{display:block;padding:1.58992em 0;width:100%;color:#000;background-color:#fff}.input-picker .picker-list td>button.othermonth{color:#888}.input-picker .picker-list td>button:hover,.input-picker .picker-list td>button.checked-value{color:#fff;background:#000}.input-picker .picker-list td>button[disabled],.input-picker .picker-list td>button[disabled]:hover{color:#888;background-color:#fff}.input-picker .picker-list table{width:100%;margin:0;border:0 none;border-collapse:collapse;table-layout:fixed}.input-picker .picker-list th,.input-picker .picker-list td.week-cell{font-size:1em;line-height:1.23077em;padding-bottom:.23077em;text-transform:uppercase;font-weight:700}.input-picker .ws-options{margin:.76923em 0 0;border-top:.07692em solid #eee;padding:.76923em 0 0;text-align:left}.input-picker .ws-options h5{margin:0 0 .38462em;padding:0;font-size:1.07692em;font-weight:700}.input-picker .ws-options ul,.input-picker .ws-options li{padding:0;margin:0;list-style:none}.input-picker .ws-options button{display:block;padding:.30769em;width:100%;text-align:left}.input-picker .ws-options button.ws-focus,.input-picker .ws-options button:focus,.input-picker .ws-options button:hover{color:#fff;background:#000}.input-picker .ws-options button[disabled],.input-picker .ws-options button[disabled].ws-focus,.input-picker .ws-options button[disabled]:focus,.input-picker .ws-options button[disabled]:hover{color:#888;background:#fff;text-decoration:none}.input-picker .ws-options button .ws-value{float:left}.input-picker .ws-options button .ws-label{float:right;font-size:96%}.input-picker .ws-week,.input-picker .ws-year-btn{display:none}.ws-picker-controls>button{display:inline-block}.ws-picker-header>button:after{display:none}.ws-picker-header select{display:none}.capture-popover .ws-po-box{padding-left:.30769em;padding-right:.30769em}.ws-videocapture-view{position:relative;height:0;width:100%;padding-bottom:70%}.ws-videocapture-view .ws-video-overlay,.ws-videocapture-view video,.ws-videocapture-view .polyfill-video{position:absolute!important;top:0;left:0;width:100%!important;height:100%!important}