(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('VeiculoController', VeiculoController);

    VeiculoController.inject = ['BaseService', '$rootScope', 'toastr', '$scope', 'PersistentDataService', '$timeout', '$state', 'PERFIL_ADMINISTRADOR'];

    function VeiculoController(BaseService, $rootScope, toastr, $scope, PersistentDataService, $timeout, $state, PERFIL_ADMINISTRADOR) {
        var vm = this;
        vm.headerItems = [{
            name: '<PERSON><PERSON><PERSON><PERSON>'
        }, {
            name: 'Veí<PERSON><PERSON>'
        }];

        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.perfil == PERFIL_ADMINISTRADOR;
        };

        vm.gridOptions = {
            data: [],
            onRegisterApi: BaseService.dataGrid.defaultOnRegisterApi,
            dataSource: {
                url: "Veiculo/ConsultarGridVeiculo"
            },
            columnDefs: [{
                name: 'A<PERSON>ões',
                width: 80,
                cellTemplate: '<div ng-if="!row.groupHeader" class="ui-grid-cell-contents" title="TOOLTIP">\
                                    <button ng-disabled="row.entity.ativo===0" tooltip-placement="right" uib-tooltip="Editar" type="button" ui-sref="veiculo.crud({link: row.entity.id})"\
                                        ng-class="{ \'btn btn-xs btn-info\': true }">\
                                        <i class="fa fa-edit"></i>\
                                    </button>\
                                    <button type="button" tooltip-placement="right" uib-tooltip="Ativar / Inativar" ng-click="grid.appScope.vm.alterarStatus(row.entity.id, row.entity.ativo)" ng-class="row.entity.ativo===1 ? \'btn btn-xs btn-success\' : \'btn btn-xs btn-danger\'">\ <i ng-class="row.entity.ativo===1 ? \'fa fa-check\' : \'fa fa-times\'"></i>\
                                    </button>\
                                </div>'
            }, {
                name: 'Código',
                width: 80,
                type: 'number',
                primaryKey: true,
                field: 'id'
            }, {
                name: 'Placa',
                width: 110,
                field: 'placa'
            }, {
                name: 'Renavam',
                width: '*',
                minWidth: 150,
                field: 'renavam'
            }, {
                name: 'Proprietário',
                width: '*',
                minWidth: 250,
                field: 'portador.nome',
                serverField: 'portador.Nome'
            }]
        };

        vm.alterarStatus = function (id, ativo) {
            BaseService.post('Veiculo', "AlterarStatus", {
                id: id
            }).then(function (response) {
                response.success ? (ativo === 1 ? toastr.success('Veículo inativado com sucesso!') : toastr.success('Veículo reativado com sucesso!')) : toastr.error(response.message);
                vm.gridOptions.dataSource.refresh();
            });
        };

        // Controle de aba!!
        $scope.$on('$stateChangeStart', function () {
            PersistentDataService.store('VeiculoController', vm, "Veículo", "VeiculoCrudController", "veiculo.index");
        });

        var selfScope = PersistentDataService.get('VeiculoController');
        var filho = PersistentDataService.get('VeiculoCrudController');
        if (angular.isDefined(filho)) {
            $timeout(function () {
                $state.go('veiculo.veiculo-crud', {
                    link: filho.data.veiculo.IdVeiculo > 0 ? 'editar' : 'novo'
                });
            }, 50);
        } else if (angular.isDefined(selfScope)) {
            angular.extend(vm, selfScope.data);

            $timeout(function () {
                vm.gridOptions.dataSource.refresh();
            }, 450);
        }
    }
})();