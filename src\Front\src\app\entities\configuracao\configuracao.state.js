(function() {
    'use strict';

    angular.module('bbcWeb.configuracao', []).config(routeConfig);

    function routeConfig($urlRouterProvider, $stateProvider) {
        $stateProvider.state('configuracao', {
            abstract: true,
            url: "/configuracao",
            templateUrl: "app/layout/content.html"
        }).state('configuracao.estado', {
            url: "/estado",
            templateUrl: "app/entities/configuracao/estado/estado.html"
        }).state('configuracao.estado-crud', {
            url: "/estado/:link",
            templateUrl: "app/entities/configuracao/estado/estado-crud.html"
        }).state('configuracao.pais', {
            url: "/pais",
            templateUrl: "app/entities/configuracao/pais/pais.html"
        }).state('configuracao.pais-crud', {
            url: "/pais/:link",
            templateUrl: "app/entities/configuracao/pais/pais-crud.html"
        }).state('configuracao.cidade', {
            url: "/cidade",
            templateUrl: "app/entities/configuracao/cidade/cidade.html"
        }).state('configuracao.cidade-crud', {
            url: "/cidade/:link",
            templateUrl: "app/entities/configuracao/cidade/cidade-crud.html"
        })
    }
})();