﻿<div>
    <form name="formPainelMotoristaEdit" novalidate ng-submit="vm.salvarPercentualTransferenciaPortador(formPainelMotoristaEdit)" novalidate ats-validator>
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" ng-click="$dismiss()" aria-label="Close">
                    <em class="fa fa-times"></em>
                </button>
                <h4 class="modal-title">Editar Motorista</h4>
            </div>
            <div class="modal-body">
                <div class="row">                                  
					<hr-label dark="true" title="'Dados do Motorista'"></hr-label>
					<br>
                    <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
						<div class="row">
							<div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
								<div class="form-group">
									<label class="control-label col-xs-12 col-sm-4 col-md-4 col-lg-4">
										Código:
									</label>
									<div class="input-group col-xs-12 col-sm-8 col-md-8 col-lg-8">
										<input type="text" class="form-control" disabled
											   value="{{vm.motorista.portadorId}}"/>
									</div>
								</div>
							</div>
						</div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
						<div class="form-group">
							<label class="control-label col-xs-12 col-sm-4 col-md-4 col-lg-4">
								Motorista:
							</label>
							<div class="input-group col-xs-12 col-sm-8 col-md-8 col-lg-8">
								<input type="text" class="form-control" disabled
									   value="{{vm.motorista.portadorNome}}"/>
							</div>
						</div>
					</div>
					<div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
						<div class="form-group">
							<label class="col-xs-12 col-sm-4 col-md-4 col-lg-4 control-label">
								CPF:
							</label>
							<div class="input-group col-xs-12 col-sm-8 col-md-8 col-lg-8">
								<input type="text" class="form-control" disabled
									   value="{{vm.motorista.portadorCpf}}"/>
							</div>
						</div>
					</div>
                </div>
                <div class="row">
					<hr-label dark="true" title="'% Transferência'"></hr-label>
					<br>
                   <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
						<div class="form-group">
							<label class="col-xs-12 col-sm-4 col-md-4 col-lg-4 control-label">
								<span class="text-danger mr-5">*</span>Adiantamento:
							</label>
							<div class="input-group col-xs-12 col-sm-8 col-md-8 col-lg-8">
								<input type="number" required
									   ng-model="vm.motorista.adiantamento"
									   required-message="'Adiantamento é obrigatório'"
									   max="100"
									   validate-on="blur"
									   name="Adiantamento" class="form-control"/>
							</div>
						</div>
					</div>
					<div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
						<div class="form-group">
							<label class="col-xs-12 col-sm-4 col-md-4 col-lg-4 control-label">
								<span class="text-danger mr-5">*</span>Saldo:
							</label>
							<div class="input-group col-xs-12 col-sm-8 col-md-8 col-lg-8">
								<input type="number" required
									   ng-model="vm.motorista.saldo"
									   required-message="'Saldo é obrigatório'"
									   max="100"
									   validate-on="blur"
									   name="Saldo" class="form-control"/>
							</div>
						</div>
					</div>
                </div>               
            </div>
            <div class="modal-footer">
                <div class="col-md-12">  
					<button type="submit" ng-disabled="vm.isSaving" data-style="expand-right"
							class="btn btn-labeled btn-success text-right">
						<span class="btn-label">
							<i class="fa fa-check-circle"></i>
						</span>
						Salvar
					</button>
					<button type='button' class="btn btn-labeled btn-default pull-left"
							ng-click="vm.fechar();"
							tooltip-placement="top">
						<i class="fa fa-close"></i>
						<span class="pl-5 ">Fechar</span>
					</button>
				</div>
            </div>
        </div>
    </form>
</div>