<div>
    <div class="modal-header text-white" style="background-color: #066334">
        <h3 class="modal-title" id="modal-title">Cadastro de usuário</h3>
    </div>
    <div class="modal-body" id="modal-body">
        <div style="margin-bottom: 10%; margin-top: 10%;" ng-show="vm.loader">
            <spinner-loader></spinner-loader>
            <div class="row">
                <center>
                    <h2>Aguarde, processando ...</h2>
                </center>
            </div>
        </div>
        <form ng-show="!vm.loader" name="formEmpresaUsuario">
            <div class="row">
                <div class="col-md-8">
                    <div class="form-group">
                        <label><span class="text-danger">*</span> Nome</label>
                        <input type="text" maxlength="200" ng-model="vm.usuario.Nome" class="form-control" />
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label><span class="text-danger">*</span> CPF</label>
                        <input type="text" ng-model="vm.usuario.Cpf" class="form-control" ui-mask="999.999.999-99" />
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-8">
                    <div class="form-group">
                        <label><span class="text-danger">*</span> E-mail</label>
                        <input type="text" maxlength="200" ng-model="vm.usuario.Email" class="form-control" />
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label><span class="text-danger">*</span> Login</label>
                        <input type="text" maxlength="200" ng-model="vm.usuario.Login" class="form-control" />
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label><span class="text-danger">*</span> Senha</label>
                        <div class="input-group">
                            <input type="password" maxlength="200" ng-model="vm.usuario.Senha" class="form-control" />
                            <span ng-show="vm.validacaoSenha === 1" class="input-group-addon">
                                <i class="fa fa-check-circle"></i>
                            </span>
                            <span ng-show="vm.validacaoSenha === 2" class="input-group-addon">
                                <i class="fa fa-check-circle text-danger"></i>
                            </span>
                            <span ng-show="vm.validacaoSenha === 3" class="input-group-addon">
                                <i class="fa fa-check-circle text-success"></i>
                            </span>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label><span class="text-danger">*</span> Confirmação de senha</label>
                        <div class="input-group">
                            <input type="password" maxlength="200" ng-model="vm.usuario.SenhaConfirmacao"
                                class="form-control" />
                            <span ng-show="vm.validacaoSenha === 1" class="input-group-addon">
                                <i class="fa fa-check-circle"></i>
                            </span>
                            <span ng-show="vm.validacaoSenha === 2" class="input-group-addon">
                                <i class="fa fa-check-circle text-danger"></i>
                            </span>
                            <span ng-show="vm.validacaoSenha === 3" class="input-group-addon">
                                <i class="fa fa-check-circle text-success"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-4">
                    <div class="form-group">
                        <label><span class="text-danger">*</span> Telefone</label>
                        <input type="text" maxlength="200" ng-model="vm.usuario.Telefone" class="form-control"
                            ui-br-phone-number />
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label><span class="text-danger">*</span> Celular</label>
                        <input type="text" maxlength="200" ng-model="vm.usuario.Celular" class="form-control"
                            ui-br-phone-number />
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label><span class="text-danger">*</span> CEP</label>
                        <input type="text" maxlength="200" ng-model="vm.usuario.Cep"
                            ng-blur="vm.buscarEndereco(vm.usuario.Cep)" class="form-control" ui-mask="99999-999" />
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label><span class="text-danger">*</span> Endereço</label>
                        <input type="text" maxlength="200" ng-model="vm.usuario.Endereco" class="form-control" />
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label><span class="text-danger">*</span> Bairro</label>
                        <input type="text" maxlength="200" ng-model="vm.usuario.Bairro" class="form-control" />
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label><span class="text-danger">*</span> Estado</label>
                        <ui-select class="master-supreme-infinity-z-index-but-the-login-z-index-is-higher"
                            ng-change="vm.estadoChange(vm.usuario.EstadoId)" required append-to-body="false"
                            ng-model="vm.usuario.EstadoId">
                            <ui-select-match>
                                <span>{{$select.selected.descricao}}</span>
                            </ui-select-match>
                            <ui-select-choices
                                repeat="ex.id as ex in vm.estados | propsFilter: {descricao: $select.search}">
                                <div ng-bind-html="ex.descricao | highlight: $select.search"></div>
                            </ui-select-choices>
                        </ui-select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label><span class="text-danger">*</span> Cidade</label>
                        <ui-select class="master-supreme-infinity-z-index-but-the-login-z-index-is-higher" required
                            append-to-body="false" ng-model="vm.usuario.CidadeId" ats-ui-select-validator validate-on="blur">
                            <ui-select-match>
                                <span>{{$select.selected.descricao}}</span>
                            </ui-select-match>
                            <ui-select-choices
                                repeat="ex.id as ex in vm.cidades | propsFilter: {descricao: $select.search}">
                                <div ng-bind-html="ex.descricao | highlight: $select.search"></div>
                            </ui-select-choices>
                        </ui-select>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-3">
                    <div class="form-group">
                        <label><span class="text-danger">*</span> Número</label>
                        <input type="number" min="0" ng-model="vm.usuario.EnderecoNumero" class="form-control" />
                    </div>
                </div>
                <div class="col-md-9">
                    <div class="form-group">
                        <label> Complemento</label>
                        <input type="text" maxlength="200" ng-model="vm.usuario.Complemento" class="form-control" />
                    </div>
                </div>
            </div>
        </form>
    </div>
    <div class="modal-footer">
        <div class="col-md-12">
            <button ng-click="vm.clickCancelar()" data-dismiss="modal" ng-disabled="vm.loader" type="button" class="btn btn-xs btn-white">
                <i class="fa fa-times">&nbsp;</i> Cancelar
            </button>
            <button ng-click="vm.clickConfirmar()" ng-disabled="vm.loader" type="button" class="btn btn-xs btn-success">
                <i class="fa fa-cog">&nbsp;</i> Confirmar
            </button>
        </div>
    </div>
</div>