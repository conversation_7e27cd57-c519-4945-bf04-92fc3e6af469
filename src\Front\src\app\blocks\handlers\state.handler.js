(function () {
    'use strict';

    angular.module('bbcWeb').factory('stateHandler', stateHandler);

    stateHandler.$inject = [
        "$rootScope",
        '$state',
        '$templateCache',
        '$timeout',
        '$window',
        'PERFIL_ADMINISTRADOR',
        'PERFIL_EMPRESA',
        'PERFIL_ESTABELECIMENTO',
        'toastr',
        'DEFAULT_TEMA'
    ];

    function stateHandler(
        $rootScope,
        $state,
        $templateCache,
        $timeout,
        $window,
        PERFIL_ADMINISTRADOR,
        PERFIL_EMPRESA,
        PERFIL_ESTABELECIMENTO,
        toastr,
        DEFAULT_TEMA
    ) {
        return {
            initialize: initialize
        };

        function initialize() {
            $rootScope.isFullScreen = false;
            $rootScope.currentYear = new Date().getFullYear();
            // Gerenciador de scopes na memória do nevegador
            $rootScope.mem = {};
            $rootScope.wndManagerMinimized = false;

            // Templates
            $templateCache.remove('ui-grid/pagination');
            $templateCache.remove('ui-grid/ui-grid');
            $templateCache.remove('ui-grid/ui-grid-filter');

            $templateCache.put('ui-grid/ui-grid-filter',
                "<div class=\"ui-grid-filter-container\" ng-style=\"col.extraStyle\" ng-repeat=\"colFilter in col.filters\" ng-class=\"{'ui-grid-filter-cancel-button-hidden' : colFilter.disableCancelFilterButton === true }\"><div ng-if=\"colFilter.type !== 'select'\"><input type=\"text\" ng-model-options=\"{ debounce: 1000 }\" class=\"ui-grid-filter-input ui-grid-filter-input-{{$index}}\" ng-model=\"colFilter.term\" ng-attr-placeholder=\"{{colFilter.placeholder || ''}}\" aria-label=\"{{colFilter.ariaLabel || aria.defaultFilterLabel}}\"><div role=\"button\" class=\"ui-grid-filter-button\" ng-click=\"removeFilter(colFilter, $index)\" ng-if=\"!colFilter.disableCancelFilterButton\" ng-disabled=\"colFilter.term === undefined || colFilter.term === null || colFilter.term === ''\" ng-show=\"colFilter.term !== undefined && colFilter.term !== null && colFilter.term !== ''\"><i class=\"ui-grid-icon-cancel\" ui-grid-one-bind-aria-label=\"aria.removeFilter\">&nbsp;</i></div></div><div ng-if=\"colFilter.type === 'select'\"><select ng-model-options=\"{ debounce: 1000 }\" class=\"ui-grid-filter-select ui-grid-filter-input-{{$index}}\" ng-model=\"colFilter.term\" ng-show=\"colFilter.selectOptions.length > 0\" ng-attr-placeholder=\"{{colFilter.placeholder || aria.defaultFilterLabel}}\" aria-label=\"{{colFilter.ariaLabel || ''}}\" ng-options=\"option.value as option.label for option in colFilter.selectOptions\"><option value=\"\"></option></select><div role=\"button\" class=\"ui-grid-filter-button-select\" ng-click=\"removeFilter(colFilter, $index)\" ng-if=\"!colFilter.disableCancelFilterButton\" ng-disabled=\"colFilter.term === undefined || colFilter.term === null || colFilter.term === ''\" ng-show=\"colFilter.term !== undefined && colFilter.term != null\"><i class=\"ui-grid-icon-cancel\" ui-grid-one-bind-aria-label=\"aria.removeFilter\">&nbsp;</i></div></div></div>"
            );

            $templateCache.put('customDateFieldTemplateUiGrid',
                "<div class=\"ui-grid-filter-container\" ng-style=\"col.extraStyle\" ng-repeat=\"colFilter in col.filters\" ng-class=\"{'ui-grid-filter-cancel-button-hidden' : colFilter.disableCancelFilterButton === true }\">\
                    <div ng-if=\"colFilter.type !== 'select' && colFilter.type !== 'date'\">\
                        <input type=\"text\" ng-model-options=\"{ debounce: 1000 }\" class=\"ui-grid-filter-input ui-grid-filter-input-{{$index}}\" ng-model=\"colFilter.term\" ng-attr-placeholder=\"{{colFilter.placeholder || ''}}\" aria-label=\"{{colFilter.ariaLabel || aria.defaultFilterLabel}}\">\
                        <div role=\"button\" class=\"ui-grid-filter-button\" ng-click=\"removeFilter(colFilter, $index)\" ng-if=\"!colFilter.disableCancelFilterButton\" ng-disabled=\"colFilter.term === undefined || colFilter.term === null || colFilter.term === ''\" ng-show=\"colFilter.term !== undefined && colFilter.term !== null && colFilter.term !== ''\"><i class=\"ui-grid-icon-cancel\" ui-grid-one-bind-aria-label=\"aria.removeFilter\">&nbsp;</i></div>\
                    </div>\
                    <div ng-if=\"colFilter.type === 'select'\">\
                        <select ng-model-options=\"{ debounce: 1000 }\" class=\"ui-grid-filter-select ui-grid-filter-input-{{$index}}\" ng-model=\"colFilter.term\" ng-show=\"colFilter.selectOptions.length > 0\" ng-attr-placeholder=\"{{colFilter.placeholder || aria.defaultFilterLabel}}\" aria-label=\"{{colFilter.ariaLabel || ''}}\" ng-options=\"option.value as option.label for option in colFilter.selectOptions\">\
                            <option value=\"\"></option>\
                        </select>\
                        <div role=\"button\" class=\"ui-grid-filter-button-select\" ng-click=\"removeFilter(colFilter, $index)\" ng-if=\"!colFilter.disableCancelFilterButton\" ng-disabled=\"colFilter.term === undefined || colFilter.term === null || colFilter.term === ''\" ng-show=\"colFilter.term !== undefined && colFilter.term != null\"><i class=\"ui-grid-icon-cancel\" ui-grid-one-bind-aria-label=\"aria.removeFilter\">&nbsp;</i></div>\
                    </div>\
                    <div style='display: flex;' ng-if=\"colFilter.type === 'date'\">\
                         <input value=\"\" style='width: 100%' type=\"date\" ng-model-options=\"{ debounce: 1000 }\" class=\"placeholderclass ui-grid-filter-input ui-grid-filter-input-{{$index}}\" ng-model=\"colFilter.term\" ng-attr-placeholder=\"{{colFilter.placeholder || ''}}\" aria-label=\"{{colFilter.ariaLabel || aria.defaultFilterLabel}}\">\
                    </div>\
                </div>"
            );

            $templateCache.put('ui-grid/ui-grid',
                "<div ui-i18n=\"en\" class=\"ui-grid\"><style ui-grid-style>.grid{{ grid.id }} {\n" +
                "      /* Styles for the grid */\n" +
                "       {{grid.options.enableTopSearch ? 'margin-top: 45px' : ''}}" +
                "    }\n" +
                "\n" +
                "    .grid{{ grid.id }} .ui-grid-row, .grid{{ grid.id }} .ui-grid-cell, .grid{{ grid.id }} .ui-grid-cell .ui-grid-vertical-bar {\n" +
                "      height: {{ grid.options.rowHeight }}px;\n" +
                "    }\n" +
                "\n" +
                "    .grid{{ grid.id }} .ui-grid-row:last-child .ui-grid-cell {\n" +
                "      border-bottom-width: {{ ((grid.getTotalRowHeight() < grid.getViewportHeight()) && '1') || '0' }}px;\n" +
                "    }\n" +
                "\n" +
                "    {{ grid.verticalScrollbarStyles }}\n" +
                "    {{ grid.horizontalScrollbarStyles }}\n" +
                "\n" +
                "    /*\n" +
                "    .ui-grid[dir=rtl] .ui-grid-viewport {\n" +
                "      padding-left: {{ grid.verticalScrollbarWidth }}px;\n" +
                "    }\n" +
                "    */\n" +
                "\n" +
                "    {{ grid.customStyles }}</style>" +
                "<center><spinner-loader style='position: absolute; margin: 100px 0px 0px -41px;' ng-show='grid.options.loading'></spinner-loader></center>" +
                "<div class=\"ui-grid-contents-wrapper\"><div ui-grid-menu-button ng-if=\"grid.options.enableGridMenu\"></div><div ng-if=\"grid.hasLeftContainer()\" style=\"width: 0\" ui-grid-pinned-container=\"'left'\"></div><div ui-grid-render-container container-id=\"'body'\" col-container-name=\"'body'\" row-container-name=\"'body'\" bind-scroll-horizontal=\"true\" bind-scroll-vertical=\"true\" enable-horizontal-scrollbar=\"grid.options.enableHorizontalScrollbar\" enable-vertical-scrollbar=\"grid.options.enableVerticalScrollbar\"></div><div ng-if=\"grid.hasRightContainer()\" style=\"width: 0\" ui-grid-pinned-container=\"'right'\"></div><div ui-grid-grid-footer ng-if=\"grid.options.showGridFooter\"></div><div ui-grid-column-menu ng-if=\"grid.options.enableColumnMenus\"></div><div ng-transclude></div></div></div>"
            );

            $templateCache.put('ui-grid/pagination',
                "<div role=\"contentinfo\" class=\"ui-grid-pager-panel\" ui-grid-pager ng-show=\"grid.options.enablePaginationControls\"><div role=\"navigation\" class=\"ui-grid-pager-container\"><div role=\"menubar\" class=\"ui-grid-pager-control\"><button type=\"button\" role=\"menuitem\" class=\"ui-grid-pager-first\" ui-grid-one-bind-title=\"aria.pageToFirst\" ui-grid-one-bind-aria-label=\"aria.pageToFirst\" ng-click=\"pageFirstPageClick()\" ng-disabled=\"cantPageBackward()\"><div ng-class=\"grid.isRTL() ? 'last-triangle' : 'first-triangle'\"><div ng-class=\"grid.isRTL() ? 'last-bar-rtl' : 'first-bar'\"></div></div></button> <button type=\"button\" role=\"menuitem\" class=\"ui-grid-pager-previous\" ui-grid-one-bind-title=\"aria.pageBack\" ui-grid-one-bind-aria-label=\"aria.pageBack\" ng-click=\"pagePreviousPageClick()\" ng-disabled=\"cantPageBackward()\"><div ng-class=\"grid.isRTL() ? 'last-triangle prev-triangle' : 'first-triangle prev-triangle'\"></div></button> <input type=\"number\" ui-grid-one-bind-title=\"aria.pageSelected\" ui-grid-one-bind-aria-label=\"aria.pageSelected\" class=\"ui-grid-pager-control-input\" ng-model=\"grid.options.paginationCurrentPage\" min=\"1\" max=\"{{ paginationApi.getTotalPages() }}\" required> <span class=\"ui-grid-pager-max-pages-number\" ng-show=\"paginationApi.getTotalPages() > 0\"><span ui-grid-one-bind-title=\"paginationOf\">de</span> {{ paginationApi.getTotalPages() }}</span> <button type=\"button\" role=\"menuitem\" class=\"ui-grid-pager-next\" ui-grid-one-bind-title=\"aria.pageForward\" ui-grid-one-bind-aria-label=\"aria.pageForward\" ng-click=\"pageNextPageClick()\" ng-disabled=\"cantPageForward()\"><div ng-class=\"grid.isRTL() ? 'first-triangle next-triangle' : 'last-triangle next-triangle'\"></div></button> <button type=\"button\" role=\"menuitem\" class=\"ui-grid-pager-last\" ui-grid-one-bind-title=\"aria.pageToLast\" ui-grid-one-bind-aria-label=\"aria.pageToLast\" ng-click=\"pageLastPageClick()\" ng-disabled=\"cantPageToLast()\"><div ng-class=\"grid.isRTL() ? 'first-triangle' : 'last-triangle'\"><div ng-class=\"grid.isRTL() ? 'first-bar-rtl' : 'last-bar'\"></div></div></button></div><div class=\"ui-grid-pager-row-count-picker\" ng-if=\"grid.options.paginationPageSizes.length > 1 && !grid.options.useCustomPagination\"><select ui-grid-one-bind-aria-labelledby-grid=\"'items-per-page-label'\" ng-change=\"grid.options.onGridPageSizeChange(grid.options.paginationPageSize)\" ng-model=\"grid.options.paginationPageSize\" ng-options=\"o as o for o in grid.options.paginationPageSizes\"></select><span ui-grid-one-bind-id-grid=\"'items-per-page-label'\" class=\"ui-grid-pager-row-count-label\">&nbsp;{{sizesLabel}}</span></div><span ng-if=\"grid.options.paginationPageSizes.length <= 1\" class=\"ui-grid-pager-row-count-label\">{{grid.options.paginationPageSize}}&nbsp;{{sizesLabel}}</span></div><div class=\"ui-grid-pager-count-container\"><div class=\"ui-grid-pager-count\"><span ng-show=\"grid.options.totalItems > 0\">{{ paginationApi.getFirstRowIndex() + 1}} <span ui-grid-one-bind-title=\"paginationThrough\">-</span> {{ grid.options.paginationCurrentPage > 1 && !cantPageToLast() ? paginationApi.getLastRowIndex() + 1 : paginationApi.getLastRowIndex() }} {{paginationOf}} {{grid.options.totalItems}} {{totalItemsLabel}}</span></div></div></div>"
            );

            $rootScope.app = {
                owner: 'Sistema Info',
                name: 'Rede Web',
                description: 'Rede Software e Hardware',
                year: ((new Date()).getFullYear())
            };

            $rootScope.usuarioLogado = {
                nome: "Indefinido",
                idUsuario: null,
                idGrupoUsuario: null,
                ativo: false
            };

            $rootScope.setUsuarioLogadoValues = function () {
                try {
                    $rootScope.usuarioLogado.idUsuario = $window.localStorage.getItem("idUsuario") ? $window.localStorage.getItem("idUsuario").toFixedType() : null;
                    $rootScope.usuarioLogado.idGrupoUsuario = $window.localStorage.getItem("idGrupoUsuario") ? $window.localStorage.getItem("idGrupoUsuario").toFixedType() : null;
                    $rootScope.usuarioLogado.nome = $window.localStorage.getItem("nomeUsuario") ? $window.localStorage.getItem("nomeUsuario") : $window.localStorage.getItem("nome");
                    $rootScope.usuarioLogado.ativo = $window.localStorage.getItem("ativo");
                    $rootScope.usuarioLogado.administrador = $window.localStorage.getItem("administrador");
                    $rootScope.usuarioLogado.empresaId = $window.localStorage.getItem("empresaId") ? $window.localStorage.getItem("empresaId").toFixedType() : null;
                    $rootScope.usuarioLogado.empresaNome = $window.localStorage.getItem("empresaNome");
                } catch (err) {

                }
            }

            $rootScope.setUsuarioLogadoValues();   
            $rootScope.menusUsuarioLogado = [];

            $rootScope.previousState;

            $rootScope.PERFIL_ADMINISTRADOR = PERFIL_ADMINISTRADOR;
            $rootScope.PERFIL_EMPRESA = PERFIL_EMPRESA;
            $rootScope.PERFIL_ESTABELECIMENTO = PERFIL_ESTABELECIMENTO;

            $rootScope.isOnAtsDomain = true;
            $rootScope.currentAtsDomain = "";

            $rootScope.isBackEndResponding = true;

            $rootScope.alertBackEndNotResponding = function () {
                if ($rootScope.usuarioLogado.perfil == 1)
                    toastr.error("Ops! Parece que os nossos servidores estão demorando para responder, entre em contato com o administrador do sistema ou tente novamente mais tarde!");
            }

            $rootScope.alertBackEndResponding = function () {
                if ($rootScope.usuarioLogado.perfil == 1)
                    toastr.success("Parece que os nossos servidores voltaram a responder! Atualize sua página para continuar usando o sistema!");
            }

            if (DEFAULT_TEMA == 1) {
                $rootScope.imgCustomLogoDashboard = 'logo_sistemainfo.png';
                var style = document.createElement('style');
                style.type = 'text/css';
                style.innerHTML = '.login .login-header { display: block; position: absolute; top: 0; left: 0; right: 0; width: 403px; padding: 0; margin: -20px auto; font-weight: 300; }  .login-cover-bg { background: radial-gradient(ellipse at center, #666666 0%, rgba(145, 145, 145, 0.72) 100%) !important }';
                document.getElementsByTagName('head')[0].appendChild(style);
            }

            $rootScope.$watch('usuarioLogado.idEmpresaBase > 0', function (newV) {
                // Muda a logo...
                if (newV) {
                    if (angular.isDefined($rootScope.usuarioLogado.customLogoDomain) && $rootScope.usuarioLogado.customLogoDomain !== null) {
                        $rootScope.imgCustomLogoDashboard = $rootScope.usuarioLogado.customLogoDomain.toString();
                        $rootScope.isOnAtsDomain = false;
                        var style = document.createElement('style');
                        style.type = 'text/css';
                        style.innerHTML = $rootScope.usuarioLogado.customCssDomain;
                        document.getElementsByTagName('head')[0].appendChild(style);
                    } else {
                        $rootScope.imgCustomLogoDashboard = 'logo_sistemainfo.png';
                        var style = document.createElement('style');
                        style.type = 'text/css';
                        style.innerHTML = '.login .login-header { display: block; position: absolute; top: 0; left: 0; right: 0; width: 403px; padding: 0; margin: -20px auto; font-weight: 300; } .logoSotranAtsNovoHotFix { width: 325px; margin-top: 80px } #logoCustomDomainFormHeader { max-height: 60px; height: 60px; margin-top: 15px } .login-cover-bg { background: radial-gradient(ellipse at center, #666666 0%, rgba(145, 145, 145, 0.72) 100%) !important }';
                        document.getElementsByTagName('head')[0].appendChild(style);
                    }
                } else
                    $rootScope.isOnAtsDomain = true;
            });

            $rootScope.$on('$stateChangeSuccess', function (event, to, toParams, from) {
                $rootScope.previousState = from;
            });

            $rootScope.$on('$stateChangeStart', function (event, toState, toParams, fromState, fromParams) {
                var sessionKey = $window.localStorage.getItem('SessionKey');
                if (sessionKey === 'invalid-key-value' && 
                    toState.name !== 'login')
                {
                    event.preventDefault();
                }
            });

            $rootScope.goBackState = function () {
                if (($rootScope.previousState.name !== '' &&
                    $rootScope.previousState.name !== 'login.logout' &&
                    $rootScope.previousState.name !== 'login.sessao-expirada-login') || !$rootScope.usuarioLogado.idUsuario > 0)
                    //$state.go($rootScope.previousState.name);
                    $state.go('index.main');
                else
                    $state.go('index.main');
            };

            $rootScope.goToState = function (state) {
                $state.go(state);
            };

            $rootScope.minimizar = function () {
                angular.element('body').addClass('mini-navbar');
                angular.element('#side-menu').hide(0);
                angular.element('#side-menu').fadeIn(0);
                angular.element($window).trigger('resize');
            }

            $rootScope.maximizar = function () {
                angular.element('#side-menu').removeAttr('style');
                angular.element('#side-menu').show(0);
                angular.element('body').removeClass('mini-navbar');
                angular.element($window).trigger('resize');
            }

            $rootScope.minimalize = function () {
                angular.element('body').toggleClass('mini-navbar');
                if (!angular.element('body').hasClass('mini-navbar') ||
                    angular.element('body').hasClass('body-small')) {
                    angular.element('#side-menu').hide(0);
                    angular.element('#side-menu').fadeIn(0);
                } else {
                    angular.element('#side-menu').removeAttr('style');
                }

                angular.element($window).trigger('resize');
            }

            if (angular.isUndefined($window.localStorage.getItem('SessionKey')) ||
                $window.localStorage.getItem('SessionKey') === null) {
                $window.localStorage.setItem('SessionKey', 'invalid-key-value');
            }


            $rootScope.$state = $state;

            // Gerenciador de statescarregados
            var loadedStates = [];
            $rootScope.$on('$stateChangeStart', function (evt, toState, toParams, fromState) {
                if (toState.name !== 'estabelecimento-base.crud' && toState.name !== 'login.logout' && $rootScope.usuarioLogado.usuarioEstabPrimeiroLoginSemEstab) {

                    if (fromState.name !== 'estabelecimento-base.crud')
                        $timeout(function () {
                            $state.go('estabelecimento-base.crud', {
                                link: 'novo',
                                forcarCadastramentoPeloUsuarioLogado: true
                            });
                        }, 250);
                    else {
                        $timeout(function () {
                            $state.go('estabelecimento-base.crud', {
                                link: 'novo',
                                forcarCadastramentoPeloUsuarioLogado: true
                            });
                            if (!$rootScope.usuarioLogado.cadastrarEstabNoLogin)
                                toastr.error("É necessário primeiramente concluir o cadastro do estabelecimento base para continuar!");
                        }, 500);
                    }
                } else {
                    if (toState.name === 'index.main' && toState.name !== 'login.logout' && $rootScope.usuarioLogado.perfil == 6) {
                        $timeout(function () {
                            $state.go('pagamento-frete.pagamento-frete-resumido-crud', {
                                link: 'novo'
                            });
                        }, 150);
                    }
                    if (loadedStates.indexOf(toState.name) === -1) loadedStates.push(toState.name);
                }

                //Disparamos um resize para bugs de mapa e grid ao mudar o state.
                $timeout(function () {
                    angular.element($window).trigger('resize');
                }, 2000);
            });

            $rootScope.CSharpDateToString = function (date) {
                return new Date(parseInt(date.replace("/Date(", "").replace(")/", ""))).toLocaleString();
            }

            try {
                $window.parent.parent.document.URL; // gera o erro de cross-domain
                $rootScope.currentAtsDomain = "temp";
                $rootScope.isOnAtsDomain = true;
                $rootScope.usuarioLogado.idEmpresaBase = null;
                $window.localStorage.removeItem("idEmpresaBase");
            } catch (err) { }

            // Login,selecionarmenu,deslogar automático
            function onMessage(event) {
                var data = event.data;
                if (angular.isDefined(data.func)) {
                    if (data.func.toLowerCase() == 'selecionarmenu') {
                        $state.go(data.link);
                        $rootScope.recarregarNotificacoes = true;
                    }

                    if (data.func.toLowerCase() == 'deslogar')
                        $state.go('login.logout', {
                            callAtsALogout: false
                        });

                    if (data.func.toLowerCase() == 'callbackdomaincfg') {
                        if (!data.data) {
                            $rootScope.currentAtsDomain = data.currentDomain.toString();
                            $rootScope.isOnAtsDomain = false;

                        } else {
                            if (angular.isDefined(data.currentDomain)) {
                                $rootScope.currentAtsDomain = data.currentDomain.toString();
                            } else {
                                $rootScope.currentAtsDomain = "temp";
                            }
                            $rootScope.isOnAtsDomain = true;
                            $rootScope.usuarioLogado.idEmpresaBase = null;
                            $window.localStorage.removeItem("idEmpresaBase");
                        }
                    }
                }
            }

            if ($window.addEventListener)
                $window.addEventListener("message", onMessage, false);
            else if ($window.attachEvent)
                $window.attachEvent("onmessage", onMessage, false);
            // Fim login automático

            $window.parent.parent.postMessage({
                'func': 'isOnSistemaInfoDomain'
            }, "*");
        }
    }
})();