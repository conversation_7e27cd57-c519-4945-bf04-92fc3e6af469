(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('CombustivelController', CombustivelController);

        CombustivelController.inject = ['BaseService', '$rootScope', 'toastr', '$scope', 'PersistentDataService', '$timeout', '$state', 'PERFIL_ADMINISTRADOR'];

    function CombustivelController(BaseService, $rootScope, toastr, $scope, PersistentDataService, $timeout, $state, PERFIL_ADMINISTRADOR) {
        var vm = this;
        vm.headerItems = [{
            name: '<PERSON><PERSON><PERSON><PERSON>'
        }, {
            name: 'Combustível' 
        }];

        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.perfil == PERFIL_ADMINISTRADOR;
        };

        vm.gridOptions = {
            data: [],
            onRegisterApi: BaseService.dataGrid.defaultOnRegisterApi,
            dataSource: {
                url: "Combustivel/ConsultarGridCombustivel"
            },
            columnDefs: [{
                name: 'A<PERSON>õ<PERSON>',
                width: 80,
                cellTemplate: '<div ng-if="!row.groupHeader" class="ui-grid-cell-contents" title="TOOLTIP">\
                                    <button ng-disabled="row.entity.ativo===0" tooltip-placement="right" uib-tooltip="Editar" type="button" ui-sref="combustivel.combustivel-crud({link: row.entity.id})"\
                                        ng-class="{ \'btn btn-xs btn-info\': true }">\
                                        <i class="fa fa-edit"></i>\
                                    </button>\
                                    <button type="button" tooltip-placement="right" uib-tooltip-html="row.entity.ativo===0 ? \'Ativar\' : \'Inativar\'" \
                                        ng-click="grid.appScope.vm.alterarStatus(row.entity.id, row.entity.ativo)" \
                                        ng-class="row.entity.ativo===1 ? \'btn btn-xs btn-success\' : \'btn btn-xs btn-danger\'"> \
                                        <i ng-class="row.entity.ativo===1 ? \'fa fa-check\' : \'fa fa-times\'"></i>\
                                    </button>\
                                </div>'
            }, {
                name: 'Código',
                width: 80,
                type: 'number',
                primaryKey: true,
                field: 'id'
            }, {
                name: 'Nome',
                width: '*',
                field: 'nome'
            }]
        };

        vm.alterarStatus = function (id, ativo) {
            BaseService.post('Combustivel', "AlterarStatus", {
                id: id
            }).then(function (response) {
                response.success ? ativo ? toastr.success('Combustível inativado com sucesso!') : toastr.success('Combustível reativado com sucesso!') : toastr.error(response.message);
                vm.gridOptions.dataSource.refresh();
            });
        };

        // Controle de aba!!
        $scope.$on('$stateChangeStart', function () {
            PersistentDataService.store('CombustivelController', vm, "Combustível", "CombustivelCrudController", "combustivel.index");
        });

        var selfScope = PersistentDataService.get('CombustivelController');
        var filho = PersistentDataService.get('CombustivelCrudController');
        if (angular.isDefined(filho)) {
            $timeout(function () {
                $state.go('combustivel.combustivel-crud', {
                    link: filho.data.combustivel.IdCombustivel > 0 ? 'editar' : 'novo'
                });
            }, 50);
        } else if (angular.isDefined(selfScope)) {
            angular.extend(vm, selfScope.data);

            $timeout(function () {
                vm.gridOptions.dataSource.refresh();
            }, 450);
        }
    }
})();