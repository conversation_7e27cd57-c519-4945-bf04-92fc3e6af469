(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('LogSmsController', LogSmsController);

        LogSmsController.$inject = [
        '$scope',
        'toastr',
        'BaseService',
        'PERFIL_ADMINISTRADOR',
        'PERFIL_EMPRESA',
        'PERFIL_MESA',
        '$window',
        'PersistentDataService',
        '$rootScope',
        'DefaultsService'
    ];

    function LogSmsController(
        $scope, toastr, 
        BaseService,
        PERFIL_ADMINISTRADOR, 
        PERFIL_EMPRESA,
        PERFIL_MESA, 
        $window, 
        PersistentDataService,
        $rootScope, 
        DefaultsService) {

        var vm = this;

        vm.headerItems = [{
            name: 'Relatórios'
        }, {
            name: 'SMS Enviadas'
        }];

        vm.load = function () {
            if (vm.isProfileAdmin())
                vm.carregarEmpresas();
        };

        vm.isProfileAdmin = function () {
            return $rootScope.usuarioLogado.perfil == PERFIL_ADMINISTRADOR;
        };

        vm.isProfileEmpresa = function () {
            return ($rootScope.usuarioLogado.perfil == PERFIL_EMPRESA);
        };

        vm.isPerfilAutorizado = function() {
            return vm.isProfileAdmin() && vm.isProfileEmpresa();
        }

        vm.gridOptions = {
            onRegisterApi: BaseService.dataGrid.defaultOnRegisterApi,
            dataSource: {
                autoBind: true,
                url: "FreteConcorrenteAts/ConsultarGrid",
                params: function () {
                    return {
                        idEmpresa: vm.isProfileAdmin() && angular.isDefined(vm.consultaEmpresa.selectedValue) ? vm.consultaEmpresa.selectedValue : 0,
                        admin: vm.isProfileAdmin(),
                        idFilial: vm.consultaFilial.selectedValue
                    };
                }
            },
            columnDefs: [{
                name: 'Código',
                width: 80,
                field: 'IdFreteConcorrente',
                type: 'number',
                primaryKey: true,
                orientation: 1,
                visible: false
            }, 
            {
                name: 'Data',
                width: 130,
                field: 'DataCadastro',
                type: 'date'
            }, 
            {
                name: 'Cidade Origem',
                width: "*",
                minWidth: 220,
                field: 'NomeCidadeOrigem',
                serverField: 'CidadeOrigem.Nome'
            }, 
            {
                name: 'Cidade Destino',
                width: "*",
                minWidth: 220,
                field: 'NomeCidadeDestino',
                serverField: 'CidadeDestino.Nome'
            }, 
            {
                name: 'Cliente',
                width: '*',
                minWidth: 250,
                field: 'NomeCliente'
            }, 
            {
                name: 'Concorrente',
                width: "*",
                minWidth: 250,
                field: 'NomeConcorrente'
            },
            {
                name: 'Frete Pessoa Física',
                width: 145,
                field: 'ValorFretePf',
                type: 'number',
                cellFilter: 'currency'
            }, 
            {
                name: 'Frete Pessoa Jurídica',
                width: 145,
                field: 'ValorFretePj',
                type: 'number',
                cellFilter: 'currency'
            },  
            {
                name: 'Mercadoria',
                width: "*",
                minWidth: 200,
                field: 'NomeMercadoria'
            }, 
            {
                name: 'Observação',
                width: "*",
                minWidth: 250,
                field: 'Observacao'
            },
            {
                name: 'Usuário',
                width: "*",
                minWidth: 220,
                field: 'Usuario',
                serverField: 'Usuario.Nome'
            }]
        }

        $scope.$watch('vm.consultaEmpresa.selectedValue', function (newValue) {
            if (angular.isUndefined(newValue)) {
                vm.consultaFilial.selectedValue = undefined;
                vm.consultaFilial.selectedText = undefined;
                vm.consultaFilial.values = [];
            }
        });

        if (!vm.isProfileAdmin()) {
            vm.consultaEmpresa.selectedValue = $window.localStorage["idEmpresa"] ? $window.localStorage["idEmpresa"].toFixedType() : $window.localStorage["idEmpresa"];
            vm.consultaEmpresa.selectedText = $window.localStorage["razaoSocialEmpresa"];
        }

        // DO NOT TOUCH!!
        $scope.$on('$stateChangeStart', function () {
            PersistentDataService.store('FreteConcorrenteController', vm, "Frete da Concorrência", "FreteConcorrenteController", "frete-concorrente.index");
        });
    }
})();