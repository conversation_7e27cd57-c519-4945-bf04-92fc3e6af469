﻿<div>
    <form name="formClienteIpCiot" novalidate ng-submit="vm.salvar(formClienteIpCiot);"
          show-validation ats-validator>
        <!-- Modal content-->
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" ng-click="$dismiss()" aria-label="Close">
                    <em class="fa fa-times"></em>
                </button>
                <h4 class="modal-title">Ip Cliente Ciot</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="form-group">
                        <label class="col-xs-12 col-sm-3 col-md-3 col-lg-3 control-label">Código:</label>
                        <div class="input-group col-xs-3 col-sm-3 col-md-3 col-lg-3">
                            <input type="text" value="{{vm.clienteIp.id}}" class="form-control" disabled/>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="form-group">
                        <label class="col-xs-12 col-sm-3 col-md-3 col-lg-3 control-label-modal"><span
                                class="text-danger mr-5">*</span>Endereço de IP</label>
                        <div class="input-group col-xs-12 col-sm-9 col-md-9 col-lg-9">
                            <input type="text" style="text-transform: uppercase" name="ip" class="form-control" autofocus
                                   ng-model="vm.clienteIp.ip" required-message="'Endereço de IP é obrigatório'"
                                   ng-required="true"/>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="form-group">
                        <label class="control-label col-xs-12 col-md-4">Permitir acesso:</label>
                        <div class="input-group col-xs-12 col-md-8">
                            <toggle-switch on-label="Sim" off-label="Não" aria-label="Não"
                                           ng-model="vm.clienteIp.status" class="switch"></toggle-switch>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <div class="text-right ">
                <button ng-click="vm.cancelar()" data-dismiss="modal" type="button" class="btn btn-labeled btn-white">
                    <i class="fa fa-times">&nbsp;</i> Cancelar
                </button>
                <button type="submit"
                        class="btn btn-labeled btn-success text-right">
                    Confirmar
                </button>
            </div>
        </div>
    </form>
</div>
