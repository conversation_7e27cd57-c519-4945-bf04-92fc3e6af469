(function () {
    'use strict';

    angular.module('bbcWeb.atualizacao-preco-combustivel.state', []).config(routeConfig);

    function routeConfig($stateProvider) {
        $stateProvider
            .state('atualizacao-preco-combustivel', {
                url: "/atualizacao-preco-combustivel",
                abstract: true,
                templateUrl: "app/layout/content.html"})
            .state('atualizacao-preco-combustivel.index', {
                url: '/index',
                templateUrl: 'app/entities/atualizacao-preco-combustivel/atualizacao-preco-combustivel.html'});
    }
})();