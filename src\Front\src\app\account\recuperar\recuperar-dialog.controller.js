(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('RecuperarDialogController', RecuperarDialogController);
    RecuperarDialogController.inject = ['toastr', 'BaseService', '$uibModalStack'];

    function RecuperarDialogController(toastr, BaseService, $uibModalStack) {
        var vm = this;

        vm.usuario = {
            Cpf: null,
            Email: null
        };

        vm.isLoading = false;

        vm.submit = function (form) {
            if (!form.$valid) {
                toastr.error('Todos os campos obrigatórios e/ou inválidos devem ser preenchidos corretamente com seus respectivos valores');
                return;
            }
            vm.isLoading = true;
            try {
                var objUsuario = { Cpf: vm.usuario.Cpf, Email: vm.usuario.Email };
                BaseService.post('Usuario', 'RecuperarSenha', objUsuario).then(function (response) {
                    if (response.sucesso) {
                        toastr.success("Operação realizada com sucesso! Verifique seu e-mail para a recuperação da senha.");
                        var modalAberta = $uibModalStack.getTop();
                        $uibModalStack.dismiss(modalAberta.key);
                    } else {
                        toastr.error(response.mensagem)
                    }
                    vm.isLoading = false;
                });
            } catch (error) {
                toastr.error(error);
            }
        }
    }
})();