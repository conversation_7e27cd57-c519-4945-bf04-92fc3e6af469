﻿<div class="form-horizontal">
    <hr />
    <div class="col-xs-12 col-md-12">
        <div class="row">
            <div class="col-xs-12 col-md-6">
                <div class="form-group">
                    <label class="col-xs-12 col-md-4 control-label">
                        Placa:
                    </label>
                    <div class="input-group col-xs-12 col-md-8">
                        <input type="text" ng-model="vm.portador.placa"
                            style="text-transform: uppercase" ui-mask="***-****"
                            maxlength="100" ng-blur="vm.consultaVeiculo(vm.portador.placa)" validate-on="blur"
                            name="Placa" class="form-control" />
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-md-6">
                <div class="form-group">
                    <consulta-padrao-modal name="Carreta" 
                        directivesizes="'col-xs-12 col-md-12'" 
                        labelsize="'col-xs-12 col-md-4 control-label alinhamento-labels'" 
                        label="'Carreta:'" placeholder="'Selecione uma carreta'" 
                        tabledefinition="vm.consultaVeiculo1">
                    </consulta-padrao-modal>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12 col-md-6">
                <div class="form-group">
                    <consulta-padrao-modal name="Carreta2" 
                        directivesizes="'col-xs-12 col-md-12'" 
                        labelsize="'col-xs-12 col-md-4 control-label alinhamento-labels'" 
                        label="'Carreta 2:'" 
                        placeholder="'Selecione uma carreta'" 
                        tabledefinition="vm.consultaVeiculo2">
                    </consulta-padrao-modal>
                </div>
            </div>
            <div class="col-xs-12 col-md-6">
                <div class="form-group">
                    <consulta-padrao-modal name="Carreta3" 
                        directivesizes="'col-xs-12 col-md-12'" 
                        labelsize="'col-xs-12 col-md-4 control-label alinhamento-labels'" 
                        label="'Carreta 3:'" 
                        placeholder="'Selecione uma carreta'" 
                        tabledefinition="vm.consultaVeiculo3">
                    </consulta-padrao-modal>
                </div>
            </div>
        </div>
        <hr-label dark="true" title="'Centro de custo'"></hr-label>
        <br />
        <br />

        <div class="row">
            <div class="col-xs-12">
                <div class="form-group">
                    <consulta-padrao-modal name="Codigo" directivesizes="'col-xs-12 col-md-12'" 
                    labelsize="'col-xs-12 col-md-2 control-label alinhamento-labels'" 
                    label="'Código:'" placeholder="'Selecione um centro de custo'" 
                    tabledefinition="vm.consultaCentroCusto">
                    </consulta-padrao-modal>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-md-4 alinhamento-labels">Controla abastecimento por centro de custo:</label>
                    <div class="col-xs-12 col-md-2 alinhamento-labels" style="padding-top: 5px !important; padding-right: 0px !important;">
                        <toggle-switch on-label="Sim" off-label="Não" ng-model="vm.portador.controlaAbastecimentoCentroCusto" class="switch-small">
                        </toggle-switch>
                    </div>
                </div>
            </div>
            <div class="col-xs-6 text-right">
                <div class="form-group">
                    <button type="button" style="margin-left: 5px;" ng-disabled="!vm.consultaCentroCusto.selectedValue" ng-click="vm.adicionarCentroCusto()" class="btn btn-labeled btn-success text-right">
                        <span class="btn-label"><i class="fa fa-plus"></i></span>
                        Adicionar
                    </button>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="form-horizontal">
                <hr />
                <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
                    <div class="table-responsive">
                        <table class="table table-sm table-striped table-hover table-bordered" id="GridAdiantViagem">
                            <thead>
                                <th>Código</th>
                                <th>Descrição</th>
                                <th></th>
                            </thead>
                            <tbody>
                                <tr ng-repeat="item in vm.portador.portadorCentroCusto">
                                    <td>{{item.centroCustoId}}</td>
                                    <td>{{item.descricao}}</td>
                                    <td>
                                        <button title="Excluir" type="button" ng-click="vm.removeCentroCusto(item.centroCustoId)" ng-class="'btn btn-xs btn-danger'">
                                            <i class="fa fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>