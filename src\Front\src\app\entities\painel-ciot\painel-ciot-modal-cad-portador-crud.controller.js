(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('PainelCiotModalCadPortadorCrudController', PainelCiotModalCadPortadorCrudController);

    PainelCiotModalCadPortadorCrudController.$inject = ['$uibModalInstance', 'toastr', 'BaseService', '$timeout', '$scope', '$rootScope'];

    function PainelCiotModalCadPortadorCrudController($uibModalInstance, toastr, BaseService, $timeout, $scope, $rootScope) {
        var vm = this;
        vm.portador = {};
        vm.estados = [];
        vm.cidades = [];
        vm.estadosDisabled = true;
        vm.cidadesDisabled = true;

        vm.cpfCnpjInformado = null;
        vm.tipoPessoaInformado = null;

        vm.desabilitaCampoCpfCnpj = false;

        init();

        function init() {
            carregarEstados();
        };

        vm.buscarEndereco = function (cep) {
            if (cep) {
                limparEndereco();
                BaseService.getEndereco(cep).then(function (response) {
                    if (response && response.erro) {
                        toastr.warning("Endereço não localizado.");
                    }

                    carregarEstados();

                    $timeout(function () {
                        var estado = _.find(vm.estados, ['sigla', response.uf]);

                        if (angular.isDefined(estado))

                            vm.portador.estadoId = estado.id;
                        carregarCidades(vm.portador.estadoId);
                        $timeout(function () {
                            var cidade = _.find(vm.cidades, ['descricao', response.localidade]);
                            if (angular.isDefined(cidade))
                                vm.portador.cidadeId = cidade.id;
                        }, 1500);
                    }, 1500);

                    $timeout(function () {
                        if (angular.isDefined(response.logradouro))
                            vm.portador.endereco = response.logradouro;
                    }, 1500);

                    $timeout(function () {
                        if (angular.isDefined(response.bairro))
                            vm.portador.bairro = response.bairro;
                    }, 1500);
                });
            }
        };

        function carregarEstados() {
            BaseService.get("Endereco", "ConsultarEstados")
                .then(function (response) {
                    if (response.success) {
                        vm.estados = response.data;
                        vm.estadosDisabled = false;
                    }
                });
        };

        function carregarCidades(EstadoId) {
            BaseService.get("Endereco", "ConsultarCidadesPorEstado", {
                estadoId: EstadoId
            }).then(function (response) {
                if (response.success) {
                    vm.cidades = response.data;
                    vm.cidadesDisabled = false;
                }
            });
        };

        function limparEndereco() {
            vm.portador.estadoId = null;
            vm.portador.cidadeId = null;
            vm.estados = null;
            vm.cidades = null;
            vm.portador.endereco = null;
            vm.portador.bairro = null;
            vm.portador.enderecoNumero = null;
            vm.portador.complemento = null;
        };

        vm.estadoChange = function (estadoId) {
            vm.cidades = null;
            vm.cidadesDisabled = true;
            vm.portador.cidadeId = null;
            carregarCidades(estadoId);
        };

        vm.salvarPortador = function (form) {
            if (!form.$valid) {
                toastr.error('Todos os campos obrigatórios e/ou inválidos devem ser preenchidos corretamente com seus respectivos valores');
                return;
            }

            BaseService.post('Portador', 'CadastrarPortadorAutomatico', vm.portador).then(function (response) {
                if (response.success) {
                    toastr.success(response.message);
                    $uibModalInstance.close();
                    return;
                } else {
                    return toastr.error(response.message);
                }
            });
        };

        vm.limpar = function () {
            vm.desabilitaCampoCpfCnpj = false; 
            vm.portador.cpfCnpj = null;
            vm.portador.nome = null;
            vm.portador.rntrc = null;
            vm.portador.celular = null;
            vm.portador.telefone = null;
            vm.portador.email = null;
            vm.portador.cep = null;
            vm.portador.estadoId = null;
            vm.portador.cidadeId = null;
            vm.portador.endereco = null;
            vm.portador.bairro = null;
            vm.portador.enderecoNumero = null;
            vm.portador.complemento = null;
        }

        vm.onChangeCpfCnpj = function (dirty) {
            if (dirty && vm.portador.cpfCnpj != null && vm.portador.cpfCnpj != vm.cpfCnpjInformado) {
                var fieldCpfCnpj = formPortadorPainelCiot['CPFCNPJ_PORTADOR'];

                angular.element(fieldCpfCnpj).addClass('loading-field');

                BaseService.get('Portador', 'VerificaPortadorCadastrado', {
                    cpfCnpj: vm.portador.cpfCnpj
                }).then(function (response) {

                    angular.element(fieldCpfCnpj).removeClass('loading-field');

                    if (response.sucesso && !response.portadorPertenceEmpresaLogada) {
                        return Sistema.Msg.confirm('Portador já cadastrado para outra empresa, deseja carregar as informações mesmo assim?', function () {
                            if (response.data != null) {
                                vm.desabilitaCampoCpfCnpj = true;
                                vm.portador = response.data;
                                carregarCidades(vm.portador.estadoId);
                            }
                        });
                    } else {
                        vm.desabilitaCampoCpfCnpj = false;
                        vm.portador.cpfCnpj = null;
                        return toastr.error(response.mensagem);
                    }
                });
                vm.cpfCnpjInformado = vm.portador.cpfCnpj;
                vm.tipoPessoaInformado = vm.portador.tipoPessoa;
            }
        };
    }
})();
