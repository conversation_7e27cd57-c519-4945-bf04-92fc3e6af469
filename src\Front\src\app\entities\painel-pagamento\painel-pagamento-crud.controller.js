(function () {
    'use strict';

    angular.module('bbcWeb').controller('PainelPagamentoCrudController', PainelPagamentoCrudController);

    PainelPagamentoCrudController.$inject = ['toastr', '$rootScope', 'BaseService', '$state', '$stateParams', '$window', 'PERFIL_ADMINISTRADOR', '$scope', '$timeout', 'PersistentDataService', 'DefaultsService', '$uibModal'];

    function PainelPagamentoCrudController(
        toastr, $rootScope,
        BaseService, $state,
        $stateParams, $window,
        PERFIL_ADMINISTRADOR,
        $scope, $timeout,
        PersistentDataService,
        DefaultsService,
        $uibModal
    ) {
        var vm = this;
        vm.painelPagamento = {};
        vm.portadores = [];
        vm.ciotsDisabled = true;
        vm.saving = false;
        vm.painelPagamento.formaPagamento = 1;

        vm.optionsDatePicker = {
            minDate: new Date()
        };

        vm.comboFormaPag = {
            data: [{ id: 0, descricao: 'Depósito' }, { id: 1, descricao: 'Cartão' }, { id: 2, descricao: 'Cheque' }, { id: 3, descricao: 'Outros' }]
        };

        vm.comboTipo = {
            data: [{ id: 0, descricao: 'Adiantamento' }, { id: 1, descricao: 'Saldo' }, { id: 2, descricao: 'Complemento' }, { id: 3, descricao: 'Avulso' }, { id: 4, descricao: 'Tarifa ANTT' }]
        };

        vm.headerItems = [{
            name: 'Movimentações'
        }, {
            name: 'Painel de pagamentos',
            link: 'painel-pagamento.index'
        }, {
            name: $stateParams.link === 'novo' ? 'Novo' : 'Editar'
        }];

        vm.load = function () {
        }

        function setDataFim(data) {
            //var isoDate = new Date('September 27, 2019 03:24:00');
            if (angular.isDefinedNotNull(data)) {
                vm.painelPagamento.DataPrevisaoPagamento = new Date(data);
            }
        }

        vm.loadEdit = function (id) {
            BaseService.get('PainelPagamento', 'BuscarPorId', {
                idPainelPagamento: id
            }).then(function (response) {
                if (!response.success) {
                    toastr.error(response.message);
                    return;
                }

                vm.painelPagamento = response.data;
                setDataFim(vm.painelPagamento.dataPrevisaoPagamento);
                vm.consultaEmpresa.selectedText = vm.painelPagamento.nomeEmpresa;
                vm.consultaEmpresa.selectedValue = vm.painelPagamento.empresaId;
                vm.consultaBeneficiario.selectedText = vm.painelPagamento.nomePortador;
                vm.consultaBeneficiario.selectedValue = vm.painelPagamento.portadorId;
                vm.consultaBanco.selectedValue = vm.painelPagamento.bancoId;
                vm.consultaBanco.selectedText = vm.painelPagamento.descricaoBanco;
            });
        };

        vm.isNew = function () {
            return $stateParams.link == 'novo';
        };

        vm.consultaEmpresa = {
            columnDefs: [{
                name: 'Código',
                field: 'id',
                width: 80,
                type: 'number',
                primaryKey: true
            }, {
                name: 'Nome Fantasia',
                field: 'nomeFantasia',
                width: '*',
                minWidth: 150
            }, {
                name: 'Razão Social',
                field: 'razaoSocial',
                width: '*',
                minWidth: 150
            }, {
                name: 'Email',
                field: 'email',
                width: '*',
                minWidth: 150
            }],
            desiredValue: 'id',
            desiredText: 'nomeFantasia',
            url: 'Empresa/ConsultarGridEmpresaCombo',
            paramsMethod: function () {
                return {}
            },
            executeAfterSelection: function () {
                vm.painelPagamento.contaOrigem = null;
                vm.painelPagamento.contasOrigem = [];
            }
        };

        vm.consultaBeneficiario = {
            columnDefs: [{
                name: 'Código',
                field: 'id',
                width: 80,
                type: 'number',
                primaryKey: true
            }, {
                name: 'Nome',
                field: 'nome',
                width: '*',
                minWidth: 150
            }, {
                name: 'CPF/CNPJ',
                field: 'cpfCnpj',
                enableGrouping: true,
                width: 145
            }],
            desiredValue: 'id',
            desiredText: 'nome',
            url: 'Portador/ConsultarGridPortadorCombo',
            paramsMethod: function () {
                return {
                }
            },
            executeAfterSelection: function () {
                vm.CIOTS = null;
                vm.ciotsDisabled = true;
                vm.painelPagamento.ciotId = null;
                vm.painelPagamento.contaDestino = null;
                vm.painelPagamento.contasDestino = [];
                vm.painelPagamento.portadorId = vm.consultaBeneficiario.selectedValue;
                carregarDeclaracaoCiot(vm.consultaBeneficiario.selectedValue);
            },
            clearFunction: function () {
                vm.CIOTS = null;
                vm.ciotsDisabled = true;
                vm.painelPagamento.ciotId = null;
                vm.painelPagamento.contaDestino = null;
            }
        };

        vm.beneficiarioLimpaCampos = function () {
            vm.CIOTS = null;
            vm.ciotsDisabled = true;
            vm.painelPagamento.ciotId = null;
            vm.painelPagamento.contaDestino = null;
            vm.painelPagamento.contasDestino = [];
        }

        vm.empresaLimpaCampos = function () {
            vm.painelPagamento.contaOrigem = null;
            vm.painelPagamento.contasOrigem = [];
        }

        function ConsultarContasOrigem(id, callback) {
            BaseService.get('Conta', 'ConsultarContasPorIdEmpresa', {
                idEmpresa: vm.consultaEmpresa.selectedValue
            }).then(function (response) {
                if (!response && vm.painelPagamento.formaPagamento === 1) {
                    toastr.error("Empresa não possui conta origem.");
                    return [];
                }
                if (response) {
                    vm.painelPagamento.contasOrigem = response;
                    vm.painelPagamento.contaOrigem = response[0].id;
                }
            });

            return { autoBind: true, url: "Conta/ConsultarExtrato", }
        }

        function ConsultarContasDestino(id, callback) {
            BaseService.get('Conta', 'ConsultarContasPorIdPortador', {
                idPortador: vm.consultaBeneficiario.selectedValue
            }).then(function (response) {
                if (!response.success && vm.painelPagamento.formaPagamento === 1) {
                    toastr.error(response.message);
                    return [];
                }

                if (response.success) {
                    vm.painelPagamento.contasDestino = response.data;
                    vm.painelPagamento.contaDestino = response.data[0].id;
                }
            });

            return { autoBind: true, url: "Conta/ConsultarExtrato" }
        }

        $scope.$watch('vm.consultaBeneficiario.selectedValue', function (_new) {
            if (_new > 0) {
                ConsultarContasDestino();
                carregarDeclaracaoCiot();
            }
        });

        $scope.$watch('vm.consultaEmpresa.selectedValue', function (_new) {
            if (_new > 0) {
                ConsultarContasOrigem();
            }
        });

        vm.ConsultaContaOrigem = function () {
            ConsultarContasOrigem();
        }

        vm.ConsultaContaDestino = function () {
            ConsultarContasDestino();
        }

        vm.consultaBanco = {
            columnDefs: [{
                name: 'Código',
                width: 80,
                primaryKey: true,
                field: 'id',
                serverField: 'Id',
                enableFiltering: true
            }, {
                name: 'Nome',
                width: '*',
                minWidth: 150,
                field: 'nome',
                serverField: 'Nome',
                enableFiltering: true
            }],
            desiredValue: 'id',
            desiredText: 'nome',
            url: 'Banco/ConsultarGridBancoCombo',
            paramsMethod: function () {
                return {

                }
            }
        };

        vm.FormaPagamento = function (data) {
            if (data != 0) {
                vm.painelPagamento.agencia = null;
                vm.painelPagamento.conta = null;
                vm.painelPagamento.verificadorConta = null;
                vm.consultaBanco.selectedValue = undefined;
                vm.consultaBanco.selectedText = undefined;
                vm.painelPagamento.tipoConta = null;
            }
        }

        function carregarDeclaracaoCiot(portadorId) {
            if (portadorId == null) { portadorId = vm.consultaBeneficiario.selectedValue; }
            BaseService.get("PainelCiot", "ConsultarDeclaracoesCiot", {
                idPortador: portadorId
            }).then(function (response) {
                if (response.success) {
                    vm.CIOTS = response.data;
                    vm.ciotsDisabled = false;
                }
            });
        }
        vm.portadorChange = function (portadorId) {
            vm.CIOTS = null;
            vm.ciotsDisabled = true;
            vm.painelPagamento.ciotId = null;
            carregarDeclaracaoCiot(portadorId);
        }

        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.administrador == true;
        };

        function init() {
            if (vm.consultaEmpresa.selectedValue != undefined && vm.consultaEmpresa.selectedValue != 0 && vm.consultaEmpresa.selectedValue != null) {
                ConsultarContasOrigem();
            }

            if (vm.consultaBeneficiario.selectedValue != undefined && vm.consultaBeneficiario.selectedValue != 0 && vm.consultaBeneficiario.selectedValue != null) {
                ConsultarContasDestino();
                carregarDeclaracaoCiot();
            }

            if (vm.isNew()) {
                vm.painelPagamento.id = 'Auto';
                if (!vm.isAdmin()) {
                    BaseService.get("Usuario", "EmpresaUsuarioLogado", { usuarioId: $rootScope.usuarioLogado.idUsuario }).then(function (response) {
                        if (response.success) {
                            vm.consultaEmpresa.selectedValue = response.data.id;
                            vm.consultaEmpresa.selectedText = response.data.nome;
                        } else {
                            toastr.error("O usuário logado não está vinculado a nenhuma empresa.")
                        }
                    });
                }
            }
        }

        vm.onClickVoltar = function (wizard) {

            var ativoIndex = wizard.getActivePosition();
            if (ativoIndex == 1)
                $state.go('painel-pagamento.index');

            wizard.go(ativoIndex - 1);
        };

        var selfScope = PersistentDataService.get('PainelPagamentoCrudController');

        if ($stateParams.link == 'novo')
            vm.painelPagamento.id = 'Auto';

        if (angular.isDefined(selfScope))
            angular.extend(vm, selfScope.data);
        else
            if (!vm.isNew())
                vm.loadEdit($stateParams.link);
            else
                vm.load();

        init();
        // DO NOT TOUCH!! 666
        $scope.$on('$stateChangeStart', function (arg1, toState) {
            if (toState.name === 'painel-pagamento.index')
                PersistentDataService.remove('PainelPagamentoCrudController');
            else
                PersistentDataService.store('PainelPagamentoCrudController', vm, "Movimentação - Painel de pagamento", null, "painel-pagamento.painel-pagamento-crud", vm.painelPagamento.id);
        });

        $timeout(function () {
            PersistentDataService.remove('PainelPagamentoController');
        }, 15);
    }
})();
