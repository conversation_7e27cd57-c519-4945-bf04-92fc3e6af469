(function () {
    'use strict';

    angular.module('bbcWeb.client-secret-adm.state', []).config(routeConfig);

    function routeConfig($urlRouterProvider, $stateProvider) {
        $stateProvider.state('client-secret-adm', {
            abstract: true,
            url: "/client-secret-adm",
            templateUrl: "app/layout/content.html"
        }).state('client-secret-adm.index', {
            url: '/index',
            templateUrl: 'app/entities/client-secret-adm/client-secret-adm.html'
        }).state('client-secret-adm.client-secret-adm-crud', {
            url: '/:link',
            templateUrl: 'app/entities/client-secret-adm/client-secret-adm-crud.html'
        });
    }
})();
