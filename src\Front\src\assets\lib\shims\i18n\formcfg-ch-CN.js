webshims.validityMessages["zh-CN"]={typeMismatch:{email:"\u8bf7\u8f93\u5165\u7535\u5b50\u90ae\u4ef6\u5730\u5740\u3002",url:"\u8bf7\u8f93\u5165\u4e00\u4e2a URL\u3002"},badInput:{number:"\u503c\u65e0\u6548\u3002",date:"\u503c\u65e0\u6548\u3002",time:"\u503c\u65e0\u6548\u3002",range:"\u503c\u65e0\u6548\u3002","datetime-local":"\u503c\u65e0\u6548\u3002"},tooLong:"\u503c\u65e0\u6548\u3002",patternMismatch:"\u8bf7\u5339\u914d\u8981\u6c42\u7684\u683c\u5f0f\uff1a {%title}\u3002",valueMissing:{defaultMessage:"\u8bf7\u586b\u5199\u6b64\u5b57\u6bb5\u3002",checkbox:"\u82e5\u8981\u7ee7\u7eed\uff0c\u8bf7\u68c0\u9009\u6b64\u68c0\u67e5\u6846\u3002",select:"\u8bf7\u9009\u62e9\u5217\u8868\u4e2d\u7684\u4e00\u9879\u3002",radio:"\u8bf7\u9009\u62e9\u4e00\u4e2a\u9009\u9879\u3002"},rangeUnderflow:{defaultMessage:"\u503c\u65e0\u6548\u3002",date:"\u503c\u65e0\u6548\u3002",time:"\u503c\u65e0\u6548\u3002","datetime-local":"\u503c\u65e0\u6548\u3002"},rangeOverflow:{defaultMessage:"\u503c\u65e0\u6548\u3002",date:"\u503c\u65e0\u6548\u3002",time:"\u503c\u65e0\u6548\u3002","datetime-local":"\u503c\u65e0\u6548\u3002"},stepMismatch:"\u503c\u65e0\u6548\u3002"},webshims.formcfg["zh-CN"]={numberFormat:{".":".",",":","},numberSigns:".",dateSigns:"-",timeSigns:":. ",dFormat:"-",patterns:{d:"yy-mm-dd"},date:{closeText:"\u5173\u95ed",prevText:"&#x3C;\u4e0a\u6708",nextText:"\u4e0b\u6708&#x3E;",currentText:"\u4eca\u5929",monthNames:["\u4e00\u6708","\u4e8c\u6708","\u4e09\u6708","\u56db\u6708","\u4e94\u6708","\u516d\u6708","\u4e03\u6708","\u516b\u6708","\u4e5d\u6708","\u5341\u6708","\u5341\u4e00\u6708","\u5341\u4e8c\u6708"],monthNamesShort:["\u4e00\u6708","\u4e8c\u6708","\u4e09\u6708","\u56db\u6708","\u4e94\u6708","\u516d\u6708","\u4e03\u6708","\u516b\u6708","\u4e5d\u6708","\u5341\u6708","\u5341\u4e00\u6708","\u5341\u4e8c\u6708"],dayNames:["\u661f\u671f\u65e5","\u661f\u671f\u4e00","\u661f\u671f\u4e8c","\u661f\u671f\u4e09","\u661f\u671f\u56db","\u661f\u671f\u4e94","\u661f\u671f\u516d"],dayNamesShort:["\u5468\u65e5","\u5468\u4e00","\u5468\u4e8c","\u5468\u4e09","\u5468\u56db","\u5468\u4e94","\u5468\u516d"],dayNamesMin:["\u65e5","\u4e00","\u4e8c","\u4e09","\u56db","\u4e94","\u516d"],weekHeader:"\u5468",firstDay:1,isRTL:!1,showMonthAfterYear:!0,yearSuffix:"\u5e74"}};