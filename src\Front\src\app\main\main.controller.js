(function () {
    'use strict';

    angular.module('bbcWeb')
        .controller('MainController', MainController);

    MainController.$inject = ['$scope', '$uibModal', 'BaseService', '$timeout', '$rootScope', 'SignalRService', 'TITULO_SISTEMA'];

    function MainController($scope, $uibModal, BaseService, $timeout, $rootScope, SignalRService, TITULO_SISTEMA) {
        var vm = this;
        vm.showData = false;
        //Timeout para carregar a imagem de logo antes da animação
        $timeout(function () {
            vm.showData = true;
        }, 1000);

        vm.userName = 'Administrador';
        vm.helloText = TITULO_SISTEMA +' Web';
        vm.descriptionText = 'Sistema Info';
        $scope.TITULO_SISTEMA = TITULO_SISTEMA;
        vm.isOnAtsDomain = $rootScope.isOnAtsDomain;

        vm.redefinirSenha = function () {
            $uibModal.open({
                animation: true,
                templateUrl: 'app/account/redefinicao/redefinicao-dialog.html',
                controller: 'RedefinicaoDialogController',
                controllerAs: 'redefinicaoDialog'
            });
        };

        vm.lastPingResponding = true;
        $rootScope.empresaCadastrSe = false;
    }
})();