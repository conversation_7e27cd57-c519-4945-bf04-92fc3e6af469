(function () {
    'use strict';

    angular.module('bbcWeb.fabricante.state', []).config(routeConfig);

    function routeConfig($urlRouterProvider, $stateProvider) {
        $stateProvider.state('fabricante', {
            abstract: true,
            url: "/fabricante",
            templateUrl: "app/layout/content.html"
        }).state('fabricante.index', {
            url: '/index',
            templateUrl: 'app/entities/fabricante/fabricante.html'
        }).state('fabricante.fabricante-crud', {
            url: '/:link',
            templateUrl: 'app/entities/fabricante/fabricante-crud.html'
        });
    }
})();