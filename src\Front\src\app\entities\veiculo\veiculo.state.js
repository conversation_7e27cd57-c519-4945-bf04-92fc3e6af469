(function () {
    'use strict';

    angular.module('bbcWeb.veiculo.state', []).config(routeConfig);

    function routeConfig($urlRouterProvider, $stateProvider) {
        $stateProvider.state('veiculo', {
            abstract: true,
            url: "/veiculo",
            templateUrl: "app/layout/content.html"
        })
        .state('veiculo.index', {
            url: '/index',
            templateUrl: 'app/entities/veiculo/consulta/veiculo.html'
        })
        .state('veiculo.crud', {
            url: '/:link',
            templateUrl: 'app/entities/veiculo/cadastro/veiculo-crud.html'
        });
    }
})();