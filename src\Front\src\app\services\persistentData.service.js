(function () {
    'use strict';

    angular.module('bbcWeb').factory('PersistentDataService', PersistentDataService);

    PersistentDataService.$inject = ['$rootScope', '$state', '$timeout', '$log', '$window'];

    function PersistentDataService($rootScope, $state, $timeout, $log, $window) {
        var enableOutputLogs = false;

        $rootScope.minimizarAreaWndManager = function () {
            $rootScope.wndManagerMinimized = true;
            $timeout(function () {
                angular.element("#totalJanelasAtivas").animate({
                bottom: ((angular.element("#totalJanelasAtivas").height() - 32) * -1) + "px"
                }, 170);
            }, 50);
        };

        $rootScope.maximizarAreaWndManager = function () {
            $rootScope.wndManagerMinimized = false;
            $timeout(function () {
                angular.element("#totalJanelasAtivas").animate({
                    bottom: ""
                }, 170);
            }, 50);
        };

        $rootScope.navegarParaLink = function (valObj) {
            var link = angular.isNumber(valObj.recordEditId) ? valObj.recordEditId : "novo";

            if (enableOutputLogs) $log.log(valObj.$url + " - " + link);

            $state.go(valObj.$url, {
                link: link
            });
        };

        $rootScope.totalJanelasAtivas = function () {
            return Object.keys($rootScope.mem).length;
        };
        // Remove qualquer dependência circular do objeto a ser guardado em memória
        var replacer = function (key, value) {
            var cache = [];
            if (angular.isObject(value) && value !== null) {
                if (cache.indexOf(value) !== -1) {
                    return '[Circular]';
                }
                cache.push(value);
            }

            cache = null;
            return value;
        };

        $rootScope.removeScopeMem = function (key) {
            if ($rootScope.mem[key])
                delete $rootScope.mem[key];
        };


        return {
            store: function (key, value, name, childCrudKey, uiRouteUrl, recordEditId) {
                if (angular.isDefined($rootScope.mem[key]))
                    delete $rootScope.mem[key];

                $rootScope.mem[key] = {};
                $rootScope.mem[key].data = {};

                // Guarda tudo que não for function
                var novoObj = value;
                for (var propertyName in novoObj) {
                    if (angular.isFunction(novoObj[propertyName]))
                        delete novoObj[propertyName];
                }

                angular.extend($rootScope.mem[key].data, novoObj);

                if (name) $rootScope.mem[key].$name = name;
                if (uiRouteUrl) $rootScope.mem[key].$url = uiRouteUrl;
                if (childCrudKey) $rootScope.mem[key].$childKey = childCrudKey;
                if (recordEditId) $rootScope.mem[key].recordEditId = recordEditId;
                $timeout(function () {
                    $rootScope.minimizarAreaWndManager();
                }, 250);
            },
            get: function (key) {
                return $rootScope.mem[key];
            },
            remove: function (key) {
                if ($rootScope.mem[key])
                    delete $rootScope.mem[key];
            },
            dispose: function () {
                $rootScope.mem = {};
            },
            applyScope: function (scope, scopeName) {
                var selfScope = $rootScope.mem[scopeName];
                if (angular.isDefined(selfScope)) {
                    $window.forEachObjProperty(selfScope.data, function (propName, propObj) {
                        if (propName !== 'gridApi' && propName !== 'gridOptions')
                            scope[propName] = propObj;
                    });
                }
            }
        };
    }
})();