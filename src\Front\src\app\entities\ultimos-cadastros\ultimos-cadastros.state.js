(function () {
    'use strict';

    angular.module('bbcWeb.ultimos-cadastros.state', []).config(routeConfig);

    function routeConfig($urlRouterProvider, $stateProvider) {
        $stateProvider
            .state('ultimos-cadastros', {
                url: "/ultimos-cadastros",
                abstract: true,  
                templateUrl: "app/layout/content.html"
            })
            .state('ultimos-cadastros.index', {
                url: "/index",
                templateUrl: "app/entities/ultimos-cadastros/ultimos-cadastros.html"
            }).state('ultimos-cadastros.crud', {
                url: '/:link',  
                templateUrl: "app/entities/ultimos-cadastros/ultimos-cadastros-detalhes.html"
            });
    }
})();