<div>
    <form name="formPainelCiot" novalidate ng-submit="vm.salvarCliente(formPainelCiot);" novalidate ats-validator>
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" ng-click="$dismiss()" aria-label="Close">
                    <em class="fa fa-times"></em>
                </button>
                <h4 class="modal-title">Novo Cliente</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                        <div class="form-group">
                            <label class="control-label-modal col-xs-3 col-sm-2 col-md-4 col-lg-3"><span
                                    class="text-danger mr-5">*</span>Nome Fantasia:</label>
                            <div class="input-group col-xs-9 col-sm-10 col-md-8 col-lg-9">
                                <input type="text" maxlength="200" name="Nome Fantasia"
                                    ng-model="vm.cliente.nomeFantasia" class="form-control" validate-on="blur"
                                    required-message="'Nome é obrigatório'" required />
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                        <div class="form-group">
                            <label class="control-label-modal col-xs-3 col-sm-2 col-md-4 col-lg-3"><span
                                    class="text-danger mr-5">*</span>Razão Social:</label>
                            <div class="input-group col-xs-9 col-sm-10 col-md-8 col-lg-9">
                                <input type="text" maxlength="200" name="Razão Social" ng-model="vm.cliente.razaoSocial"
                                    class="form-control" validate-on="blur"
                                    required-message="'Razão Social é obrigatório'" required />
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                        <div class="form-group">
                            <label class="control-label col-xs-3 col-sm-2 col-md-4 col-lg-3">
                                <span class="text-danger mr-5">*</span>CPF/CNPJ:
                            </label>
                            <div class="input-group col-xs-9 col-sm-10 col-md-8 col-lg-9">
                                <input type="text" name="CPF/CNPJ" class="form-control" ui-br-cpfcnpj-mask
                                    name="CPF/CNPJ" maxlength="18" validate-on="blur" ng-model="vm.cliente.cpfCnpj"
                                    required-message="'CPF/CNPJ é obrigatório'" required />
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                        <div class="form-group">
                            <label class="control-label col-xs-3 col-sm-2 col-md-4 col-lg-3">
                                <span class="text-danger mr-5">*</span>E-mail:
                            </label>
                            <div class="input-group col-xs-9 col-sm-10 col-md-8 col-lg-9">
                                <input type="email" name="Email" maxlength="200" class="form-control" validate-on="blur"
                                    ng-model="vm.cliente.email" required-message="'E-mail é obrigatório'" required />
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                        <div class="form-group">
                            <label class="control-label col-xs-3 col-sm-2 col-md-4 col-lg-3">
                                <span class="text-danger mr-5">*</span>Celular:
                            </label>
                            <div class="input-group col-xs-9 col-sm-10 col-md-8 col-lg-9">
                                <input type="text" name="Celular" class="form-control" ng-model="vm.cliente.celular"
                                    ui-br-phone-number validate-on="blur" required-message="'Celular é obrigatório'"
                                    required />
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                        <div class="form-group">
                            <label class="control-label col-xs-3 col-sm-2 col-md-4 col-lg-3">
                                <span class="text-danger mr-5">*</span>Telefone:
                            </label>
                            <div class="input-group col-xs-9 col-sm-10 col-md-8 col-lg-9">
                                <input type="text" name="Telefone" class="form-control" ng-model="vm.cliente.telefone"
                                    ui-br-phone-number validate-on="blur" required-message="'Telefone é obrigatório'"
                                    required />
                            </div>
                        </div>
                    </div>
                </div>
                <hr style="border: 0; 
                    height: 1px; 
                    background-image: -webkit-linear-gradient(left, #f0f0f0, #8c8b8b, #f0f0f0);
                    background-image: -moz-linear-gradient(left, #f0f0f0, #8c8b8b, #f0f0f0);
                    background-image: -ms-linear-gradient(left, #f0f0f0, #8c8b8b, #f0f0f0);
                    background-image: -o-linear-gradient(left, #f0f0f0, #8c8b8b, #f0f0f0); ">
                <div class="row">
                    <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                        <div class="form-group">
                            <label class="control-label col-xs-3 col-sm-2 col-md-4 col-lg-3">
                                <span class="text-danger mr-5">*</span>CEP:
                            </label>
                            <div class="input-group col-xs-9 col-sm-10 col-md-8 col-lg-9">
                                <input type="text" name="CEP" ui-br-cep-mask class="form-control" validate-on="blur"
                                    ng-model="vm.cliente.cep" ng-blur="vm.buscarEndereco(vm.cliente.cep)"
                                    required-message="'CEP é obrigatório'" required />
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                        <div class="form-group">
                            <label class="control-label col-xs-3 col-sm-2 col-md-4 col-lg-3">
                                <span class="text-danger mr-5">*</span>Estado:
                            </label>
                            <div class="input-group col-xs-9 col-sm-10 col-md-8 col-lg-9">
                                <ui-select name="Estado" ats-ui-select-validator validate-on="blur"
                                    ng-model="vm.cliente.estadoId" ng-change="vm.estadoChange(vm.cliente.estadoId)"
                                    required-message="'Estado é obrigatório'" required append-to-body="false">
                                    <ui-select-match>
                                        <span>{{$select.selected.descricao}}</span>
                                    </ui-select-match>
                                    <ui-select-choices
                                        repeat="ex.id as ex in vm.estados | propsFilter: {descricao: $select.search}">
                                        <div ng-bind-html="ex.descricao | highlight: $select.search"></div>
                                    </ui-select-choices>
                                </ui-select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                        <div class="form-group">
                            <label class="control-label col-xs-3 col-sm-2 col-md-4 col-lg-3">
                                <span class="text-danger mr-5">*</span>Cidade:
                            </label>
                            <div class="input-group col-xs-9 col-sm-10 col-md-8 col-lg-9">
                                <ui-select name="Cidade" ats-ui-select-validator validate-on="blur"
                                    ng-model="vm.cliente.cidadeId" ng-disabled="vm.cidadesDisabled"
                                    required-message="'Cidade é obrigatória'" required append-to-body="false">
                                    <ui-select-match>
                                        <span>{{$select.selected.descricao}}</span>
                                    </ui-select-match>
                                    <ui-select-choices
                                        repeat="ex.id as ex in vm.cidades | propsFilter: {descricao: $select.search}">
                                        <div ng-bind-html="ex.descricao | highlight: $select.search"></div>
                                    </ui-select-choices>
                                </ui-select>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                        <div class="form-group">
                            <label class="control-label col-xs-3 col-sm-2 col-md-4 col-lg-3">
                                <span class="text-danger mr-5">*</span>Endereço:
                            </label>
                            <div class="input-group col-xs-9 col-sm-10 col-md-8 col-lg-9">
                                <input type="text" name="Endereço" maxlength="200" class="form-control"
                                    validate-on="blur" ng-model="vm.cliente.endereco"
                                    required-message="'Endereço é obrigatório'" required />
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                        <div class="form-group">
                            <label class="control-label col-xs-3 col-sm-2 col-md-4 col-lg-3">
                                <span class="text-danger mr-5">*</span>Bairro:
                            </label>
                            <div class="input-group col-xs-9 col-sm-10 col-md-8 col-lg-9">
                                <input type="text" name="Bairro" maxlength="100" class="form-control" validate-on="blur"
                                    ng-model="vm.cliente.bairro" required-message="'Bairro é obrigatório'" required />
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                        <div class="form-group">
                            <label class="control-label col-xs-3 col-sm-2 col-md-4 col-lg-3">Número:</label>
                            <div class="input-group col-xs-9 col-sm-10 col-md-8 col-lg-9">
                                <input type="text" maxlength="10" ats-numeric ng-model="vm.cliente.enderecoNumero"
                                    name="Número" class="form-control" />
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
                        <div class="form-group">
                            <label>Complemento:</label>
                            <div>
                                <textarea type="text" maxlength="100" ng-model="vm.cliente.complemento"
                                    name="Complemento" class="form-control">
                                </textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <div class="text-right ">
                    <button type="submit" class="btn btn-labeled btn-success text-right">
                        Salvar
                    </button>
                </div>
            </div>
        </div>
    </form>
</div>