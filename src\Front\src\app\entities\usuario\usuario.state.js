(function () {
    'use strict';

    angular.module('bbcWeb.usuario.state', []).config(routeConfig);

    function routeConfig($stateProvider) {
        $stateProvider
            .state('usuario', {
                url: "/usuario",
                abstract: true,
                templateUrl: "app/layout/content.html"
            })
            .state('usuario.index', {
                url: '/index',
                templateUrl: 'app/entities/usuario/usuario.html'
            })
            .state('usuario.crud', {
                url: '/:link',
                templateUrl: 'app/entities/usuario/usuario-crud.html'
            });
    }
})();