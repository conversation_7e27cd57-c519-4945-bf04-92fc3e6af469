(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('GrupoEmpresaController', GrupoEmpresaController);

    GrupoEmpresaController.inject = ['BaseService', '$rootScope', 'toastr', '$scope', 'PersistentDataService', '$timeout', '$state', 'PERFIL_ADMINISTRADOR'];

    function GrupoEmpresaController(BaseService, $rootScope, toastr, $scope, PersistentDataService, $timeout, $state, PERFIL_ADMINISTRADOR) {
        var vm = this;
        vm.headerItems = [{
            name: '<PERSON><PERSON><PERSON><PERSON>'
        }, {
            name: 'Grupo de empresas'
        }];


        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.perfil == PERFIL_ADMINISTRADOR;
        };

        vm.alterarStatus = function (id, ativo) {
            BaseService.post('GrupoEmpresa', 'AtivarInativarGrupoEmpresa', id).then(function (response) {
                response.success 
                    ? toastr.success(ativo ? 'Grupo de empresa  inativado com sucesso!' : 'Grupo de empresa  reativado com sucesso!')
                    : toastr.error(response.message);
                vm.gridOptions.dataSource.refresh();
            });
        };
        
        vm.gridOptions = {
            data: [],  
            onRegisterApi: BaseService.dataGrid.defaultOnRegisterApi,
            dataSource: {
                url: "GrupoEmpresa/ConsultarGridGrupoEmpresa",
            },
            columnDefs: [{
                name: 'Ações',
                width: 80,
                cellTemplate: '<div ng-if="!row.groupHeader" class="ui-grid-cell-contents" title="TOOLTIP">\
                                    <button tooltip-placement="right" uib-tooltip="Editar" ng-disabled="!row.entity.ativo" type="button" ui-sref="grupo-empresa.grupo-empresa-crud({link: row.entity.id})"\
                                        ng-class="{ \'btn btn-xs btn-info\': true }">\
                                        <i class="fa fa-edit"></i>\
                                    </button>\
                                    \
                                    <button type="button" title="Ativar/Desativar" ng-click="grid.appScope.vm.alterarStatus(row.entity.id, row.entity.ativo)" ng-class="row.entity.ativo===1 ? \'btn btn-xs btn-success\' : \'btn btn-xs btn-danger\'">\                                            <i ng-class="row.entity.ativo===1 ? \'fa fa-check\' : \'fa fa-times\'"></i>\
                                                                                            </button>\
                                </div>'
            }, {
                name: 'Código',
                width: 80,
                type : 'number',
                primaryKey: true,
                field: 'id'
            }, {
                name: 'Razão social',
                width: '*',
                minWidth: 180,
                field: 'razaosocial'
            }, {
                name: 'Cnpj',
                width: '*',
                minWidth: 180,
                field: 'cnpj'
            }
          ]
        };
        vm.gridOptions.minimumColumnSize = 100;
       
        // Controle de aba!!
        $scope.$on('$stateChangeStart', function () {
            PersistentDataService.store('GrupoEmpresaController', vm, "Grupo de empresas", "GrupoEmpresaCrudController", "grupo-empresa.index");
        });

        var selfScope = PersistentDataService.get('GrupoEmpresaController');
        var filho = PersistentDataService.get('GrupoEmpresaCrudController');
        if (angular.isDefined(filho)) {
            $timeout(function () {
                $state.go('grupo-empresa.grupo-empresa-crud', {
                    link: filho.data.grupoEmpresa.id > 0 ? 'editar' : 'novo'
                });
            }, 50);
        } else if (angular.isDefined(selfScope)) {
            angular.extend(vm, selfScope.data);

            $timeout(function () {
                vm.gridOptions.dataSource.refresh();
            }, 450);
        }
    }
})();