(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .factory('EmprestimoCrudService', EmprestimoCrudService);

    EmprestimoCrudService.$inject = [
        'BaseService'
    ];

    function EmprestimoCrudService(BaseService) {
        const EMPRESTIMO_CONTROLLER = "Emprestimo";
        const RETENCAO_CPTALYS_CONTROLLER = "RetencaoCaptalys";
        const RETENCAO_CONTROLLER = "Retencao";

        var functions = {
            getHeader: function () {
                return [{ name: 'Empréstimo' }, { name: 'Cadastro de empréstimo' }];
            },
            getEnumStatus: function () {
                return [{ id: 0, descricao: 'Quitada' }, { id: 1, descricao: 'Aberta' }, { id: 2, descricao: 'Cobrança' }, { id: 3, descricao: 'Reestruturado' }, { id: 4, descricao: '<PERSON>r<PERSON><PERSON><PERSON>' }, { id: 5, descricao: 'Execução' }];
            },
            getClass6: function () {
                return "col-md-6";
            },
            getClass12: function () {
                return "col-md-12";
            },
            cadastrar: function (emprestimo, callback) {
                BaseService.post(EMPRESTIMO_CONTROLLER, 'Cadastrar', emprestimo).then(function (response) {
                    callback(response.success, response.data, response.message);
                });
            },
            consultarParaEdicao: function (id, callback) {
                BaseService.get(EMPRESTIMO_CONTROLLER, 'ConsultarParaEdicao', { id: id }).then(function (response) {
                    callback(response.success, response.data, response.message);
                })
            },
            sinalizaClienteEmprestimo: function (emprestimo, callback) {
                BaseService.post(EMPRESTIMO_CONTROLLER, 'SinalizaClienteEmprestimo', emprestimo).then(function (response) {
                    callback(response.success, response.message);
                });
            },
            integrarRetencao: function (request, callback) {
                BaseService.post(RETENCAO_CPTALYS_CONTROLLER, 'Integrar', request).then(function (response) {
                    callback(response.data.sucesso, response.data.mensagem);
                });
            },
            removerMascaraCpfCnpj: function (cpfCnpj) {
                if (cpfCnpj.length === 14)
                    cpfCnpj = cpfCnpj.replace(".", "").replace(".", "").replace("-", "");

                if (cpfCnpj.length === 18)
                    cpfCnpj = cpfCnpj.replace(".", "").replace(".", "").replace("/", "").replace("-", "");

                return cpfCnpj;
            },
            setarRetencaoIntegrada: function (id, callback) {
                BaseService.post(RETENCAO_CONTROLLER, 'SetarRetencaoIntegrada', id).then(function (response) {
                    callback(response.success, response.message);
                });
            }
        };

        return functions;
    }
})();