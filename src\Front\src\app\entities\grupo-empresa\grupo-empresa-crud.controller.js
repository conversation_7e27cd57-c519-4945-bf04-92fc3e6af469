(function () {
    'use strict';

    angular.module('bbcWeb').controller('GrupoEmpresaCrudController', GrupoEmpresaCrudController);

    GrupoEmpresaCrudController.$inject = ['toastr', '$rootScope', 'BaseService', '$state', '$stateParams', '$window', 'PERFIL_ADMINISTRADOR', '$scope', '$timeout', 'PersistentDataService', 'DefaultsService', '$uibModal', '$location'];

    function GrupoEmpresaCrudController(
        toastr, $rootScope,
        BaseService, $state,
        $stateParams, $window,
        PERFIL_ADMINISTRADOR,
        $scope, $timeout,
        PersistentDataService,
        DefaultsService,
        $uibModal,
        $location
    ) {
        var vm = this;
        vm.grupoEmpresa = {};
        vm.grupoEmpresa.qtdMensalSemTaxaPix = 0;
        vm.grupoEmpresa.valorTarifaPix = 0;
        vm.grupoEmpresa.percentualTarifaBbc = 0;
        vm.grupoEmpresa.cobrancaTarifa = false;
        vm.grupoEmpresa.ativo = 1;
        vm.grupoEmpresa.cobrarTarifaBbcValePedagio = false;
        vm.grupoEmpresa.percentualTarifaValePedagio = 0;
        vm.grupoEmpresa.utilizaTarifaEmpresaPagamentoPedagio = true;
        vm.grupoEmpresa.reprocessamentoPagamentoFrete = 0;

        vm.headerItems = [{
            name: 'Cadastros'
        }, {
            name: 'Grupo de empresas',
            link: 'grupo-empresa.index'
        }, {
            name: $stateParams.link == 'novo' ? 'Novo' : 'Editar'
        }];

        vm.consultaporid = false;

        var selfScope = PersistentDataService.get('GrupoEmpresaCrudController');

        if ($stateParams.link == 'novo')
            vm.grupoEmpresa.id = 'Auto';
        

        vm.isNew = function () {
            return $stateParams.link == 'novo';
        };

        //primeira consulta
        vm.loadEdit = function (id) {
            var idGrupoEmpresa = parseInt(id);
            BaseService.get('GrupoEmpresa', 'ConsultarPorId', {
                idGrupoEmpresa: idGrupoEmpresa
            }).then(function (response) {
                if (!response.success) {
                    toastr.error(response.message);
                    return;
                } else {
                    // Sucesso
                    vm.consultaporid = true;
                    vm.grupoEmpresa = response.data;
                    //consulta os modulos
                    vm.consultaporid = false;
                }
            });
        };

// Centralizar as funcoes de email para serem usadas por todo o projeto.
        vm.checkForEmailEnd = function(event) {
            if (event.key === ' ' || event.key === 'Enter') {
                vm.grupoEmpresa.emailsNotificacaoContingenciaCiot = processEmails(vm.grupoEmpresa.emailsNotificacaoContingenciaCiot);
            }
        };
    
        function processEmails(emailString) {
            if (!emailString) return '';
    
            // Remove espaços e quebras de linha no início e no fim da string
            emailString = emailString.trim();
    
            // Divide a string em partes utilizando espaços, quebras de linha ou ponto e vírgula como delimitadores
            var emails = emailString.split(/[\s;\n]+/).filter(Boolean);
    
            // Valida e formata os emails
            var formattedEmails = emails.map(function(email) {
                if (isValidEmail(email)) {
                    return email.trim() + ';';
                } else {
                    return email.trim();
                }
            });
    
            // Junta os emails formatados em uma única string
            return formattedEmails.join(' ');
        }
    
        function isValidEmail(email) {
            // Expressão regular para validar email
            var emailPattern = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
            return emailPattern.test(email);
        }


        $scope.$watch('vm.imagemUpload', function (nVal) {
            if (nVal) {
                if (nVal.filesize > 524288) { // 524288 = 512kb 
                    vm.usuario.foto = null;
                    vm.imagemUpload = null;
                    vm.imagemSelecionadaInput = null;
                    toastr.error('A imagem escolhida excede o tamanho de 512Kb!');
                } else {
                    vm.grupoEmpresa.imagemCartao = nVal.base64;
                }
            }
        });
        
        vm.save = function (form) {
            if (!form.$valid) {
                toastr.error('Todos os campos obrigatórios devem ser preenchidos corretamente com seus respectivos valores');
                return;
            }

            if (vm.isSaving === true)
                return;
         
            vm.isSaving = true;

            var saveGrupoEmpresa = vm.grupoEmpresa;

            saveGrupoEmpresa.qtdMensalSemTaxaPix = parseInt(saveGrupoEmpresa.qtdMensalSemTaxaPix);
            
            if(saveGrupoEmpresa.id == "Auto"){
                saveGrupoEmpresa.id = 0;
            }

            if(saveGrupoEmpresa.cobrancaTarifa == true){
                saveGrupoEmpresa.cobrancaTarifa = 1;
            }else{
                saveGrupoEmpresa.cobrancaTarifa = 0;
            }
       
            BaseService.post('GrupoEmpresa', 'SaveGrupoEmpresa', saveGrupoEmpresa).then(function (response) {
                vm.isSaving = false;
                if (response.success) {
                    toastr.success(response.message);
                    $state.go('grupo-empresa.index');
                } else
                    toastr.error(response.message);
            });
        };

        vm.goToEmpresa = function() {
            $state.go('empresa.index');
        }

        vm.gridOptionsEmpresas = {
            data: [],
            onRegisterApi: BaseService.dataGrid.getGridApiRegistration("gridOptionsEmpresas"),
            dataSource: {
                url: 'Empresa/ConsultarGridPorGrupoEmpresa',
                params: function () {
                    return {
                        grupoEmpresaId: vm.grupoEmpresa.id === 'Auto' ? null : vm.grupoEmpresa.id
                    };
                }
            },
            width: vm.viewportWidth,
            paginationPageSizes: [10, 25, 50],
            columnDefs: [
                { name: 'Código', width: 80, primaryKey: true, type: 'number', field: 'id' },
                { displayName: 'CNPJ', field: 'cnpj', width: 145 },
                { displayName: 'Razão social', field: 'razaoSocial', width: '*', minWidth: 150 },
                { name: 'Telefone', field: 'telefone', width: 120 }
            ]
        };

        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.administrador == true;
        };

        function init() {

            if (vm.isNew()) {
                vm.grupoEmpresa.id = 'Auto';
            
            }
        }
        
        vm.onClickAvancar = function (wizard) {
            wizard.go(wizard.getActivePosition() + 1);
        };

        vm.onClickVoltar = function (wizard) {
            
            var ativoIndex = wizard.getActivePosition();
            if (ativoIndex == 1)
                $state.go('grupo-empresa.index');

            wizard.go(ativoIndex - 1);
        };

        init();
        // DO NOT TOUCH!! 666
        $scope.$on('$stateChangeStart', function (arg1, toState) {
            if (toState.name === 'grupo-empresa.index')
                PersistentDataService.remove('GrupoEmpresaCrudController');
            else
                PersistentDataService.store('GrupoEmpresaCrudController', vm, "Cadastro - Grupos de usuário", null, "grupo-empresa.grupo-empresa-crud", vm.grupoEmpresa.id);
        });

        if (angular.isDefined(selfScope))
            angular.extend(vm, selfScope.data);
        else
        if (!vm.isNew())
            vm.loadEdit($stateParams.link);
        else{
            vm.grupoEmpresa.id = "Auto";
        }
         
        $timeout(function () {
            PersistentDataService.remove('GrupoEmpresaController');
        }, 15);
    }
})();