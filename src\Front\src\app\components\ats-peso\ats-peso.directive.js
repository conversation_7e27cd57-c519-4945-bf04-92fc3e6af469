(function () {
    'use strict';

    angular.module('bbcWeb').directive('atsPeso', function ($filter) {
        return {
            bindToController: true,
            require: 'ngModel',
            controller: function () {

            },
            controllerAs: 'vm',
            link: function (scope, element, attrs, ctrl) {
                ctrl.$parsers.unshift(function (value) {
                    element[0].value = element[0].value.replace('.', '');
                    return element[0].value;
                });

                ctrl.$formatters.unshift(function (value){
                    if (ctrl.$modelValue >= 0)
                        element[0].value = $filter('number')(ctrl.$modelValue);
                        
                    return element[0].value;
                });
            },
            restrict: 'AE',
            scope: {}
        };
    });
})();