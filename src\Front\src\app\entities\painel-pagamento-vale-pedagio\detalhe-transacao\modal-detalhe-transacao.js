(function() {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('ModalDetalhesTransacaoController', ModalDetalhesTransacaoController);

        ModalDetalhesTransacaoController.$inject = ['$uibModalInstance', 'toastr', 'BaseService', '$timeout', 'idTransacao', 'jsonEnvioDock', 'jsonRespostaDock', 'responseCodeDock', 'dataCadastroReq', 'dataRetornoDock'];

    function ModalDetalhesTransacaoController($uibModalInstance,toastr, BaseService, $timeout, idTransacao, jsonEnvioDock, jsonRespostaDock, responseCodeDock, dataCadastroReq, dataRetornoDock) {
        var vm = this;
        vm.modal = {};

        vm.idTransacao = idTransacao;
        vm.jsonEnvioDock = jsonEnvioDock;
        vm.jsonRespostaDock = jsonRespostaDock; 
        vm.responseCodeDock = responseCodeDock; 
        vm.dataHoraRequisicao = dataCadastroReq;
        vm.dataHoraResposta = dataRetornoDock;


        vm.copiar = function (campo) {
            unsecuredCopyToClipboard(campo == 1 ? vm.jsonEnvioDock : vm.jsonRespostaDock)

            toastr.success("Json copiado para área de transferência")
        }

        function unsecuredCopyToClipboard(text) {
            var textArea = document.createElement("textarea");
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            try {
                document.execCommand('copy');
            } catch (err) {
                console.error('Unable to copy to clipboard', err);
            }
            document.body.removeChild(textArea);
        }

        vm.prettyPrint = function (objJson) {
            if (!objJson) {
                return ''; // Retorna uma string vazia ou mensagem alternativa
            }
            try {
                var obj = JSON.parse(objJson);
                var pretty = JSON.stringify(obj, undefined, 4);
                return pretty;
            } catch (error) {
                console.error('Erro ao fazer o parsing do JSON:', error);
                return ''; // Em caso de erro, retorna uma string vazia ou mensagem alternativa
            }
        }

    }
})();