<div id="PagamentosViagemController" ng-controller="PagamentosViagemController as vm">
    <form-header items="vm.headerItems" head="'Pagamentos'"></form-header>
    <div class="wrapper wrapper-content animated fadeIn mt-5 overflow-hidden" >
        <div class="row">
            <div class="col-lg-12">
                <div class="ibox float-e-margins">
                    <div style="height: 38px;" class="ibox-title">
                        <h5>Painel de pagamentos Viagem</h5>
                        <div ibox-tools></div>
                    </div>
                    <div class="ibox-content wizard">
                        <div form-wizard steps="2" class="row">
                            <div class="form-wizard">
                                <ol class="row">
                                    <li ng-class="{'active':wizard.active(1)}" ng-click="wizard.go(1); vm.gridOptions.dataSource.refresh();"
                                        class="control-label col-xs-12 col-md-6" style="text-align: center;">
                                        <h4>Pagamentos</h4>
                                    </li>
                                    <li ng-class="{'active':wizard.active(2)}" ng-click="wizard.go(2); vm.gridPagamentoHistoricoOptions.dataSource.refresh()"
                                        class="control-label col-xs-12 col-md-6" style="text-align: center;">
                                        <h4>Histórico Pagamentos</h4>
                                    </li>
                                </ol>
                                <div>
                                    <div ng-show="wizard.active(1)">
                                        <br>
                                        <div class="row">
                                            <div class="col-xs-12 col-sm-12 col-md-4 col-lg-4">
                                                <div class="form-group">
                                                    <div class="form-group">
                                                        <label class="col-xs-12 col-md-4 control-label"
                                                             style="text-align: right; padding-top: 10px;">Código da viagem:</label>
                                                        </label>
                                                        <div class="input-group col-xs-12 col-md-8">
                                                            <input type="text" ng-required="true" ng-disabled="true" ng-model="vm.codViagem" class="form-control"/>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <hr-label dark="true" title="'Pagamentos'"></hr-label>
                                        <br><br>
                                        <div class="row">
                                            <div class="col-lg-12">
                                                <div ui-grid="vm.gridOptions" ng-style="{height: vm.gridOptions.getGridHeight()}" class="grid" style="width: 100%;" 
                                                    ui-grid-pinning 
                                                    ui-grid-save-state 
                                                    ui-grid-pagination 
                                                    ui-grid-auto-resize 
                                                    ui-grid-resize-columns 
                                                    ui-grid-grouping>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div ng-show="wizard.active(2)">
                                        <br/>
                                        <row>
                                            <div class="col-lg-12">
                                                <div class="pull-right">
                                                    <button tooltip-placement="top" ng-click="vm.gridPagamentoHistoricoOptions.dataSource.refresh();" uib-tooltip="Atualizar" type='button' class="btn btn-labeled btn-default">
                                                        <i class="fa fa-refresh"></i>
                                                        <span class="pl-5 ">Atualizar</span>
                                                    </button>
                                                </div>
                                            </div>
                                        </row>
                                        <div class="col-lg-12">
                                            <div ui-grid="vm.gridPagamentoHistoricoOptions" ng-style="{height: vm.gridPagamentoHistoricoOptions.getGridHeight()}" class="grid" style="width: 100%;" 
                                                ui-grid-pinning 
                                                ui-grid-save-state 
                                                ui-grid-pagination 
                                                ui-grid-auto-resize 
                                                ui-grid-resize-columns 
                                                ui-grid-grouping>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <hr />
                                <br/>
                                <div class="row">
                                    <div class="form-group">
                                        <div class="col-md-12 col-lg-12 text-right">
                                            <button type="button" ng-click="vm.onClickVoltar(wizard)" class="btn btn-labeled btn-default">
                                                <span class="btn-label">
                                                    <i class="fa fa-arrow-circle-left"></i>
                                                </span>
                                                Voltar
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
