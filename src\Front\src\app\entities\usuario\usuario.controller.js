(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('UsuarioController', UsuarioController);

    function UsuarioController(
        $scope,
        BaseService,
        PersistentDataService,
        toastr) {

        var vm = this;
        vm.headerItems = [{
            name: '<PERSON><PERSON><PERSON><PERSON>'
        }, {
            name: '<PERSON>u<PERSON><PERSON>'
        }];
        
        vm.alterarStatus = function (id, ativo) {
            BaseService.post('Usuario', 'AlterarStatus', id
            ).then(function (response) {
                response.success ? ativo ? toastr.success('Usuário inativado com sucesso!') : toastr.success('Usuário reativado com sucesso!') : toastr.error(response.message);
                vm.gridOptions.dataSource.refresh();
            });
        };
        vm.gridOptions = {
            data: [],
            onRegisterApi: BaseService.dataGrid.defaultOnRegisterApi,
            dataSource: {
                autoBind: true,
                url: "Usuario/ConsultarGridUsuario"
            },
            columnDefs: [{
                name: '<PERSON><PERSON><PERSON><PERSON>',
                width: 80,
                cellTemplate: '<div ng-if="!row.groupHeader" class="ui-grid-cell-contents" title="TOOLTIP">\
                                    <button ng-disabled="!row.entity.ativo" title="Editar" type="button" ui-sref="usuario.crud({link: row.entity.id})"\
                                        ng-class="{ \'btn btn-xs btn-info\': true }">\
                                        <i class="fa fa-edit"></i>\
                                    </button>\
                                    <button type="button" ng-click="grid.appScope.vm.alterarStatus(row.entity.id, row.entity.ativo)" ng-class="row.entity.ativo ? \'btn btn-xs btn-success\' : \'btn btn-xs btn-danger\'">\
                                        <i ng-class="row.entity.ativo ? \'fa fa-check\' : \'fa fa-times\'"></i>\
                                    </button>\
                                </div>'
            },
            {
                name: 'Código',
                field: 'id',
                primaryKey: true,
                type : 'number',
                width: 80
            },
            {
                name: 'nome',
                field: 'nome',
                width: '*',
                minWidth: 250,
                enableFiltering: true
            },
            {
                name: 'CPF',
                displayName: 'CPF',
                field: 'cpf',
                width: 150,
                enableFiltering: true
            },
            {
                name: 'Login',
                field: 'login',
                width: 150,
                enableFiltering: true
            },
            {
                displayName: 'Grupo de usuário',
                field: 'descricaoGrupoUsuario',
                serverField: 'GrupoUsuario.Descricao',
                width: '*',
                minWidth: 300,
                enableFiltering: true
            }
        ]
        };

        $scope.$on('$stateChangeStart', function () {
            PersistentDataService.store('UsuarioController', vm, "Usuário", "UsuarioCrudController", "usuario.index");
        });
    }
})();