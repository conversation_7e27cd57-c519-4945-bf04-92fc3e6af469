<div id="PainelFinanceiroController" ng-controller="PainelFinanceiroController as vm">
    <form-header items="vm.headerItems" head="'Painel financeiro'"></form-header>
    <div class="wrapper wrapper-content animated fadeIn mt-5 overflow-hidden">
        <div class="row">
            <div class="col-lg-12">
                <div class="ibox float-e-margins">
                    <div style="height: 38px;" class="ibox-title">
                        <h5>Painel financeiro</h5>
                        <div ibox-tools></div>
                    </div>
                    <div class="ibox-content">
                        <div class="row">
                            <div class="col-xs-12 col-md-6">
                                <div class="form-group">
                                    <label class="col-xs-12 col-md-3 control-label"
                                           style="text-align: right; padding-top: 10px;"
                                    >Período:</label>
                                    <div class="input-group col-xs-12 col-md-9">
                                        <input date-range-picker class="form-control date-picker"
                                               ui-date-mask="DD/MM/YYYY - DD/MM/YYYY" type="text" ng-model="vm.date"
                                               options="vm.dateOptions" id="periodoDatePicker"/>
                                    </div>
                                </div>
                            </div>

                            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                                <div class="form-group">
                                    <label class="col-xs-12 col-md-3 control-label"
                                           style="text-align: right; padding-top: 10px;">
                                        Status financeiro:
                                    </label>
                                    <div class="input-group col-xs-12 col-sm-9 col-md-9 col-lg-9">
                                        <ui-select name="status" ats-ui-select-validator validate-on="blur"
                                                   ng-model="vm.status" ng-change="vm.atualizaTela()">
                                            <ui-select-match>
                                                <span>{{$select.selected.descricao}}</span>
                                            </ui-select-match>
                                            <ui-select-choices
                                                    repeat="ex.id as ex in vm.enumStatus | propsFilter: {descricao: $select.search}">
                                                <div ng-bind-html="ex.descricao | highlight: $select.search"></div>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                                <div class="form-group">
                                    <label class="col-xs-12 col-md-3 control-label"
                                           style="text-align: right; padding-top: 10px;">
                                        Pagamento(s) realizado(s):
                                    </label>
                                    <div class="input-group col-xs-12 col-sm-9 col-md-9 col-lg-9">
                                        <input type="text" ng-model="vm.gridOptions.totalPgtos"
                                               disabled validate-on="blur" name="AbastecimentosRealizados"
                                               class="form-control"/>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-3 pull-right">
                                <button tooltip-placement="top"
                                        type='button' ng-click="vm.atualizaTela()"
                                        class="btn btn-labeled btn-primary pull-right">
                                    <span class="btn-label text-right">
                                    </span>
                                    <span class="pl-5 ">Consultar</span>
                                </button>
                            </div>
                        </div>
                        <div class="row"></div>
                        <hr>
                        <div ui-grid="vm.gridOptions" ng-style="{height: vm.gridOptions.getGridHeight()}" class="grid" style="width: 100%;"
                             ui-grid-pinning ui-grid-save-state
                             ui-grid-pagination ui-grid-auto-resize ui-grid-resize-columns ui-grid-grouping>
                        </div>
                        <div class="row">
                            <div class="pull-right mt-15">
                                <button type="button"
                                        class="btn btn-labeled btn-primary pull-right"
                                        ng-click="vm.abrirModalRelatorio(vm)">
                                    <i class="fa fa-file-pdf-o"></i> Exportar Relatório
                                </button>
                            </div>
                        </div>
                        <div class="row"></div>
                        <div id="exportable-xls">
                            <table style="font-family:Arial, Helvetica, sans-serif" ng-show="false" id="exportable"
                                   class="table table-bordered" width="100%">
                                <thead>
                                <tr>
                                    <th style="text-align: left"
                                        ng-repeat="option in vm.modalRelatorioOptions"
                                        ng-if="option.enabled && option.field">
                                        {{option.name}}
                                    </th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr style="text-align: left"
                                    ng-repeat="item in vm.dadosRelatorio">
                                    <td ng-repeat="option in vm.modalRelatorioOptions"
                                        ng-if="option.enabled && option.field">
                                        {{option.pipe ? (option.pipe(item[option.field])) : (item[option.field])}}
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                        <br>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>