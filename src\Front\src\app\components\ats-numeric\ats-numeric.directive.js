(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .directive('atsNumeric', atsNumeric);

    function atsNumeric() {

        var directive = {
            bindToController: true,
            require: 'ngModel',
            controller: atsPriceController,
            controllerAs: 'vm',
            link: link,
            restrict: 'AE',
            scope: {}
        };
        return directive;

        function link(scope, element, attrs, ctrl) {
            ctrl.$parsers.push(function (inputValue) {
                var transformedInput = inputValue ? inputValue.replace(/[^\d.-]/g, '') : null;

                if (transformedInput != inputValue) {
                    ctrl.$setViewValue(transformedInput);
                    ctrl.$render();
                }

                return transformedInput;
            });
        }

    }

    function atsPriceController() {

    }

})();