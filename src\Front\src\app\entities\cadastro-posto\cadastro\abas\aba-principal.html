<div class="form-horizontal">
    <hr-label dark="true"></hr-label>
    <br>
    <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3 col-lg-3">
                        Código:
                    </label>
                    <div class="input-group col-xs-12 col-sm-3 col-md-3 col-lg-3">
                        <input type="text" ng-model="vm.posto.id" class="form-control" disabled value="{{vm.isNew()}}" />
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-3 col-lg-3 control-label">
                        <span class="text-danger mr-5">*</span>CNPJ:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-9 col-lg-9">
                        <input type="text" ui-br-cnpj-mask required 
                               ng-model="vm.posto.cnpj" 
                               ng-blur="vm.carregarDadosDaConta(vm.posto.cnpj)" 
                               ng-disabled="vm.carregandoDadosDaConta"
                               required-message="'CNPJ é obrigatório'" 
                               maxlength="18" validate-on="blur" idname="cnpj" 
                               name="Cnpj" id="cnpj" class="form-control" 
                               invalid-message="'CNPJ é inválido'"/>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-3 col-lg-3 control-label">
                        <span class="text-danger mr-5">*</span>Nome fantasia:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-9 col-lg-9">
                        <input type="text" required ng-model="vm.posto.nomeFantasia" required-message="'Nome fantasia é obrigatório'" maxlength="200" validate-on="blur" name="NomeFantasia" class="form-control"
                        />
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-3 col-lg-3 control-label">
                        <span class="text-danger mr-5">*</span>Razão social:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-9 col-lg-9">
                        <input type="text" maxlength="200" required ng-model="vm.posto.razaoSocial" required-message="'Razão social é obrigatória'" validate-on="blur" name="RazaoSocial" class="form-control"
                        />
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-3 col-lg-3 control-label">
                        Inscrição estadual:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-9 col-lg-9">
                        <input type="text" ng-model="vm.posto.inscricaoEstadual" maxlength="15" validate-on="blur" name="Inscricaoestadual" class="form-control"
                        />
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-3 col-lg-3 control-label">
                        Bandeira:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-9 col-lg-9">
                        <input type="text" maxlength="100" ng-model="vm.posto.bandeira" validate-on="blur" name="RazaoSocial" class="form-control"
                        />
                    </div>
                </div>
            </div>
        </div>  
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-3 col-lg-3 control-label">
                        Responsável:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-9 col-lg-9">
                        <input type="text" ng-model="vm.posto.responsavel" maxlength="100" validate-on="blur" name="Responsavel" class="form-control"
                        />
                    </div>
                </div>
            </div>    
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-3 col-lg-3 control-label">
                        <span class="text-danger mr-5">*</span>E-mail posto:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-9 col-lg-9">
                        <input type="email" required ng-model="vm.posto.emailPosto" required-message="'E-mail do posto é obrigatório'" maxlength="100" validate-on="blur" name="emailposto" class="form-control"
                        />
                    </div>
                </div>
            </div>      
        </div> 
        <div class="row">
            
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-3 col-lg-3 control-label">
                        <span class="text-danger mr-5" ng-show="vm.isNew()">*</span>Senha:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-9 col-lg-9">
                        <input type="password" ng-required="vm.isNew()" ng-model="vm.posto.senha" required-message="'Senha é obrigatório'" maxlength="100" validate-on="blur" name="Senha" class="form-control" autocomplete="new-password"
                        />
                    </div>
                </div>
            </div>
            
            <consulta-padrao-modal tabledefinition="vm.consultaFilial" label="'Filial:'" idname="consultaFilial"
                placeholder="'Selecione uma filial'" directivesizes="'col-xs-12 col-sm-12 col-md-6 col-lg-6'"
                labelsize="'col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label'">
            </consulta-padrao-modal>
        </div> 
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-3 col-lg-3 control-label">
                        Latitude:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-9 col-lg-9">
                        <input type="text" ng-model="vm.posto.latitude" onkeypress='return event.charCode >= 48 && event.charCode <= 57 || event.charCode == 45' validate-on="blur" name="Latitude" class="form-control"
                        />
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-3 col-lg-3 control-label">
                        Longitude:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-9 col-lg-9">
                        <input type="text" onkeypress='return event.charCode >= 48 && event.charCode <= 57 || event.charCode == 45' ng-model="vm.posto.longitude" validate-on="blur" name="Longitude" class="form-control"
                        />
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <hr-label dark="true" title="'Credenciamento'"></hr-label>
            <br>
            <br>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-2 col-md-4 col-lg-3">
                        Status:
                    </label>
                    <div class="input-group col-xs-12 col-sm-10 col-md-8 col-lg-9">
                        <ui-select 
                            name="statuscadastro"
                            ats-ui-select-validator
                            ng-disabled="true"
                            ng-model="vm.posto.statusCadastro">
                            <ui-select-match>
                                <span>{{$select.selected.descricao}}</span>
                            </ui-select-match>
                            <ui-select-choices repeat="ex.id as ex in vm.statusCadastroCombo.data | propsFilter: {descricao: $select.search}">
                                <div ng-bind-html="ex.descricao | highlight: $select.search"></div>
                            </ui-select-choices>
                        </ui-select>
                    </div>
                </div>
            </div>

            <div class="col-xs-12 col-md-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-md-3">Bloqueado</label>
                    <div class="input-group col-xs-12 col-md-9">
                        <toggle-switch on-label="Sim" off-label="Não" ng-model="vm.posto.bloqueado" 
                        ng-change="vm.limparMotivoBloqueio()" class="switch-mini"></toggle-switch>
                    </div>
                </div>
            </div>  
            
        </div> 
        <div class="row">
            <div class="col-xs-12 col-md-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-md-3">
                        <span class="text-danger mr-5" ng-show="vm.posto.bloqueado" >*</span>Motivo bloqueio:
                    </label>
                    <div class="input-group col-xs-12 col-md-9">
                        <textarea style="resize: none;" 
                        value="" 
                        ng-disabled="!vm.posto.bloqueado" 
                        ng-required="vm.posto.bloqueado"
                        type="text" validate-on="blur"
                        multiple maxlength="100" 
                        ng-model="vm.posto.motivoBloqueio"
                        name="motivoBloqueio" 
                        class="form-control ng-pristine ng-valid ng-empty ng-valid-maxlength ng-touched user-success"> </textarea>
                    </div>
                </div>
            </div>            
        </div> 
    </div>
</div>