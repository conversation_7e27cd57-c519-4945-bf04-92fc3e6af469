(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('GestaoTransportadoresController', GestaoTransportadoresController);

        GestaoTransportadoresController.inject = ['BaseService', '$rootScope', 'toastr', '$scope', 'PersistentDataService', '$timeout', '$state', 'PERFIL_ADMINISTRADOR'];

    function GestaoTransportadoresController(BaseService, $rootScope, toastr, $scope, PersistentDataService, $timeout, $state, PERFIL_ADMINISTRADOR) {
        var vm = this;
        vm.headerItems = [{
            name: '<PERSON><PERSON><PERSON><PERSON>'
        }, {
            name: 'Gestão de transportadores' 
        }];

        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.administrador == true;
        };

        vm.gridOptions = {
            data: [],
            onRegisterApi: BaseService.dataGrid.defaultOnRegisterApi,
            dataSource: {
                url: "Transportador/ConsultarGridTransportador"
            },
            columnDefs: [{
                name: 'Ações',
                width: 80,
                cellTemplate: '<div ng-if="!row.groupHeader" class="ui-grid-cell-contents" title="TOOLTIP">\
                                    <button ng-disabled="row.entity.ativo===0" tooltip-placement="right" uib-tooltip="Editar" type="button" ui-sref="gestao-transportador.gestao-transportador-crud({link: row.entity.id})"\
                                        ng-class="{ \'btn btn-xs btn-info\': true }">\
                                        <i class="fa fa-edit"></i>\
                                    </button>\
                                    <button type="button" tooltip-placement="right" uib-tooltip-html="row.entity.ativo===0 ? \'Ativar\' : \'Inativar\'" \
                                        ng-click="grid.appScope.vm.alterarStatus(row.entity.id, row.entity.ativo)" \
                                        ng-class="row.entity.ativo===1 ? \'btn btn-xs btn-success\' : \'btn btn-xs btn-danger\'"> \
                                        <i ng-class="row.entity.ativo===1 ? \'fa fa-check\' : \'fa fa-times\'"></i>\
                                    </button>\
                                </div>'
            }, 
            {
                name: 'Código',
                width: 80,
                type: 'number',
                primaryKey: true,
                field: 'id'
            }, 
            {
                name: 'nomeRazaoSocial',
                displayName: 'Nome / Razão social',
                width: "*",
                field: 'nome',
                serverField: 'nome',
                enableFiltering: true,
                enableSorting: true
            }, 
            {
                name: 'cpfCnpj',
                displayName: 'CPF / CNPJ',
                width: 200,
                field: 'cpfCnpj',
                serverField: 'cpfCnpj',
                enableFiltering: true,
                enableSorting: true
            }, 
            {
                name: 'tipoPessoa',
                displayName: 'Tipo pessoa',
                width: 150,
                field: 'tipoPessoa',
                serverField: 'tipoPessoa',
                enableFiltering: false,
                enableSorting: false
            }
        ]};

        vm.alterarStatus = function (id, ativo) {
            BaseService.post('Transportador', "AlterarStatus", {
                id: id
            }).then(function (response) {
                response.success ? ativo ? toastr.success('Transportador inativado com sucesso!') : toastr.success('Transportador reativado com sucesso!') : toastr.error(response.message);
                vm.gridOptions.dataSource.refresh();
            });
        };

        // Controle de aba!!
        $scope.$on('$stateChangeStart', function () {
            PersistentDataService.store('GestaoTransportadoresController', vm, "Gestão de Transportadores", "GestaoTransportadoresCrudController", "gestao-transportador.index");
        });

        var selfScope = PersistentDataService.get('GestaoTransportadoresController');
        var filho = PersistentDataService.get('GestaoTransportadoresCrudController');
        if (angular.isDefined(filho)) {
            $timeout(function () {
                $state.go('transportador.transportador-crud', {
                    link: filho.data.transportador.IdTransportadores > 0 ? 'editar' : 'novo'
                });
            }, 50);
        } else if (angular.isDefined(selfScope)) {
            angular.extend(vm, selfScope.data);

            $timeout(function () {
                vm.gridOptions.dataSource.refresh();
            }, 450);
        }
    }
})();