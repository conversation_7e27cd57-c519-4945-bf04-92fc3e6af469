<div class="form-horizontal">
    <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-4 control-label">
                        E-mails - Notificação contingência CIOT’s:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-7 col-lg-8">
                        <textarea style="resize: none;" type="text" maxlength="1500" rows="7" ng-model="vm.grupoEmpresa.emailsNotificacaoContingenciaCiot" 
                        name="EmailsNotificacaoContingenciaCiot" class="form-control ng-pristine ng-valid ng-empty ng-touched user-success" ng-keydown="vm.checkForEmailEnd($event)"></textarea>
                    </div>
                </div>
            </div>
        </div>
        <hr-label class="mb-15" dark="true" title="'Controle de pagamentos de frete'"></hr-label>
        <br /><br />
        <div class="row">
            <div class="col-xs-12 col-md-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-md-4">Status de reprocessamento pagamento de frete</label>
                    <div class="input-group col-xs-12 col-md-8">
                        <toggle-switch on-label="Sim" off-label="Não" ng-model="vm.grupoEmpresa.statusReprocessamentoPagamentoFrete"
                            class="switch"></toggle-switch>
                    </div>
                </div>
            </div>
        </div>
        <hr-label class="mb-15" dark="true" title="'Controle de pagamentos de pedágio'"></hr-label>
        <br /><br />
        <div class="row">
            <div class="col-xs-12 col-md-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-md-4">Status de processamento de pagamento de vale pedágio</label>
                    <div class="input-group col-xs-12 col-md-8">
                        <toggle-switch on-label="Sim" off-label="Não" ng-model="vm.grupoEmpresa.habilitaReprocessamentoValePedagio"
                            class="switch"></toggle-switch>
                    </div>
                </div>
            </div>
        </div>
        <hr-label class="mb-15" dark="true" title="'Controle telão de saldo'"></hr-label>
        <br /><br />
    </div>
</div>
