(function() {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('ModalDetalhesHistoricoCiotController', ModalDetalhesHistoricoCiotController);

        ModalDetalhesHistoricoCiotController.$inject = ['$uibModalInstance', 'toastr', 'BaseService', '$timeout', 'jsonRequisicao', 'jsonResposta'];

    function ModalDetalhesHistoricoCiotController($uibModalInstance, toastr, BaseService, $timeout, jsonRequisicao, jsonResposta) {
        var vm = this;
        vm.modal = {};
        vm.jsonRequisicao = jsonRequisicao;
        vm.jsonResposta = jsonResposta;

        vm.copiar = function (campo) {
            unsecuredCopyToClipboard(campo == 1 ? vm.jsonRequisicao : vm.jsonResposta)

            toastr.success("Json copiado para área de transferência")
        }

        function unsecuredCopyToClipboard(text) {
            var textArea = document.createElement("textarea");
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            try {
                document.execCommand('copy');
            } catch (err) {
                console.error('Unable to copy to clipboard', err);
            }
            document.body.removeChild(textArea);
        }

        vm.prettyPrint = function (objJson) {
            if (!objJson) {
                return ''; // Retorna uma string vazia ou mensagem alternativa
            }
            try {
                var obj = JSON.parse(objJson);
                var pretty = JSON.stringify(obj, undefined, 4);
                return pretty;
            } catch (error) {
                console.error('Erro ao fazer o parsing do JSON:', error);
                return ''; // Em caso de erro, retorna uma string vazia ou mensagem alternativa
            }
        }

    }
})();