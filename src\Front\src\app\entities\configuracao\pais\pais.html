<style>
    .imgRecarregar {
        margin: 5px;
    }
</style>
<div ng-controller="PaisController as vm">
        <form-header items="vm.headerItems" head="'País'"></form-header>
        <div class="animated fadeIn filter-position">
            <div class="row">
                <div class="col-lg-12">
                    <div style="display: flex; background-color: white;padding: 5px;border-top-left-radius: 8px;border-top-right-radius: 8px;float:right;margin-bottom: -5px;">
                            <div>
                                    <img alt="" id="imgRecarregar" class="imgRecarregar" ng-click="vm.refresh();" title="Recarregar dados" src="https://cdn3.iconfinder.com/data/icons/watchify-v1-0/70/rotate-forward-70px-24.png" />
                                </div>
                        <button tooltip-placement="top" ui-sref="configuracao.pais-crud ({link:'novo'})" uib-tooltip="Cadastrar" type='button' class="btn btn-labeled btn-primary">
                            <span class="btn-label text-right"><i class="fa fa-plus"></i></span>
                            <span class="pl-5">Novo</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="wrapper wrapper-content animated fadeIn mt-5 ibox">
            <div class="row">
                <div class="col-lg-12">
                    <div class="ibox float-e-margins">
                        <div class="ibox-title">
                            <h5>Versões de hardware</h5>
                            <div ibox-tools></div>
                        </div>
                        <div class="ibox-content">
                            <div ui-grid="vm.gridOptions" class="grid" style="width: 100%;" ui-grid-pinning ui-grid-save-state ui-grid-pagination ui-grid-auto-resize ui-grid-resize-columns ui-grid-grouping>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>