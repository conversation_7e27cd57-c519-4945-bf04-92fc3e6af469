(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('PainelCiotModalCadClienteCrudController', PainelCiotModalCadClienteCrudController);

    PainelCiotModalCadClienteCrudController.$inject = ['$rootScope', '$uibModalInstance', 'toastr', 'BaseService', '$timeout'];

    function PainelCiotModalCadClienteCrudController($rootScope, $uibModalInstance, toastr, BaseService, $timeout) {
        var vm = this;
        vm.cliente = {};
        vm.estados = [];
        vm.cidades = [];
        vm.estadosDisabled = true;
        vm.cidadesDisabled = true;

        vm.buscarEndereco = function (cep) {
            if (cep) {
                limparEndereco();
                BaseService.getEndereco(cep).then(function (response) {
                    if (response && response.erro) {
                        toastr.warning("Endereço não localizado.");
                    }

                    carregarEstados();

                    $timeout(function () {
                        var estado = _.find(vm.estados, ['sigla', response.uf]);

                        if (angular.isDefined(estado))

                            vm.cliente.estadoId = estado.id;
                        carregarCidades(vm.cliente.estadoId);
                        $timeout(function () {
                            var cidade = _.find(vm.cidades, ['descricao', response.localidade]);
                            if (angular.isDefined(cidade))
                                vm.cliente.cidadeId = cidade.id;
                        }, 1500);
                    }, 1500);

                    $timeout(function () {
                        if (angular.isDefined(response.logradouro))
                            vm.cliente.endereco = response.logradouro;
                    }, 1500);

                    $timeout(function () {
                        if (angular.isDefined(response.bairro))
                            vm.cliente.bairro = response.bairro;
                    }, 1500);

                });
            }
        };

        function carregarEstados() {
            BaseService.get("Endereco", "ConsultarEstados")
                .then(function (response) {
                    if (response.success) {
                        vm.estados = response.data;
                        vm.estadosDisabled = false;
                    }
                });
        };

        function carregarCidades(EstadoId) {
            BaseService.get("Endereco", "ConsultarCidadesPorEstado", {
                estadoId: EstadoId
            }).then(function (response) {
                if (response.success) {
                    vm.cidades = response.data;
                    vm.cidadesDisabled = false;
                }
            });
        };

        function limparEndereco() {
            vm.cliente.estadoId = null;
            vm.cliente.cidadeId = null;
            vm.estados = null;
            vm.cidades = null;
            vm.cliente.endereco = null;
            vm.cliente.bairro = null;
            vm.cliente.enderecoNumero = null;
            vm.cliente.complemento = null;
        };

        vm.estadoChange = function (estadoId) {
            vm.cidades = null;
            vm.cidadesDisabled = true;
            vm.cliente.cidadeId = null;
            carregarCidades(estadoId);
        };

        vm.salvarCliente = function(form) {
            if (!form.$valid) {
                toastr.error('Todos os campos obrigatórios e/ou inválidos devem ser preenchidos corretamente com seus respectivos valores');
                return;
            }
            vm.cliente.empresaId = $rootScope.usuarioLogado.empresaId;
            BaseService.post('Cliente', "SaveCliente", vm.cliente
            ).then(function (response) {
                response.success ? toastr.success("Cliente cadastrado com sucesso.") : toastr.error(response.message);
            });
            $uibModalInstance.close();
        };
    }
})();
