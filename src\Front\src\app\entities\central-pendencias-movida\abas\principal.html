<div class="form-horizontal">
    <hr />
    <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label">Código:</label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <input type="text" ng-model="vm.pendencia.id" class="form-control" disabled
                            value="{{vm.isNew()}}" />
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-5 col-lg-3 control-label">
                        Valor:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-7 col-lg-9">
                        <input type="text" disabled ng-model="vm.pendencia.valor"
                            maxlength="200" name="descricao" class="form-control" />
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-5 col-lg-3 control-label">
                        Código externo:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-7 col-lg-9">
                        <input type="text" disabled ng-model="vm.pendencia.pagamentoExternoId"
                            maxlength="200" name="valor" class="form-control" />
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-md-6">
                <div class="form-group ">
                    <label class="control-label col-xs-12 col-md-3 alinhamento-labels" style="padding-top: 6px; ">Data baixa:</label>
                    <div class="input-group col-xs-12 col-md-9">
                        <input type="text" name="Databaixa" class="form-control" disabled ng-change="vm.changeData()" readonly current-text="Hoje" clear-text="Limpar" close-text="Fechar" uib-datepicker-popup="dd/MM/yyyy" datepicker-append-to-body="true"
                            ng-model="vm.pendencia.dataBaixa" is-open="vm.datePicker" ng-blur="vm.adicionarItemTabela()"/>
                        <span class="input-group-btn">
                            <button type="button" class="btn btn-default" ng-disabled="vm.contratoAgregado.disabled" ng-click="vm.datePicker = !vm.datePicker"><i class="fa fa-calendar"></i>
                            </button>
                        </span>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-2 col-md-4 col-lg-3">
                        <span class="text-danger mr-5">*</span>Status:
                    </label>
                    <div class="input-group col-xs-12 col-sm-10 col-md-8 col-lg-9">
                        <ui-select 
                            name="status"
                            ats-ui-select-validator
                            validate-on="blur"
                            ng-model="vm.pendencia.status"
                            ng-disabled="true"
                            required-message="'Cidade é obrigatória'"
                            required>
                            <ui-select-match>
                                <span>{{$select.selected.descricao}}</span>
                            </ui-select-match>
                            <ui-select-choices repeat="ex.id as ex in vm.cmbStatus.data | propsFilter: {descricao: $select.search}">
                                <div ng-bind-html="ex.descricao | highlight: $select.search"></div>
                            </ui-select-choices>
                        </ui-select>
                    </div>
                </div>
            </div>  
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-2 col-md-4 col-lg-3">
                        Evento:
                    </label>
                    <div class="input-group col-xs-12 col-sm-10 col-md-8 col-lg-9">
                        <ui-select 
                            name="tipo"
                            ats-ui-select-validator
                            validate-on="blur"
                            ng-model="vm.pendencia.tipo"
                            ng-disabled="true">
                            <ui-select-match>
                                <span>{{$select.selected.descricao}}</span>
                            </ui-select-match>
                            <ui-select-choices repeat="ex.id as ex in vm.cmbTipo.data | propsFilter: {descricao: $select.search}">
                                <div ng-bind-html="ex.descricao | highlight: $select.search"></div>
                            </ui-select-choices>
                        </ui-select>
                    </div>
                </div>
            </div>  
        </div>
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-5 col-lg-3 control-label">
                        Código viagem:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-7 col-lg-9">
                        <input type="text" disabled ng-model="vm.pendencia.viagemId"
                            maxlength="200" name="viagemid" class="form-control" />
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-5 col-lg-3 control-label">
                        Motivo:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-7 col-lg-9">
                        <input type="text" disabled ng-model="vm.pendencia.motivoPendencia"
                            maxlength="200" name="codigoexterno" class="form-control" />
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>