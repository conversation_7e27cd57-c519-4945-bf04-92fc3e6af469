(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('PainelCiotModalEncerraCrudController', PainelCiotModalEncerraCrudController);

    PainelCiotModalEncerraCrudController.$inject = [
        'toastr', '$rootScope', 'BaseService', '$state', '$stateParams', '$window', 'PERFIL_ADMINISTRADOR',
        '$scope', '$timeout', 'PersistentDataService', 'DefaultsService', '$uibModalInstance', 'CiotId', 'Ciot','VerificadorCiot' ,'CpfCnpjCliente'];

    function PainelCiotModalEncerraCrudController(toastr, $rootScope,
        BaseService, $state,
        $stateParams, $window,
        PERFIL_ADMINISTRADOR,
        $scope, $timeout,
        PersistentDataService,
        DefaultsService,
        $uibModalInstance, CiotId, Ciot,VerificadorCiot, CpfCnpjCliente) {
        var vm = this;
        vm.viagensOperacaoTransporte = [];
        vm.viagem = {};
        vm.valoresEfetivos = {};
        vm.disableFields = false;
        vm.disableFieldPesoCarga = false;
        vm.disableFieldValorFrete = false;
        vm.disableFieldNaturezaCarga = false;
        vm.saving = false;

        vm.consultaViagem = {
            columnDefs: [{
                name: 'Código',
                width: 80,
                primaryKey: true,
                field: 'id',
                type: 'number',
                enableFiltering: true
            }, {
                name: 'Código Externo Id',
                width: 125,
                primaryKey: true,
                field: 'codigoExternoId',
                enableFiltering: true
            }, {
                name: 'Origem',
                width: '*',
                minWidth: 150,
                field: 'nomeOrigem',
                serverField: 'CidadeOrigem.Nome',
                enableFiltering: true
            }, {
                name: 'Destino',
                width: '*',
                minWidth: 150,
                field: 'nomeDestino',
                serverField: 'CidadeDestino.Nome',
                enableFiltering: true
            }, {
                name: 'Id Origem',
                width: '*',
                minWidth: 150,
                field: 'idMunicipioOrigem',
                enableFiltering: true,
                visible: false
            }, {
                name: 'Id Destino',
                width: '*',
                minWidth: 150,
                field: 'idMunicipioDestino',
                enableFiltering: true,
                visible: false
            }, {
                name: 'IBGE Origem',
                width: '*',
                minWidth: 150,
                field: 'codigoMunicipioOrigem',
                enableFiltering: true,
                visible: false
            }, {
                name: 'IBGE Destino',
                width: '*',
                minWidth: 150,
                field: 'codigoMunicipioDestino',
                enableFiltering: true,
                visible: false
            }, {
                name: 'Peso Carga',
                width: '*',
                minWidth: 150,
                field: 'pesoCarga',
                serverField: 'pesoCarga',
                enableFiltering: true
            }, {
                name: 'Natureza Carga',
                width: '*',
                minWidth: 150,
                field: 'naturezaCarga',
                serverField: 'naturezaCarga',
                enableFiltering: true
            }, {
                name: 'Valor Frete',
                width: '*',
                minWidth: 150,
                field: 'valorFrete',
                serverField: 'valorFrete',
                enableFiltering: true
            }],
            desiredValue: 'id',
            desiredText: 'codigoExternoId',
            url: 'Viagem/ConsultarGridViagemCombo',
            paramsMethod: function () {
                return {
                }
            },
            executeAfterSelection: function () {
                vm.consultaCidadeOrigem.selectedText = vm.consultaViagem.selectedEntity.nomeOrigem;
                vm.consultaCidadeDestino.selectedText = vm.consultaViagem.selectedEntity.nomeDestino;
                vm.ibgeOrigem = vm.consultaViagem.selectedEntity.codigoMunicipioOrigem  ? vm.consultaViagem.selectedEntity.codigoMunicipioOrigem : null;
                vm.ibgeDestino = vm.consultaViagem.selectedEntity.codigoMunicipioDestino  ? vm.consultaViagem.selectedEntity.codigoMunicipioDestino : null;
                vm.codNaturezaCarga = vm.consultaViagem.selectedEntity.naturezaCarga  ? vm.consultaViagem.selectedEntity.naturezaCarga : null;
                vm.consultaNaturezaCarga.selectedText = vm.consultaViagem.selectedEntity.naturezaCarga ? vm.consultaViagem.selectedEntity.naturezaCarga : null;
                vm.pesoCarga = vm.consultaViagem.selectedEntity.pesoCarga ? vm.consultaViagem.selectedEntity.pesoCarga : null;
                vm.valorFrete = vm.consultaViagem.selectedEntity.valorFrete ? vm.consultaViagem.selectedEntity.valorFrete : null;
                vm.disableFields = true;
                vm.disableFieldPesoCarga = vm.pesoCarga != null;
                vm.disableFieldValorFrete = vm.valorFrete != null;
                vm.disableFieldNaturezaCarga = vm.consultaNaturezaCarga.selectedText != null;
                vm.viagemId = vm.consultaViagem.selectedEntity.id;
            }
        };

        vm.limpaInfosViagem = function () {
            vm.disableFields = false;
            vm.disableFieldPesoCarga = false;
            vm.disableFieldValorFrete = false;
            vm.disableFieldNaturezaCarga = false;
            vm.consultaCidadeOrigem.selectedText = null;
            vm.consultaCidadeDestino.selectedText = null;
            vm.consultaNaturezaCarga.selectedText = null;
            vm.pesoCarga = null;
            vm.valorFrete = null
        };

        vm.consultarViagensCiot = function (ciot) {
            BaseService.get('Viagem', 'ConsultarViagensCiot', { ciot: ciot }).then(function (response) {
                    if (response.success) {
                        vm.viagensOperacaoTransporte = response.data;
                    } else {
                        toastr.error(response.message)
                    }
                })
        }

        function init() {
            vm.consultarViagensCiot(Ciot);
        }

        init();

        vm.consultaCidadeOrigem = {
            columnDefs: [{
                name: 'Código',
                width: 80,
                primaryKey: true,
                field: 'id',
                type: 'number',
                enableFiltering: true
            }, {
                name: 'IBGE',
                width: 80,
                primaryKey: true,
                field: 'ibge',
                enableFiltering: true
            }, {
                name: 'Nome',
                width: '*',
                minWidth: 150,
                field: 'nome',
                serverField: 'Nome',
                enableFiltering: true
            }, {
                name: 'Estado',
                width: '*',
                minWidth: 150,
                field: 'nomeEstado',
                serverField: 'Estado.Nome',
                enableFiltering: true
            }],
            desiredValue: 'ibge',
            desiredText: 'nome',
            url: 'Cidade/ConsultarGridCidade',
            paramsMethod: function () {
                return {
                }
            }
        };

        vm.consultaCidadeDestino = {
            columnDefs: [{
                name: 'Código',
                width: 80,
                primaryKey: true,
                field: 'id',
                type: 'number',
                enableFiltering: true
            }, {
                name: 'IBGE',
                width: 80,
                primaryKey: true,
                field: 'ibge',
                enableFiltering: true
            }, {
                name: 'Nome',
                width: '*',
                minWidth: 150,
                field: 'nome',
                serverField: 'Nome',
                enableFiltering: true
            }, {
                name: 'Estado',
                width: '*',
                minWidth: 150,
                field: 'nomeEstado',
                serverField: 'Estado.Nome',
                enableFiltering: true
            }],
            desiredValue: 'ibge',
            desiredText: 'nome',
            url: 'Cidade/ConsultarGridCidade',
            paramsMethod: function () {
                return {

                }
            }
        };

        vm.fechar = function () {
            $uibModalStack.dismissAll();
        }


        vm.consultaNaturezaCarga = {
            columnDefs: [{
                name: 'Código',
                width: 80,
                field: 'codigo',
                enableFiltering: true
            }, {
                name: 'Descrição',
                width: '*',
                minWidth: 150,
                field: 'descricao',
                serverField: 'Descricao',
                enableFiltering: true
            }],
            desiredValue: 'id',
            desiredText: 'codigo',
            url: 'NaturezaCarga/ConsultarGridNaturezaCarga',
            paramsMethod: function () {
                return {
                }
            }
        };


        vm.removerViagem = function (row) {
            vm.viagensOperacaoTransporte.splice(row.$index, 1)
            if (vm.viagensOperacaoTransporte.length < 1) {
                vm.isChangeTipe = true;
                vm.limpar()
            }
        }

        vm.adicionarViagem = function (form) {
            if (!form.$valid) {
                toastr.error('Todos os campos obrigatórios e/ou inválidos devem ser preenchidos corretamente com seus respectivos valores');
                return;
            }

            //Monta requisição da lista de viagens
            vm.viagem.codigoMunicipioOrigem = vm.consultaCidadeOrigem.selectedValue ? vm.consultaCidadeOrigem.selectedValue : vm.ibgeOrigem;
            vm.viagem.codigoMunicipioDestino = vm.consultaCidadeDestino.selectedValue ? vm.consultaCidadeDestino.selectedValue : vm.ibgeDestino;
            vm.viagem.codigoNaturezaCarga = vm.consultaNaturezaCarga.selectedValue ? vm.consultaNaturezaCarga.selectedValue : vm.codNaturezaCarga;

            //Mostra  
            vm.viagem.nomeOrigem = vm.consultaCidadeOrigem.selectedText;
            vm.viagem.nomeDestino = vm.consultaCidadeDestino.selectedText;
            vm.viagem.codigoNaturezaCarga = vm.consultaNaturezaCarga.selectedText;
            vm.viagem.peso = vm.pesoCarga;

            vm.viagem.valorTarifa = vm.valorTarifaAntt;
            vm.viagem.valorFrete = vm.valorFrete;
            vm.viagem.valorImposto = vm.valorImposto;
            vm.viagem.valorDespesas = vm.valorDespesas;
            vm.viagem.valorCombustivel = vm.valorCombustivel;
            vm.viagem.valorPedagio = vm.valorPedagio;
            vm.viagem.viagemId = vm.viagemId;

            vm.viagensOperacaoTransporte.push(vm.viagem);
            vm.viagem = {};
            vm.limpar();
        };

        vm.encerrar = function () {
            var request = {
                Ciot: Ciot,
                VerificadorCiot: VerificadorCiot,
                Viagens: vm.viagensOperacaoTransporte,
                Id: CiotId,
                CpfCnpjClienteAdmOrCompanyGroup: CpfCnpjCliente
            }

            vm.saving = true;

            BaseService.post('PainelCiot', 'AlterarStatusEncerrado', request).then(function (response) {
                if (response.success) {
                    toastr.success('CIOT encerrado com sucesso!');
                    $uibModalInstance.close();
                } else {
                    toastr.error(response.message);
                    vm.saving = false;
                }
            });
        };


        vm.limpar = function () {
            vm.consultaCidadeOrigem.selectedValue = undefined;
            vm.consultaCidadeOrigem.selectedText = null;
            vm.consultaCidadeDestino.selectedValue = undefined;
            vm.consultaCidadeDestino.selectedText = null;
            vm.consultaNaturezaCarga.selectedValue = undefined;
            vm.consultaNaturezaCarga.selectedText = null;
            vm.consultaViagem.selectedValue = undefined;
            vm.consultaViagem.selectedText = null;
            vm.valorTarifaAntt = undefined;
            vm.valorFrete = undefined;
            vm.valorImposto = undefined;
            vm.valorDespesas = undefined;
            vm.valorCombustivel = undefined;
            vm.valorPedagio = undefined;
            vm.pesoCarga = undefined;
            vm.disableFields = false;
            vm.disableFieldPesoCarga = false;
            vm.disableFieldValorFrete = false;
            vm.disableFieldNaturezaCarga = false;
            vm.viagemId = undefined;
        };

    }
})();