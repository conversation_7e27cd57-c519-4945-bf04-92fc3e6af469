<div class="form-horizontal">
    <hr />
    <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
        <!--<PERSON><PERSON><PERSON> e Beneficiári-->
        <div class="row">
            <!--Código-->
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label">
                        Código:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <input type="text" ng-model="vm.pagamentoEvento.id" class="form-control" disabled
                            value="{{vm.isNew()}}" />
                    </div>
                </div>
            </div>
            <!--Beneficiário-->
            <!--<consulta-padrao-modal tabledefinition="vm.consultaBeneficiario" idname="Beneficiario" idmodel="Beneficiario" label="'Beneficiário:'"
                placeholder="'Selecione um Beneficiário'" labelsize="'col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label'"
                functionclear="vm.beneficiarioLimpaCampos"
                ng-disabled="true">
            </consulta-padrao-modal>-->
        </div>
        <!--Forma de pagamento-->
        <!--<div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label" style="word-wrap: break-word; margin-top: -7px">
                       Forma de pagamento:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <ui-select name="FormaPagamento" ng-model="vm.painelPagamento.formaPagamento"
                            ats-ui-select-validator ng-change="vm.FormaPagamento(vm.painelPagamento.formaPagamento)"
                            ng-disabled="true"
                            validate-on="blur">
                            <ui-select-match>
                                <span>{{$select.selected.descricao}}</span>
                            </ui-select-match>
                            <ui-select-choices
                                repeat="ex.id as ex in vm.comboFormaPag.data | propsFilter: {descricao: $select.search}">
                                <div ng-bind-html="ex.descricao | highlight: $select.search"></div>
                            </ui-select-choices>
                        </ui-select>
                    </div>
                </div>
            </div>
            &lt;!&ndash;Tipo&ndash;&gt;&lt;!&ndash;
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label">
                        Tipo:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <ui-select name="Tipo" ng-model="vm.painelPagamento.tipo" ats-ui-select-validator
                            ng-disabled="true"
                            validate-on="blur" >
                            <ui-select-match>
                                <span>{{$select.selected.descricao}}</span>
                            </ui-select-match>
                            <ui-select-choices
                                repeat="ex.id as ex in vm.comboTipo.data | propsFilter: {descricao: $select.search}">
                                <div ng-bind-html="ex.descricao | highlight: $select.search"></div>
                            </ui-select-choices>
                        </ui-select>
                    </div>
                </div>
            </div>&ndash;&gt;
        </div>-->
        <!--Valor e Ciot-->
        <!--<div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label">
                        Valor:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        R$
                        <div class="message-required-size">
                            <input type="text" maxlength="100" ats-price
                            ng-disabled="true"
                            ng-model="vm.painelPagamento.valor" name="Valor" class="form-control" validate-on="blur"
                            />
                        </div> 
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label">CIOT:</label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <ui-select name="CIOT" ng-model="vm.painelPagamento.ciotId"
                            ng-disabled="true">
                            <ui-select-match>
                               {{$select.selected.ciot}} {{$select.selected.verificador}}
                            </ui-select-match>
                            <ui-select-choices repeat="ex.id as ex in vm.CIOTS | filter: $select.search">
                                <span ng-bind-html="ex.ciot | highlight: $select.search"></span>
                                <span ng-bind-html="ex.verificador | highlight: $select.search"></span>
                            </ui-select-choices>
                        </ui-select>
                    </div>
                </div>
            </div>
        </div>-->
        <!--Agencia e Conta-->
        <!--<div class="row" ng-show="vm.painelPagamento.formaPagamento === 0">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs12 col-sm-3 col-md-4 col-lg-3 control-label">Agência:</label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <input type="text" ng-pattern="/^[0-9]*$/" maxlength="10" ng-model="vm.painelPagamento.agencia"
                            ng-disabled="true"
                            name="Agencia" class="form-control" validate-on="blur" />
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label">Conta:</label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <input type="text" maxlength="10" ng-pattern="/^[0-9]*$/" ng-model="vm.painelPagamento.conta" name="Conta"
                            class="form-control" validate-on="blur" 
                            ng-disabled="true"/>
                        <span class="input-group-addon">-</span>
                        <input type="text" maxlength="10" ng-model="vm.painelPagamento.verificadorConta"
                            name="Verificador"
                            ng-disabled="true"
                            class="form-control" style="width: 50px;" validate-on="blur" />
                    </div>
                </div>
            </div>
        </div>-->
        <!--Banco e Tipo Conta-->
        <!--<div class="row" ng-show="vm.painelPagamento.formaPagamento === 0">
            <consulta-padrao-modal
                ng-disabled="true"
                tabledefinition="vm.consultaBanco" label="'Banco:'" placeholder="'Selecione um Banco'">
            </consulta-padrao-modal>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label">Tipo Conta:</label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <input type="text" maxlength="10" ng-model="vm.painelPagamento.tipoConta"
                            ng-disabled="true"
                            name="TipoConta" class="form-control" validate-on="blur" />
                    </div>
                </div>
            </div>
        </div>-->
        <!--Empresa-->
        <!--<div class="row">
            <consulta-padrao-modal ng-show="vm.isAdmin()"
                ng-disabled="true" functionclear="vm.empresaLimpaCampos"
                tabledefinition="vm.consultaEmpresa" idname="Empresa" idmodel="Empresa" label="'Empresa:'" labelsize="'col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label'" placeholder="'Selecione uma Empresa'">
            </consulta-padrao-modal>
        </div>-->
        <!--Conta origem e Conta destino-->
        <!--<div class="row" ng-show="vm.painelPagamento.formaPagamento === 1">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label">Conta origem:</label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <ui-select name="ContaOrigem" ng-model="vm.painelPagamento.contaOrigem"
                            ng-disabled="true"
                            ats-ui-select-validator validate-on="blur">
                            <ui-select-match>
                                <span>{{$select.selected.descricao}}</span>
                            </ui-select-match>
                            <ui-select-choices
                                repeat="ex.id as ex in vm.painelPagamento.contasOrigem | filter: $select.search">
                                <div ng-bind-html="ex.descricao | highlight: $select.search"></div>
                                <small ng-bind-html="ex.cpfCnpj | highlight: $select.search"></small>
                            </ui-select-choices>
                        </ui-select>
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label">Conta destino:</label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <ui-select name="ContaDestino" ng-model="vm.painelPagamento.contaDestino"
                            ng-disabled="true"
                            ats-ui-select-validator validate-on="blur">
                            <ui-select-match>
                                <span>{{$select.selected.descricao}}</span>
                            </ui-select-match>
                            <ui-select-choices
                                repeat="ex.id as ex in vm.painelPagamento.contasDestino | filter: $select.search">
                                <div ng-bind-html="ex.descricao | highlight: $select.search"></div>
                                <small ng-bind-html="ex.cpfCnpj | highlight: $select.search"></small>
                            </ui-select-choices>
                        </ui-select>
                    </div>
                </div>
            </div>
        </div>-->
        <!--Verificações de Status Pix e Data da 3a Verificação-->
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label">
                        Verificações de Status Pix:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <input type="text" class="form-control" disabled ng-value="vm.pagamentoEvento.contadorVerificacaoStatusPix"/>
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label">
                        Data da última Verificação:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-8 col-lg-9">
                        <input type="text" class="form-control" disabled ng-value="vm.pagamentoEvento.dataTerceiraVerificacaoStatusPix.replace('T', ' ')"/>
                    </div>
                </div>
            </div>
        </div>
        <!--Ocorrência-->
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-3 col-md-2 col-lg-1-5">
                        Ocorrência:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-10 col-lg-10-5" >
                        <input type="text" maxlength="500" class="form-control" ng-model="vm.pagamentoEvento.ocorrencia"/>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<style>
    @media (min-width: 1200px){
        .col-lg-1-5 {
            width: 12.5%;
        }
        .col-lg-10-5 {
            width: 87.5%;
        }
    }
</style>