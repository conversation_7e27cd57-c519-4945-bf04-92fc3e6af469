(function () {
    'use strict';

    angular.module('bbcWeb.grupoUsuario.state', []).config(routeConfig);

    function routeConfig($urlRouterProvider, $stateProvider) {
        $stateProvider.state('grupo-usuario', {
            abstract: true,
            url: "/grupo-usuario",
            templateUrl: "app/layout/content.html"
        }).state('grupo-usuario.index', {
            url: '/index',
            templateUrl: 'app/entities/grupo-usuario/grupo-usuario.html'
        }).state('grupo-usuario.grupo-usuario-crud', {
            url: '/:link',
            templateUrl: 'app/entities/grupo-usuario/grupo-usuario-crud.html'
        });
    }
})();