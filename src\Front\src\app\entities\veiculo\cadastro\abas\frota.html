<div class="form-horizontal">
    <hr />
    <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12"> 
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-3 col-lg-3 control-label">
                        Odômetro:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-9 col-lg-9">
                        <input type="text" onpaste="return event.charCode >= 48 && event.charCode <= 57"
                        onkeypress="return event.charCode >= 48 && event.charCode <= 57" ng-model="vm.veiculo.odometro" maxlength="200" validate-on="blur" name="odometro" class="form-control"
                        />
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-3 col-lg-3 control-label">
                        Frota:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-9 col-lg-9">
                        <input type="text" ng-model="vm.veiculo.numeroFrota" maxlength="200" validate-on="blur" name="numerofrota" class="form-control"
                        />
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-2 col-md-4 col-lg-3">
                        Tipo de abastecimento:
                    </label>
                    <div class="input-group col-xs-12 col-sm-10 col-md-8 col-lg-9">
                        <ui-select
                            name="tipoAbastecimento"
                            id="tipoabast"
                            ats-ui-select-validator
                            validate-on="blur"
                            ng-model="vm.veiculo.tipoAbastecimento">
                            <ui-select-match>
                                <span>{{$select.selected.descricao}}</span>
                            </ui-select-match>
                            <ui-select-choices repeat="ex.id as ex in vm.comboTipoAbatecimento.data | propsFilter: {descricao: $select.search}">
                                <div ng-bind-html="ex.descricao | highlight: $select.search"></div>
                            </ui-select-choices>
                        </ui-select>
                    </div>
                </div>
            </div>
            <consulta-padrao-modal tabledefinition="vm.consultaCombustivelPreferencial" label="'Combustivel preferêncial:'" name="CombustivelPreferencial" id="conbpref"
                placeholder="'Selecione um combustivel'" directivesizes="'col-xs-12 col-sm-12 col-md-6 col-lg-6'"
                labelsize="'col-xs-12 col-sm-3 col-md-4 col-lg-3 control-label'"></consulta-padrao-modal>
        </div>
    </div>
</div>