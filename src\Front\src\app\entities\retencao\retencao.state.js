(function () {
    'use strict';

    angular.module('bbcWeb.retencao.state', []).config(routeConfig);

    function routeConfig( $stateProvider) {
        $stateProvider
            .state('retencao', {
                url: "/retencao",
                abstract: true,  
                templateUrl: "app/layout/content.html"
            })
            .state('retencao.index', {
                url: "/index",
                templateUrl: "app/entities/retencao/retencao.html"
            });
    }
})();