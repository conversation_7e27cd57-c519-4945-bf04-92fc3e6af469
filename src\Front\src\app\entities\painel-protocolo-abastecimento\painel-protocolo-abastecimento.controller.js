(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('PainelProtocoloAbastecimentoController', PainelProtocoloAbastecimentoController);

    PainelProtocoloAbastecimentoController.inject = [
        'BaseService',
        '$rootScope',
        'toastr',
        '$scope',
        'PersistentDataService',
        '$timeout',
        '$state',
        '$stateParams',
        'PERFIL_ADMINISTRADOR',
        'oitozero.ngSweetAlert',
        '$uibModal',
        '$window'
    ];

    function PainelProtocoloAbastecimentoController(
        BaseService,
        $rootScope,
        toastr,
        $scope,
        PersistentDataService,
        $timeout,
        $state,
        $stateParams,
        PERFIL_ADMINISTRADOR,
        SweetAlert,
        $uibModal,
        $window) {
        var vm = this;

        vm.status = 2;

        vm.reenviarPagamento = [];
        vm.reenviarPagamentoId = [];
        vm.retencaoRelatorio = [];

        vm.modalRelatorioOptions = [{}];
        vm.modalRelatorioAbastecimentoOptions = [{}];
        vm.ativarTodasConta = false;
        vm.desabilitarBtnRelatorio = false;
        vm.atualizando = false;
        vm.maisDetalhes = false;


        vm.enumStatus = [
            { id: 0, descricao: 'Reprovado' },
            { id: 1, descricao: 'Aprovado' },
            { id: 2, descricao: 'Pendente' }
        ]

        vm.headerItems = [{ name: 'Movimentações' }, { name: 'Painel protocolo abastecimento' }];

        vm.load = function () {
        }

        vm.atualizaTela = function () {
            vm.gridOptions.dataSource.refresh();
            vm.AtualizaComboStatus();
        }

        vm.AtualizaComboStatus = function () {
            vm.enumStatus = [];
        }

        vm.dateOptions = {
            timePicker: false,
            timePicker24Hour: false,
            applyClass: 'btn-primary',
            locale: {
                applyLabel: "Aplicar",
                fromLabel: "De",
                format: "DD/MM/YYYY",
                toLabel: "Até",
                cancelLabel: 'Cancelar',
                customRangeLabel: 'Período',
                monthNames: ['Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho', 'Julho', 'Agosto', 'Setembro',
                    'Outubro', 'Novembro', 'Dezembro'],
                daysOfWeek: ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb']
            },
            ranges: {
                'Últimos 1 dias': [moment().add(-1, 'days'), moment()],
                'Últimos 7 dias': [moment().add(-7, 'days'), moment()]
            }
        };

        vm.date = {
            startDate: moment().add(-7, 'days'),
            endDate: moment().add(3, 'hours')
        };

        $scope.$watch('vm.date.endDate', function () {
            vm.date.endDate = vm.date.endDate.add(-3, 'hours')
        })

        vm.contas = [];
        vm.clicouNoCheckBoxConta = 0;

        vm.onClickMarcarConta = function (row) {
            row.IsChecked = true;
            vm.clicouNoCheckBoxConta = 1;

            var newItem = {
                Id: row.id,
                dtInicial: vm.date.startDate.toDate(),
                dtFinal: vm.date.endDate,
                tipoOperacao: vm.tipoOperacao,
                status: vm.status,
                EmpresaId: vm.consultaEmpresa.selectedValue,
                Ativo: false
            };
            vm.contas.push(newItem);
        };

        vm.isContaMarcada = function (row) {
            var idxOf = vm.contas.indexOf(row.Id);
            if (idxOf > -1 || row.IsChecked)
                return true;
            return row.ativo;
        };

        vm.abrirModalRelatorio = function (controllerPai) {
            $uibModal.open({
                animation: true,
                ariaLabelledBy: 'modal-title',
                ariaDescribedBy: 'modal-body',
                templateUrl: 'app/entities/painel-protocolo-abastecimento/modal/modal-relatorios.html',
                controller: function ($uibModalInstance, $uibModalStack) {
                    var vm = this;

                    vm.modalRelatorioOptions = [{}]

                    vm.headerItems = [{ name: 'Protocolos de Abastecimentos' }];

                    //Campos do "Mais detalhes"
                    //Mesmos nomes de propriedades da classe export no back pro relatorio novo
                    vm.modalRelatorioAbastecimentoOptions = [
                        {
                            name: "Codigo Abastecimento",
                            field: "codigoAbastecimento",
                            enabled: true
                        },
                        {
                            name: "Combustível",
                            field: "combustivelNome",
                            enabled: true
                        },
                        {
                            name: "Valor Abastecimento",
                            field: "valor",
                            enabled: true
                        },
                        {
                            name: "Item NF",
                            field: "itemNf",
                            enabled: true
                        },
                        {
                            name: "Pedido SAP",
                            field: "pedidoSap",
                            enabled: true
                        }
                    ]

                    for (var x in controllerPai.gridOptions.columnDefs) {
                        vm.modalRelatorioOptions[x] = {
                            name: controllerPai.gridOptions.columnDefs[x].name,
                            field: controllerPai.gridOptions.columnDefs[x].field,
                            enabled: true
                        }
                    }

                    vm.fechar = function () {
                        controllerPai.modalRelatorioOptions = vm.modalRelatorioOptions;
                        controllerPai.modalRelatorioAbastecimentoOptions = vm.modalRelatorioAbastecimentoOptions;
                        $uibModalStack.dismissAll();
                    }

                    vm.exportarRelatorio = function (extensao) {
                        if (vm.modalRelatorioOptions.filter(function (x) { return x.enabled }).length < 1) {
                            toastr.error("Escolha alguma coluna para gerar o relatório.");
                            return;
                        }

                        if (controllerPai.date.endDate.diff(controllerPai.date.startDate, 'days') > 30) {
                            toastr.error("Escolha um período de no máximo 30 dias para gerar o relatório.")
                            return;
                        }

                        controllerPai.modalRelatorioOptions = vm.modalRelatorioOptions;
                        controllerPai.modalRelatorioAbastecimentoOptions = vm.modalRelatorioAbastecimentoOptions;
                        if (vm.maisDetalhes) {
                            controllerPai.exportarRelatorio(extensao);
                        } else {
                            controllerPai.exportarRelatorioOld(extensao)
                        }
                        $uibModalStack.dismissAll();
                    }
                },
                controllerAs: 'vm',
                keyboard: true,
                size: 'lg'
            }).result.then(function () {
            });
        };


        vm.exportarRelatorio = function (extensao) {
            var idEmpresa = vm.consultaEmpresa.selectedValue;

            if (vm.date.startDate > vm.date.endDate) {
                toastr.error('O período inicial deve ser menor que o período final!');
                return;
            }

            var filteredArrayAgrupador = vm.modalRelatorioOptions.filter(function (item) {
                return item.enabled === true && item.field;
            });
            var filteredArrayItens = vm.modalRelatorioAbastecimentoOptions.filter(function (item) {
                return item.enabled === true;
            });

            var requestPayload = {
                DtInicial: vm.date.startDate._d,
                DtFinal: vm.date.endDate._d,
                Status: vm.status,
                EmpresaId: idEmpresa || 0
            };

            var objExport = {
                TipoExport: extensao,
                GridRequest: requestPayload,
                Fields: {
                    AgrupadorFields: [],
                    ItensFields: []
                }
            };

            if (extensao !== 3) {
                for (var i = 0; i < filteredArrayAgrupador.length; i++) {
                    objExport.Fields.AgrupadorFields[i] = filteredArrayAgrupador[i].name
                }

                for (var j = 0; j < filteredArrayItens.length; j++) {
                    objExport.Fields.ItensFields[j] = filteredArrayItens[j].name
                }
            } else {
                for (var w = 0; w < filteredArrayAgrupador.length; w++) {
                    objExport.Fields.AgrupadorFields[w] = filteredArrayAgrupador[w].field
                }

                for (var k = 0; k < filteredArrayItens.length; k++) {
                    objExport.Fields.ItensFields[k] = filteredArrayItens[k].field
                }
            }

            if (!vm.gridOptions.data || !vm.gridOptions.data.length) {
                toastr.error("Não existem protocolos para exportar!")
                return;
            }

            BaseService.post('ProtocoloAbastecimento', 'ExportarRelatorioControle', objExport).then(function (response) {
                if (!response) {
                    toastr.error('Erro de comunicação com o servidor, tente novamente em alguns minutos.');
                    return;
                }

                if (!response.success) {
                    toastr.error(response.message);
                    return;
                }

                switch (extensao) {
                    case 1:
                        downloadFile(response.data, 'BBC_Relatorio_Protocolo_Abastecimentos' + vm.date.startDate.toDate().toLocaleDateString().replace("/", "-") + "_a_" + vm.date.endDate.toDate().toLocaleDateString().replace("/", "-") + '.xlsx', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
                        break;
                    case 2:
                        downloadFile(response.data, 'BBC_Relatorio_Protocolo_Abastecimentos' + vm.date.startDate.toDate().toLocaleDateString().replace("/", "-") + "_a_" + vm.date.endDate.toDate().toLocaleDateString().replace("/", "-") + '.pdf', 'application/pdf');
                        break;
                    case 3:
                        downloadFile(response.data, 'BBC_Relatorio_Protocolo_Abastecimentos' + vm.date.startDate.toDate().toLocaleDateString().replace("/", "-") + "_a_" + vm.date.endDate.toDate().toLocaleDateString().replace("/", "-") + '.txt', 'text/plain');
                        break;
                    default:
                        toastr.error('Extensão de arquivo não suportada.');
                }
                toastr.success(response.message);
            });
        };

        function downloadFile(base64Data, fileName, mimeType) {
            var blob = base64ToBlob(base64Data, mimeType);
            var url = URL.createObjectURL(blob);

            var a = document.createElement('a');
            a.href = url;
            a.download = fileName;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        function base64ToBlob(base64Data, mimeType) {
            var byteCharacters = atob(base64Data);
            var byteNumbers = new Array(byteCharacters.length);

            for (var i = 0; i < byteCharacters.length; i++) {
                byteNumbers[i] = byteCharacters.charCodeAt(i);
            }

            var byteArray = new Uint8Array(byteNumbers);
            return new Blob([byteArray], { type: mimeType });
        }

        vm.exportarRelatorioOld = function (extensao) {
            if (!vm.gridOptions.data || !vm.gridOptions.data.length) {
                toastr.error("Não existem protocolos para exportar!")
                return;
            }

            switch (extensao) {
                case 1: {
                    exportarEmExcel(true)
                    break;
                }
                case 2: {
                    exportarEmPdf()
                    break;
                }
                case 3: {
                    exportarEmTxt()
                    break;
                }
                case 4: {
                    exportarEmExcel(false)
                    break;
                }
                default:
                    exportarEmPdf()
                    break;
            }

            function exportarEmPdf() {
                vm.desabilitarBtnRelatorio = true;
                vm.gridOptions.dataSource.consultarDadosRelatorio(function (response) {
                    vm.dadosRelatorio = response.data.data.items;
                    $timeout(function () {
                        BaseService.exportarTabelaEmPdfNovo(
                            "#exportable",
                            "Relatório Protocolo Abastecimentos",
                            "BBC_Relatorio_Protocolo_Abastecimentos_" + vm.date.startDate.toDate().toLocaleDateString().replace("/", "-") + "_a_" + vm.date.endDate.toDate().toLocaleDateString().replace("/", "-")
                        )
                    }, 500);
                    vm.desabilitarBtnRelatorio = false;
                })
            }

            function exportarEmExcel(formatoXls) {
                vm.desabilitarBtnRelatorio = true;
                vm.gridOptions.dataSource.consultarDadosRelatorio(function (response) {
                    vm.dadosRelatorio = response.data.data.items;
                    $timeout(function () {
                        BaseService.exportarTabelaEmExcel2("exportable-xls", "BBC_Relatorio_Protocolo_Abastecimentos_" + vm.date.startDate.toDate().toLocaleDateString().replace("/", "-") + "_a_" + vm.date.endDate.toDate().toLocaleDateString().replace("/", "-"))
                    }, 500);
                    vm.desabilitarBtnRelatorio = false;
                });
            }

            function exportarEmTxt() {
                vm.desabilitarBtnRelatorio = true;
                vm.gridOptions.dataSource.consultarDadosRelatorio(function (response) {
                    vm.dadosRelatorio = response.data.data.items;
                    $timeout(function () {
                        BaseService.exportarTabelaEmTxt("exportable", "BBC_Relatorio_Protocolo_Abastecimentos_" + vm.date.startDate.toDate().toLocaleDateString().replace("/", "-") + "_a_" + vm.date.endDate.toDate().toLocaleDateString().replace("/", "-"))
                    }, 500);
                    vm.desabilitarBtnRelatorio = false;
                });
            }
        }

        vm.onClickDesmarcarConta = function (row) {
            vm.clicouNoCheckBoxConta = 1;

            if (vm.ativarTodasConta) {
                var newItem = {
                    Id: row.id,
                    dtInicial: vm.date.startDate.toDate(),
                    dtFinal: vm.date.endDate,
                    status: vm.status,
                    EmpresaId: vm.consultaEmpresa.selectedValue,
                    Ativo: false
                };
                vm.contas.push(newItem);
            } else {
                vm.contas = vm.contas.filter(function (el) {
                    return el.Id !== row.id;
                });
            }

            row.IsChecked = false;
        };

        vm.inativarAtivarTodasContas = function () {
            vm.ativarTodasConta = !vm.ativarTodasConta;

            for (var i = 0; i < this.gridApi.grid.rows.length; i++)
                this.gridApi.grid.rows[i].entity.ativo = vm.ativarTodasConta;

        }

        $scope.$watch('vm.consultaEmpresa.selectedValue', function (newValue) {
            vm.empresaConsulta = vm.consultaEmpresa.selectedValue;
        });

        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.empresaId == null;
        }; 
        vm.gridOptions = {
            data: [],
            onRegisterApi: BaseService.dataGrid.defaultOnRegisterApi,
            urlRelatorio: "ProtocoloAbastecimento/ConsultarGridPainelProtocoloAbastecimentoRelatorio",
            dataSource: {
                autoBind: false,
                url: "ProtocoloAbastecimento/ConsultarGridPainelProtocolo",
                params: function () {
                    return {
                        DtInicial: vm.date.startDate.toDate(),
                        DtFinal: vm.date.endDate,
                        empresaId: vm.isAdmin() ? vm.consultaEmpresa.selectedValue : $rootScope.usuarioLogado.empresaId,
                        status: vm.status
                        //AtivarTodasConta : vm.ativarTodasConta
                    }
                }
            },
            columnDefs: [{
                name: ' ',
                width: 40,
                cellTemplate: '<input type=\"checkbox\" ng-model=\"row.entity.ativo\"  ng-click="grid.appScope.vm.onClickDesmarcarConta(row.entity)" ng-false-value=\'false\' />\
                    <input type=\"checkbox\" ng-show="!grid.appScope.vm.isContaMarcada(row.entity)" ng-model=\"row.entity.ativo\"  ng-click="grid.appScope.vm.onClickMarcarConta(row)" ng-true-value=\'true\'/>'
            },
            {
                name: 'Ações',
                width: '8%',
                cellTemplate: '<div ng-if="!row.groupHeader" class="ui-grid-cell-contents" title="TOOLTIP">\
                                    <div class="container-btn-action">\
                                        <button tooltip-placement="right" uib-tooltip="Exibir abastecimentos" type="button" ng-click="grid.appScope.vm.consultarProtocolo(row.entity.id)" ng-class="row.entity.status===\'Aberto\' ? \'btn btn-xs btn-info\' : \'btn btn-xs btn-info\'">\
                                            <i ng-class="\'fa fa-eye\'"></i>\
                                        </button>\
                                    </div>\
                                    <button  title="Baixar PDF" type="button" ng-click="grid.appScope.vm.baixarDocumento(row.entity, 0)"\
                                    ng-class="{ \'btn btn-xs btn-info\': true }">\
                                    <i class="fa fa-file-pdf-o"></i>\
                                    </button>\
                                    <button  title="Baixar XML" type="button" ng-click="grid.appScope.vm.baixarDocumento(row.entity, 1)"\
                                    ng-class="{ \'btn btn-xs btn-info\': true }">\
                                    <i class="fa fa-file-code-o"></i>\
                                    </button>\
                                </div>'
            },
            {
                name: 'Código Abastecimento',
                displayName: 'Código Abastecimento',
                width: 100,
                field: 'abastecimentoId',
                visible: false,
                serverField: 'abastecimentoId',
                type: 'number',
                enableFiltering: true
            },
            {
                name: 'Código',
                displayName: 'Código',
                width: 100,
                field: 'id',
                serverField: 'id',
                primaryKey: true,
                type: 'number',
                enableFiltering: true
            },
            {
                name: 'Razão Social',
                displayName: 'Razão social',
                width: 140,
                field: 'razaoSocial',
                serverField: 'razaoSocial',
                type: 'text',
                enableFiltering: false
            },
            {
                name: 'Cnpj',
                displayName: 'Cnpj',
                width: 140,
                field: 'cnpj',
                serverField: 'Posto.Cnpj',
                type: 'text',
                enableFiltering: true
            },
            {
                name: 'Data Cadastro Protocolo',
                displayName: 'Data cadastro protocolo',
                width: '*',
                minWidth: 150,
                field: 'dataCadastro',
                serverField: 'dataCadastro',
                enableFiltering: false
            },
            {
                name: 'Quantidade Litros',
                displayName: 'Quantidade de litros',
                width: '*',
                minWidth: 150,
                field: 'qtdLitrosXml',
                serverField: 'qtdLitrosXml',
                type: 'number',
                enableFiltering: true
            },
            {
                name: 'Valor Abastecido',
                displayName: 'Valor abastecido',
                width: '*',
                minWidth: 150,
                field: 'valorXml',
                serverField: 'valorXml',
             
                enableFiltering: true
            },
            {
                name: 'Pedido SAP',
                displayName: 'Pedido SAP',
                width: 100,
                type: 'text',
                field: 'pedidoSAP',
                serverField: 'pedidoSAP',
                enableFiltering: false,
                enableSorting: false
            },
            {
                name: 'NF',
                displayName: 'NF',
                width: 135,
                field: 'notaFiscal',
                serverField: 'notaFiscal',
                enableFiltering: false,
                enableSorting: false
            },
            {
                name: 'Status',
                displayName: 'Status',
                width: 170,
                field: 'status',
                serverField: 'status',
                enableFiltering: false
            }
            ]
        };

        vm.consultaEmpresa = {
            columnDefs: [{
                name: 'Cód.',
                field: 'id',
                width: 60,
                type: 'number',
                primaryKey: true
            }, {
                name: 'Nome Fantasia',
                field: 'nomeFantasia',
                width: '*'
            }, {
                name: 'Razão Social',
                field: 'razaoSocial',
                width: '*'
            }, {
                name: 'Email',
                field: 'email',
                width: 120
            }],
            desiredValue: 'id',
            desiredText: 'nomeFantasia',
            url: 'Empresa/ConsultarGridEmpresaCombo',
            paramsMethod: function () {
                return {}
            }
        };


        vm.baixarDocumento = function (documento, tipo) {
            var downloadArchive = document.createElement("a");

            if (tipo == 0) {
                downloadArchive.href = "data:application/pdf;base64," + documento.pdf;
                downloadArchive.download = "Nota_" + documento.cnpj + ".pdf";
            }
            if (tipo == 1) {
                downloadArchive.href = "data:application/pdf;base64," + documento.xml;
                downloadArchive.download = "Nota_" + documento.cnpj + ".xml";
            }

            downloadArchive.click();
        };

        vm.reprovaProtocolo = function () {
            vm.protocolosPendenteId = [];
            vm.protocolosPendente = [];

            //So os marcados
            for (var i = 0; i < this.gridApi.grid.rows.length; i++) {
                if (this.gridApi.grid.rows[i].entity.ativo)
                    vm.protocolosPendente.push(this.gridApi.grid.rows[i].entity);
            }

            for (var i = 0; i < vm.protocolosPendente.length; i++) {
                vm.protocolosPendenteId.push({ IdProtocolo: vm.protocolosPendente[i].id });

                if (vm.protocolosPendente[i].status != 'Pendente') {
                    toastr.info('Apenas itens com status de pendente são permitidos para este processo.');
                    vm.protocolosPendenteId = [];
                    vm.protocolosPendente = [];
                    return;
                }
            }

            if (vm.protocolosPendenteId.length <= 0) {
                toastr.info('Selecione pelo menos um item para realizar a aporeção!');
                return;
            }

            vm.atualizando = true;
            BaseService.post('ProtocoloAbastecimento', 'ReprovarProtocolo', vm.protocolosPendenteId
            ).then(function (response) {
                vm.atualizando = false;
                if (response.success) {
                    toastr.success(response.message);
                    vm.protocolosPendenteId = [];
                    vm.gridOptions.dataSource.refresh();
                } else {
                    toastr.error(response.message);
                    vm.protocolosPendenteId = [];
                    vm.gridOptions.dataSource.refresh();
                }

            });
        };

        vm.aprovaProtocolo = function () {
            vm.protocolosPendenteId = [];
            vm.protocolosPendente = [];

            //So os marcados
            for (var i = 0; i < this.gridApi.grid.rows.length; i++) {
                if (this.gridApi.grid.rows[i].entity.ativo)
                    vm.protocolosPendente.push(this.gridApi.grid.rows[i].entity);
            }

            for (var i = 0; i < vm.protocolosPendente.length; i++) {
                vm.protocolosPendenteId.push({ IdProtocolo: vm.protocolosPendente[i].id });

                if (vm.protocolosPendente[i].status != 'Pendente') {
                    toastr.info('Apenas itens com status de pendente são permitidos para este processo.');
                    vm.protocolosPendenteId = [];
                    vm.protocolosPendente = [];
                    return;
                }
            }

            if (vm.protocolosPendenteId.length <= 0) {
                toastr.info('Selecione pelo menos um item para realizar a aporeção!');
                return;
            }

            vm.atualizando = true;
            BaseService.post('ProtocoloAbastecimento', 'AprovarProtocolo', vm.protocolosPendenteId
            ).then(function (response) {
                vm.atualizando = false;
                if (response.success && response.data > 0) {
                    toastr.warning(response.message);
                    vm.protocolosPendenteId = [];
                    vm.gridOptions.dataSource.refresh();
                } else if (response.success) {
                    toastr.success(response.message);
                    vm.protocolosPendenteId = [];
                    vm.gridOptions.dataSource.refresh();
                } else {
                    toastr.warning(response.message);
                    vm.protocolosPendenteId = [];
                    vm.gridOptions.dataSource.refresh();
                }

            });
        };

        vm.consultarProtocolo = function (id) {
            $uibModal.open({
                animation: true,
                ariaLabelledBy: 'modal-title',
                ariaDescribedBy: 'modal-body',
                templateUrl: 'app/entities/painel-protocolo-abastecimento/modal-abastecimentos/modal-abastecimentos.html',
                controller: function ($uibModalInstance, $uibModalStack, $scope, BaseService, id) {
                    var vm = this;

                    vm.protocolo = id;

                    vm.headerItems = [{
                        name: 'Abastecimentos'
                    }];

                    vm.gridAbastecimentosOptions = {
                        data: [],
                        onRegisterApi: BaseService.dataGrid.getGridApiRegistration("gridAbastecimentosOptions"),
                        dataSource: {
                            autoBind: true,
                            url: "Abastecimento/ConsultarGridProtocoloAbastecimento",
                            params: function () {
                                return {
                                    protocolo: vm.protocolo
                                }
                            }
                        },
                        columnDefs: [{
                            name: 'Codigo',
                            displayName: 'Código',
                            width: 80,
                            field: 'id',
                            serverField: 'id',
                            primaryKey: true,
                            type: 'number',
                            enableFiltering: true
                        },
                        {
                            name: 'Combustivel',
                            displayName: 'Combustível',
                            width: 140,
                            field: 'combustivel',
                            serverField: 'combustivel',
                            enableFiltering: false,
                            enableSorting: false
                        },
                        {
                            name: 'Valor abastecimento',
                            displayName: 'Valor abastecimento',
                            width: 140,
                            field: 'valorAbastecimento',
                            serverField: 'valorAbastecimento',
                            cellTemplate: '<div class="ui-grid-cell-contents">\
                                                <input type="text" ng-model="row.entity.valorAbastecimento" readonly\
                                                        class="no-borders" style="background: none;" ui-money-mask="3" />\
                                        </div>',
                            enableFiltering: false,
                            enableSorting: false
                        },
                        {
                            name: 'ItemNotaFiscal',
                            displayName: 'Item NF',
                            width: 140,
                            field: 'itemNotaFiscal',
                            serverField: 'itemNotaFiscal',
                            enableFiltering: false,
                            enableSorting: false
                        },
                        {
                            name: 'PedidoSap',
                            displayName: 'Pedido SAP',
                            width: 140,
                            field: 'pedidoSap',
                            serverField: 'pedidoSap',
                            enableFiltering: false,
                            enableSorting: false
                        }]
                    };

                    vm.fechar = function () {
                        $uibModalStack.dismissAll();
                    }

                },
                controllerAs: 'vm',
                keyboard: true,
                size: 'lg',
                resolve: {
                    id: id
                }
            }).result.then(function () {
            });
        };

        // Controle de aba!!
        $scope.$on('$stateChangeStart', function () {
            PersistentDataService.store('PainelProtocoloAbastecimentoController', vm, "Painel protocolo abastecimento", "PainelProtocoloAbastecimentoController", "painel-protocolo-abastecimento.index");
        });

        var selfScope = PersistentDataService.get('PainelProtocoloAbastecimentoController');
    }
})();