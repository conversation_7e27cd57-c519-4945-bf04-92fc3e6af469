.logo {
    // margin-bottom     : 25px;
    width             : 270px;
    transition        : all 0.5s ease-out;
    -webkit-transition: all 0.5s ease-out;
}

.logo:hover {
    // margin-bottom     : 25px;
    width             : 280px;
    transition        : all 0.5s ease-out;
    -webkit-transition: all 0.5s ease-out;
}

.login-background {
    background-size: cover;
    height         : 100%;
    width          : 100%;
    padding        : 0;
    margin         : 0;
}

.esqueceu-senha {
    color: rgb(0, 0, 0);
}

#view-login {
    height    : auto;
    overflow-y: hidden;
    min-height: 100%;
}

.bem-vindo-bbc {
    color      : #fff;
    font-weight: bold;
}

.descricao-produto {
    font-size: 14px;
    color    : white;
}

#esqueceuSenhaBtn {
    cursor    : pointer;
    font-style: normal;


}

.center-screen {
    display        : flex;
    flex-direction : column;
    justify-content: center;
    align-items    : center;
    min-height     :
        /*100vh;*/
        75vh;

}

.body-login {
    margin-top: 8%;
    background: rgba(0, 0, 0, 0.8);
}

.center-body {
    width     : 100%;
    display   : block;
    text-align: center;
}

.center-content {
    padding-top   : 3%;
    padding-bottom: 3%;
    width         : 70%;
    display       : inline-block;
    text-align    : left !important;
}

.input-form {
    border       : none;
    color        : black;
    height       : 46px;
    padding      : 10px 16px;
    line-height  : 1.3333333;
    border-radius: 6px;
}

.video-produto {
    background     : url('../../../assets/images/youtube.jpg');
    background-size: 100%;
}

.btn-login {
    width           : 100%;
    font-size       : 1.2em;
    background-color: #056233 !important;
    color: #ffffff;
    border-color: #056233 !important;
}

// .background-login {
//    background: url("../../../assets/images/Telainicial.jpg");
// }

@media only screen and (max-width: 1200px) {

    .body-login {
        margin-top: 8%;
        overflow  : auto;
    }

    #view-login {
        height    : auto;
        overflow-y: hidden;
        min-height: 100%;
    }
}