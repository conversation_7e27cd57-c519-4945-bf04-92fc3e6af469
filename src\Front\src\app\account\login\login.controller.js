(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('LoginController', LoginController);

    LoginController.$inject = ['$window', 'toastr', '$rootScope', 'BaseService', '$uibModal', '$timeout', '$state', '$scope'];

    function LoginController($window, toastr, $rootScope, BaseService, $uibModal, $timeout, $state, $scope) {


        $rootScope.menucarregar = true; 
        $rootScope.usuarioLogado = {
            nome: "Indefinido",
            idUsuario: null,
            idGrupoUsuario: null,
            ativo: false
        };

        var vm = this;

        vm.login = {
            Usuario: "",
            Senha: "",
            dominio: document.location.origin,
            LembrarDados: "False"
        };

        vm.isLoading = false;
        document.body.style.background = "../../../assets/images/Telainicial.jpg";
        document.body.style.backgroundImage = "url('../../../assets/images/Telainicial.jpg')";

        // Login,selecionarmenu,deslogar automático 
        function onMessage(event) {
            var data = event.data;
            if (angular.isDefined(data.func) && data.func.toLowerCase() == 'login') {
                vm.login.Usuario = data.usuario;
                vm.login.Senha = data.senha;
                vm.login.dominio = data.dominio;
                vm.login.LembrarDados = data.lembrarDados;
                vm.submit();
            }
        }

        if ($window.addEventListener)
            $window.addEventListener("message", onMessage, false);
        else if ($window.attachEvent)
            $window.attachEvent("onmessage", onMessage, false);
        // Fim login automático

        vm.recuperarSenha = function () {
            $uibModal.open({
                animation: true,
                templateUrl: 'app/account/recuperar/recuperar-dialog.html',
                controller: 'RecuperarDialogController',
                controllerAs: 'recuperarDialog'
            });
        };

        vm.redirecionarCadastreSe = function () {
            $rootScope.empresaCadastrSe = true;
        }

        vm.submit = function () {
            vm.isLoading = true;
            $rootScope.imgCustomLogoDashboard = null;;
            BaseService.gerarToken(vm.login)
                .then(function (response) {
                    vm.isLoading = false;
                    if (!response) {
                        toastr.error('Erro de comunicação com o servidor, tente novamente em alguns minutos.');
                        vm.login = {
                            Usuario: "",
                            Senha: ""
                        };
                        return
                    }
                    
                    if (!response.success) {
                        toastr.error(response.message);

                        vm.login = {
                            Usuario: "",
                            Senha: ""
                        };
                        return
                    }
                    
                    $window.localStorage.setItem('SessionKey', response.data.token);
                    
                    for (var x in response.data.usuario) {
                        $window.localStorage.setItem(x.toString(), response.data.usuario[x]);
                        $rootScope.usuarioLogado[x] = response.data.usuario[x];
                    }

                    toastr.success(response.message);
                    $state.go('index.main');
                    document.body.style.backgroundColor = "#FEC726";
                    document.body.style.backgroundImage = "url('')";

                    $rootScope.goBackState = function () {
                        if (($rootScope.previousState.name !== '' &&
                            $rootScope.previousState.name !== 'login.logout' &&
                            $rootScope.previousState.name !== 'login.sessao-expirada-login') || !$rootScope.usuarioLogado.idUsuario > 0)
                            $state.go($rootScope.previousState.name);
                        else {
                            $state.go('index.main');
                        }
                    };
                });
        };
    }
})();