<style>
    .afastar-label-esquerda {
        margin-left: 5px;
    }

    .aproximar-botao-limpar {
        margin-left: -5px;
    }

    .k-grid {
        font-size: 10px;
    }
    .k-theme-test-class,
    .ktb-theme-id-nova {
    opacity: 0;
    }
    .ktb-var-accent {
    color: #dc3545;
    }
    .ktb-var-base {
    color: #ffffff;
    }
    .ktb-var-background {
    color: #ffffff;
    }
    .ktb-var-border-radius {
    border-radius: 0px;
    }
    .ktb-var-normal-background {
    color: #ffffff;
    }
    .ktb-var-normal-gradient {
    background-image: none;
    }
    .ktb-var-normal-text-color {
    color: #32364c;
    }
    .ktb-var-hover-background {
    color: #f5f6f6;
    }
    .ktb-var-hover-gradient {
    background-image: none;
    }
    .ktb-var-hover-text-color {
    color: #888e90;
    }
    .ktb-var-selected-background {
    color: #dc3545;
    }
    .ktb-var-selected-gradient {
    background-image: none;
    }
    .ktb-var-selected-text-color {
    color: #ffffff;
    }
    .ktb-var-error {
    color: #ffbfc4;
    }
    .ktb-var-warning {
    color: #ffecc7;
    }
    .ktb-var-success {
    color: #a5d6a7;
    }
    .ktb-var-info {
    color: #80deea;
    }
    .ktb-var-series-a {
    color: #dc3545;
    }
    .ktb-var-series-b {
    color: #ff9ea5;
    }
    .ktb-var-series-c {
    color: #00acc1;
    }
    .ktb-var-series-d {
    color: #80deea;
    }
    .ktb-var-series-e {
    color: #ffbf46;
    }
    .ktb-var-series-f {
    color: #ffd78c;
    }
    .k-grid-norecords-template {
    background-color: #ffffff;
    border: 1px solid #e0e0e0;
    }
    .k-button {
    border-radius: 0px;
    border-color: #9da2a4;
    color: #9da2a4;
    background-color: #ffffff;
    background-position: 50% 50%;
    background-image: none;
    background-image: none, linear-gradient(to bottom, false);
    }
    .k-button:hover,
    .k-button.k-state-hover {
    color: #9da2a4;
    border-color: #9da2a4;
    background-color: #f5f6f6;
    background-image: none;
    background-image: none, linear-gradient(to bottom, false);
    }
    .k-button:active,
    .k-button.k-state-active {
    color: #ffffff;
    background-color: #dc3545;
    border-color: #dc3545;
    background-image: none;
    background-image: none, linear-gradient(to bottom, false);
    box-shadow: none;
    }
    .k-button.k-state-active:hover {
    color: #ffffff;
    border-color: #c52f3e;
    background-color: #c52f3e;
    }
    .k-button[disabled],
    .k-state-disabled .k-button,
    .k-state-disabled .k-button:hover,
    .k-button.k-state-disabled,
    .k-button.k-state-disabled:hover {
    color: #c4c4c4;
    border-color: #f0f0f0;
    background-color: #ffffff;
    background-image: none;
    }
    .k-button[disabled],
    .k-button.k-state-disabled,
    .k-button.k-state-disabled:active {
    box-shadow: none;
    }
    .k-button:focus,
    .k-button:focus:hover,
    .k-button.k-state-focused,
    .k-button.k-state-focused.k-state-disabled,
    .k-state-disabled .k-button.k-state-focused {
    border-color: #dc3545;
    }
    .k-primary {
    color: #ffffff;
    border-color: #dc3545;
    background-color: #dc3545;
    background-image: none;
    background-image: none, linear-gradient(to bottom, false);
    }
    .k-primary:hover,
    .k-primary.k-state-hover {
    color: #ffffff;
    border-color: #c52f3e;
    background-color: #c52f3e;
    background-image: none;
    background-image: none, linear-gradient(to bottom, false);
    }
    .k-primary:active,
    .k-primary.k-state-active {
    color: #ffffff;
    border-color: #c52f3e;
    background-color: #c52f3e;
    background-image: none;
    background-image: none, linear-gradient(to bottom, false);
    }
    .k-primary[disabled],
    .k-state-disabled .k-primary,
    .k-state-disabled .k-primary:hover,
    .k-primary.k-state-disabled,
    .k-primary.k-state-disabled:hover {
    color: #ffffff;
    border-color: #ea838d;
    background-color: #ea838d;
    box-shadow: none;
    background-image: none;
    background-image: none, linear-gradient(to bottom, false);
    }
    .k-primary[disabled],
    .k-primary.k-state-disabled {
    box-shadow: none;
    }
    .k-primary:focus,
    .k-primary.k-state-focused {
    border-color: #c52f3e;
    }
    .k-button-group {
    border-radius: 0px;
    }
    .k-button-group .k-button {
    border-radius: 0;
    }
    .k-button-group .k-group-start,
    .k-button-group .k-button:first-child {
    border-top-left-radius: 0px;
    border-bottom-left-radius: 0px;
    }
    .k-button-group .k-group-end,
    .k-button-group .k-button:last-child {
    border-top-right-radius: 0px;
    border-bottom-right-radius: 0px;
    }
    .k-button-group .k-group-start.k-group-end,
    .k-button-group .k-button:first-child:last-child {
    border-radius: 0px;
    }
    .k-rtl .k-button-group .k-button {
    border-radius: 0;
    }
    .k-rtl .k-button-group .k-group-start,
    .k-rtl .k-button-group .k-button:first-child {
    border-top-right-radius: 0px;
    border-bottom-right-radius: 0px;
    }
    .k-rtl .k-button-group .k-group-end,
    .k-rtl .k-button-group .k-button:last-child {
    border-top-left-radius: 0px;
    border-bottom-left-radius: 0px;
    }
    .k-rtl .k-button-group .k-group-start.k-group-end,
    .k-rtl .k-button-group .k-button:first-child:last-child {
    border-radius: 0px;
    }
    .k-split-button {
    border-radius: 0px;
    }
    .k-split-button.k-state-border-down > .k-button,
    .k-split-button.k-state-border-up > .k-button {
    color: #9da2a4;
    background-color: #f5f6f6;
    border-color: #9da2a4;
    background-image: none;
    background-image: none, linear-gradient(to bottom, false);
    box-shadow: none;
    }
    .k-split-button:focus {
    border-color: #dc3545;
    outline: none;
    }
    .k-split-button:focus > .k-button {
    background: transparent;
    border-color: #dc3545;
    }
    .k-split-button:focus:not(.k-state-disabled) > .k-state-active,
    .k-split-button:focus:not(.k-state-disabled) > .k-button:hover {
    color: #9da2a4;
    background-color: #f5f6f6;
    border-color: #dc3545;
    background-image: none;
    background-image: none, linear-gradient(to bottom, false);
    box-shadow: none;
    }
    .k-split-button.k-state-disabled {
    color: #c4c4c4;
    background: #ffffff;
    background-image: none;
    }
    .k-edit-buttons {
    border-color: #e0e0e0;
    background: #dc3545;
    }
    .k-splitbar .k-resize-handle {
    background-color: #32364c;
    }
    .k-in,
    .k-item,
    .k-window-action {
    border-color: transparent;
    }
    .k-block,
    .k-widget {
    background-color: #ffffff;
    }
    .k-block,
    .k-widget,
    .k-input,
    .k-textbox,
    .k-group,
    .k-content,
    .k-header,
    .k-filter-row > th,
    .k-editable-area,
    .k-separator,
    .k-textbox > input,
    .k-autocomplete,
    .k-dropdown-wrap,
    .k-toolbar,
    .k-group-footer td,
    .k-grid-footer,
    .k-footer-template td,
    .k-state-default,
    .k-state-disabled,
    .k-grid-header,
    .k-grid-header-wrap,
    .k-grid-header-locked,
    .k-grid-footer-locked,
    .k-grid-content-locked,
    .k-grid td,
    .k-grid td.k-state-selected,
    .k-grid-footer-wrap,
    .k-pager-wrap,
    .k-pager-wrap .k-link,
    .k-pager-refresh,
    .k-grouping-header,
    .k-grouping-header .k-group-indicator,
    .k-panelbar > .k-item > .k-link,
    .k-panel > .k-item > .k-link,
    .k-panelbar .k-panel,
    .k-panelbar .k-content,
    .k-treemap-tile,
    .k-calendar th,
    .k-slider-track,
    .k-splitbar,
    .k-dropzone-active,
    .k-tiles,
    .k-toolbar,
    .k-tooltip,
    .k-button-group .k-tool,
    .k-upload-files,
    .k-popup.k-align .k-list .k-item:last-child,
    .k-maskedtextbox.k-state-disabled > .k-textbox:hover,
    .k-dateinput.k-state-disabled > .k-textbox:hover {
    border-color: #e0e0e0;
    }
    .k-group,
    .k-toolbar,
    .k-grouping-header,
    .k-pager-wrap,
    .k-group-footer td,
    .k-grid-footer,
    .k-footer-template td,
    .k-widget .k-status,
    .k-calendar th,
    .k-dropzone-hovered,
    .k-popup {
    background-color: #ffffff;
    }
    .k-mediaplayer-toolbar {
    background: rgba(255, 255, 255, 0.85);
    }
    .k-header,
    .k-grid-header,
    .k-grouping-header,
    .k-pager-wrap,
    .k-state-highlight,
    .k-panelbar .k-tabstrip-items .k-item {
    background-color: #dc3545;
    border-color: #dc3545;
    }
    .k-block,
    .k-widget,
    .k-popup,
    .k-content,
    .k-toolbar,
    .k-dropdown .k-input {
    color: #32364c;
    }
    .k-popup,
    .k-menu .k-menu-group,
    .k-grid .k-filter-options,
    .k-time-popup,
    .k-tooltip {
    box-shadow: 0 0px 6px 1px rgba(0, 0, 0, 0.2);
    }
    .k-content,
    .k-panelbar > li.k-item,
    .k-panel > li.k-item,
    .k-tiles {
    background-color: #ffffff;
    }
    .k-alt,
    .k-separator,
    .k-resource.k-alt,
    .k-pivot-layout > tbody > tr:first-child > td:first-child {
    background-color: #ffffff;
    }
    .k-input,
    input.k-textbox,
    textarea.k-textbox,
    input.k-textbox:hover,
    textarea.k-textbox:hover,
    .k-textbox > input,
    .k-multiselect-wrap {
    color: #32364c;
    background-color: #ffffff;
    border-color: #e0e0e0;
    }
    .k-state-selected {
    background-color: rgba(128, 222, 234, 0.25);
    }
    .k-state-focused:not(.k-button) {
    box-shadow: inset 0 0 0 1px #878787;
    }
    .k-state-hover,
    .k-state-hover:hover,
    .k-splitbar-horizontal-hover:hover,
    .k-splitbar-vertical-hover:hover,
    .k-list > .k-state-hover,
    .k-pager-wrap .k-link:hover,
    .k-dropdown .k-state-focused,
    .k-filebrowser-dropzone {
    color: #888e90;
    background-color: #f5f6f6;
    border-color: #f5f6f6;
    }
    .k-pager-wrap .k-link.k-state-disabled {
    color: #00acc1;
    }
    .k-state-disabled,
    .k-state-disabled .k-link,
    .k-dropzone em,
    .k-tile-empty strong,
    .k-slider .k-draghandle {
    color: #f0f0f0;
    }
    .k-block,
    .k-drag-clue,
    .k-touch-scrollbar,
    .k-window,
    .k-window-titleless .k-window-content,
    .k-window-action,
    .k-inline-block,
    .k-grid .k-filter-options,
    .k-grouping-header .k-group-indicator,
    .k-notification,
    .k-treeview .k-in,
    .k-editor-inline,
    .k-tile,
    .k-slider-track,
    .k-slider-selection {
    border-radius: 0px;
    }
    .k-textbox,
    .k-autocomplete,
    .k-autocomplete .k-input,
    .k-multiselect,
    .k-combobox,
    .k-dropdown,
    .k-dropdown-wrap,
    .k-datepicker,
    .k-timepicker,
    .k-colorpicker,
    .k-datetimepicker,
    .k-numerictextbox,
    .k-picker-wrap,
    .k-numeric-wrap,
    .k-tooltip,
    .k-upload,
    .k-split-button {
    border-radius: 3px;
    }
    .k-dropdown-wrap .k-input,
    .k-picker-wrap .k-input,
    .k-numeric-wrap .k-input,
    .k-toolbar .k-split-button .k-button {
    border-radius: 3px 0 0 3px;
    }
    .k-toolbar .k-split-button .k-split-button-arrow {
    border-radius: 0 3px 3px 0;
    }
    .k-hr {
    border-color: #e0e0e0;
    }
    .k-rtl .k-dropdown-wrap .k-input,
    .k-rtl .k-picker-wrap .k-input,
    .k-rtl .k-numeric-wrap .k-input {
    border-radius: 0 3px 3px 0;
    }
    .k-button .k-image,
    .k-panelbar > .k-state-active > .k-header > .k-icon,
    .k-panelbar > .k-state-active > .k-header .k-state-default .k-icon {
    opacity: .4;
    }
    .k-primary .k-icon,
    .k-state-active .k-icon,
    .k-button:active .k-icon,
    .k-numerictextbox .k-state-selected .k-icon,
    .k-multiselect-wrap .k-icon,
    .k-calendar .k-header .k-icon,
    .k-window-titlebar .k-icon,
    .k-drag-clue .k-icon,
    .k-group-indicator .k-icon,
    .k-pivot-toolbar .k-icon,
    .k-grid-content .k-button:active .k-icon,
    .k-splitbar.k-state-focused .k-icon,
    .k-scheduler-toolbar .k-icon,
    .k-gantt-toolbar .k-icon,
    .k-toolbar .k-overflow-anchor.k-state-border-down .k-i-more-vertical,
    .k-panelbar > .k-state-active > .k-header .k-state-active .k-icon {
    opacity: 1;
    }
    .k-state-disabled .k-icon,
    .k-state-disabled .k-button .k-icon,
    .k-button.k-state-disabled .k-icon {
    opacity: .7;
    }
    .k-split-button.k-state-disabled .k-icon {
    opacity: 1;
    }
    .k-i-loading {
    background-image: url('https://kendo.cdn.telerik.com/2018.1.221/styles/Nova/loading.gif');
    }
    .k-loading-image {
    background-image: url('https://kendo.cdn.telerik.com/2018.1.221/styles/Nova/loading-image.gif');
    }
    .k-loading-color {
    background-color: #ffffff;
    }
    .k-button {
    border-radius: 3px;
    }
    .k-button.k-bare {
    position: relative;
    }
    .k-button.k-bare:before {
    content: "";
    background-color: currentcolor;
    opacity: 0.12;
    border-radius: inherit;
    height: 100%;
    width: 100%;
    position: absolute;
    left: 0;
    top: 0;
    display: none;
    }
    .k-button.k-bare.k-state-focused:before,
    .k-button.k-bare:focus:before {
    display: block;
    }
    .k-mediaplayer-toolbar .k-button.k-bare:active,
    .k-mediaplayer-toolbar .k-button.k-bare.k-state-active,
    .k-mediaplayer-toolbar .k-button.k-bare.k-state-active:hover {
    color: #dc3545;
    }
    .k-mediaplayer-toolbar .k-button.k-bare:active:before,
    .k-mediaplayer-toolbar .k-button.k-bare.k-state-active:before,
    .k-mediaplayer-toolbar .k-button.k-bare.k-state-active:hover:before {
    opacity: 0.24;
    }
    .k-slider.k-mediaplayer-seekbar {
    top: -14px;
    }
    .k-quality-list {
    margin-left: -25px !important;
    }
    .k-menu {
    background-color: #ffffff;
    border-color: #ffffff;
    }
    .k-menu > .k-item.k-state-hover {
    color: #32364c;
    }
    .k-menu .k-popup .k-item {
    color: #00acc1;
    }
    .k-menu .k-item > .k-state-active {
    color: #ffffff;
    background-color: #dc3545;
    }
    .k-menu .k-state-selected.k-state-hover,
    .k-menu .k-menu-scroll-button:hover {
    background-color: #b0ebf2;
    }
    .k-menu .k-state-hover .k-link.k-state-active {
    background-color: #c52f3e;
    }
    .k-list-container {
    border-color: #e0e0e0;
    background-color: #ffffff;
    }
    .k-dropdown,
    .k-combobox,
    .k-datepicker,
    .k-datetimepicker,
    .k-colorpicker {
    background-color: #ffffff;
    }
    .k-autocomplete.k-state-default,
    .k-picker-wrap.k-state-default,
    .k-numeric-wrap.k-state-default,
    .k-dropdown-wrap.k-state-default {
    background-color: #ffffff;
    border-color: #e0e0e0;
    }
    .k-multiselect.k-header {
    background-color: #ffffff;
    border-color: #e0e0e0;
    }
    .k-autocomplete.k-state-hover,
    .k-picker-wrap.k-state-hover,
    .k-numeric-wrap.k-state-hover,
    .k-dropdown-wrap.k-state-hover {
    background-color: #f5f6f6;
    }
    .k-autocomplete.k-state-focused,
    .k-picker-wrap.k-state-focused,
    .k-numeric-wrap.k-state-focused,
    .k-dropdown-wrap.k-state-focused,
    .k-multiselect.k-header.k-state-focused {
    background-color: #ffffff;
    border-color: #9da2a4;
    box-shadow: none;
    }
    .k-autocomplete.k-state-active,
    .k-picker-wrap.k-state-active,
    .k-numeric-wrap.k-state-active,
    .k-dropdown-wrap.k-state-active,
    .k-multiselect.k-header.k-state-active {
    color: #ffffff;
    background-color: #dc3545;
    border-color: #dc3545;
    }
    .k-textbox.k-state-disabled,
    .k-state-disabled .k-picker-wrap,
    .k-autocomplete.k-state-disabled,
    .k-dropdown-wrap.k-state-disabled,
    .k-picker-wrap.k-state-disabled,
    .k-multiselect.k-state-disabled,
    .k-numeric-wrap.k-state-disabled {
    background-color: #ffffff;
    border-color: #f0f0f0;
    }
    .k-numerictextbox .k-select,
    .k-combobox .k-select,
    .k-picker-wrap .k-select {
    border-color: #ffffff;
    }
    .k-autocomplete.k-state-default.k-state-border-up,
    .k-autocomplete.k-state-default.k-state-border-down,
    .k-picker-wrap.k-state-active .k-select,
    .k-numeric-wrap.k-state-active .k-select,
    .k-dropdown-wrap.k-state-active .k-select {
    border-color: #dc3545;
    }
    .k-numeric-wrap .k-link.k-state-selected,
    .k-grid .k-numeric-wrap .k-link.k-state-selected {
    background-color: #dc3545;
    }
    .k-dropdown .k-state-active .k-input {
    color: #ffffff;
    }
    .k-colorpicker .k-selected-color {
    background-color: #ffffff;
    }
    input.k-textbox:focus {
    border-color: #9da2a4;
    }
    input.k-textbox:active {
    border-color: #dc3545;
    }
    .k-popup > .k-group-header,
    .k-popup > .k-virtual-wrap > .k-group-header {
    background: #dc3545;
    color: #ffffff;
    }
    .k-popup .k-list .k-item {
    color: #00acc1;
    }
    .k-group-header + div > .k-list > .k-item.k-first:before {
    border-top-color: #dc3545;
    }
    .k-popup .k-list .k-state-selected.k-state-focused.k-first:before {
    border-top-color: #85202a;
    }
    .k-popup .k-list .k-item > .k-group {
    background: #dc3545;
    color: #ffffff;
    }
    .k-popup .k-list .k-state-selected {
    color: #ffffff;
    background-color: #dc3545;
    border-color: #dc3545;
    }
    .k-popup .k-list .k-state-focused {
    border-color: #9c9c9c;
    box-shadow: none;
    }
    .k-popup .k-list .k-state-selected.k-state-focused {
    border-color: #85202a;
    }
    .k-popup .k-list .k-state-selected.k-state-hover {
    background-color: #b02a37;
    }
    .k-multiselect .k-button {
    color: #ffffff;
    background-color: #dc3545;
    border-color: #dc3545;
    }
    .k-multiselect.k-state-focused.k-state-border-up,
    .k-multiselect.k-state-focused.k-state-border-down {
    border-color: #dc3545;
    }
    .k-multiselect.k-header.k-state-focused .k-button,
    .k-multiselect.k-state-focused.k-state-border-up .k-button,
    .k-multiselect.k-state-focused.k-state-border-down .k-button {
    background-color: #dc3545;
    border-color: #dc3545;
    }
    .k-multiselect.k-header.k-state-hover .k-button,
    .k-multiselect .k-button:hover,
    .k-multiselect .k-button.k-state-hover {
    background-color: #c52f3e;
    border-color: #c52f3e;
    }
    .k-list-optionlabel {
    border-color: transparent;
    }
    .k-nodata {
    color: #c4c4c4;
    }
    .k-calendar th,
    .k-calendar .k-alt {
    color: #9da2a4;
    }
    .k-calendar td {
    border-radius: 50%;
    border-color: transparent;
    }
    .k-calendar .k-alt {
    border-radius: 0;
    background-color: #fafafa;
    font-weight: normal;
    }
    .k-calendar .k-content .k-link,
    .k-calendar .k-footer .k-link {
    color: #00acc1;
    }
    .k-calendar .k-state-disabled .k-link {
    color: #c5c8c9;
    }
    .k-calendar .k-header .k-link {
    color: #ffffff;
    }
    .k-calendar .k-state-hover {
    border-color: #f5f6f6;
    }
    .k-calendar .k-state-focused {
    border-color: #9da2a4;
    box-shadow: none;
    }
    .k-calendar .k-header .k-state-hover {
    background-color: #c52f3e;
    }
    .k-calendar .k-other-month,
    .k-calendar .k-other-month .k-link {
    color: #9da2a4;
    }
    .k-calendar .k-today {
    background-color: #dc3545;
    }
    .k-calendar .k-today .k-link,
    .k-calendar .k-today.k-state-hover .k-link {
    color: #ffffff;
    }
    .k-calendar .k-today.k-state-hover {
    background-color: #c52f3e;
    border-color: #c52f3e;
    }
    .k-calendar .k-state-selected {
    background-color: #e0f7fa;
    border-color: #e0f7fa;
    }
    .k-calendar .k-state-selected.k-state-hover {
    background-color: #b0ebf2;
    border-color: #b0ebf2;
    }
    .k-calendar .k-today.k-state-selected:active {
    border-color: #9c9c9c;
    }
    .k-calendar .k-today:active {
    border-color: #85202a;
    }
    .k-calendar .k-today.k-state-selected .k-link {
    color: #00acc1;
    }
    .k-calendar .k-state-selected.k-state-focused {
    border-color: #9c9c9c;
    box-shadow: none;
    }
    .k-calendar .k-footer {
    border-color: #e0e0e0;
    }
    .k-notification-info {
    background-color: #80deea;
    color: #188390;
    border-color: #80deea;
    }
    .k-notification-success {
    background-color: #a5d6a7;
    color: #4ca64f;
    border-color: #a5d6a7;
    }
    .k-notification-warning {
    background-color: #ffecc7;
    color: #ffbd3d;
    border-color: #ffecc7;
    }
    .k-notification-error {
    background-color: #ffbfc4;
    color: #ff404e;
    border-color: #ffbfc4;
    }
    .k-widget.k-tooltip,
    .k-chart-crosshair-tooltip,
    .k-chart-shared-tooltip {
    color: #32364c;
    background-color: #ffffff;
    border-color: #e0e0e0;
    box-shadow: 0 4px 10px 4px rgba(0, 0, 0, 0.2);
    }
    .k-widget.k-tooltip-validation {
    color: #ffc147;
    background-color: #ffecc7;
    border-color: #ffecc7;
    box-shadow: none;
    border-radius: 0;
    }
    .k-badge {
    background-color: #dc3545;
    color: #ffffff;
    }
    .k-treeview .k-state-hover {
    color: #32364c;
    }
    .k-treeview .k-state-selected {
    color: #dc3545;
    }
    .k-splitbar {
    background: #ffffff;
    border-color: #ffffff;
    }
    .k-splitbar.k-state-focused {
    background-color: #dc3545;
    border-color: #dc3545;
    }
    .k-window {
    box-shadow: 0 4px 10px 4px rgba(0, 0, 0, 0.2);
    }
    .k-window > .k-header {
    color: #ffffff;
    }
    .k-window > .k-header .k-state-hover {
    background-color: #c52f3e;
    }
    .k-panelbar {
    color: #32364c;
    background-color: #ffffff;
    border-color: #e0e0e0;
    }
    .k-panelbar > .k-item .k-panel > .k-item > .k-link,
    .k-panelbar > .k-item .k-panel > .k-item .k-panel > .k-item > .k-link,
    .k-panelbar .k-panel > .k-item .k-panel .k-item > .k-link {
    color: #32364c;
    }
    .k-panelbar .k-item > .k-link.k-header {
    color: #dc3545;
    background-color: #ffffff;
    }
    .k-panelbar > .k-item > .k-link.k-state-hover,
    .k-panelbar > .k-item .k-panel > .k-item > .k-link.k-state-hover {
    background-color: #f5f6f6;
    }
    .k-tabstrip.k-header {
    background-color: #ffffff;
    }
    .k-tabstrip-items {
    background-image: none;
    background-image: none, linear-gradient(to bottom, #303553 0%, #072138 100%);
    background-color: #081421;
    }
    .k-tabstrip-items .k-item {
    color: #ffffff;
    border-color: transparent;
    }
    .k-tabstrip-items .k-item.k-state-hover,
    .k-tabstrip-items .k-item.k-state-selected {
    border-top-color: #32364c;
    background-image: none;
    background-image: none, linear-gradient(to bottom, #202333 0%, #081421 100%);
    background-color: #081421;
    }
    .k-tabstrip-left > .k-tabstrip-items > .k-item.k-state-active {
    border-right-color: #dc3545;
    }
    .k-tabstrip-right > .k-tabstrip-items > .k-item.k-state-active {
    border-left-color: #dc3545;
    }
    .k-tabstrip-top > .k-tabstrip-items .k-item.k-state-active {
    background-image: none;
    background-image: none, linear-gradient(to bottom, #202333 0%, #081421 100%);
    border-bottom-color: #dc3545;
    }
    .k-tabstrip-bottom > .k-tabstrip-items > .k-item.k-state-active {
    border-top-color: #dc3545;
    }
    .k-progressbar {
    background: #cccccc;
    border-color: #cccccc;
    }
    .k-progressbar .k-state-selected {
    background-color: #dc3545;
    }
    .k-progressbar-horizontal .k-state-selected {
    border-top-color: #dc3545;
    border-bottom-color: #dc3545;
    border-left-color: #e0e0e0;
    border-right-color: #e0e0e0;
    }
    .k-progressbar-horizontal .k-state-selected.k-first {
    border-left-color: #dc3545;
    }
    .k-progressbar-horizontal .k-state-selected.k-last {
    border-right-color: #dc3545;
    }
    .k-progressbar-vertical .k-state-selected {
    border-left-color: #dc3545;
    border-right-color: #dc3545;
    border-top-color: #e0e0e0;
    border-bottom-color: #e0e0e0;
    }
    .k-progressbar-vertical > .k-complete {
    border-top-color: #dc3545;
    border-bottom-color: #dc3545;
    }
    .k-progressbar-vertical .k-state-selected.k-first {
    border-top-color: #dc3545;
    }
    .k-progressbar-vertical .k-state-selected.k-last {
    border-bottom-color: #dc3545;
    }
    .k-grid-header,
    .k-grid-header .k-header,
    .k-pager-wrap,
    .k-pager-numbers .k-state-selected,
    .k-grid-footer,
    .k-grid-footer td,
    .k-scheduler-agenda .k-scheduler-header,
    .km-pane-wrapper .k-grid-header .k-header {
    background-color: #fafafa;
    border-color: #e0e0e0;
    }
    .k-grid-header .k-header .k-link,
    .k-grid-header .k-header,
    .k-grid-header .k-link,
    .k-grid-header .k-link:link,
    .k-pager-info,
    .k-scheduler-agenda .k-scheduler-header,
    .k-scheduler-agendaview .k-scheduler-datecolumn {
    color: #9da2a4;
    }
    .k-grid .k-alt {
    background-color: #ffffff;
    }
    .k-grid tr:hover {
    background-color: #f5f6f6;
    }
    .k-grid-header th.k-state-focused,
    .k-grid td.k-state-focused {
    box-shadow: inset 0 0 0 1px #9c9c9c;
    }
    .k-grid-toolbar .k-button,
    .k-grid-content .k-button {
    color: #dc3545;
    border-color: transparent;
    }
    .k-grid-toolbar .k-button-icontext .k-icon,
    .k-grid-content .k-button-icontext .k-icon,
    .k-edit-form-container .k-edit-buttons .k-button-icontext .k-icon {
    display: none;
    }
    .k-grid-toolbar .k-button:hover {
    border-color: #f5f6f6;
    }
    .k-grid-content .k-button-icontext {
    background-color: transparent;
    }
    .k-grid-content .k-primary {
    color: #ffffff;
    background-color: #dc3545;
    }
    .k-grid-toolbar .k-button:active,
    .k-grid-content .k-button-icontext:active {
    color: #ffffff;
    background-color: #dc3545;
    border-color: #dc3545;
    }
    .k-grid-content .k-button:active {
    border-color: #dc3545;
    }
    .k-header.k-grid-toolbar {
    background-color: #ffffff;
    border-color: #e0e0e0;
    }
    .k-grid .k-grouping-header {
    color: rgba(255, 255, 255, 0.5);
    }
    .k-grouping-header .k-group-indicator {
    color: #ffffff;
    background-color: #c52f3e;
    border-color: #c52f3e;
    }
    .k-group-indicator .k-link {
    color: #ffffff;
    }
    .k-grouping-row {
    background-color: #fafafa;
    font-weight: bold;
    }
    .k-group-cell {
    background-color: #fafafa;
    }
    .k-filter-menu .k-button,
    .k-edit-form-container .k-button {
    color: #32364c;
    background: transparent;
    border-color: transparent;
    }
    .k-filter-menu .k-button:active {
    color: #32364c;
    }
    .k-filter-menu .k-primary,
    .k-edit-form-container .k-primary {
    color: #dc3545;
    }
    .k-filter-menu .k-primary {
    border-left-color: #e0e0e0;
    }
    .k-filter-menu > div > div:last-child {
    border-color: #e0e0e0;
    }
    .k-grid td.k-state-selected,
    .k-grid tr.k-state-selected > td {
    border-color: rgba(224, 224, 224, 0.8);
    }
    .k-grid tr.k-state-selected,
    .k-grid td.k-state-selected,
    .k-grid td.k-state-selected.k-state-focused {
    background-color: #e0f7fa;
    }
    .k-grid td.k-state-selected:hover,
    .k-grid tr.k-state-selected:hover td {
    background-color: #caf1f6;
    }
    .k-grid tr:hover .k-state-focused.k-state-selected,
    .k-grid tr.k-state-selected:hover td.k-state-focused {
    box-shadow: inset 0 0 0 1px #878787;
    }
    .k-grid .k-state-selected:hover .k-group-cell {
    background-color: #fafafa;
    }
    .k-marquee-color {
    background-color: #86d9e4;
    }
    .k-alt,
    .k-grid.k-alt,
    .k-fieldselector .k-item.k-header {
    background-color: #fafafa;
    border-color: #e0e0e0;
    }
    .k-pivot-toolbar .k-button {
    color: #ffffff;
    background-color: #c52f3e;
    border-color: #c52f3e;
    }
    .k-pivot-toolbar .k-empty {
    color: rgba(255, 255, 255, 0.5);
    }
    .k-fieldselector .k-item.k-header.k-state-hover {
    color: #32364c;
    }
    .k-grid-footer td,
    .k-pivot-rowheaders .k-grid-footer {
    font-weight: bold;
    }
    .k-grid .k-filter-row:hover,
    .k-pivot-rowheaders .k-grid tr:hover {
    background: none;
    }
    .k-pager-wrap,
    .k-pager-numbers .k-link,
    .k-pager-numbers .k-link:link {
    color: #32364c;
    }
    .k-pager-wrap .k-link {
    border-color: #fafafa;
    }
    .k-pager-wrap .k-link:hover {
    background-color: transparent;
    border-color: transparent;
    }
    .k-pager-numbers .k-state-selected {
    color: #dc3545;
    border-color: #dc3545 transparent transparent;
    border-radius: 0;
    }
    .k-scheduler th,
    .k-scheduler tr,
    .k-scheduler td {
    border-color: #e0e0e0;
    }
    .k-scheduler-table td.k-state-selected {
    background-color: #e0f7fa;
    }
    .k-scheduler-toolbar {
    border-color: #fafafa;
    }
    .k-scheduler .k-header .k-button,
    .k-scheduler .k-header li,
    .k-scheduler .k-header .k-link {
    color: #ffffff;
    }
    .k-scheduler .k-header .k-button {
    color: #ffffff;
    background-color: #c52f3e;
    border-color: #c52f3e;
    }
    .k-scheduler .k-header li,
    .k-scheduler .k-header .k-link,
    .k-gantt > .k-header li {
    border-color: #dc3545;
    }
    .k-scheduler .k-scheduler-toolbar ul li.k-state-hover,
    .k-scheduler .k-scheduler-toolbar .k-state-selected {
    background-color: #dc3545;
    border-color: #dc3545;
    }
    .k-scheduler .k-header .k-button:hover,
    .k-scheduler .k-scheduler-toolbar .k-scheduler-views li.k-state-hover,
    .k-scheduler .k-scheduler-toolbar .k-scheduler-views li.k-state-hover .k-link {
    background-color: #c52f3e;
    border-color: #c52f3e;
    }
    .k-scheduler .k-scheduler-toolbar .k-scheduler-views li.k-state-selected.k-state-hover,
    .k-scheduler-toolbar > .k-scheduler-views > li.k-state-selected {
    border-bottom-color: #ffffff;
    }
    .k-scheduler-footer {
    border-color: #e0e0e0;
    }
    .k-scheduler-footer > .k-header li {
    border-color: transparent;
    }
    .k-scheduler-footer > .k-header {
    background-color: #fafafa;
    border-color: #e0e0e0;
    }
    .k-scheduler-footer > .k-header .k-link {
    color: #00acc1;
    }
    .k-event,
    .k-task-complete {
    color: #ffffff;
    background: #24c6db;
    border-color: #24c6db;
    }
    .k-event.k-state-selected {
    box-shadow: 0 0 0 2px #32364c;
    }
    .k-scheduler-table .k-nonwork-hour,
    .k-scheduler-dayview .k-today.k-nonwork-hour,
    .k-scheduler-timelineview .k-today.k-nonwork-hour {
    background-color: #fafafa;
    }
    .k-scheduler-table .k-today,
    .k-today > .k-scheduler-datecolumn,
    .k-today > .k-scheduler-groupcolumn {
    background-color: #f5f5f5;
    }
    .k-scheduler-header .k-today {
    background: transparent;
    }
    table:not(.k-scheduler-dayview) .k-scheduler-header-wrap .k-today {
    color: #dc3545;
    }
    .k-gantt .k-treelist,
    .k-gantt .k-treelist .k-alt,
    .k-gantt .k-header.k-nonwork-hour,
    .k-gantt .k-treelist .k-alt,
    .k-gantt .k-header.k-nonwork-hour {
    background-color: #fafafa;
    }
    .k-gantt .k-treelist .k-state-selected,
    .k-gantt .k-treelist .k-state-selected td,
    .k-gantt .k-treelist .k-alt.k-state-selected,
    .k-gantt .k-treelist .k-alt.k-state-selected > td {
    background-color: #e0f7fa;
    }
    .k-gantt-toolbar {
    border-color: #fafafa;
    }
    .k-gantt-toolbar .k-button {
    background-color: #dc3545;
    border-color: #dc3545;
    }
    .k-gantt > .k-header li,
    .k-gantt > .k-header .k-link,
    .k-gantt-toolbar .k-button {
    color: #ffffff;
    }
    .k-gantt .k-gantt-toolbar .k-gantt-views li.k-state-hover,
    .k-gantt .k-gantt-toolbar .k-gantt-views li.k-state-hover .k-link,
    .k-gantt .k-gantt-toolbar .k-button:hover {
    background-color: #c52f3e;
    border-color: #c52f3e;
    }
    .k-gantt-toolbar > .k-gantt-views > li.k-state-selected,
    .k-gantt .k-gantt-toolbar .k-gantt-views li.k-state-selected.k-state-hover {
    border-bottom-color: #ffffff;
    }
    .k-gantt-toolbar li:first-child,
    .k-gantt-toolbar li:first-child > .k-link,
    .k-gantt-toolbar li:last-child,
    .k-gantt-toolbar li:last-child > .k-link {
    border-radius: 0;
    }
    .k-gantt .k-task-draghandle {
    border-color: #24c6db;
    }
    .k-task-dot:after {
    background-color: #9da2a4;
    border-color: #9da2a4;
    }
    .k-task-dot:hover:after {
    background-color: #32364c;
    border-color: #32364c;
    }
    .k-task-summary {
    background: #e0e0e0;
    }
    .k-task-milestone {
    background-color: #4fad53;
    }
    .k-task-summary-complete {
    background: #32364c;
    }
    .k-state-selected.k-task-summary {
    background: #92e3ed;
    }
    .k-state-selected.k-task-milestone,
    .k-state-selected .k-task-summary-complete {
    background-color: #e0f7fa;
    border-color: #e0f7fa;
    }
    .k-task-single {
    color: #ffffff;
    background-color: #92e3ed;
    border-color: #92e3ed;
    }
    .k-state-selected.k-task-single {
    border-color: #dc3545;
    }
    .k-line {
    background-color: #32364c;
    color: #32364c;
    }
    .k-state-selected.k-line {
    background-color: #dc3545;
    color: #dc3545;
    }
    .k-resource {
    background-color: #ffffff;
    }
    .k-editor.k-header,
    .editorToolbarWindow.k-header,
    .k-filebrowser .k-header {
    color: #32364c;
    background-color: #ffffff;
    border-color: #e0e0e0;
    }
    .k-editor .k-tool.k-state-selected,
    .k-window .k-editor-toolbar .k-tool.k-state-selected {
    background-color: #9da2a4;
    border-color: #9da2a4;
    }
    .k-window .k-editor-toolbar .k-state-hover {
    background-color: #f5f6f6;
    }
    .k-toolbar .k-button,
    .k-toolbar .k-button-group {
    color: #32364c;
    border-color: #e0e0e0;
    }
    .k-toolbar .k-button:active,
    .k-toolbar .k-button.k-state-active {
    color: #ffffff;
    }
    .k-toolbar .k-button-group .k-button {
    border-color: transparent;
    }
    .k-toolbar .k-button-group .k-button:active,
    .k-toolbar .k-button-group .k-button.k-state-active,
    .k-toolbar .k-overflow-anchor.k-state-active,
    .k-toolbar .k-toggle-button.k-state-active {
    background-color: #9da2a4;
    }
    .k-toolbar .k-overflow-anchor.k-state-active {
    border-color: #9da2a4;
    }
    .k-toolbar .k-overflow-anchor.k-state-border-down,
    .k-toolbar .k-overflow-anchor.k-state-border-up {
    background-color: #dc3545;
    border-color: #dc3545;
    }
    .k-toolbar .k-button.k-state-disabled,
    .k-toolbar .k-button.k-state-disabled:hover,
    .k-toolbar .k-button.k-state-disabled:active,
    .k-toolbar .k-state-disabled.k-split-button > .k-button {
    color: #c4c4c4;
    border-color: #f0f0f0;
    background-color: #ffffff;
    }
    .k-toolbar .k-button-group .k-button.k-state-disabled {
    border-color: transparent;
    }
    .k-toolbar .k-button:focus,
    .k-toolbar .k-button.k-state-disabled:focus,
    .k-toolbar .k-split-button:focus > .k-button {
    border-color: #dc3545;
    }
    .k-overflow-container .k-button,
    .k-split-container .k-button {
    color: #00acc1;
    }
    .k-overflow-container .k-button {
    border-radius: 0;
    }
    .k-overflow-container .k-overflow-group,
    .k-overflow-container .k-separator {
    border-color: #e0e0e0;
    }
    .k-overflow-container .k-overflow-button.k-state-active,
    .k-split-container .k-button.k-state-active {
    color: #ffffff;
    }
    .k-overflow-container .k-state-disabled .k-button,
    .k-overflow-container .k-state-disabled.k-button,
    .k-split-container .k-state-disabled .k-button {
    color: #c4c4c4;
    }
    .k-overflow-container .k-state-disabled .k-button:focus,
    .k-overflow-container .k-state-disabled.k-button:focus,
    .k-split-container .k-state-disabled .k-button:focus {
    border-color: #dc3545;
    }
    .k-file,
    .k-upload {
    background-color: #ffffff;
    border-color: #e0e0e0;
    }
    .k-dropzone .k-upload-status {
    color: #9da2a4;
    }
    .k-file .k-upload-status {
    color: #32364c;
    }
    .k-file-progress {
    color: #32364c;
    }
    .k-file-progress .k-progress {
    background-color: #21b6c9;
    }
    .k-file-success .k-file-name,
    .k-file-success .k-upload-pct {
    color: #4fad53;
    }
    .k-file-success .k-progress {
    background-color: #4fad53;
    }
    .k-file-error {
    color: #ff404e;
    }
    .k-file-error .k-file-extension-wrapper,
    .k-file-error .k-multiple-files-extension-wrapper {
    color: #ff404e;
    border-color: #ff404e;
    }
    .k-file-error .k-file-extension-wrapper:before,
    .k-file-error .k-multiple-files-extension-wrapper:before {
    background-color: #ffffff;
    border-color: transparent transparent #ff404e #ff404e;
    }
    .k-file-error .k-progress {
    background-color: #dc3545;
    }
    .k-file-extension-wrapper,
    .k-multiple-files-extension-wrapper {
    color: #c4c4c4;
    border-color: #c4c4c4;
    }
    .k-file-invalid .k-file-name-invalid {
    color: #ff404e;
    }
    .k-file-invalid-extension-wrapper,
    .k-multiple-files-invalid-extension-wrapper {
    color: #ff404e;
    border-color: #ff404e;
    }
    .k-file-extension-wrapper:before,
    .k-multiple-files-extension-wrapper:before {
    background-color: #ffffff;
    border-color: transparent transparent #c4c4c4 #c4c4c4;
    }
    .k-file-invalid-extension-wrapper:before,
    .k-multiple-files-invalid-extension-wrapper:before {
    background-color: #ffffff;
    border-color: transparent transparent #ff404e #ff404e;
    }
    .k-multiple-files-extension-wrapper:after {
    border-top-color: #c4c4c4;
    border-left-color: #c4c4c4;
    }
    .k-multiple-files-invalid-extension-wrapper:after {
    border-top-color: #ff404e;
    border-left-color: #ff404e;
    }
    .k-file-size,
    .k-file-information,
    .k-file-validation-message {
    color: #c4c4c4;
    }
    .k-upload .k-upload-selected {
    color: #dc3545;
    border-color: #e0e0e0;
    }
    .k-upload .k-upload-selected:hover {
    color: #ffffff;
    background-color: #dc3545;
    }
    .k-slider-track {
    background-color: #e0e0e0;
    }
    .k-slider-selection {
    background-color: #dc3545;
    }
    .k-slider-horizontal .k-tick {
    background-image: url('https://kendo.cdn.telerik.com/2018.1.221/styles/Nova/slider-h.gif');
    }
    .k-slider-vertical .k-tick {
    background-image: url('https://kendo.cdn.telerik.com/2018.1.221/styles/Nova/slider-v.gif');
    }
    .k-draghandle {
    background-color: #ffffff;
    border-color: #e0e0e0;
    box-shadow: 0 0px 6px 1px rgba(0, 0, 0, 0.2);
    border-radius: 50%;
    }
    .k-draghandle:hover {
    background-color: #f5f6f6;
    }
    .k-flatcolorpicker .k-hue-slider .k-draghandle {
    background-color: #ffffff;
    border-color: #ffffff;
    box-shadow: 0 0px 6px 1px rgba(0, 0, 0, 0.2);
    border-radius: 50%;
    }
    .k-flatcolorpicker .k-hue-slider .k-draghandle:hover,
    .k-flatcolorpicker .k-transparency-slider .k-draghandle:hover,
    .k-flatcolorpicker .k-hue-slider .k-draghandle:focus,
    .k-flatcolorpicker .k-transparency-slider .k-draghandle:focus {
    background-color: #f5f6f6;
    border-color: #f5f6f6;
    box-shadow: 0 0px 6px 1px rgba(0, 0, 0, 0.2);
    }
    .k-checkbox-label:before {
    border-color: #e0e0e0;
    background: #ffffff;
    border-radius: 3px;
    }
    .k-checkbox:hover + .k-checkbox-label:before,
    .k-checkbox:checked:hover + .k-checkbox-label:before,
    .k-checkbox-label:hover:before,
    .k-checkbox:checked + .k-checkbox-label:hover:before {
    border-color: #e0e0e0;
    box-shadow: none;
    }
    .k-checkbox:checked + .k-checkbox-label:before {
    background-color: #ffffff;
    border-color: #e0e0e0;
    color: #dc3545;
    }
    .k-checkbox:active + .k-checkbox-label:before,
    .k-checkbox-label:active:before {
    box-shadow: none;
    border-color: #dc3545;
    }
    .k-checkbox:checked:active + .k-checkbox-label:before,
    .k-checkbox:checked + .k-checkbox-label:active:before {
    box-shadow: none;
    border-color: #dc3545;
    }
    .k-checkbox:disabled + .k-checkbox-label {
    color: #b5b5b5;
    }
    .k-checkbox:disabled + .k-checkbox-label:hover:before {
    box-shadow: none;
    }
    .k-checkbox:disabled + .k-checkbox-label:before,
    .k-checkbox:checked:disabled + .k-checkbox-label:before,
    .k-checkbox:checked:disabled + .k-checkbox-label:active:before,
    .k-checkbox:checked:disabled + .k-checkbox-label:hover:before {
    color: #b5b5b5;
    background: #ffffff;
    border-color: #e0e0e0;
    border-radius: 3px;
    }
    .k-checkbox:focus + .k-checkbox-label:before,
    .k-checkbox:focus + .k-checkbox-label:hover:before {
    border-color: #dc3545;
    box-shadow: none;
    }
    .k-checkbox:indeterminate + .k-checkbox-label:after {
    background-color: #dc3545;
    background-image: none;
    border-color: #dc3545;
    border-radius: 0;
    }
    .k-checkbox:indeterminate:hover + .k-checkbox-label:after {
    border-color: #dc3545;
    background-color: #dc3545;
    }
    .k-radio-label:before {
    border-color: #e0e0e0;
    border-radius: 50%;
    background-color: #ffffff;
    border-width: 1px;
    }
    .k-radio-label:hover:before,
    .k-radio:checked + .k-radio-label:hover:before {
    border-color: #e0e0e0;
    box-shadow: none;
    }
    .k-radio:checked + .k-radio-label:after {
    background-color: #dc3545;
    border-radius: 50%;
    }
    .k-radio-label:active:before {
    border-color: #dc3545;
    box-shadow: none;
    }
    .k-radio:checked + .k-radio-label:active:before {
    box-shadow: none;
    border-color: #dc3545;
    }
    .k-radio:disabled + .k-radio-label {
    color: #e6e6e6;
    }
    .k-radio:disabled + .k-radio-label:before,
    .k-radio:disabled + .k-radio-label:active:before,
    .k-radio:disabled + .k-radio-label:hover:after,
    .k-radio:disabled + .k-radio-label:hover:before {
    background: #ffffff;
    border-color: #e0e0e0;
    box-shadow: none;
    }
    .k-radio:disabled:checked + .k-radio-label:after {
    background-color: #dc3545;
    opacity: .5;
    }
    .k-radio:focus + .k-radio-label:before {
    border-color: #dc3545;
    box-shadow: none;
    }
    .k-radio:checked + .k-radio-label:hover:before,
    .k-radio:checked + .k-radio-label:active:before {
    border-color: #dc3545;
    }
    .k-radio:disabled:checked + .k-radio-label:hover:before {
    border-color: #e0e0e0;
    }
    .k-drag-clue {
    color: #ffffff;
    box-shadow: 0 4px 10px 4px rgba(0, 0, 0, 0.2);
    }
    .k-grid-mobile .k-column-active + th.k-header {
    border-left-color: #e0e0e0;
    }
    .km-header {
    color: #dc3545;
    background-color: #ffffff;
    border-color: #ffffff;
    box-shadow: 0 0px 6px 1px rgba(0, 0, 0, 0.2);
    }
    .km-pane-wrapper > .km-pane > .km-view > .km-content {
    background-color: #fafafa;
    }
    .km-header .k-button,
    .km-header .k-button:hover {
    color: #dc3545;
    border-color: #ffffff;
    background-color: #ffffff;
    }
    html .km-pane-wrapper .km-widget,
    .k-ie .km-pane-wrapper .k-widget,
    .k-ie .km-pane-wrapper .k-group,
    .k-ie .km-pane-wrapper .k-content,
    .k-ie .km-pane-wrapper .k-header,
    .k-ie .km-pane-wrapper .k-popup-edit-form .k-edit-field .k-button,
    .km-pane-wrapper .k-mobile-list .k-item,
    .km-pane-wrapper .k-mobile-list .k-edit-label,
    .km-pane-wrapper .k-mobile-list .k-edit-field {
    color: #32364c;
    }
    @media screen and (-ms-high-contrast: active) and (-ms-high-contrast: none) {
    div.km-pane-wrapper a {
        color: #32364c;
    }
    }
    .km-pane-wrapper .k-mobile-list .k-check,
    .km-pane-wrapper .k-mobile-list .k-edit-field textarea {
    outline: none;
    }
    .km-pane-wrapper .k-mobile-list .k-item.k-state-selected {
    color: #ffffff;
    background-color: #dc3545;
    border-top-color: #dc3545;
    }
    .km-pane-wrapper .k-mobile-list > ul > li > .k-link,
    .km-pane-wrapper .k-mobile-list .k-recur-view > .k-edit-label:nth-child(3),
    .km-pane-wrapper #recurrence .km-scroll-container > .k-edit-label:first-child {
    color: normal-text-color;
    }
    .km-pane-wrapper .k-mobile-list .k-item:last-child,
    .km-pane-wrapper .k-mobile-list > ul > li > .k-link {
    border-bottom: 1px solid #e0e0e0;
    }
    .km-actionsheet .k-grid-delete,
    .km-actionsheet .k-scheduler-delete,
    .km-pane-wrapper .k-scheduler-delete,
    .km-pane-wrapper .k-filter-menu .k-button[type=reset] {
    color: #fff;
    border-color: red;
    background-color: red;
    }
    .km-actionsheet .k-grid-delete:active,
    .km-actionsheet .k-scheduler-delete:active,
    .km-pane-wrapper .k-scheduler-delete:active,
    .km-pane-wrapper .k-filter-menu .k-button[type=reset]:active {
    background-color: #990000;
    }
    .k-mobile-list .k-check:checked,
    .k-mobile-list .k-edit-field [type=checkbox]:checked,
    .k-mobile-list .k-edit-field [type=radio]:checked {
    opacity: .4;
    }
    .k-mobile-list .k-check:checked,
    .k-mobile-list .k-edit-field [type=checkbox]:checked,
    .k-mobile-list .k-edit-field [type=radio]:checked {
    background-image: url('https://kendo.cdn.telerik.com/2018.1.221/styles/Nova/sprite.png');
    border-color: transparent;
    }
    .k-mobile-list .k-check:checked,
    .k-mobile-list .k-edit-field [type=checkbox]:checked {
    font-family: 'WebComponentsIcons';
    background-image: none;
    }
    .k-mobile-list .k-check:checked:before,
    .k-mobile-list .k-edit-field [type=checkbox]:checked:before {
    content: "e118";
    }
    .k-mobile-list .k-item > .k-link:active,
    .k-mobile-list .k-item > .k-label:active,
    .k-mobile-list .k-edit-label.k-check:active,
    .k-mobile-list .k-recur-view .k-check:active {
    color: #888e90;
    background-color: #f5f6f6;
    border-color: #888e90;
    }
    .km-pane-wrapper .k-mobile-list .k-item,
    .km-pane-wrapper .k-mobile-list .k-edit-field,
    .km-pane-wrapper .k-mobile-list .k-recur-view > .k-edit-field .k-check {
    background-color: #ffffff;
    border-top: 1px solid #e0e0e0;
    }
    .km-pane-wrapper .k-mobile-list .k-recur-view > .k-edit-field .k-check:first-child {
    border-top-color: transparent;
    }
    .km-pane-wrapper .km-pane .k-mobile-list.k-filter-menu .k-space-right {
    background: #ffffff;
    border-color: #e0e0e0;
    }
    .km-pane-wrapper .km-pane .k-mobile-list.k-filter-menu .k-space-right > input {
    background-color: #ffffff;
    border-color: #e0e0e0;
    }
    .km-pane-wrapper .km-pane .k-mobile-list.k-filter-menu .k-space-right > input:focus {
    border-color: #9da2a4;
    }
    @media only screen and (max-width: 1450px) {
    .k-webkit,
    .k-ff,
    .k-ie11,
    .k-edge,
    .k-safari {
    }
    .k-webkit .k-scheduler-toolbar .k-nav-current,
    .k-ff .k-scheduler-toolbar .k-nav-current,
    .k-ie11 .k-scheduler-toolbar .k-nav-current,
    .k-edge .k-scheduler-toolbar .k-nav-current,
    .k-safari .k-scheduler-toolbar .k-nav-current {
        margin: 0;
    }
    }
    @media only screen and (max-width: 1400px) {
    .k-webkit,
    .k-ff,
    .k-ie11,
    .k-edge,
    .k-safari {
    }
    .k-webkit .k-nav-current > .k-link .k-lg-date-format,
    .k-ff .k-nav-current > .k-link .k-lg-date-format,
    .k-ie11 .k-nav-current > .k-link .k-lg-date-format,
    .k-edge .k-nav-current > .k-link .k-lg-date-format,
    .k-safari .k-nav-current > .k-link .k-lg-date-format {
        max-width: 100px;
        display: inline-block;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        vertical-align: top;
    }
    .k-webkit .k-nav-current > .k-link .k-sm-date-format,
    .k-ff .k-nav-current > .k-link .k-sm-date-format,
    .k-ie11 .k-nav-current > .k-link .k-sm-date-format,
    .k-edge .k-nav-current > .k-link .k-sm-date-format,
    .k-safari .k-nav-current > .k-link .k-sm-date-format {
        display: none;
    }
    .k-webkit .k-scheduler-toolbar .k-link,
    .k-ff .k-scheduler-toolbar .k-link,
    .k-ie11 .k-scheduler-toolbar .k-link,
    .k-edge .k-scheduler-toolbar .k-link,
    .k-safari .k-scheduler-toolbar .k-link {
        padding: 0 .5em;
    }
    }
    @media only screen and (max-width: 1420px) {
    .k-webkit .k-scheduler .k-scheduler-toolbar .k-nav-current .k-link .k-i-calendar,
    .k-ff .k-scheduler .k-scheduler-toolbar .k-nav-current .k-link .k-i-calendar,
    .k-ie11 .k-scheduler .k-scheduler-toolbar .k-nav-current .k-link .k-i-calendar,
    .k-edge .k-scheduler .k-scheduler-toolbar .k-nav-current .k-link .k-i-calendar,
    .k-safari .k-scheduler .k-scheduler-toolbar .k-nav-current .k-link .k-i-calendar {
        display: none;
    }
    }
    @media only screen and (max-width: 1024px) {
    .k-webkit,
    .k-ff,
    .k-ie11,
    .k-edge,
    .k-safari {
    }
    .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views,
    .k-ff .k-scheduler-toolbar > ul.k-scheduler-views,
    .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views,
    .k-edge .k-scheduler-toolbar > ul.k-scheduler-views,
    .k-safari .k-scheduler-toolbar > ul.k-scheduler-views {
        right: 13px;
        top: 0;
    }
    .k-webkit .k-gantt-toolbar > ul.k-gantt-views > li.k-current-view,
    .k-ff .k-gantt-toolbar > ul.k-gantt-views > li.k-current-view,
    .k-ie11 .k-gantt-toolbar > ul.k-gantt-views > li.k-current-view,
    .k-edge .k-gantt-toolbar > ul.k-gantt-views > li.k-current-view,
    .k-safari .k-gantt-toolbar > ul.k-gantt-views > li.k-current-view,
    .k-webkit .k-gantt-toolbar > ul.k-gantt-views > li.k-current-view:hover,
    .k-ff .k-gantt-toolbar > ul.k-gantt-views > li.k-current-view:hover,
    .k-ie11 .k-gantt-toolbar > ul.k-gantt-views > li.k-current-view:hover,
    .k-edge .k-gantt-toolbar > ul.k-gantt-views > li.k-current-view:hover,
    .k-safari .k-gantt-toolbar > ul.k-gantt-views > li.k-current-view:hover,
    .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view,
    .k-ff .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view,
    .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view,
    .k-edge .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view,
    .k-safari .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view,
    .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover,
    .k-ff .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover,
    .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover,
    .k-edge .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover,
    .k-safari .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover {
        background-position: 50% 50%;
        color: #ffffff;
        background-color: #dc3545;
        border-color: #dc3545;
        text-align: right;
    }
    .k-webkit .k-gantt-toolbar > ul.k-gantt-views.k-state-expanded > li,
    .k-ff .k-gantt-toolbar > ul.k-gantt-views.k-state-expanded > li,
    .k-ie11 .k-gantt-toolbar > ul.k-gantt-views.k-state-expanded > li,
    .k-edge .k-gantt-toolbar > ul.k-gantt-views.k-state-expanded > li,
    .k-safari .k-gantt-toolbar > ul.k-gantt-views.k-state-expanded > li,
    .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li,
    .k-ff .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li,
    .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li,
    .k-edge .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li,
    .k-safari .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li {
        border-radius: 0;
    }
    .k-webkit .k-gantt-toolbar > ul.k-gantt-views.k-state-expanded > .k-state-hover,
    .k-ff .k-gantt-toolbar > ul.k-gantt-views.k-state-expanded > .k-state-hover,
    .k-ie11 .k-gantt-toolbar > ul.k-gantt-views.k-state-expanded > .k-state-hover,
    .k-edge .k-gantt-toolbar > ul.k-gantt-views.k-state-expanded > .k-state-hover,
    .k-safari .k-gantt-toolbar > ul.k-gantt-views.k-state-expanded > .k-state-hover,
    .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > .k-state-hover,
    .k-ff .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > .k-state-hover,
    .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > .k-state-hover,
    .k-edge .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > .k-state-hover,
    .k-safari .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > .k-state-hover {
        background-color: #f5f6f6;
    }
    .k-webkit .k-gantt-toolbar > ul.k-gantt-views.k-state-expanded > .k-state-selected,
    .k-ff .k-gantt-toolbar > ul.k-gantt-views.k-state-expanded > .k-state-selected,
    .k-ie11 .k-gantt-toolbar > ul.k-gantt-views.k-state-expanded > .k-state-selected,
    .k-edge .k-gantt-toolbar > ul.k-gantt-views.k-state-expanded > .k-state-selected,
    .k-safari .k-gantt-toolbar > ul.k-gantt-views.k-state-expanded > .k-state-selected,
    .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > .k-state-selected,
    .k-ff .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > .k-state-selected,
    .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > .k-state-selected,
    .k-edge .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > .k-state-selected,
    .k-safari .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > .k-state-selected {
        color: #ffffff;
        background-color: #dc3545;
    }
    .k-webkit .k-gantt-toolbar > ul.k-gantt-views.k-state-expanded > .k-state-hover .k-link,
    .k-ff .k-gantt-toolbar > ul.k-gantt-views.k-state-expanded > .k-state-hover .k-link,
    .k-ie11 .k-gantt-toolbar > ul.k-gantt-views.k-state-expanded > .k-state-hover .k-link,
    .k-edge .k-gantt-toolbar > ul.k-gantt-views.k-state-expanded > .k-state-hover .k-link,
    .k-safari .k-gantt-toolbar > ul.k-gantt-views.k-state-expanded > .k-state-hover .k-link,
    .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > .k-state-hover .k-link,
    .k-ff .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > .k-state-hover .k-link,
    .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > .k-state-hover .k-link,
    .k-edge .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > .k-state-hover .k-link,
    .k-safari .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > .k-state-hover .k-link {
        background-color: transparent;
    }
    .k-webkit .k-gantt-toolbar > ul.k-gantt-views.k-state-expanded > li.k-current-view .k-link,
    .k-ff .k-gantt-toolbar > ul.k-gantt-views.k-state-expanded > li.k-current-view .k-link,
    .k-ie11 .k-gantt-toolbar > ul.k-gantt-views.k-state-expanded > li.k-current-view .k-link,
    .k-edge .k-gantt-toolbar > ul.k-gantt-views.k-state-expanded > li.k-current-view .k-link,
    .k-safari .k-gantt-toolbar > ul.k-gantt-views.k-state-expanded > li.k-current-view .k-link,
    .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li.k-current-view .k-link,
    .k-ff .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li.k-current-view .k-link,
    .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li.k-current-view .k-link,
    .k-edge .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li.k-current-view .k-link,
    .k-safari .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li.k-current-view .k-link {
        color: #ffffff;
    }
    .k-webkit .k-scheduler-toolbar > ul li:first-child,
    .k-ff .k-scheduler-toolbar > ul li:first-child,
    .k-ie11 .k-scheduler-toolbar > ul li:first-child,
    .k-edge .k-scheduler-toolbar > ul li:first-child,
    .k-safari .k-scheduler-toolbar > ul li:first-child,
    .k-webkit .k-scheduler-toolbar > ul li:first-child .k-link,
    .k-ff .k-scheduler-toolbar > ul li:first-child .k-link,
    .k-ie11 .k-scheduler-toolbar > ul li:first-child .k-link,
    .k-edge .k-scheduler-toolbar > ul li:first-child .k-link,
    .k-safari .k-scheduler-toolbar > ul li:first-child .k-link,
    .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views li,
    .k-ff .k-scheduler-toolbar > ul.k-scheduler-views li,
    .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views li,
    .k-edge .k-scheduler-toolbar > ul.k-scheduler-views li,
    .k-safari .k-scheduler-toolbar > ul.k-scheduler-views li,
    .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views li .k-link,
    .k-ff .k-scheduler-toolbar > ul.k-scheduler-views li .k-link,
    .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views li .k-link,
    .k-edge .k-scheduler-toolbar > ul.k-scheduler-views li .k-link,
    .k-safari .k-scheduler-toolbar > ul.k-scheduler-views li .k-link {
        border-radius: 0;
    }
    .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view > .k-link,
    .k-ff .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view > .k-link,
    .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view > .k-link,
    .k-edge .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view > .k-link,
    .k-safari .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view > .k-link,
    .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover > .k-link,
    .k-ff .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover > .k-link,
    .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover > .k-link,
    .k-edge .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover > .k-link,
    .k-safari .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover > .k-link {
        min-width: 20px;
    }
    .k-webkit .k-gantt-toolbar > ul.k-gantt-views.k-state-expanded .k-link,
    .k-ff .k-gantt-toolbar > ul.k-gantt-views.k-state-expanded .k-link,
    .k-ie11 .k-gantt-toolbar > ul.k-gantt-views.k-state-expanded .k-link,
    .k-edge .k-gantt-toolbar > ul.k-gantt-views.k-state-expanded .k-link,
    .k-safari .k-gantt-toolbar > ul.k-gantt-views.k-state-expanded .k-link,
    .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded .k-link,
    .k-ff .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded .k-link,
    .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded .k-link,
    .k-edge .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded .k-link,
    .k-safari .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded .k-link {
        color: #00acc1;
    }
    .k-webkit .k-gantt-toolbar > ul.k-gantt-views.k-state-expanded .k-state-selected .k-link,
    .k-ff .k-gantt-toolbar > ul.k-gantt-views.k-state-expanded .k-state-selected .k-link,
    .k-ie11 .k-gantt-toolbar > ul.k-gantt-views.k-state-expanded .k-state-selected .k-link,
    .k-edge .k-gantt-toolbar > ul.k-gantt-views.k-state-expanded .k-state-selected .k-link,
    .k-safari .k-gantt-toolbar > ul.k-gantt-views.k-state-expanded .k-state-selected .k-link,
    .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded .k-state-selected .k-link,
    .k-ff .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded .k-state-selected .k-link,
    .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded .k-state-selected .k-link,
    .k-edge .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded .k-state-selected .k-link,
    .k-safari .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded .k-state-selected .k-link {
        color: #ffffff;
    }
    .k-webkit .k-scheduler-views > li.k-state-selected > .k-link:after,
    .k-ff .k-scheduler-views > li.k-state-selected > .k-link:after,
    .k-ie11 .k-scheduler-views > li.k-state-selected > .k-link:after,
    .k-edge .k-scheduler-views > li.k-state-selected > .k-link:after,
    .k-safari .k-scheduler-views > li.k-state-selected > .k-link:after {
        display: block;
        content: "";
        position: absolute;
        top: 50%;
        margin-top: -0.5em;
        right: 0.333em;
        width: 1.333em;
        height: 1.333em;
        opacity: 0.45;
    }
    .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view .k-link:after,
    .k-ff .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view .k-link:after,
    .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view .k-link:after,
    .k-edge .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view .k-link:after,
    .k-safari .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view .k-link:after {
        background-position: -14px -30px;
    }
    .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded,
    .k-ff .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded,
    .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded,
    .k-edge .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded,
    .k-safari .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded {
        border-width: 1px 1px 0 1px;
        border-style: solid;
    }
    .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded,
    .k-ff .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded,
    .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded,
    .k-edge .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded,
    .k-safari .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded {
        border-width: 1px;
        background-image: none;
    }
    .k-webkit .k-pager-wrap,
    .k-ff .k-pager-wrap,
    .k-ie11 .k-pager-wrap,
    .k-edge .k-pager-wrap,
    .k-safari .k-pager-wrap {
        min-height: 2.7em;
    }
    .k-webkit .k-pager-wrap .k-pager-nav,
    .k-ff .k-pager-wrap .k-pager-nav,
    .k-ie11 .k-pager-wrap .k-pager-nav,
    .k-edge .k-pager-wrap .k-pager-nav,
    .k-safari .k-pager-wrap .k-pager-nav,
    .k-webkit .k-pager-input,
    .k-ff .k-pager-input,
    .k-ie11 .k-pager-input,
    .k-edge .k-pager-input,
    .k-safari .k-pager-input {
        display: inline-block;
        vertical-align: top;
    }
    .k-webkit .k-pager-numbers,
    .k-ff .k-pager-numbers,
    .k-ie11 .k-pager-numbers,
    .k-edge .k-pager-numbers,
    .k-safari .k-pager-numbers,
    .k-webkit .k-grid .k-pager-numbers,
    .k-ff .k-grid .k-pager-numbers,
    .k-ie11 .k-grid .k-pager-numbers,
    .k-edge .k-grid .k-pager-numbers,
    .k-safari .k-grid .k-pager-numbers {
        position: absolute;
        left: 5.6em;
        display: -moz-inline-flex;
        display: -webkit-inline-flex;
        display: inline-flex;
        -webkit-flex-direction: column-reverse;
        flex-direction: column-reverse;
        overflow: visible;
        height: auto;
        background-color: #ffffff;
        border: 1px solid #e0e0e0;
        border-radius: 3px;
    }
    .k-webkit .k-pager-numbers:first-child,
    .k-ff .k-pager-numbers:first-child,
    .k-ie11 .k-pager-numbers:first-child,
    .k-edge .k-pager-numbers:first-child,
    .k-safari .k-pager-numbers:first-child,
    .k-webkit .k-grid .k-pager-numbers:first-child,
    .k-ff .k-grid .k-pager-numbers:first-child,
    .k-ie11 .k-grid .k-pager-numbers:first-child,
    .k-edge .k-grid .k-pager-numbers:first-child,
    .k-safari .k-grid .k-pager-numbers:first-child {
        left: .3em;
    }
    .k-webkit .k-pager-numbers:hover,
    .k-ff .k-pager-numbers:hover,
    .k-ie11 .k-pager-numbers:hover,
    .k-edge .k-pager-numbers:hover,
    .k-safari .k-pager-numbers:hover,
    .k-webkit .k-pager-wrap .k-pager-numbers li:hover,
    .k-ff .k-pager-wrap .k-pager-numbers li:hover,
    .k-ie11 .k-pager-wrap .k-pager-numbers li:hover,
    .k-edge .k-pager-wrap .k-pager-numbers li:hover,
    .k-safari .k-pager-wrap .k-pager-numbers li:hover {
        background-color: #f5f6f6;
    }
    .k-webkit .k-pager-numbers.k-state-expanded,
    .k-ff .k-pager-numbers.k-state-expanded,
    .k-ie11 .k-pager-numbers.k-state-expanded,
    .k-edge .k-pager-numbers.k-state-expanded,
    .k-safari .k-pager-numbers.k-state-expanded,
    .k-webkit .k-grid .k-pager-numbers.k-state-expanded,
    .k-ff .k-grid .k-pager-numbers.k-state-expanded,
    .k-ie11 .k-grid .k-pager-numbers.k-state-expanded,
    .k-edge .k-grid .k-pager-numbers.k-state-expanded,
    .k-safari .k-grid .k-pager-numbers.k-state-expanded {
        -webkit-transform: translatey(-100%);
        -moz-transform: translatey(-100%);
        transform: translatey(-100%);
    }
    .k-webkit .km-pane-wrapper .k-pager-numbers,
    .k-ff .km-pane-wrapper .k-pager-numbers,
    .k-ie11 .km-pane-wrapper .k-pager-numbers,
    .k-edge .km-pane-wrapper .k-pager-numbers,
    .k-safari .km-pane-wrapper .k-pager-numbers,
    .k-webkit .km-pane-wrapper .k-grid .k-pager-numbers,
    .k-ff .km-pane-wrapper .k-grid .k-pager-numbers,
    .k-ie11 .km-pane-wrapper .k-grid .k-pager-numbers,
    .k-edge .km-pane-wrapper .k-grid .k-pager-numbers,
    .k-safari .km-pane-wrapper .k-grid .k-pager-numbers {
        position: relative;
        left: 50%;
        transform: translate(-50%, 0%);
        -webkit-transform: translate(-50%, 0%);
    }
    .k-webkit .km-pane-wrapper .k-pager-numbers.k-state-expanded,
    .k-ff .km-pane-wrapper .k-pager-numbers.k-state-expanded,
    .k-ie11 .km-pane-wrapper .k-pager-numbers.k-state-expanded,
    .k-edge .km-pane-wrapper .k-pager-numbers.k-state-expanded,
    .k-safari .km-pane-wrapper .k-pager-numbers.k-state-expanded,
    .k-webkit .km-pane-wrapper .k-grid .k-pager-numbers.k-state-expanded,
    .k-ff .km-pane-wrapper .k-grid .k-pager-numbers.k-state-expanded,
    .k-ie11 .km-pane-wrapper .k-grid .k-pager-numbers.k-state-expanded,
    .k-edge .km-pane-wrapper .k-grid .k-pager-numbers.k-state-expanded,
    .k-safari .km-pane-wrapper .k-grid .k-pager-numbers.k-state-expanded {
        -webkit-transform: translate(-50%, -100%);
        transform: translate(-50%, -100%);
    }
    .k-webkit .km-pane-wrapper .k-pager-numbers .k-link,
    .k-ff .km-pane-wrapper .k-pager-numbers .k-link,
    .k-ie11 .km-pane-wrapper .k-pager-numbers .k-link,
    .k-edge .km-pane-wrapper .k-pager-numbers .k-link,
    .k-safari .km-pane-wrapper .k-pager-numbers .k-link,
    .k-webkit .km-pane-wrapper .k-pager-numbers .k-state-selected,
    .k-ff .km-pane-wrapper .k-pager-numbers .k-state-selected,
    .k-ie11 .km-pane-wrapper .k-pager-numbers .k-state-selected,
    .k-edge .km-pane-wrapper .k-pager-numbers .k-state-selected,
    .k-safari .km-pane-wrapper .k-pager-numbers .k-state-selected,
    .k-webkit .km-pane-wrapper .k-pager-wrap > .k-link,
    .k-ff .km-pane-wrapper .k-pager-wrap > .k-link,
    .k-ie11 .km-pane-wrapper .k-pager-wrap > .k-link,
    .k-edge .km-pane-wrapper .k-pager-wrap > .k-link,
    .k-safari .km-pane-wrapper .k-pager-wrap > .k-link,
    .k-webkit .km-pane-wrapper .k-pager-wrap > .k-pager-info,
    .k-ff .km-pane-wrapper .k-pager-wrap > .k-pager-info,
    .k-ie11 .km-pane-wrapper .k-pager-wrap > .k-pager-info,
    .k-edge .km-pane-wrapper .k-pager-wrap > .k-pager-info,
    .k-safari .km-pane-wrapper .k-pager-wrap > .k-pager-info {
        padding-top: 0;
        padding-bottom: 0;
    }
    .k-webkit .k-rtl .k-pager-numbers,
    .k-ff .k-rtl .k-pager-numbers,
    .k-ie11 .k-rtl .k-pager-numbers,
    .k-edge .k-rtl .k-pager-numbers,
    .k-safari .k-rtl .k-pager-numbers,
    .k-webkit .k-rtl .k-grid .k-pager-numbers,
    .k-ff .k-rtl .k-grid .k-pager-numbers,
    .k-ie11 .k-rtl .k-grid .k-pager-numbers,
    .k-edge .k-rtl .k-grid .k-pager-numbers,
    .k-safari .k-rtl .k-grid .k-pager-numbers {
        right: 5.6em;
        width: 5.15em;
    }
    .k-webkit .k-rtl .k-pager-numbers:first-child,
    .k-ff .k-rtl .k-pager-numbers:first-child,
    .k-ie11 .k-rtl .k-pager-numbers:first-child,
    .k-edge .k-rtl .k-pager-numbers:first-child,
    .k-safari .k-rtl .k-pager-numbers:first-child,
    .k-webkit .k-rtl .k-grid .k-pager-numbers:first-child,
    .k-ff .k-rtl .k-grid .k-pager-numbers:first-child,
    .k-ie11 .k-rtl .k-grid .k-pager-numbers:first-child,
    .k-edge .k-rtl .k-grid .k-pager-numbers:first-child,
    .k-safari .k-rtl .k-grid .k-pager-numbers:first-child {
        left: auto;
        right: .3em;
    }
    .k-webkit .k-pager-numbers .k-current-page,
    .k-ff .k-pager-numbers .k-current-page,
    .k-ie11 .k-pager-numbers .k-current-page,
    .k-edge .k-pager-numbers .k-current-page,
    .k-safari .k-pager-numbers .k-current-page,
    .k-webkit .k-grid .k-pager-numbers .k-current-page,
    .k-ff .k-grid .k-pager-numbers .k-current-page,
    .k-ie11 .k-grid .k-pager-numbers .k-current-page,
    .k-edge .k-grid .k-pager-numbers .k-current-page,
    .k-safari .k-grid .k-pager-numbers .k-current-page {
        display: block;
        border-left: 0;
    }
    .k-webkit .k-pager-numbers li:not(.k-current-page),
    .k-ff .k-pager-numbers li:not(.k-current-page),
    .k-ie11 .k-pager-numbers li:not(.k-current-page),
    .k-edge .k-pager-numbers li:not(.k-current-page),
    .k-safari .k-pager-numbers li:not(.k-current-page) {
        display: none;
    }
    .k-webkit .k-pager-numbers .k-current-page .k-link,
    .k-ff .k-pager-numbers .k-current-page .k-link,
    .k-ie11 .k-pager-numbers .k-current-page .k-link,
    .k-edge .k-pager-numbers .k-current-page .k-link,
    .k-safari .k-pager-numbers .k-current-page .k-link {
        width: 3.8em;
        line-height: 2.564em;
        padding: 0 .429em 0 0.8em;
        border-radius: 3px;
        background-position: 50% 50%;
        background-color: #ffffff;
        border: 1px solid transparent;
    }
    .k-webkit .k-pager-numbers .k-current-page .k-link:hover,
    .k-ff .k-pager-numbers .k-current-page .k-link:hover,
    .k-ie11 .k-pager-numbers .k-current-page .k-link:hover,
    .k-edge .k-pager-numbers .k-current-page .k-link:hover,
    .k-safari .k-pager-numbers .k-current-page .k-link:hover {
        background-color: #f5f6f6;
    }
    .k-webkit .k-pager-numbers .k-current-page:hover .k-link,
    .k-ff .k-pager-numbers .k-current-page:hover .k-link,
    .k-ie11 .k-pager-numbers .k-current-page:hover .k-link,
    .k-edge .k-pager-numbers .k-current-page:hover .k-link,
    .k-safari .k-pager-numbers .k-current-page:hover .k-link {
        border-top: 0;
    }
    .k-webkit .k-pager-numbers .k-current-page .k-link:after,
    .k-ff .k-pager-numbers .k-current-page .k-link:after,
    .k-ie11 .k-pager-numbers .k-current-page .k-link:after,
    .k-edge .k-pager-numbers .k-current-page .k-link:after,
    .k-safari .k-pager-numbers .k-current-page .k-link:after {
        display: block;
        content: "";
        position: absolute;
        top: 50%;
        margin-top: -0.6em;
        right: 0.6em;
        width: 1.333em;
        height: 1.333em;
        opacity: .45;
    }
    .k-webkit .k-pager-numbers.k-state-expanded .k-current-page .k-link:after,
    .k-ff .k-pager-numbers.k-state-expanded .k-current-page .k-link:after,
    .k-ie11 .k-pager-numbers.k-state-expanded .k-current-page .k-link:after,
    .k-edge .k-pager-numbers.k-state-expanded .k-current-page .k-link:after,
    .k-safari .k-pager-numbers.k-state-expanded .k-current-page .k-link:after {
        background-position: -15px 0;
        opacity: 1;
    }
    .k-webkit .k-pager-numbers + .k-link,
    .k-ff .k-pager-numbers + .k-link,
    .k-ie11 .k-pager-numbers + .k-link,
    .k-edge .k-pager-numbers + .k-link,
    .k-safari .k-pager-numbers + .k-link {
        margin-left: 5.4em;
    }
    .k-webkit .k-rtl .k-pager-numbers + .k-link,
    .k-ff .k-rtl .k-pager-numbers + .k-link,
    .k-ie11 .k-rtl .k-pager-numbers + .k-link,
    .k-edge .k-rtl .k-pager-numbers + .k-link,
    .k-safari .k-rtl .k-pager-numbers + .k-link {
        margin-right: 5.4em;
        margin-left: 0;
    }
    .k-webkit .k-pager-wrap .k-pager-numbers .k-state-selected,
    .k-ff .k-pager-wrap .k-pager-numbers .k-state-selected,
    .k-ie11 .k-pager-wrap .k-pager-numbers .k-state-selected,
    .k-edge .k-pager-wrap .k-pager-numbers .k-state-selected,
    .k-safari .k-pager-wrap .k-pager-numbers .k-state-selected,
    .k-webkit .k-pager-wrap .k-pager-numbers .k-link,
    .k-ff .k-pager-wrap .k-pager-numbers .k-link,
    .k-ie11 .k-pager-wrap .k-pager-numbers .k-link,
    .k-edge .k-pager-wrap .k-pager-numbers .k-link,
    .k-safari .k-pager-wrap .k-pager-numbers .k-link {
        color: #00acc1;
        display: block;
        margin-top: 0;
        margin-right: 0;
        padding: 1px 5px 1px .8em;
        text-align: left;
        border-top: 0;
    }
    .k-webkit .k-pager-wrap .k-pager-numbers .k-pager-nav,
    .k-ff .k-pager-wrap .k-pager-numbers .k-pager-nav,
    .k-ie11 .k-pager-wrap .k-pager-numbers .k-pager-nav,
    .k-edge .k-pager-wrap .k-pager-numbers .k-pager-nav,
    .k-safari .k-pager-wrap .k-pager-numbers .k-pager-nav {
        color: #32364c;
    }
    .k-webkit .k-pager-wrap .k-pager-numbers .k-state-selected,
    .k-ff .k-pager-wrap .k-pager-numbers .k-state-selected,
    .k-ie11 .k-pager-wrap .k-pager-numbers .k-state-selected,
    .k-edge .k-pager-wrap .k-pager-numbers .k-state-selected,
    .k-safari .k-pager-wrap .k-pager-numbers .k-state-selected {
        color: #ffffff;
        background-color: #dc3545;
        border-color: #dc3545;
    }
    .k-webkit .k-pager-numbers.k-state-expanded,
    .k-ff .k-pager-numbers.k-state-expanded,
    .k-ie11 .k-pager-numbers.k-state-expanded,
    .k-edge .k-pager-numbers.k-state-expanded,
    .k-safari .k-pager-numbers.k-state-expanded {
        box-sizing: border-box;
        padding: 2px 0 0;
        border-width: 1px 1px 0 1px;
        border-style: solid;
        border-color: #e0e0e0;
        background-color: #ffffff;
        box-shadow: 0 0px 6px 1px rgba(0, 0, 0, 0.2);
    }
    .k-webkit .k-pager-numbers.k-state-expanded .k-current-page,
    .k-ff .k-pager-numbers.k-state-expanded .k-current-page,
    .k-ie11 .k-pager-numbers.k-state-expanded .k-current-page,
    .k-edge .k-pager-numbers.k-state-expanded .k-current-page,
    .k-safari .k-pager-numbers.k-state-expanded .k-current-page {
        margin: -2.2em -1px 0;
        padding: 0;
    }
    .k-webkit .k-pager-numbers.k-state-expanded .k-current-page .k-link,
    .k-ff .k-pager-numbers.k-state-expanded .k-current-page .k-link,
    .k-ie11 .k-pager-numbers.k-state-expanded .k-current-page .k-link,
    .k-edge .k-pager-numbers.k-state-expanded .k-current-page .k-link,
    .k-safari .k-pager-numbers.k-state-expanded .k-current-page .k-link {
        border-radius: 0 0 3px 3px;
        border-top: 0;
        color: #ffffff;
        background-color: #dc3545;
        border-color: #dc3545;
    }
    .k-webkit .k-pager-numbers.k-state-expanded li,
    .k-ff .k-pager-numbers.k-state-expanded li,
    .k-ie11 .k-pager-numbers.k-state-expanded li,
    .k-edge .k-pager-numbers.k-state-expanded li,
    .k-safari .k-pager-numbers.k-state-expanded li {
        display: inline-block;
    }
    .k-webkit .k-gantt-toolbar > ul.k-gantt-views,
    .k-ff .k-gantt-toolbar > ul.k-gantt-views,
    .k-ie11 .k-gantt-toolbar > ul.k-gantt-views,
    .k-edge .k-gantt-toolbar > ul.k-gantt-views,
    .k-safari .k-gantt-toolbar > ul.k-gantt-views {
        top: 0;
    }
    .k-webkit .k-widget.k-grid .k-pager-nav + .k-pager-numbers,
    .k-ff .k-widget.k-grid .k-pager-nav + .k-pager-numbers,
    .k-ie11 .k-widget.k-grid .k-pager-nav + .k-pager-numbers,
    .k-edge .k-widget.k-grid .k-pager-nav + .k-pager-numbers,
    .k-safari .k-widget.k-grid .k-pager-nav + .k-pager-numbers {
        position: absolute;
    }
    }
    @media only screen and (max-width: 678px) {
    .k-webkit .k-pager-info,
    .k-ff .k-pager-info,
    .k-ie11 .k-pager-info,
    .k-edge .k-pager-info,
    .k-safari .k-pager-info {
        display: none;
    }
    }
    @media only screen and (max-width: 530px) {
    .k-webkit .k-pager-sizes,
    .k-ff .k-pager-sizes,
    .k-ie11 .k-pager-sizes,
    .k-edge .k-pager-sizes,
    .k-safari .k-pager-sizes {
        display: none;
    }
    }
    @media only screen and (max-width: 350px) {
    .k-webkit .k-pager-refresh,
    .k-ff .k-pager-refresh,
    .k-ie11 .k-pager-refresh,
    .k-edge .k-pager-refresh,
    .k-safari .k-pager-refresh {
        display: none;
    }
    }
    .k-chart .k-mask {
    background-color: #ffffff;
    filter: alpha(opacity=68);
    -moz-opacity: 0.68;
    opacity: 0.68;
    }
    .k-chart .k-selection {
    border-color: #e0e0e0;
    }
    .k-chart .k-handle {
    width: 15px;
    height: 15px;
    border-width: 1px;
    border-style: solid;
    -moz-border-radius: 10px;
    -webkit-border-radius: 10px;
    border-radius: 10px;
    }
    .k-chart .k-left-handle {
    left: -8px;
    }
    .k-chart .k-right-handle {
    right: -8px;
    }
    .k-chart .k-handle:hover {
    background-color: #f5f6f6;
    }
    .k-chart .k-navigator-hint .k-tooltip {
    border: #e0e0e0;
    box-shadow: 0 0px 6px 1px rgba(0, 0, 0, 0.2);
    background: #ffffff;
    color: #32364c;
    }
    .k-chart .k-navigator-hint .k-scroll {
    background: #ffffff;
    height: 4px;
    }
    .k-chart-tooltip {
    background-image: none;
    }
    .k-map .k-header {
    background-color: #ffffff;
    border-color: #e0e0e0;
    }
    .k-map .k-marker {
    font-size: 28px;
    color: #dc3545;
    }
    .k-map .k-attribution {
    color: #666666;
    }
    .k-spreadsheet-row-header,
    .k-spreadsheet-column-header {
    background-color: #ffffff;
    }
    .k-spreadsheet-top-corner,
    .k-spreadsheet-row-header,
    .k-spreadsheet-column-header {
    background-color: #ffffff;
    background-image: none;
    color: #000000;
    border-color: #cccccc;
    }
    .k-spreadsheet-top-corner {
    border-color: #cccccc;
    }
    .k-spreadsheet-top-corner:after {
    border-color: transparent #cccccc #cccccc transparent;
    }
    .k-spreadsheet-pane {
    border-color: #cccccc;
    }
    .k-spreadsheet-pane .k-spreadsheet-vaxis,
    .k-spreadsheet-pane .k-spreadsheet-haxis {
    border-color: #e6e6e6;
    }
    .k-spreadsheet-pane .k-spreadsheet-column-header,
    .k-spreadsheet-pane .k-spreadsheet-row-header {
    border-color: #cccccc;
    }
    .k-spreadsheet-pane .k-spreadsheet-merged-cell {
    background-color: #ffffff;
    }
    .k-spreadsheet-pane .k-selection-partial,
    .k-spreadsheet-pane .k-selection-full {
    border-color: rgba(38, 198, 218, 0.2);
    background-color: rgba(38, 198, 218, 0.2);
    }
    .k-spreadsheet-pane .k-filter-range {
    border-color: #26c6da;
    }
    .k-spreadsheet-pane .k-spreadsheet-column-header .k-selection-partial,
    .k-spreadsheet-pane .k-spreadsheet-column-header .k-selection-full {
    border-bottom-color: #26c6da;
    }
    .k-spreadsheet-pane .k-spreadsheet-row-header .k-selection-partial,
    .k-spreadsheet-pane .k-spreadsheet-row-header .k-selection-full {
    border-right-color: #26c6da;
    }
    .k-auto-fill,
    .k-spreadsheet-selection {
    border-color: #26c6da;
    box-shadow: inset 0 0 0 1px #26c6da;
    }
    .k-auto-fill-wrapper .k-tooltip {
    background: #ffffff;
    }
    .k-spreadsheet-selection {
    background-color: rgba(38, 198, 218, 0.2);
    }
    .k-spreadsheet-active-cell {
    box-shadow: inset 0 0 0 1px #26c6da;
    background-color: #ffffff;
    }
    .k-spreadsheet-active-cell.k-right {
    box-shadow: inset 0 0 0 1px #26c6da, inset -1px 0 0 1px #26c6da;
    }
    .k-spreadsheet-active-cell.k-bottom {
    box-shadow: inset 0 0 0 1px #26c6da, inset 0 -1px 0 1px #26c6da;
    }
    .k-spreadsheet-active-cell.k-bottom.k-right {
    box-shadow: inset 0 0 0 1px #26c6da, inset -1px -1px 0 1px #26c6da;
    }
    .k-spreadsheet-active-cell.k-single {
    color: #32364c;
    background-color: #ffffff;
    }
    .k-spreadsheet .k-spreadsheet-action-bar {
    background-color: #ffffff;
    border-color: #e0e0e0;
    }
    .k-spreadsheet .k-spreadsheet-action-bar .k-spreadsheet-name-editor {
    border-color: #cccccc;
    }
    .k-spreadsheet .k-spreadsheet-action-bar .k-spreadsheet-formula-bar::before {
    border-color: #cccccc;
    }
    .k-spreadsheet .k-spreadsheet-formula-input {
    background-color: #ffffff;
    color: #32364c;
    }
    .k-spreadsheet .k-resize-handle,
    .k-spreadsheet .k-resize-hint-handle,
    .k-spreadsheet .k-resize-hint-marker {
    background-color: #dc3545;
    }
    .k-spreadsheet .k-resize-hint-vertical .k-resize-hint-handle,
    .k-spreadsheet .k-resize-hint-vertical .k-resize-hint-marker {
    background-color: #dc3545;
    }
    .k-spreadsheet .k-single-selection::after {
    background-color: #26c6da;
    border-color: #ffffff;
    }
    .k-spreadsheet .k-auto-fill-punch {
    background-color: rgba(255, 255, 255, 0.5);
    }
    .k-spreadsheet .k-single-selection.k-dim-auto-fill-handle::after {
    background-color: rgba(38, 198, 218, 0.5);
    }
    .k-spreadsheet-format-cells .k-spreadsheet-preview {
    border-color: #e0e0e0;
    }
    .k-spreadsheet-filter {
    border-radius: 0px;
    background-color: #ffffff;
    box-shadow: inset 0 0 0 1px #e6e6e6;
    }
    .k-spreadsheet-filter.k-state-active {
    color: #ffffff;
    background-color: #dc3545;
    }
    .k-spreadsheet-filter:hover {
    color: #888e90;
    background: #f5f6f6;
    border-color: #e0e3e3;
    }
    .k-action-window .k-action-buttons {
    border-color: #e0e0e0;
    background: #dc3545;
    }
    .k-spreadsheet-sample {
    color: #747ba3;
    }
    .k-state-selected .k-spreadsheet-sample {
    color: inherit;
    }
    .k-spreadsheet-window .k-list {
    border-color: #e0e0e0;
    border-radius: 0px;
    }
    .k-spreadsheet-toolbar.k-toolbar .k-button-group .k-button:not(.k-toggle-button) {
    border-radius: 0px;
    }
    .k-spreadsheet-toolbar > .k-widget,
    .k-spreadsheet-toolbar > .k-button,
    .k-spreadsheet-toolbar > .k-button-group {
    border-radius: 0px;
    }
    .k-spreadsheet-toolbar > .k-separator {
    border-color: #e0e0e0;
    }
    .k-spreadsheet-toolbar .k-overflow-anchor {
    border-radius: 0;
    }
    .k-spreadsheet-popup {
    border-radius: 0px;
    }
    .k-spreadsheet-popup .k-separator {
    background-color: #e0e0e0;
    }
    .k-spreadsheet-popup .k-button {
    background-color: transparent;
    }
    .k-spreadsheet-popup .k-button:hover {
    background-color: #f5f6f6;
    }
    .k-spreadsheet-popup .k-state-active {
    background-color: #dc3545;
    color: #000000;
    }
    .k-spreadsheet-popup .k-state-active:hover {
    background-color: #bd2130;
    }
    .k-spreadsheet-filter-menu .k-details {
    border-color: #e0e0e0;
    }
    .k-spreadsheet-filter-menu .k-details-content .k-space-right {
    background-color: #ffffff;
    }
    .k-spreadsheet-filter-menu .k-spreadsheet-value-treeview-wrapper {
    background-color: #ffffff;
    border-color: #e0e0e0;
    border-radius: 0px 0 0 0px;
    }
    .k-syntax-ref {
    color: #ff8822;
    }
    .k-syntax-num {
    color: #0099ff;
    }
    .k-syntax-func {
    font-weight: bold;
    }
    .k-syntax-str {
    color: #38b714;
    }
    .k-syntax-error {
    color: red;
    }
    .k-syntax-bool {
    color: #a9169c;
    }
    .k-syntax-startexp {
    font-weight: bold;
    }
    .k-syntax-paren-match {
    background-color: #caf200;
    }
    .k-series-a {
    border-color: #dc3545;
    background-color: rgba(220, 53, 69, 0.15);
    }
    .k-series-b {
    border-color: #ff9ea5;
    background-color: rgba(255, 158, 165, 0.15);
    }
    .k-series-c {
    border-color: #00acc1;
    background-color: rgba(0, 172, 193, 0.15);
    }
    .k-series-d {
    border-color: #80deea;
    background-color: rgba(128, 222, 234, 0.15);
    }
    .k-series-e {
    border-color: #ffbf46;
    background-color: rgba(255, 191, 70, 0.15);
    }
    .k-series-f {
    border-color: #ffd78c;
    background-color: rgba(255, 215, 140, 0.15);
    }
    .k-spreadsheet-sheets-remove:hover .k-icon {
    color: #cc2222;
    }
    .k-spreadsheet-formula-list .k-state-focused {
    background-color: #dc3545;
    color: #ffffff;
    }
    .k-spreadsheet .k-spreadsheet-quick-access-toolbar .k-button,
    .k-spreadsheet .k-spreadsheet-sheets-bar .k-button {
    box-shadow: none;
    color: #ffffff;
    border-radius: 0;
    line-height: 2.6em;
    height: calc(5.1em);
    width: 3em;
    }
    .k-spreadsheet .k-spreadsheet-quick-access-toolbar .k-button:hover,
    .k-spreadsheet .k-spreadsheet-sheets-bar .k-button:hover {
    background-image: none;
    background-image: none, linear-gradient(to bottom, #202333 0%, #081421 100%);
    border-color: #2e3350;
    }
    .k-spreadsheet .k-spreadsheet-sheets-bar .k-button {
    padding-top: .5em;
    padding-bottom: .5em;
    line-height: 2.2em;
    }
    .k-spreadsheet .k-spreadsheet-sheets-bar-add {
    left: 0;
    bottom: 0;
    }
    .k-spreadsheet .k-spreadsheet-sheets-remove {
    margin: 0 0 0 -1em;
    }
    .k-spreadsheet-sheets-items {
    color: #ffffff;
    background-image: none;
    background-image: none, linear-gradient(to bottom, #303553 0%, #072138 100%);
    }
    .k-spreadsheet-sheets-items .k-item.k-state-hover,
    .k-spreadsheet-tabstrip .k-item.k-state-hover,
    .k-spreadsheet-sheets-items .k-item.k-state-active,
    .k-spreadsheet-tabstrip .k-item.k-state-active,
    .k-spreadsheet-sheets-items .k-item.k-state-focused,
    .k-spreadsheet-tabstrip .k-item.k-state-focused {
    background-color: transparent;
    }
    .k-spreadsheet-sheets-items .k-item.k-state-hover .k-link,
    .k-spreadsheet-tabstrip .k-item.k-state-hover .k-link,
    .k-spreadsheet-sheets-items .k-item.k-state-active .k-link,
    .k-spreadsheet-tabstrip .k-item.k-state-active .k-link,
    .k-spreadsheet-sheets-items .k-item.k-state-focused .k-link,
    .k-spreadsheet-tabstrip .k-item.k-state-focused .k-link {
    color: #ffffff;
    }
    .k-spreadsheet-sheets-items .k-state-active .k-link,
    .k-spreadsheet-tabstrip .k-state-active .k-link {
    color: #ffffff;
    }
    .k-spreadsheet-toolbar > .k-button {
    border-color: transparent;
    }
    .k-window .k-popup-edit-form .k-edit-field label.k-checkbox-label {
    margin-top: 0.8em;
    }
    .k-dialog.k-alert .k-window-titlebar,
    .k-dialog.k-confirm .k-window-titlebar,
    .k-dialog.k-prompt .k-window-titlebar {
    border-bottom: none;
    }
    .k-dialog.k-alert .k-window-titlebar .k-dialog-title,
    .k-dialog.k-confirm .k-window-titlebar .k-dialog-title,
    .k-dialog.k-prompt .k-window-titlebar .k-dialog-title {
    color: #32364c;
    }
    .k-dialog .k-content {
    border-bottom-right-radius: 0px;
    border-bottom-left-radius: 0px;
    }
    .k-dialog.k-dialog-titleless .k-content {
    border-top-right-radius: 0px;
    border-top-left-radius: 0px;
    }
    .k-dialog .k-dialog-buttongroup.k-dialog-button-layout-stretched .k-button {
    padding: 1em;
    }
    .k-dialog .k-dialog-buttongroup.k-dialog-button-layout-normal {
    border-top: solid 1px #e0e0e0;
    }
    .k-dialog .k-dialog-buttongroup.k-dialog-button-layout-normal .k-button {
    border-radius: 0px;
    }
    .k-rtl .k-grid-header .k-header:first-child,
    .k-rtl .k-filter-row th:first-child,
    .k-rtl .k-grid tbody td:first-child,
    .k-rtl .k-grid tfoot td:first-child {
    border-left-width: 0;
    }
    .k-rtl .k-dialog .k-window-titlebar .k-dialog-title {
    padding-right: 1em;
    padding-left: 2em;
    }
    .k-rtl .k-dialog a.k-dialog-action.k-dialog-close {
    left: 0;
    }
    .k-rtl .k-dialog .k-dialog-buttongroup.k-dialog-button-layout-stretched .k-button:first-child {
    border-bottom-right-radius: 0px;
    }
    .k-rtl .k-dialog .k-dialog-buttongroup.k-dialog-button-layout-stretched .k-button:last-child {
    border-bottom-left-radius: 0px;
    }
    .k-rtl .k-dialog .k-dialog-buttongroup.k-dialog-button-layout-normal {
    text-align: left;
    }
    .k-rtl .k-dialog .k-dialog-buttongroup.k-dialog-button-layout-normal .k-button {
    margin-left: 0;
    margin-right: 0.5em;
    }
    .k-rtl .k-dialog .k-dialog-buttongroup.k-dialog-button-layout-normal .k-button:first-child {
    margin-right: 0;
    }
    .k-numeric-wrap .k-i-warning {
    color: #ff404e;
    position: absolute;
    top: 0;
    right: 2.2em;
    width: 2.2em;
    }
    .k-numeric-wrap.k-state-invalid {
    border-color: #ff404e;
    }
    .k-numeric-wrap.k-state-invalid input {
    color: #ff404e;
    }
    .k-rtl .k-numeric-wrap.k-state-invalid .k-i-warning {
    right: auto;
    left: 2.2em;
    }
    .k-maskedtextbox.k-state-invalid .k-textbox {
    border-color: #ff404e;
    color: #ff404e;
    }
    .k-maskedtextbox.k-state-invalid .k-i-warning {
    color: #ff404e;
    }
    .k-dateinput.k-state-invalid .k-textbox {
    color: #ff404e;
    border-color: #ff404e;
    }
    .k-dateinput.k-state-invalid .k-i-warning {
    margin-left: 0;
    margin-right: 0.8em;
    color: #ff404e;
    }
    .k-rtl .k-dateinput .k-i-warning {
    margin-right: 0;
    margin-left: 0.8em;
    }
    .k-datepicker .k-picker-wrap.k-state-invalid,
    .k-timepicker .k-picker-wrap.k-state-invalid {
    border-color: #ff404e;
    }
    .k-datepicker .k-picker-wrap.k-state-invalid .k-input,
    .k-timepicker .k-picker-wrap.k-state-invalid .k-input {
    color: #ff404e;
    }
    .k-datepicker .k-picker-wrap .k-i-warning,
    .k-timepicker .k-picker-wrap .k-i-warning {
    color: #ff404e;
    margin-left: 0;
    margin-right: 2.8em;
    }
    .k-rtl .k-datepicker .k-picker-wrap .k-i-warning,
    .k-rtl .k-timepicker .k-picker-wrap .k-i-warning {
    margin-right: 0;
    margin-left: 2.8em;
    }
    .k-datetimepicker .k-picker-wrap.k-state-invalid {
    border-color: #ff404e;
    }
    .k-datetimepicker .k-picker-wrap.k-state-invalid .k-input {
    color: #ff404e;
    }
    .k-datetimepicker .k-picker-wrap .k-i-warning {
    color: #ff404e;
    margin-left: 0;
    margin-right: 5.3em;
    }
    .k-rtl .k-datetimepicker .k-picker-wrap .k-icon.k-i-warning {
    margin-right: 0;
    margin-left: 5.3em;
    }
    .k-listbox .k-list-scroller {
    border-color: #e0e0e0;
    background-color: #ffffff;
    }
    .k-listbox .k-item {
    padding: 1px 11px;
    color: #32364c;
    }
    .k-listbox .k-item:hover:not(.k-state-disabled) {
    background-color: #f5f6f6;
    }
    .k-listbox .k-item.k-state-selected {
    color: #ffffff;
    background-color: #dc3545;
    border-color: transparent;
    }
    .k-listbox .k-item.k-state-selected:hover {
    background-color: #b02a37;
    }
    .k-listbox .k-item.k-state-disabled {
    color: #c4c4c4;
    }
    .k-listbox .k-item.k-state-focused {
    border-color: #9c9c9c;
    box-shadow: none;
    }
    .k-listbox .k-button.k-state-disabled:active .k-icon {
    opacity: .2;
    }
    .k-listbox .k-button.k-state-disabled:active .k-icon {
    opacity: .2;
    }
    .k-listbox .k-drop-hint {
    height: 0;
    border-top: 1px solid #dc3545;
    }
    .k-item.k-state-selected.k-drag-clue {
    color: #ffffff;
    background-color: #dc3545;
    }
    .k-grid-header .k-i-sort-asc-sm,
    .k-grid-header .k-i-sort-desc-sm,
    .k-grid-header .k-i-sort-asc-sm:hover,
    .k-grid-header .k-i-sort-desc-sm:hover,
    .k-grid-header .k-sort-order {
    color: #dc3545;
    opacity: 1;
    margin-bottom: -1px;
    }
    .k-menu-scroll-button {
    border-width: 0;
    box-shadow: none;
    border-color: #e0e0e0;
    background-color: #ffffff;
    }
    .k-menu-scroll-wrapper.horizontal .k-scroll-left {
    border-right-width: 1px;
    }
    .k-menu-scroll-wrapper.horizontal .k-scroll-right {
    border-left-width: 1px;
    }
    .k-menu-scroll-wrapper.vertical .k-scroll-up {
    border-bottom-width: 1px;
    }
    .k-menu-scroll-wrapper.vertical .k-scroll-down {
    border-top-width: 1px;
    }
</style>
<div ng-controller="RequestLogController as vm">
    <div class="animated fadeIn filter-position">
        <div class="row">
            <div class="col-lg-12">
                <div class="ibox">
                    <div class="ibox-title">
                        <h5>Logs de integração</h5>
                    </div>
                    <div class="ibox-content">
                        <form class="form-inline">
                            <div class="form-group">
                                <label class="control-label">
                                    Campo:
                                </label>
                                <div class="input-group afastar-label-esquerda">
                                    <input type="text" ng-model="vm.filtro.Campo" class="form-control" />
                                </div>
                            </div>
                            <div class="form-group afastar-label-esquerda">
                                <label class="control-label">
                                    Valor:
                                </label>
                                <div class="input-group afastar-label-esquerda">
                                    <input type="text" ng-model="vm.filtro.Valor" class="form-control" />
                                </div>
                            </div>
                            <button type="button" tooltip-placement="left" ng-click="vm.consultarEvent()" uib-tooltip="Consultar" class="mr-5 btn btn-labeled btn-default">
                                <span class="btn-label text-right">
                                    <i class="fa fa-filter"></i>
                                </span>
                                <span class="pl-5">Consultar</span>
                            </button>
                            <button type="button" tooltip-placement="left" ng-click="vm.limparFiltros()" class="mr-5 btn btn-labeled btn-default aproximar-botao-limpar">
                                <span class="btn-label text-right">
                                    <i class="fa fa-trash"></i>
                                </span>
                                <span class="pl-5">Limpar</span>
                            </button>
                        </form>
                        </br>
                        <div id="dialogo"></div>
                        <div id="grid"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div>