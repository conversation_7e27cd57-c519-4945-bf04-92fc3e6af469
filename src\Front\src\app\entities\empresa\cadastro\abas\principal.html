<div class="form-horizontal">
    <hr />
    <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-12 col-md-3 col-lg-3">
                        Código:
                    </label>
                    <div class="input-group col-xs-12 col-sm-12 col-md-9 col-lg-9">
                        <input type="text" ng-model="vm.empresa.id" class="form-control" disabled
                            value="{{vm.isNew()}}" />
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3 col-lg-3">
                        <span class="text-danger mr-5">*</span>CNPJ:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-9 col-lg-9">
                        <input type="text" name="CNPJ" class="form-control" ui-br-cnpj-mask ng-disabled="!vm.isNew()"
                            validate-on="blur" ng-model="vm.empresa.cnpj" ng-blur="vm.consultaContas();"
                            required-message="'CNPJ é obrigatório'" required />
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-12 col-md-3 col-lg-3">
                        <span class="text-danger mr-5">*</span>Razão social:
                    </label>
                    <div class="input-group col-xs-12 col-sm-12 col-md-9 col-lg-9">
                        <input class="form-control" type="text" name="RazaoSocial" ng-model="vm.empresa.razaoSocial"
                            validate-on="blur" required-message="'Razão Social é obrigatório'" maxlength="200"
                            ng-required="true" />
                    </div>
                </div>
            </div>

            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3 col-lg-3">
                        RNTRC:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-9 col-lg-9">
                        <input type="text" name="RNTRC" class="form-control" validate-on="blur"
                            ng-model="vm.empresa.rntrc" maxlength="8"/>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3 col-lg-3">
                        <span class="text-danger mr-5">*</span>Celular:
                    </label>
                    <div class="input-group col-xs-12 col-sm-12 col-md-9 col-lg-9">
                        <input type="text" name="Celular" class="form-control" ng-model="vm.empresa.celular"
                            ui-br-phone-number validate-on="blur" required-message="'Celular é obrigatório'" required />
                    </div>
                </div>
            </div>

            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3 col-lg-3">
                        <span class="text-danger mr-5">*</span>Nome fantasia:
                    </label>
                    <div class="input-group col-xs-12 col-sm-12 col-md-9 col-lg-9">
                        <input class="form-control" type="text" name="NomeFantasia" ng-model="vm.empresa.nomeFantasia"
                            validate-on="blur" required-message="'Nome Fantasia é obrigatório'" maxlength="200"
                            ng-required="true" />
                    </div>
                </div>
            </div>

        </div>
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3 col-lg-3">
                        <span class="text-danger mr-5">*</span>Telefone:
                    </label>
                    <div class="input-group col-xs-12 col-sm-12 col-md-9 col-lg-9">
                        <input type="text" name="Telefone" class="form-control" ng-model="vm.empresa.telefone"
                            ui-br-phone-number validate-on="blur" required-message="'Telefone é obrigatório'"
                            required="true" />
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3 col-lg-3">
                        <span class="text-danger mr-5">*</span>E-mail:
                    </label>
                    <div class="input-group col-xs-12 col-sm-9 col-md-9 col-lg-9">
                        <input type="email" name="Email" maxlength="200" class="form-control" validate-on="blur"
                            ng-model="vm.empresa.email" required-message="'E-mail é obrigatório'" required />
                        <!--<small>Caso informado mais de um e-mail, separar por ponto e vírgula.</small>-->
                    </div>
                </div>
            </div>

        </div>
        <div class="row">
            
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6" ng-show="!vm.isNew()">
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-3 col-md-3 col-lg-3">Portador:</label>
                    <p class="input-group col-xs-12 col-sm-9 col-md-9 col-lg-9">
                        <input type="text" disabled="true" name="inputBuscar" id="inputBuscar"
                        autocomplete="off"
                            ng-model="vm.empresa.nomeCnpjPortador" class="form-control" />
                        <span class="input-group-btn">
                            <button ui-sref="portador.portador-crud({link: vm.empresa.idPortador})" type="button"
                                class="btn btn-default">
                                <i class="fa fa-eye"></i>
                            </button>
                        </span>
                    </p>
                </div>
            </div>
                    <consulta-padrao-modal
                            tabledefinition="vm.consultaGrupoEmpresa"
                            label="'Grupo de empresa:'"
                            placeholder="'Selecione um grupo de empresa'"
                            required-message="'grupo de empresa é obrigatório'"
                            ng-required="false"
                            ng-disabled="!vm.isAdmin()"
                            ng-click="vm.setGrupoEmpresa()">
                    </consulta-padrao-modal>
        </div>
        <div class="row">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6 text-center">
                <div class="mb-3">
                    <img ng-show="vm.empresa.imagemCartao"
                    height="200"
                    width="350"
                        src="data:image/gif;base64,{{ vm.empresa.imagemCartao}}"
                        class="img-fluid" />
                </div>
        
                <div class="d-inline-flex align-items-center">
                    <label tooltip-placement="left"
                        uib-tooltip="Apenas arquivos nos formatos JPG, JPEG, PNG e GIF são aceitos!"
                        title="{{vm.imagemSelecionadaInput == null ? 'Selecione (Máximo 512Kb)' : vm.imagemSelecionadaInput }}"
                        for="Foto" class="btn btn-warning btn-sm mr-2">Selecione a imagem do cartão
                        <input id="Foto" name="foto" type="file" ng-model="vm.imagemUpload" do-not-parse-if-oversize
                            base-sixty-four-input ng-show="false" accept="image/png, image/jpeg, image/gif">
                    </label>
                    
                    <button type="button" tooltip-placement="right" uib-tooltip="Remover imagem"
                        ng-click="vm.imagemUpload = undefined; vm.usuario.foto = undefined; vm.empresa.imagemCartao = undefined; vm.imagemSelecionadaInput = undefined;"
                        class="btn btn-default btn-sm">
                        <i class="fa fa-close"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>