(function () {
    'use strict';

    angular.module('bbcWeb.central-pendencias-movida.state', []).config(routeConfig);

    function routeConfig($urlRouterProvider, $stateProvider) {
        $stateProvider.state('central-pendencias-movida', {
            abstract: true,
            url: "/central-pendencias-movida",
            templateUrl: "app/layout/content.html"
        }).state('central-pendencias-movida.index', {
            url: '/index',
            templateUrl: 'app/entities/central-pendencias-movida/central-pendencias-movida.html'
        }).state('central-pendencias-movida.central-pendencias-movida-crud', {
            url: '/:link',
            templateUrl: 'app/entities/central-pendencias-movida/central-pendencias-movida-crud.html'
        });
    }
})();