(function() {
    'use strict';

    angular
        .module('bbcWeb').controller('AutorizacaoContingenciaController', AutorizacaoContingenciaController);

        AutorizacaoContingenciaController.inject = ['URL_SERVER_DEV', 'BaseService', '$rootScope', 'toastr', '$scope', 'PersistentDataService', '$timeout', '$state', 'PERFIL_ADMINISTRADOR', 'DefaultsService', '$uibModal'];

    function AutorizacaoContingenciaController(URL_SERVER_DEV, BaseService, $rootScope, toastr, $scope, PersistentDataService, $timeout, $state, PERFIL_ADMINISTRADOR, DefaultsService, $uibModal) {
        var vm = this;
        
        vm.nomeBotaoSelecionarTodos = "Selecionar todos";
        vm.mSolicitacoesPendentes = [];
        vm.idPosto = "";
        vm.MotivoInterno = "";
        vm.MotivoExterno = "";
        vm.status = "";
        
        vm.saving = false;

        vm.headerItems = [{
            name: 'Administração'
        }, {
            name: 'Autorização de contingência'
        }];

       
        vm.StatusAtualizacao = [
            {data:3,label:'Aguardando Aprovação'},
            {data:2,label:'Reprovado'},
            {data:1,label:'Aprovado'}
        ];

        vm.comboStatus = [
            {data:1,label:'Aprovada'},
			{data:2,label:'Reprovada'}
        ];

        vm.dateOptions = {
            timePicker: false,
            applyClass: 'btn-primary',
            locale: {
                applyLabel: "Aplicar",
                fromLabel: "De",
                format: "DD/MM/YYYY",
                toLabel: "Até",
                cancelLabel: 'Cancelar',
                customRangeLabel: 'Período',
                monthNames: ['Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho', 'Julho', 'Agosto', 'Setembro',
                    'Outubro', 'Novembro', 'Dezembro'
                ],
                daysOfWeek: ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb']
            },
            ranges: {
                'Últimos 1 dias': [moment().add(-1, 'days'), moment()],
                'Últimos 7 dias': [moment().add(-7, 'days'), moment()]
            }
        };

        vm.date = {
            startDate: moment().add(-1, 'days'),
            endDate: moment()
        };

        var aQuerySettings = {
            "SortColumn": null,
            "PageSize": 0,
            "SortOrder": "asc",
            "PageIndex": 0,
            "Filters": {
                "Operator": "OR",
                "Rules": []
            },
            "Filtering": true
        };
        
        
        vm.consultaPostoModal = {
            columnDefs: [{
                name: 'Cód.',
                field: 'id',
                width: 60,
                type: 'number',
                primaryKey: true
            },{
                name: 'Razão Social',
                field: 'razaoSocial',
                width: '*'
            }, {
                name: 'CNPJ',
                field: 'cnpj',
                width: 200
            }],
            desiredValue: 'id',
            desiredText: 'razaoSocial',
            url: 'Posto/ConsultarGridPosto',
            paramsMethod: function () {
                return {}
            }
        }

        vm.buscar = function() {
            if (vm.date.startDate > vm.date.endDate) {
                toastr.error('O período inicial deve ser menor que o período final!');
                return;
            }
            
            var dtInicial = vm.date.startDate.toDate();
            var dtFinal = vm.date.endDate.toDate();
            var postoId = vm.consultaPostoModal.selectedValue ? vm.consultaPostoModal.selectedValue : 0;

            BaseService.get("AutorizacaoContingecia", "SolicitacoesPendentes", {
                dataInicio: dtInicial,
                dataFim: dtFinal,
                idPosto: postoId,
            }).then(function(response) {
                vm.gridOptions.dataSource.refresh();

                if (!response.success) {
                    vm.mSolicitacoesPendentes = [];
                    toastr.error(response.message);
                    return false;
                }

                if (response.data.length <= 0 && postoId > 0) {
                    toastr.info("Não existem dados para serem exibidos para o posto selecionado!");
                    return;
                }

                if (response.data.length <= 0) {
                    toastr.info("Não existem dados para serem exibidos!");
                    return;
                }

                vm.mSolicitacoesPendentes = response.data;
                for (var i = 0; i < vm.mSolicitacoesPendentes.length; i++) {
                    vm.mSolicitacoesPendentes[i].selecionado = 'N';
                }
            });
        };

        vm.LimparCampos = function() {
            vm.date = {
                startDate: moment().add(-1, 'days'),
                endDate: moment()
            };
               
            vm.mSolicitacoesPendentes = [];
            vm.consultaPostoModal.selectedValue = null;
            vm.consultaPostoModal.selectedText = "";
            vm.MotivoInterno = "";
            vm.MotivoExterno = "";
            vm.status = "";
            vm.gridOptions.data = [];
        };


        vm.MarcarTodos = function() {
            if(vm.mSolicitacoesPendentes){
                for (var i = 0; i < vm.mSolicitacoesPendentes.length; i++) {
                    vm.mSolicitacoesPendentes[i].selecionado = 'S';
                }
            }
        }

        vm.DesmarcarTodos = function() {
            if(vm.mSolicitacoesPendentes){
                for (var i = 0; i < vm.mSolicitacoesPendentes.length; i++) {
                    vm.mSolicitacoesPendentes[i].selecionado = 'N';
                }
            }
        }

        vm.salvar = function(frmAtualizacaoPrecoCombustivel) {
            if (!frmAtualizacaoPrecoCombustivel.$valid) {
                toastr.error('Todos os campos obrigatórios e/ou inválidos devem ser preenchidos corretamente com seus respectivos valores!');
                return;
            }

            var encontrouItemSelecionado = false;

            for (var i = 0; i < vm.mSolicitacoesPendentes.length; i++) {
                const element = vm.mSolicitacoesPendentes[i];
                if(element.selecionado == 'S'){
                    encontrouItemSelecionado = true;
                    break;
                }
            }

            if(!encontrouItemSelecionado){
                toastr.error("Informe ao menos um evento para aprovação!");
                return;
            }

            var lItemAdd = [];

            for (var i = 0; i < vm.mSolicitacoesPendentes.length; i++) {
                const element = vm.mSolicitacoesPendentes[i];
                if(element.selecionado == 'S'){
                    var lItemSolicitacao = {}
                    
                    lItemSolicitacao.Id = element.id;
                    lItemSolicitacao.Status = vm.status;
                    lItemSolicitacao.Motivo = vm.MotivoInterno;
                    
                    lItemAdd.push(lItemSolicitacao);
                }
            }

            var lContingenciaReq = lItemAdd;
            vm.saving = true;

            BaseService.post("AutorizacaoContingecia", "Salvar", {AutorizacaoContingeciaRequestItems: lContingenciaReq})
            .then(function(response) {

                if (!response.sucesso) {
                    toastr.error(response.mensagem);
                    vm.buscar();
                    vm.saving = false;
                    return false;
                }else{
                    toastr.success(response.mensagem);
                    vm.LimparCampos();
                    vm.saving = false;
                }
               
            });
        };

        vm.onClickLimpar = function() {
            vm.LimparCampos();
        };

        vm.onClickLimparMotivoStatus = function() {
            vm.MotivoInterno = "";
            vm.MotivoExterno = "";
            vm.status = "";
        };
        

        vm.gridOptions = {
            data: [],
            onRegisterApi: BaseService.dataGrid.defaultOnRegisterApi,
            dataSource: {
                autoBind: false,
                url: "AutorizacaoContingecia/HistoricoContingencias",
                params: function() {
                    return {
                        idPosto: vm.idPosto
                    };
                }
            },
            columnDefs: [
            {
                name: 'Código do posto',
                field: 'postoId',
                width: '*',
                enableFiltering: false,
                minWidth: 120,
                enableSorting: false
            },
            {
                name: 'Nome do posto',
                field: 'nomePosto',
                minWidth: 120,
                width: '*',
                enableFiltering: false,
                enableSorting: false
            },
            {
                name: 'Placa',
                field: 'placa',
                minWidth: 80,
                width: '*',
                enableFiltering: false,
                enableSorting: false
            },
            {
                displayName: 'Nome do combustível',
                field: 'nomeCombustivel',
                minWidth: 120,
                width: '*',
                enableFiltering: false,
                enableSorting: false
            },
            {
                displayName: 'Litragem',
                field: 'litragem',
                minWidth: 120,
                width: '*',
                enableFiltering: false,
                cellTemplate: '<div class="ui-grid-cell-contents">\
                                    <input type="text" ng-model="row.entity.litragem" readonly\
                                            class="no-borders" style="background: none;" ui-number-mask="3" />\
                               </div>',
                enableSorting: false
            },
            {
                displayName: 'Valor',
                field: 'valor',
                minWidth: 120,
                width: '*',
                cellTemplate: '<div class="ui-grid-cell-contents">\
                                    <input type="text" ng-model="row.entity.valor" readonly\
                                            class="no-borders" style="background: none;" ui-money-mask="3" />\
                               </div>',
                enableFiltering: false,
                enableSorting: false
            },
            {
                displayName: 'Status',
                field: 'status',
                width: '*',
                minWidth: 120,
                enableFiltering: false,
                enableSorting: false
            },
            {
                displayName: 'Data solicitação',
                field: 'dataCadastro',
                minWidth: 120,
                width: '*',
                enableFiltering: false,
                enableSorting: false
            },
            {
                displayName: 'Motivo solicitação',
                field: 'motivo',
                minWidth: 120,
                width: '*',
                enableFiltering: false,
                enableSorting: false
            },
            {
                displayName: 'Usuário alteração',
                field: 'usuarioAlteracao',
                minWidth: 120,
                width: '*',
                enableFiltering: false,
                enableSorting: false
            },
            {
                displayName: 'Data alteração',
                field: 'dataAlteracao',
                minWidth: 120,
                width: '*',
                enableFiltering: false,
                enableSorting: false
            }]
        };

        $scope.$watch('vm.consultaPostoModal.selectedValue', function (value) {
            if(value){
                vm.idPosto = value;
            } else {
                vm.idPosto = 0;
            }
            vm.mSolicitacoesPendentes = [];
            vm.gridOptions.data = [];
        });

        vm.checkBoxSelecionado = function(item) {
            if (!angular.isUndefinedOrNullOrEmpty(item)) 
                item.selecionado = item.selecionado == 'S' ? 'N' : 'S';
        };

        //Remove evento click nas setas
        $(document).off('click', 'table .arrow');
        //Evento click nas setas para mostrar os dados
        $(document).on('click', 'table .arrow', function() {
            var $tr = $(this).parent().parent().parent().find('tr.subItem');
            if (!$($tr).is(':visible')) {
                $($tr).show();
                $(this).attr("class", "arrow rotated");
            } else {
                $($tr).hide();
                $(this).attr("class", "arrow");
            }
        });

        
    }
})();