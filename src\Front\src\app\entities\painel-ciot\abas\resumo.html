<style>
    .font-text-title {
        color: black;
        text-align: left;
        font-weight: bold;
        font-size: 20px;
    }

    .button-edit-style {
        padding: 0px;
        margin-left: -8px;
        margin-top: 8px;
        background-color: transparent;
        border-color: #565656;
        color: #565656;
        font-weight: bold;
        font-size: 13px;
    }

    .labels-resume-style {
        color: #606060;
        font-weight: bold
    }
</style>

<div class="form-horizontal">
    <div class="navbar navbar-default" style="background-color: rgba(204, 204, 204, 0.4)">
        <div>
            <a class="col-xs-9 col-sm-11 navbar-brand"
                style="color: black; text-align: left; font-weight: bold; font-size: 20px">Principal</a>
            <a class="col-xs-3 col-sm-1 btn btn-primary; button-edit-style" role="button"
                ng-click="wizard.go(1)">Editar</a>
        </div>

        <div class="row" style="text-align: left; padding: 16px; line-height: 25px;">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6" style="font-size: 13px;">
                <a class="labels-resume-style">Tipo viagem: </a>
                {{vm.painelCiot.tipo ? vm.consultaDescricaoTipo() : 'Não informado'}}
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6" style="font-size: 13px;">
                <a class="labels-resume-style">CIOT: </a> {{vm.painelCiot.ciot ? vm.painelCiot.ciot : 'Não informado'}}
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6" style="font-size: 13px;">
                <a class="labels-resume-style">Nome do proprietário: </a>
                {{vm.consultaProprietario.selectedText ? vm.consultaProprietario.selectedText : 'Não informado'}}
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6" style="font-size: 13px;">
                <a class="labels-resume-style">CPF/CNPJ: </a>
                {{vm.painelCiot.cpfCnpjProp ? vm.mascaraCpfCnpj(vm.painelCiot.cpfCnpjProp) : 'Não informado'}}
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6" style="font-size: 13px;">
                <a class="labels-resume-style">Nome do motorista: </a>
                {{vm.consultaMotorista.selectedText ? vm.consultaMotorista.selectedText : 'Não informado'}}
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6" style="font-size: 13px;">
                <a class="labels-resume-style">CPF: </a>
                {{vm.painelCiot.cpfCnpjMot ? vm.mascaraCpfCnpj(vm.painelCiot.cpfCnpjMot) : 'Não informado'}}
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6" style="font-size: 13px;">
                <a class="labels-resume-style">Data final do contrato: </a>
                {{vm.painelCiot.dataFim ? (vm.painelCiot.dataFim | date:"dd/MM/yyyy") : 'Não informado'}}
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6" style="font-size: 13px;">
                <a class="labels-resume-style">Quantidade das tarifas: </a>
                {{vm.painelCiot.quantidadeTarifas ? vm.painelCiot.quantidadeTarifas : 'Não informado'}}
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6" style="font-size: 13px;">
                <a class="labels-resume-style">Valor das tarifas: </a>
                {{vm.painelCiot.valorTarifas ? 'R$ ' + vm.painelCiot.valorTarifas : 'Não informado'}}
            </div>

            <div ng-show="vm.isNew() && vm.painelCiot.tipo === 3">
                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6" style="font-size: 13px;">
                    <a class="labels-resume-style">Valor do frete: </a>
                    {{vm.ciotViagem.valorFrete ? 'R$ ' + vm.ciotViagem.valorFrete : 'Não informado'}}
                </div>
                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6" style="font-size: 13px;">
                    <a class="labels-resume-style">Valor dos impostos: </a>
                    {{vm.ciotViagem.valorImposto ? 'R$ ' + vm.ciotViagem.valorImposto : 'Não informado'}}
                </div>
                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6" style="font-size: 13px;">
                    <a class="labels-resume-style">Valor das despesas: </a>
                    {{vm.ciotViagem.valorDespesas ? 'R$ ' + vm.ciotViagem.valorDespesas : 'Não informado'}}
                </div>
                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6" style="font-size: 13px;">
                    <a class="labels-resume-style">Valor do combustível: </a>
                    {{vm.ciotViagem.valorCombustivel ? 'R$ ' + vm.ciotViagem.valorCombustivel : 'Não informado'}}
                </div>
                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6" style="font-size: 13px;">
                    <a class="labels-resume-style">Valor dos pedágios: </a>
                    {{vm.ciotViagem.valorPedagio ? 'R$ ' + vm.ciotViagem.valorPedagio : 'Não informado'}}
                </div>
            </div>
        </div>

        <a class="col-xs-12 navbar-brand" style="color: black; text-align: left; font-weight: bold;">Veículos</a>
        <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12" style="font-size: 13px; line-height: 25px;"
            ng-show="vm.painelCiot.veiculosList.length === 0">
            <a class="labels-resume-style">Nenhum veículo foi adicionado</a>
        </div>

        <div class="col-xs-12" style="background-color: #ebebeb; padding: 8px;"
            ng-show="vm.painelCiot.veiculosList.length !== 0">
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th style="color: black">Placa</th>
                            <th style="color: black">RNTRC</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr ng-repeat="veiculo in vm.painelCiot.veiculosList">
                            <td>{{veiculo.placa}}</td>
                            <td>{{vm.consultaProprietario.selectedEntity.rntrc ?
                                vm.consultaProprietario.selectedEntity.rntrc : veiculo.rntrc}}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="navbar navbar-default" style="background-color: rgba(204, 204, 204, 0.4)"
        ng-show="vm.painelCiot.tipo === 1 && vm.isVisualizar()">
        <div>
            <a class="col-xs-9 col-sm-11 navbar-brand"
                style="color: black; text-align: left; font-weight: bold; font-size: 20px">Viagem</a>
            <a class="col-xs-3 col-sm-1 btn btn-primary; button-edit-style" role="button"
                ng-click="wizard.go(2)">Editar</a>
        </div>

        <a class="col-xs-12 navbar-brand" style="color: black; text-align: left; font-weight: bold;">Informações da Viagem Padrão</a>

        <div class="row" style="text-align: left; padding: 16px; line-height: 25px;">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6" style="font-size: 13px;">
                <a class="labels-resume-style">Cidade origem: </a>
                {{vm.consultaCidadeOrigem.selectedText ? vm.consultaCidadeOrigem.selectedText : 'Não informado'}}
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6" style="font-size: 13px;">
                <a class="labels-resume-style">Cidade destino: </a>
                {{vm.consultaCidadeDestino.selectedText ? vm.consultaCidadeDestino.selectedText : 'Não informado'}}
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6" style="font-size: 13px;">
                <a class="labels-resume-style">Remetente: </a>
                {{vm.consultaRemetente.selectedText ? vm.consultaRemetente.selectedText : 'Não informado'}}
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6" style="font-size: 13px;">
                <a class="labels-resume-style">Consignatário: </a>
                {{vm.consultaConsignatario.selectedText ? vm.consultaConsignatario.selectedText : 'Não informado'}}
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6" style="font-size: 13px;">
                <a class="labels-resume-style">Destinatário: </a>
                {{vm.consultaDestinatario.selectedText ? vm.consultaDestinatario.selectedText : 'Não informado'}}
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6" style="font-size: 13px;">
                <a class="labels-resume-style">Natureza Carga: </a>
                {{vm.consultaNaturezaCarga.selectedText ? vm.consultaNaturezaCarga.selectedText : 'Não informado'}}
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6" style="font-size: 13px;">
                <a class="labels-resume-style">Peso da carga: </a>
                {{vm.painelCiot.peso ? vm.painelCiot.peso : 'Não informado'}}
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6" style="font-size: 13px;">
                <a class="labels-resume-style">Valor do frete: </a>
                {{vm.painelCiot.valorFrete ? 'R$ ' + vm.painelCiot.valorFrete : 'Não informado'}}
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6" style="font-size: 13px;">
                <a class="labels-resume-style">Valor dos impostos: </a>
                {{vm.painelCiot.valorImposto ? 'R$ ' + vm.painelCiot.valorImposto : 'Não informado'}}
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6" style="font-size: 13px;">
                <a class="labels-resume-style">Valor das despesas: </a>
                {{vm.painelCiot.valorDespesas ? 'R$ ' + vm.painelCiot.valorDespesas : 'Não informado'}}
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6" style="font-size: 13px;">
                <a class="labels-resume-style">Valor do combustível: </a>
                {{vm.painelCiot.valorCombustivel ? 'R$ ' + vm.painelCiot.valorCombustivel : 'Não informado'}}
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6" style="font-size: 13px;">
                <a class="labels-resume-style">Valor dos pedágios: </a>
                {{vm.painelCiot.valorPedagio ? 'R$ ' + vm.painelCiot.valorPedagio : 'Não informado'}}
            </div>
        </div>
    </div>

    <div class="navbar navbar-default" style="background-color: rgba(204, 204, 204, 0.4)"
        ng-show="vm.painelCiot.tipo === 1 && vm.isNew()">
        <div>
            <a class="col-xs-9 col-sm-11 navbar-brand"
                style="color: black; text-align: left; font-weight: bold; font-size: 20px">Viagem</a>
            <a class="col-xs-3 col-sm-1 btn btn-primary; button-edit-style" role="button"
                ng-click="wizard.go(2)">Editar</a>
        </div>
        <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6" style="font-size: 13px; line-height: 25px;"
            ng-show="vm.painelCiot.viagensList.length === 0">
            <a class="labels-resume-style">Nenhuma viagem foi adicionada</a>
        </div>
        <div style="text-align: left; padding-left: 16px; line-height: 25px;">
            <div ng-repeat="viagem in vm.painelCiot.viagensList">
                <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12"
                    style="font-size: 16px; line-height: 40px; border-top: 1px solid #69696a; left: -9px;">
                    <a class="labels-resume-style" style="color: black;">Viagem - {{$index + 1}}</a>
                </div>
                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6" style="font-size: 13px;">
                    <a class="labels-resume-style">Cidade de origem: </a>
                    {{viagem.nomeOrigem ? viagem.nomeOrigem : 'Não informado'}}
                </div>
                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6" style="font-size: 13px;">
                    <a class="labels-resume-style">Cidade de destino: </a>
                    {{viagem.nomeDestino ? viagem.nomeDestino : 'Não informado'}}
                </div>
                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6" style="font-size: 13px;">
                    <a class="labels-resume-style">Nome remetente: </a>
                    {{viagem.nomeRemetente ? viagem.nomeRemetente : 'Não informado'}}
                </div>
                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6" style="font-size: 13px;">
                    <a class="labels-resume-style">Nome consignatário: </a>
                    {{viagem.nomeConsignatario ? viagem.nomeConsignatario : 'Não informado'}}
                </div>
                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6" style="font-size: 13px;">
                    <a class="labels-resume-style">Nome destinatário: </a>
                    {{viagem.nomeDestinatario ? viagem.nomeDestinatario : 'Não informado'}}
                </div>
                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6" style="font-size: 13px;">
                    <a class="labels-resume-style">Natureza da carga: </a>
                    {{viagem.codigoNaturezaCarga ? viagem.codigoNaturezaCarga : 'Não informado'}}
                </div>
                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6" style="font-size: 13px;">
                    <a class="labels-resume-style">Peso da carga: </a>
                    {{viagem.peso ? viagem.peso : 'Não informado'}}
                </div>
                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6" style="font-size: 13px;">
                    <a class="labels-resume-style">Valor do frete: </a>
                    {{viagem.valorFrete ? viagem.valorFrete : 'Não informado'}}
                </div>
                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6" style="font-size: 13px;">
                    <a class="labels-resume-style">Valor dos impostos: </a>
                    {{viagem.valorImposto ? viagem.valorImposto : 'Não informado'}}
                </div>
                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6" style="font-size: 13px;">
                    <a class="labels-resume-style">Valor das despesas: </a>
                    {{viagem.valorDespesas ? viagem.valorDespesas : 'Não informado'}}
                </div>
                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6" style="font-size: 13px;">
                    <a class="labels-resume-style">Valor do combustível: </a>
                    {{viagem.valorCombustivel ? viagem.valorCombustivel : 'Não informado'}}
                </div>
                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6" style="font-size: 13px;">
                    <a class="labels-resume-style">Valor dos pedágios: </a>
                    {{viagem.valorPedagio ? viagem.valorPedagio : 'Não informado'}}
                </div>
            </div>
        </div>
    </div>


    <div class="navbar navbar-default" style="background-color: rgba(204, 204, 204, 0.4)"
        ng-show="!vm.isNew() && vm.painelCiot.tipo === 3">
        <div>
            <a class="col-xs-9 col-sm-11 navbar-brand"
                style="color: black; text-align: left; font-weight: bold; font-size: 20px">Viagem</a>
            <a class="col-xs-3 col-sm-1 btn btn-primary; button-edit-style" role="button"
                ng-click="wizard.go(2)">Editar</a>
        </div>
        <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6" style="font-size: 13px; line-height: 25px;"
            ng-show="vm.painelCiot.viagensList.length === 0">
            <a class="labels-resume-style">Nenhuma viagem foi adicionada</a>
        </div>
        <div style="text-align: left; padding-left: 16px; line-height: 25px;">
            <div ng-repeat="viagem in vm.painelCiot.viagensList">
                <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12"
                    style="font-size: 16px; line-height: 40px; border-top: 1px solid #69696a; left: -9px;">
                    <a class="labels-resume-style" style="color: black;">Viagem - {{$index + 1}}</a>
                </div>
                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6" style="font-size: 13px;">
                    <a class="labels-resume-style">Cidade de origem: </a>
                    {{viagem.cidadeOrigem.nome ? viagem.cidadeOrigem.nome : 'Não informado'}}
                </div>
                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6" style="font-size: 13px;">
                    <a class="labels-resume-style">Cidade de destino: </a>
                    {{viagem.cidadeDestino.nome ? viagem.cidadeDestino.nome : 'Não informado'}}
                </div>
                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6" style="font-size: 13px;">
                    <a class="labels-resume-style">Nome remetente: </a>
                    {{viagem.nomeRemetente ? viagem.nomeRemetente : 'Não informado'}}
                </div>
                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6" style="font-size: 13px;">
                    <a class="labels-resume-style">Nome consignatário: </a>
                    {{viagem.nomeConsignatario ? viagem.nomeConsignatario : 'Não informado'}}
                </div>
                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6" style="font-size: 13px;">
                    <a class="labels-resume-style">Nome destinatário: </a>
                    {{viagem.nomeDestinatario ? viagem.nomeDestinatario : 'Não informado'}}
                </div>
                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6" style="font-size: 13px;">
                    <a class="labels-resume-style">Natureza da carga: </a>
                    {{viagem.codigoNaturezaCarga ? viagem.codigoNaturezaCarga : 'Não informado'}}
                </div>
                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6" style="font-size: 13px;">
                    <a class="labels-resume-style">Peso da carga: </a>
                    {{viagem.peso ? viagem.peso : 'Não informado'}}
                </div>
                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6" style="font-size: 13px;">
                    <a class="labels-resume-style">Valor do frete: </a>
                    {{viagem.valorFrete ? viagem.valorFrete : 'Não informado'}}
                </div>
                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6" style="font-size: 13px;">
                    <a class="labels-resume-style">Valor dos impostos: </a>
                    {{viagem.valorImposto ? viagem.valorImposto : 'Não informado'}}
                </div>
                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6" style="font-size: 13px;">
                    <a class="labels-resume-style">Valor das despesas: </a>
                    {{viagem.valorDespesas ? viagem.valorDespesas : 'Não informado'}}
                </div>
                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6" style="font-size: 13px;">
                    <a class="labels-resume-style">Valor do combustível: </a>
                    {{viagem.valorCombustivel ? viagem.valorCombustivel : 'Não informado'}}
                </div>
                <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6" style="font-size: 13px;">
                    <a class="labels-resume-style">Valor dos pedágios: </a>
                    {{viagem.valorPedagio ? viagem.valorPedagio : 'Não informado'}}
                </div>
            </div>
        </div>
    </div>


    <div class="navbar navbar-default" style="background-color: rgba(204, 204, 204, 0.4)">
        <div>
            <a class="col-xs-9 col-sm-11 navbar-brand"
                style="color: black; text-align: left; font-weight: bold; font-size: 20px">Pagamento</a>
            <a class="col-xs-3 col-sm-1 btn btn-primary; button-edit-style" role="button"
                ng-click="wizard.go(3)">Editar</a>
        </div>
        <div class="row" style="text-align: left; padding: 16px; line-height: 25px;">
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6" style="font-size: 13px;">
                <a class="labels-resume-style">Forma de pagamento: </a>
                {{vm.painelCiot.pagamento.formaPagamento ? vm.consultaDescricaoFormaPagamento() :
                vm.painelCiot.pagamento.formaPagamento === 0 ? 'Depósito' : 'Não informado'}}
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6" style="font-size: 13px;"
                ng-show="vm.painelCiot.formaPagamento === 0">
                <a class="labels-resume-style">Agência: </a>
                {{vm.painelCiot.agencia ? vm.painelCiot.agencia : 'Não informado'}}
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6" style="font-size: 13px;"
                ng-show="vm.painelCiot.formaPagamento === 0">
                <a class="labels-resume-style">Conta: </a>
                {{vm.painelCiot.verificadorConta ? vm.painelCiot.conta + '-' + vm.painelCiot.verificadorConta : 'Não
                informado'}}
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6" style="font-size: 13px;"
                ng-show="vm.painelCiot.formaPagamento === 0">
                <a class="labels-resume-style">Banco: </a>
                {{vm.consultaBanco.selectedText ? vm.consultaBanco.selectedText : 'Não informado'}}
            </div>
            <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6" style="font-size: 13px;"
                ng-show="vm.painelCiot.formaPagamento === 0">
                <a class="labels-resume-style">Tipo conta: </a>
                {{vm.painelCiot.tipoConta ? vm.painelCiot.tipoConta : 'Não informado'}}
            </div>
        </div>
    </div>
</div>