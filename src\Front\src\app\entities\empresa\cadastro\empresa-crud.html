<div id="EmpresaCrudController" ng-controller="EmpresaCrudController as vm">
    <form-header ng-submit="return" items="vm.headerItems" head="'Empresa'"></form-header>
    <div class="wrapper-content animated overflow-hidden fadeIn mb-0 pb-0">
        <div class="row">
            <div class="col-lg-12">
                <div class="ibox animated fadeIn">
                    <div class="ibox-title">
                        <h5>{{vm.isNew() ? 'Nova' : 'Editar'}} empresa</h5>
                        <div ibox-tools></div>
                    </div>
                    <div class="ibox-content wizard">
                        <form name="frmEmpresaCrud" role="form" novalidate ats-validator
                            ng-submit="vm.save(frmEmpresaCrud)" show-validation>
                            <div form-wizard steps="11">
                                <div class="form-wizard">
                                    <ol class="row" style="display: flex; flex-wrap: wrap;">
                                        <li ng-class="{'active':wizard.active(1)}" ng-click="wizard.go(1)"
                                            class="control-label col-xs-12 col-sm-4 col-md-4 col-lg-4">
                                            <h4>Principal</h4>
                                        </li>
                                        <li ng-class="{'active':wizard.active(2)}" ng-click="wizard.go(2)"
                                            class="control-label col-xs-12 col-sm-4 col-md-4 col-lg-4">
                                            <h4>Endereço</h4>
                                        </li>
                                        <li ng-show="!vm.isNew()" ng-class="{'active':wizard.active(3)}"
                                            ng-click="wizard.go(3)"
                                            class="control-label col-xs-12 col-sm-4 col-md-4 col-lg-4">
                                            <h4>Extrato</h4>
                                        </li>
                                        <li ng-show="vm.isAdmin()" ng-class="{'active':wizard.active(4)}"
                                            ng-click="wizard.go(4)"
                                            class="control-label col-xs-12 col-sm-4 col-md-4 col-lg-4">
                                            <h4>Parâmetros</h4>
                                        </li>
                                        <li ng-show="vm.isAdmin()" ng-class="{'active':wizard.active(5)}"
                                            ng-click="wizard.go(5)"
                                            class="control-label col-xs-12 col-sm-4 col-md-4 col-lg-4">
                                            <h4>Link</h4>
                                        </li>
                                        <li ng-show="vm.isAdmin() && vm.isNew()" ng-class="{'active':wizard.active(6)}"
                                            ng-click="wizard.go(6)"
                                            class="control-label col-xs-12 col-sm-4 col-md-4 col-lg-4">
                                            <h4>Abastecimento</h4>
                                        </li>
                                        <li ng-show="vm.isAdmin() && !vm.isNew()" ng-class="{'active':wizard.active(6)}"
                                            ng-click="wizard.go(6)"
                                            class="control-label col-xs-12 col-sm-4 col-md-4 col-lg-4">
                                            <h4>Abastecimento</h4>
                                        </li>
                                        <li ng-show="!vm.isNew()" ng-class="{'active':wizard.active(7)}"
                                            ng-click="wizard.go(7)"
                                            class="control-label col-xs-12 col-sm-4 col-md-4 col-lg-4">
                                            <h4>CFOP</h4>
                                        </li>
                                        <li ng-show="!vm.isNew()" ng-class="{'active':wizard.active(8)}"
                                            ng-click="wizard.go(8)"
                                            class="control-label col-xs-12 col-sm-4 col-md-4 col-lg-4">
                                            <h4>Impostos</h4>
                                        </li>
                                    
                                        <li ng-class="{'active':wizard.active(9)}"
                                            ng-click="wizard.go(9)" ng-show="vm.empresa.registraCiot"
                                            class="control-label col-xs-12 col-sm-4 col-md-4 col-lg-4">
                                            <h4>CIOT</h4>
                                        </li>
                                   
                                        <li ng-class="{'active':wizard.active(10)}" ng-show="vm.isAdmin()"
                                            ng-click="wizard.go(10)" 
                                            class="control-label col-xs-12 col-sm-4 col-md-4 col-lg-4">
                                            <h4>Tarifas</h4>
                                        </li>
                                   
                                        <li ng-class="{'active':wizard.active(11)}" ng-show="vm.isAdmin()"
                                            ng-click="wizard.go(11)" 
                                            class="control-label col-xs-12 col-sm-4 col-md-4 col-lg-4">
                                            <h4>Documentos</h4>
                                        </li>
                                    </ol>

                                    <div ng-show="!vm.carregandoEdit">
                                        <div ng-show="wizard.active(1)">
                                            <div ng-include="'app/entities/empresa/cadastro/abas/principal.html'"
                                                class="form-horizontal"> </div>
                                        </div>
                                        <div ng-show="wizard.active(2)">
                                            <div ng-include="'app/entities/empresa/cadastro/abas/endereco.html'"
                                                class="form-horizontal"></div>
                                        </div>
                                        <div ng-show="wizard.active(3)">
                                            <div ng-include="'app/entities/empresa/cadastro/abas/extrato.html'"
                                                class="form-horizontal"></div>
                                        </div>
                                        <div ng-show="wizard.active(4)">
                                            <div ng-include="'app/entities/empresa/cadastro/abas/parametro.html'"
                                                class="form-horizontal"></div>
                                        </div>
                                        <div ng-show="wizard.active(5)">
                                            <div ng-include="'app/entities/empresa/cadastro/abas/link.html'"
                                                class="form-horizontal"></div>
                                        </div>
                                        <div ng-show="wizard.active(6)">
                                            <div ng-include="'app/entities/empresa/cadastro/abas/abastecimento.html'"
                                                class="form-horizontal"></div>
                                        </div>
                                        <div ng-show="wizard.active(7);">
                                            <div ng-include="'app/entities/empresa/cadastro/abas/cfop.html'"
                                                class="form-horizontal"></div>
                                        </div>
                                        <div ng-show="wizard.active(8)">
                                            <div ng-include="'app/entities/empresa/cadastro/abas/impostos.html'"
                                                class="form-horizontal"></div>
                                        </div>
                                        <div ng-show="wizard.active(9)">
                                            <div ng-include="'app/entities/empresa/cadastro/abas/ciot.html'"
                                                 class="form-horizontal"></div>
                                        </div>
                                        <div ng-show="wizard.active(10)">
                                            <div ng-include="'app/entities/empresa/cadastro/abas/tarifas.html'"
                                                 class="form-horizontal"></div>
                                        </div>
                                        </div>
                                        <div ng-show="wizard.active(11)">
                                            <div ng-include="'app/entities/empresa/cadastro/abas/documentos.html'"
                                                 class="form-horizontal"></div>
                                        </div>
                                    </div>
                                    <hr />
                                    <div ng-show="vm.carregandoEdit">
                                        <div class="spiner-example">
                                            <div class="sk-spinner sk-spinner-wave">
                                                <div class="sk-rect1"></div>
                                                <div class="sk-rect2"></div>
                                                <div class="sk-rect3"></div>
                                                <div class="sk-rect4"></div>
                                                <div class="sk-rect5"></div>
                                                <div class="sk-rect6"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row clearfix"> </div>
                                <hr-label dark="true"></hr-label>
                                <br/>
                                <div class="row">
                                    <div class="form-group">
                                        <div class="text-right">
                                            <button type="button" ng-disabled="vm.saving"
                                                ng-click="vm.onClickVoltar(wizard)"
                                                class="btn btn-labeled btn-default">
                                                <span class="btn-label">
                                                    <i class="fa fa-arrow-circle-left"></i>
                                                </span>
                                                Voltar
                                            </button>
                                            <button type="button" ng-show="!wizard.active(vm.lastIndex)" ng-disabled="vm.saving"
                                                ng-click="vm.onClickAvancar(wizard)"
                                                class="btn btn-labeled btn-default">
                                                <span class="btn-label">
                                                    <i class="fa fa-arrow-circle-right"></i>
                                                </span>
                                                Avançar
                                            </button>
                                            <button type="submit" ng-show="wizard.active(vm.lastIndex)" ng-disabled="vm.saving"
                                                class="btn btn-labeled btn-success text-right ladda-button"
                                                data-style="expand-right">
                                                <span class="btn-label">
                                                    <i class="fa fa-check-circle"></i>
                                                </span>
                                                Salvar
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>