(function () {
    'use strict';

    angular.module('bbcWeb').controller('PainelPagamentoEventoCrudController', PainelPagamentoEventoCrudController);

    PainelPagamentoEventoCrudController.$inject = ['toastr', '$rootScope', 'BaseService', '$state', '$stateParams', 
        '$window', 'PERFIL_ADMINISTRADOR', '$scope', '$timeout', 'PersistentDataService', 'DefaultsService', '$uibModal'];

    function PainelPagamentoEventoCrudController(
        toastr, $rootScope,
        BaseService, $state,
        $stateParams, $window,
        PERFIL_ADMINISTRADOR,
        $scope, $timeout,
        PersistentDataService,
        DefaultsService,
        $uibModal
    ) {
        var vm = this;
        vm.pagamentoEvento = {};
        vm.salvandoOcorrencia = false;

        vm.headerItems = [
            {name: 'Movimentações'},
            {name: '<PERSON><PERSON> de pagamentos', link: 'painel-pagamento.index'},
            {name: ($stateParams.link === 'novo' ? 'Novo' : 'Editar') + ' pagamento BBC'}
        ];

        vm.load = function () {
        }

        vm.loadEdit = function (id) {
            BaseService.get('PagamentoEvento', 'ConsultarPorId', {idPagamentoEvento: id})
                .then(function (response) {
                    if (!response.success) {
                        toastr.error(response.message);
                        return;
                    }

                    vm.pagamentoEvento = response.data;
                });
        };

        vm.isNew = function () {
            return $stateParams.link === 'novo';
        };

        vm.salvarOcorrencia = function () {
            var request = {
                pagamentoEventoId: vm.pagamentoEvento.id,
                ocorrencia: vm.pagamentoEvento.ocorrencia
            };

            vm.salvandoOcorrencia = true;
            
            BaseService.post('PagamentoEvento', 'SalvarOcorrencia', request).then(function (response) {
                vm.salvandoOcorrencia = false;
                
                if (!response.success) return toastr.error(response.message)

                toastr.success(response.message)
                
                $state.go('painel-pagamento.index');
            });
        }

        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.administrador;
        };

        vm.onClickVoltar = function (wizard) {
            var ativoIndex = wizard.getActivePosition();
            if (ativoIndex === 1)
                $state.go('painel-pagamento.index');

            wizard.go(ativoIndex - 1);
        };

        var selfScope = PersistentDataService.get('PainelPagamentoEventoCrudController');

        if ($stateParams.link === 'novo')
            vm.painelPagamento.id = 0;

        if (angular.isDefined(selfScope))
            angular.extend(vm, selfScope.data);
        else if (!vm.isNew())
            vm.loadEdit($stateParams.link);
        else
            vm.load();

        function init() {
        }
        
        init();
        
        // DO NOT TOUCH!
        $scope.$on('$stateChangeStart', function (arg1, toState) {
            if (toState.name === 'painel-pagamento.index')
                PersistentDataService.remove('PainelPagamentoEventoCrudController');

            else
                PersistentDataService.store('PainelPagamentoEventoCrudController', vm, "Movimentação - Painel de pagamento",
                    null, "painel-pagamento.painel-pagamentoevento-crud", vm.painelPagamento.id);
        });

        $timeout(function () {
            PersistentDataService.remove('PainelPagamentoController');
        }, 15);
    }
})();
