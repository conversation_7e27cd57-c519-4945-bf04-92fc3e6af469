(function () {
    'use strict';

    angular.module('bbcWeb').controller('ConfiguracaoTelaoController', ConfiguracaoTelaoController);

    ConfiguracaoTelaoController.$inject = ['toastr', '$rootScope', 'BaseService', '$state', '$stateParams', '$scope', '$timeout', 'PersistentDataService'];

    function ConfiguracaoTelaoController(
        toastr,
        $rootScope,
        BaseService,
        $state,
        $stateParams,
        $scope,
        $timeout,
        PersistentDataService
    ) {
        var vm = this;
        vm.configuracaoValePedagio = {};
        vm.tipoValorMaximoComplemento = true;

        vm.headerItems = [{
            name: 'Cadastros'
        }, {
            name: 'Parâmetros gerais',
            link: 'parametros.index'
        }];

        vm.loadEdit = function () {
            BaseService.get('Parametros', 'ConsultaParametrosConfiguracaoTelaoSaldo', {})
                .then(function (response) {
                    if (!response) {
                        toastr.error('Erro de comunicação com o servidor, tente novamente em alguns minutos.');
                        return;
                    }

                    if (!response.success) {
                        toastr.error(response.message);
                        return;
                    }

                    vm.configuracaoTelao = response.data;
                });
        };

        vm.save = function (form) {
            if (!form.$valid) {
                toastr.error('Todos os campos obrigatórios devem ser preenchidos corretamente com seus respectivos valores.');
                return;
            }

            if (vm.saving) return;

            var request = {}
            request.quantidadeLayoutTelao = vm.configuracaoTelao.quantidadeLayoutTelao
            request.tempoAtualizacaoTelao = vm.configuracaoTelao.tempoAtualizacaoTelao
            
            if(request.quantidadeLayoutTelao <= 0){
                toastr.error('Quantidade de layout não pode ser menor que 1.');
                return;
            }

            if(request.tempoAtualizacaoTelao <= 0){
                toastr.error('Tempo de atualização não pode ser menor que 1.');
                return;
            }


            vm.saving = true;

            BaseService.post('Parametros', 'SalvarConfiguracaoTelaoSaldo', request)
                .then(function (response) {
                    vm.saving = false;
                    if (!response) {
                        toastr.error('Erro de comunicação com o servidor, tente novamente em alguns minutos.');
                        return;
                    }
                    if (!response.success) {
                        toastr.error(response.message);
                        return;
                    }
                    toastr.success(response.message);
                    $state.go('parametros.index');
                })
        };


        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.administrador == true;
        };

        function init() {
            vm.loadEdit();
        };

        vm.onClickVoltar = function (wizard) {

            var ativoIndex = wizard.getActivePosition();
            if (ativoIndex == 1)
                $state.go('parametros.index');

            wizard.go(ativoIndex - 1);
        };

        init();
    }
})();
