webshims.validityMessages.he={typeMismatch:{email:"\u05e0\u05d0 \u05dc\u05d4\u05d6\u05d9\u05df \u05db\u05ea\u05d5\u05d1\u05ea \u05d3\u05d5\u05d0\u05f4\u05dc.",url:"\u05e0\u05d0 \u05dc\u05d4\u05d6\u05d9\u05df \u05db\u05ea\u05d5\u05d1\u05ea."},badInput:{number:"\u05e2\u05e8\u05da \u05dc\u05d0 \u05d7\u05d5\u05e7\u05d9.",date:"\u05e2\u05e8\u05da \u05dc\u05d0 \u05d7\u05d5\u05e7\u05d9.",time:"\u05e2\u05e8\u05da \u05dc\u05d0 \u05d7\u05d5\u05e7\u05d9.",range:"\u05e2\u05e8\u05da \u05dc\u05d0 \u05d7\u05d5\u05e7\u05d9.","datetime-local":"\u05e2\u05e8\u05da \u05dc\u05d0 \u05d7\u05d5\u05e7\u05d9."},tooLong:"\u05e2\u05e8\u05da \u05dc\u05d0 \u05d7\u05d5\u05e7\u05d9.",patternMismatch:"\u05e0\u05d0 \u05dc\u05d4\u05ea\u05d0\u05d9\u05dd \u05dc\u05de\u05d1\u05e0\u05d4 \u05d4\u05de\u05d1\u05d5\u05e7\u05e9: {%title}.",valueMissing:{defaultMessage:"\u05d0\u05e0\u05d0 \u05de\u05dc\u05d0 \u05d0\u05ea \u05e9\u05d3\u05d4 \u05d6\u05d4.",checkbox:"\u05e1\u05de\u05df \u05d0\u05ea \u05ea\u05d9\u05d1\u05d4 \u05d6\u05d5 \u05d0\u05dd \u05d1\u05e8\u05e6\u05d5\u05e0\u05da \u05dc\u05d4\u05de\u05e9\u05d9\u05da.",select:"\u05e0\u05d0 \u05dc\u05d1\u05d7\u05d5\u05e8 \u05e4\u05e8\u05d9\u05d8 \u05de\u05d4\u05e8\u05e9\u05d9\u05de\u05d4.",radio:"\u05d0\u05e0\u05d0 \u05d1\u05d7\u05e8 \u05d0\u05d7\u05ea \u05de\u05d0\u05e4\u05e9\u05e8\u05d5\u05d9\u05d5\u05ea \u05d0\u05dc\u05d5."},rangeUnderflow:{defaultMessage:"\u05d4\u05e2\u05e8\u05da \u05d7\u05d9\u05d9\u05d1 \u05dc\u05d4\u05d9\u05d5\u05ea \u05d2\u05d3\u05d5\u05dc \u05de-{%min} \u05d0\u05d5 \u05e9\u05d5\u05d5\u05d4 \u05dc\u05d5.",date:"\u05d4\u05e2\u05e8\u05da \u05d7\u05d9\u05d9\u05d1 \u05dc\u05d4\u05d9\u05d5\u05ea \u05d2\u05d3\u05d5\u05dc \u05de-{%min} \u05d0\u05d5 \u05e9\u05d5\u05d5\u05d4 \u05dc\u05d5.",time:"\u05d4\u05e2\u05e8\u05da \u05d7\u05d9\u05d9\u05d1 \u05dc\u05d4\u05d9\u05d5\u05ea \u05d2\u05d3\u05d5\u05dc \u05de-{%min} \u05d0\u05d5 \u05e9\u05d5\u05d5\u05d4 \u05dc\u05d5.","datetime-local":"\u05d4\u05e2\u05e8\u05da \u05d7\u05d9\u05d9\u05d1 \u05dc\u05d4\u05d9\u05d5\u05ea \u05d2\u05d3\u05d5\u05dc \u05de-{%min} \u05d0\u05d5 \u05e9\u05d5\u05d5\u05d4 \u05dc\u05d5."},rangeOverflow:{defaultMessage:"\u05d4\u05e2\u05e8\u05da \u05d7\u05d9\u05d9\u05d1 \u05dc\u05d4\u05d9\u05d5\u05ea \u05e7\u05d8\u05df \u05de-{%max} \u05d0\u05d5 \u05e9\u05d5\u05d5\u05d4 \u05dc\u05d5.",date:"\u05d4\u05e2\u05e8\u05da \u05d7\u05d9\u05d9\u05d1 \u05dc\u05d4\u05d9\u05d5\u05ea \u05e7\u05d8\u05df \u05de-{%max} \u05d0\u05d5 \u05e9\u05d5\u05d5\u05d4 \u05dc\u05d5.",time:"\u05d4\u05e2\u05e8\u05da \u05d7\u05d9\u05d9\u05d1 \u05dc\u05d4\u05d9\u05d5\u05ea \u05e7\u05d8\u05df \u05de-{%max} \u05d0\u05d5 \u05e9\u05d5\u05d5\u05d4 \u05dc\u05d5.","datetime-local":"\u05d4\u05e2\u05e8\u05da \u05d7\u05d9\u05d9\u05d1 \u05dc\u05d4\u05d9\u05d5\u05ea \u05e7\u05d8\u05df \u05de-{%max} \u05d0\u05d5 \u05e9\u05d5\u05d5\u05d4 \u05dc\u05d5."},stepMismatch:"\u05e2\u05e8\u05da \u05dc\u05d0 \u05d7\u05d5\u05e7\u05d9."},webshims.formcfg.he={numberFormat:{".":".",",":","},numberSigns:".",dateSigns:"/",timeSigns:":. ",dFormat:"/",patterns:{d:"dd/mm/yy"},date:{closeText:"\u05e1\u05d2\u05d5\u05e8",prevText:"&#x3C;\u05d4\u05e7\u05d5\u05d3\u05dd",nextText:"\u05d4\u05d1\u05d0&#x3E;",currentText:"\u05d4\u05d9\u05d5\u05dd",monthNames:["\u05d9\u05e0\u05d5\u05d0\u05e8","\u05e4\u05d1\u05e8\u05d5\u05d0\u05e8","\u05de\u05e8\u05e5","\u05d0\u05e4\u05e8\u05d9\u05dc","\u05de\u05d0\u05d9","\u05d9\u05d5\u05e0\u05d9","\u05d9\u05d5\u05dc\u05d9","\u05d0\u05d5\u05d2\u05d5\u05e1\u05d8","\u05e1\u05e4\u05d8\u05de\u05d1\u05e8","\u05d0\u05d5\u05e7\u05d8\u05d5\u05d1\u05e8","\u05e0\u05d5\u05d1\u05de\u05d1\u05e8","\u05d3\u05e6\u05de\u05d1\u05e8"],monthNamesShort:["\u05d9\u05e0\u05d5","\u05e4\u05d1\u05e8","\u05de\u05e8\u05e5","\u05d0\u05e4\u05e8","\u05de\u05d0\u05d9","\u05d9\u05d5\u05e0\u05d9","\u05d9\u05d5\u05dc\u05d9","\u05d0\u05d5\u05d2","\u05e1\u05e4\u05d8","\u05d0\u05d5\u05e7","\u05e0\u05d5\u05d1","\u05d3\u05e6\u05de"],dayNames:["\u05e8\u05d0\u05e9\u05d5\u05df","\u05e9\u05e0\u05d9","\u05e9\u05dc\u05d9\u05e9\u05d9","\u05e8\u05d1\u05d9\u05e2\u05d9","\u05d7\u05de\u05d9\u05e9\u05d9","\u05e9\u05d9\u05e9\u05d9","\u05e9\u05d1\u05ea"],dayNamesShort:["\u05d0'","\u05d1'","\u05d2'","\u05d3'","\u05d4'","\u05d5'","\u05e9\u05d1\u05ea"],dayNamesMin:["\u05d0'","\u05d1'","\u05d2'","\u05d3'","\u05d4'","\u05d5'","\u05e9\u05d1\u05ea"],weekHeader:"Wk",firstDay:0,isRTL:!0,showMonthAfterYear:!1,yearSuffix:""}};