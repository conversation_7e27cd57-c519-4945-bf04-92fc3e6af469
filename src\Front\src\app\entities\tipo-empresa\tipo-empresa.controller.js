(function () {
    'use strict';

    angular
        .module('bbcWeb')
        .controller('TipoEmpresaController', TipoEmpresaController);

    TipoEmpresaController.inject = ['BaseService', '$rootScope', 'toastr', '$scope', 'PersistentDataService', '$timeout', '$state', 'PERFIL_ADMINISTRADOR'];

    function TipoEmpresaController(BaseService, $rootScope, toastr, $scope, PersistentDataService, $timeout, $state, PERFIL_ADMINISTRADOR) {
        var vm = this;
        vm.headerItems = [{
            name: '<PERSON><PERSON><PERSON><PERSON>'
        }, {
            name: 'Tipo de empresa' 
        }];

        vm.isAdmin = function () {
            return $rootScope.usuarioLogado.perfil == PERFIL_ADMINISTRADOR;
        };

        vm.gridOptions = {
            data: [],
            onRegisterApi: BaseService.dataGrid.defaultOnRegisterApi,
            
            dataSource: {
                url: "TipoEmpresa/ConsultarGridTipoEmpresa",
                params: function () {
                    return {
                        isModal: true
                    }
                }
            },
            columnDefs: [{
                name: '<PERSON><PERSON>õ<PERSON>',
                width: 80,
                cellTemplate: '<div ng-if="!row.groupHeader" class="ui-grid-cell-contents" title="TOOLTIP">\
                                    <button ng-disabled="row.entity.ativo===0" tooltip-placement="right" uib-tooltip="Editar" type="button" ui-sref="tipo-empresa.tipo-empresa-crud({link: row.entity.id})"\
                                        ng-class="{ \'btn btn-xs btn-info\': true }">\
                                        <i class="fa fa-edit"></i>\
                                    </button>\
                                    <button type="button" tooltip-placement="right" uib-tooltip-html="row.entity.ativo===0 ? \'Ativar\' : \'Inativar\'" \
                                        ng-click="grid.appScope.vm.alterarStatus(row.entity.id, row.entity.ativo)" \
                                        ng-class="row.entity.ativo===1 ? \'btn btn-xs btn-success\' : \'btn btn-xs btn-danger\'"> \
                                        <i ng-class="row.entity.ativo===1 ? \'fa fa-check\' : \'fa fa-times\'"></i>\
                                    </button>\
                                </div>'
            }, {
                name: 'Código',
                width: 80,
                type: 'number',
                primaryKey: true,
                field: 'id'
            }, {
                displayName: 'Tipo de empresa',
                width: '*',
                field: 'nome'
            }]
        };

        vm.alterarStatus = function (id, ativo) {
            BaseService.post('TipoEmpresa', "AlterarStatus", {
                id: id
            }).then(function (response) {
                response.success ? ativo ? toastr.success('Tipo de empresa inativado com sucesso!') : toastr.success('Tipo de empresa reativado com sucesso!') : toastr.error(response.message);
                vm.gridOptions.dataSource.refresh();
            });
        };

        // Controle de aba!!
        $scope.$on('$stateChangeStart', function () {
            PersistentDataService.store('TipoEmpresaController', vm, "TipoEmpresa", "TipoEmpresaCrudController", "tipoEmpresa.index");
        });

        var selfScope = PersistentDataService.get('TipoEmpresaController');
        var filho = PersistentDataService.get('TipoEmpresaCrudController');
        if (angular.isDefined(filho)) {
            $timeout(function () {
                $state.go('tipo-empresa.tipoEmpresa-crud', {
                    link: filho.data.tipoEmpresa.IdTipoEmpresa > 0 ? 'editar' : 'novo'
                });
            }, 50);
        } else if (angular.isDefined(selfScope)) {
            angular.extend(vm, selfScope.data);

            $timeout(function () {
                vm.gridOptions.dataSource.refresh();
            }, 450);
        }
    }
})();